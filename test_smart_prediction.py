#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试智能推测方案 - 比赛ID 2213559
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from enhanced_odds_scraper import EnhancedOddsScraper
import logging

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_smart_prediction():
    """测试智能推测方案"""
    print("🚀 测试智能推测方案 - 比赛ID 2213559")
    print("=" * 60)
    
    scraper = EnhancedOddsScraper()
    match_id = "2213559"
    
    print(f"\n🎯 测试比赛: {match_id}")
    print("阿斯顿维拉 vs 埃弗顿 (2022-08-13 19:30)")
    print("-" * 40)
    
    # 直接测试智能推测方案
    print("\n📋 测试智能推测方案")
    print("-" * 40)
    
    company_links = scraper.get_company_odds_links_from_known_pattern(match_id)
    
    if company_links:
        print(f"✅ 智能推测成功：生成了 {len(company_links)} 家公司的链接")
        
        # 显示前几家公司的链接
        for i, (company_id, link_data) in enumerate(list(company_links.items())[:5], 1):
            company_name = link_data['company_name']
            record_id = link_data['record_id']
            url = link_data['url']
            print(f"  {i}. {company_name} (ID: {company_id}, Record: {record_id})")
            print(f"     URL: {url}")
    else:
        print("❌ 智能推测失败")
        return
    
    # 测试前3家公司的链接是否有效
    print(f"\n📋 验证推测链接的有效性（测试前3家）")
    print("-" * 40)
    
    test_companies = list(company_links.items())[:3]
    
    for company_id, link_data in test_companies:
        company_name = link_data['company_name']
        company_url = link_data['url']
        
        print(f"\n🔍 验证 {company_name}...")
        
        # 解析该公司的赔率历史
        company_records = scraper.parse_company_odds_history_page(company_url, company_name)
        
        if company_records:
            print(f"✅ {company_name} 链接有效，获取 {len(company_records)} 条记录")
            print(f"   📈 前3条记录:")
            for i, record in enumerate(company_records[:3], 1):
                home_odds = record.get('home_odds', 'N/A')
                draw_odds = record.get('draw_odds', 'N/A')
                away_odds = record.get('away_odds', 'N/A')
                change_time = record.get('change_time', 'N/A')
                print(f"     {i}. {home_odds} {draw_odds} {away_odds} ({change_time})")
                
            # 检查时间是否正确（应该是08月份）
            first_record = company_records[0] if company_records else {}
            change_time = first_record.get('change_time', '')
            if '08-' in change_time:
                print(f"   ✅ 时间正确：包含08月份数据")
            else:
                print(f"   ⚠️ 时间可能不正确：{change_time}")
                
        else:
            print(f"❌ {company_name} 链接无效或解析失败")
    
    # 测试完整的两步方案
    print(f"\n📋 测试完整的两步方案")
    print("-" * 40)
    
    all_records = scraper.parse_company_odds_from_two_step_method(match_id)
    
    if all_records:
        print(f"✅ 完整方案成功：获取 {len(all_records)} 条记录")
        
        # 按公司统计
        company_stats = {}
        for record in all_records:
            company = record.get('company', 'Unknown')
            if company not in company_stats:
                company_stats[company] = 0
            company_stats[company] += 1
        
        print(f"📊 涉及 {len(company_stats)} 家公司:")
        for company, count in company_stats.items():
            print(f"   {company}: {count} 条记录")
            
        # 检查时间数据
        print(f"\n📅 时间数据检查:")
        time_samples = []
        for record in all_records[:10]:  # 检查前10条
            change_time = record.get('change_time', '')
            if change_time and change_time not in time_samples:
                time_samples.append(change_time)
        
        print(f"   样本时间: {time_samples[:5]}")
        
        # 检查是否包含08月份的数据
        august_records = [r for r in all_records if '08-' in r.get('change_time', '')]
        if august_records:
            print(f"   ✅ 包含 {len(august_records)} 条08月份记录")
        else:
            print(f"   ❌ 未找到08月份记录")
            
    else:
        print("❌ 完整方案失败")
    
    print("\n" + "=" * 60)
    print("📋 测试总结")
    print("=" * 60)
    print("智能推测方案: ✅ 成功" if company_links else "智能推测方案: ❌ 失败")
    print("完整两步方案: ✅ 成功" if all_records else "完整两步方案: ❌ 失败")
    
    if all_records:
        august_count = len([r for r in all_records if '08-' in r.get('change_time', '')])
        print(f"08月份数据: ✅ {august_count} 条" if august_count > 0 else "08月份数据: ❌ 0 条")
    
    print(f"\n🎉 智能推测方案测试完成！")

if __name__ == "__main__":
    test_smart_prediction()

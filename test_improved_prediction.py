#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试改进后的智能推测算法
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from enhanced_odds_scraper import EnhancedOddsScraper
import logging

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_improved_prediction():
    """测试改进后的智能推测算法"""
    print("🚀 测试改进后的智能推测算法")
    print("=" * 60)
    
    scraper = EnhancedOddsScraper()
    
    # 测试多个比赛
    test_matches = [
        ("2213559", "2022年比赛", "已知真实数据"),
        ("2398985", "2023年比赛", "新发现的规律"),
        ("2590898", "2024年比赛", "线性推测"),
    ]
    
    for match_id, description, note in test_matches:
        print(f"\n🎯 测试比赛: {match_id}")
        print(f"描述: {description} ({note})")
        print("-" * 40)
        
        # 测试智能推测
        company_links = scraper.get_company_odds_links_from_known_pattern(match_id)
        
        if company_links:
            print(f"✅ 智能推测成功：生成了 {len(company_links)} 家公司的链接")
            
            # 显示关键公司的推测结果
            key_companies = ['281', '115', '82']  # bet365, 威廉希尔, 立博
            
            for company_id in key_companies:
                if company_id in company_links:
                    link_data = company_links[company_id]
                    company_name = link_data['company_name']
                    record_id = link_data['record_id']
                    print(f"  📊 {company_name}: record_id = {record_id}")
            
            # 验证前2家公司的链接有效性
            print(f"\n🔍 验证链接有效性（前2家）:")
            test_companies = list(company_links.items())[:2]
            
            for company_id, link_data in test_companies:
                company_name = link_data['company_name']
                company_url = link_data['url']
                
                print(f"  测试 {company_name}...")
                
                try:
                    # 解析该公司的赔率历史
                    company_records = scraper.parse_company_odds_history_page(company_url, company_name)
                    
                    if company_records:
                        print(f"    ✅ 成功获取 {len(company_records)} 条记录")
                        
                        # 检查时间数据
                        time_samples = []
                        for record in company_records[:3]:
                            change_time = record.get('change_time', '')
                            if change_time and change_time not in time_samples:
                                time_samples.append(change_time)
                        
                        if time_samples:
                            print(f"    📅 时间样本: {time_samples}")
                            
                            # 检查是否包含合理的月份数据
                            month_pattern = r'(\d{2})-'
                            months = []
                            for time_str in time_samples:
                                import re
                                match = re.search(month_pattern, time_str)
                                if match:
                                    months.append(match.group(1))
                            
                            if months:
                                unique_months = list(set(months))
                                print(f"    📊 涉及月份: {unique_months}")
                                
                                # 根据比赛判断时间是否合理
                                if match_id == "2213559" and "08" in unique_months:
                                    print(f"    ✅ 时间正确：2022年8月比赛")
                                elif match_id == "2398985" and "08" in unique_months:
                                    print(f"    ✅ 时间正确：2023年8月比赛")
                                elif match_id == "2590898" and "08" in unique_months:
                                    print(f"    ✅ 时间正确：2024年8月比赛")
                                else:
                                    print(f"    ⚠️ 时间可能不正确：{unique_months}")
                            else:
                                print(f"    ⚠️ 无法解析月份信息")
                        else:
                            print(f"    ⚠️ 无时间数据")
                    else:
                        print(f"    ❌ 未获取到记录")
                        
                except Exception as e:
                    print(f"    ❌ 解析失败: {e}")
        else:
            print("❌ 智能推测失败")
    
    # 测试完整的两步方案（只测试一个比赛以节省时间）
    print(f"\n📋 测试完整的两步方案（比赛2398985）")
    print("-" * 40)
    
    try:
        complete_data = scraper.scrape_complete_match_data("2398985", max_companies=3)
        
        if complete_data:
            print("✅ 完整数据抓取成功")
            
            # 检查比赛信息
            match_info = complete_data.get('match_info', {})
            if match_info:
                print(f"  📊 比赛信息:")
                print(f"    时间: {match_info.get('match_time', 'N/A')}")
                print(f"    主队: {match_info.get('home_team', 'N/A')}")
                print(f"    客队: {match_info.get('away_team', 'N/A')}")
            
            # 检查赔率数据
            odds_data = complete_data.get('odds_data', [])
            if odds_data:
                print(f"  💰 赔率数据: {len(odds_data)} 条记录")
                
                # 统计公司
                companies = set(record.get('company_name', 'Unknown') for record in odds_data)
                print(f"    涉及公司: {len(companies)} 家")
                
                # 检查时间数据
                august_records = [r for r in odds_data if '08-' in r.get('change_time', '')]
                if august_records:
                    print(f"    ✅ 包含 {len(august_records)} 条08月份记录")
                else:
                    print(f"    ❌ 未找到08月份记录")
            
            # 检查摘要
            summary = complete_data.get('summary', {})
            if summary:
                print(f"  📈 摘要:")
                print(f"    抓取方法: {summary.get('scraping_method', 'Unknown')}")
                print(f"    成功公司: {summary.get('successful_companies', 0)}")
        else:
            print("❌ 完整数据抓取失败")
            
    except Exception as e:
        print(f"❌ 完整数据抓取失败: {e}")
    
    print("\n" + "=" * 60)
    print("📋 测试总结")
    print("=" * 60)
    print("🎉 改进后的智能推测算法测试完成！")

if __name__ == "__main__":
    test_improved_prediction()

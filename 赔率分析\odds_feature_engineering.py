#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
赔率行为特征工程
从标准化的赔率时间序列中提取关键行为特征
"""

import json
import pandas as pd
import numpy as np
from datetime import datetime
import os
from collections import defaultdict
import matplotlib.pyplot as plt
import seaborn as sns
from scipy import stats
from sklearn.preprocessing import StandardScaler

class OddsFeatureEngineer:
    def __init__(self, normalized_data_file=None):
        self.normalized_data_file = normalized_data_file
        self.features_data = {}
        
        # 定义特征类别
        self.feature_categories = {
            'opening': ['opening_home', 'opening_draw', 'opening_away', 'opening_time'],
            'closing': ['closing_home', 'closing_draw', 'closing_away', 'closing_time'],
            'trend': ['trend_slope_home', 'trend_slope_draw', 'trend_slope_away', 'trend_r2'],
            'volatility': ['volatility_home', 'volatility_draw', 'volatility_away', 'max_change'],
            'pattern': ['direction_changes', 'monotonic_periods', 'acceleration'],
            'market': ['return_rate_trend', 'kelly_consistency', 'market_efficiency']
        }
    
    def load_normalized_data(self, filename=None):
        """加载标准化的赔率数据"""
        if filename:
            self.normalized_data_file = filename
        
        if not self.normalized_data_file:
            # 查找最新的标准化数据文件
            files = [f for f in os.listdir('.') if f.startswith('normalized_odds_') and f.endswith('.json')]
            if files:
                self.normalized_data_file = sorted(files)[-1]
                print(f"自动选择最新文件: {self.normalized_data_file}")
            else:
                print("未找到标准化数据文件")
                return None
        
        with open(self.normalized_data_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        print(f"加载标准化数据: {self.normalized_data_file}")
        return data
    
    def extract_basic_features(self, time_points, odds_series, odds_type='home'):
        """提取基础特征"""
        if len(odds_series) < 2:
            return {}
        
        odds_array = np.array(odds_series)
        time_array = np.array(time_points)
        
        features = {}
        
        # 开盘和收盘特征
        features[f'opening_{odds_type}'] = odds_array[0]
        features[f'closing_{odds_type}'] = odds_array[-1]
        features[f'opening_time'] = time_array[0]
        features[f'closing_time'] = time_array[-1]
        
        # 变化特征
        total_change = odds_array[-1] - odds_array[0]
        total_change_pct = (odds_array[-1] - odds_array[0]) / odds_array[0] * 100
        features[f'total_change_{odds_type}'] = total_change
        features[f'total_change_pct_{odds_type}'] = total_change_pct
        
        # 极值特征
        features[f'max_{odds_type}'] = np.max(odds_array)
        features[f'min_{odds_type}'] = np.min(odds_array)
        features[f'range_{odds_type}'] = np.max(odds_array) - np.min(odds_array)
        
        # 最大变化幅度
        max_increase = np.max(odds_array) - odds_array[0]
        max_decrease = odds_array[0] - np.min(odds_array)
        features[f'max_increase_{odds_type}'] = max_increase
        features[f'max_decrease_{odds_type}'] = max_decrease
        features[f'max_change_{odds_type}'] = max(abs(max_increase), abs(max_decrease))
        
        return features
    
    def extract_trend_features(self, time_points, odds_series, odds_type='home'):
        """提取趋势特征"""
        if len(odds_series) < 3:
            return {}
        
        odds_array = np.array(odds_series)
        time_array = np.array(time_points)
        
        features = {}
        
        # 线性趋势
        try:
            slope, intercept, r_value, p_value, std_err = stats.linregress(time_array, odds_array)
            features[f'trend_slope_{odds_type}'] = slope
            features[f'trend_intercept_{odds_type}'] = intercept
            features[f'trend_r2_{odds_type}'] = r_value ** 2
            features[f'trend_p_value_{odds_type}'] = p_value
        except:
            features[f'trend_slope_{odds_type}'] = 0
            features[f'trend_r2_{odds_type}'] = 0
        
        # 分段趋势（前半段 vs 后半段）
        mid_point = len(odds_array) // 2
        if mid_point > 1:
            try:
                # 前半段趋势
                slope1, _, r1, _, _ = stats.linregress(time_array[:mid_point], odds_array[:mid_point])
                # 后半段趋势
                slope2, _, r2, _, _ = stats.linregress(time_array[mid_point:], odds_array[mid_point:])
                
                features[f'early_trend_{odds_type}'] = slope1
                features[f'late_trend_{odds_type}'] = slope2
                features[f'trend_change_{odds_type}'] = slope2 - slope1
                features[f'trend_consistency_{odds_type}'] = 1 if slope1 * slope2 > 0 else 0
            except:
                features[f'trend_change_{odds_type}'] = 0
                features[f'trend_consistency_{odds_type}'] = 0
        
        return features
    
    def extract_volatility_features(self, time_points, odds_series, odds_type='home'):
        """提取波动性特征"""
        if len(odds_series) < 2:
            return {}
        
        odds_array = np.array(odds_series)
        
        features = {}
        
        # 基础波动性
        features[f'volatility_{odds_type}'] = np.std(odds_array)
        features[f'cv_{odds_type}'] = np.std(odds_array) / np.mean(odds_array) if np.mean(odds_array) > 0 else 0
        
        # 变化幅度分析
        if len(odds_array) > 1:
            changes = np.diff(odds_array)
            features[f'mean_change_{odds_type}'] = np.mean(changes)
            features[f'std_change_{odds_type}'] = np.std(changes)
            features[f'max_single_change_{odds_type}'] = np.max(np.abs(changes))
        
        # 方向变化次数
        if len(odds_array) > 2:
            direction_changes = 0
            for i in range(1, len(odds_array) - 1):
                if ((odds_array[i] > odds_array[i-1] and odds_array[i+1] < odds_array[i]) or
                    (odds_array[i] < odds_array[i-1] and odds_array[i+1] > odds_array[i])):
                    direction_changes += 1
            features[f'direction_changes_{odds_type}'] = direction_changes
            features[f'direction_change_rate_{odds_type}'] = direction_changes / (len(odds_array) - 2)
        
        return features
    
    def extract_pattern_features(self, time_points, odds_series, odds_type='home'):
        """提取模式特征"""
        if len(odds_series) < 3:
            return {}
        
        odds_array = np.array(odds_series)
        time_array = np.array(time_points)
        
        features = {}
        
        # 单调性分析
        is_increasing = all(odds_array[i] <= odds_array[i+1] for i in range(len(odds_array)-1))
        is_decreasing = all(odds_array[i] >= odds_array[i+1] for i in range(len(odds_array)-1))
        features[f'is_monotonic_{odds_type}'] = 1 if (is_increasing or is_decreasing) else 0
        features[f'is_increasing_{odds_type}'] = 1 if is_increasing else 0
        features[f'is_decreasing_{odds_type}'] = 1 if is_decreasing else 0
        
        # 加速度分析（二阶导数）
        if len(odds_array) > 2:
            second_diff = np.diff(odds_array, n=2)
            features[f'acceleration_mean_{odds_type}'] = np.mean(second_diff)
            features[f'acceleration_std_{odds_type}'] = np.std(second_diff)
        
        # 临场变化特征（最后几个时间点的变化）
        if len(odds_array) >= 3:
            late_changes = odds_array[-3:] - odds_array[-4:-1] if len(odds_array) >= 4 else odds_array[-2:] - odds_array[-3:-1]
            features[f'late_volatility_{odds_type}'] = np.std(late_changes) if len(late_changes) > 0 else 0
            features[f'final_momentum_{odds_type}'] = odds_array[-1] - odds_array[-2]
        
        # 关键时间点特征
        key_times = [-24, -12, -6, -1]  # 关键时间点
        for key_time in key_times:
            if key_time in time_array:
                idx = list(time_array).index(key_time)
                features[f'odds_at_{abs(key_time)}h_{odds_type}'] = odds_array[idx]
                
                # 该时间点前后的变化
                if idx > 0:
                    features[f'change_before_{abs(key_time)}h_{odds_type}'] = odds_array[idx] - odds_array[idx-1]
                if idx < len(odds_array) - 1:
                    features[f'change_after_{abs(key_time)}h_{odds_type}'] = odds_array[idx+1] - odds_array[idx]
        
        return features
    
    def extract_company_features(self, company_data, company_name):
        """提取单个公司的所有特征"""
        time_points = company_data['time_points']
        
        all_features = {'company': company_name}
        
        # 为每种赔率类型提取特征
        for odds_type in ['home', 'draw', 'away']:
            odds_key = f'{odds_type}_odds'
            if odds_key in company_data:
                odds_series = company_data[odds_key]
                
                # 提取各类特征
                basic_features = self.extract_basic_features(time_points, odds_series, odds_type)
                trend_features = self.extract_trend_features(time_points, odds_series, odds_type)
                volatility_features = self.extract_volatility_features(time_points, odds_series, odds_type)
                pattern_features = self.extract_pattern_features(time_points, odds_series, odds_type)
                
                all_features.update(basic_features)
                all_features.update(trend_features)
                all_features.update(volatility_features)
                all_features.update(pattern_features)
        
        # 跨赔率类型的特征
        if all(key in company_data for key in ['home_odds', 'draw_odds', 'away_odds']):
            home_odds = np.array(company_data['home_odds'])
            draw_odds = np.array(company_data['draw_odds'])
            away_odds = np.array(company_data['away_odds'])
            
            # 返还率计算
            implied_probs = 1/home_odds + 1/draw_odds + 1/away_odds
            return_rates = 1 / implied_probs * 100
            all_features['avg_return_rate'] = np.mean(return_rates)
            all_features['return_rate_volatility'] = np.std(return_rates)
            
            # 概率分布变化
            home_prob = 1/home_odds / implied_probs
            draw_prob = 1/draw_odds / implied_probs
            away_prob = 1/away_odds / implied_probs
            
            all_features['prob_entropy'] = np.mean(-home_prob * np.log(home_prob) - 
                                                  draw_prob * np.log(draw_prob) - 
                                                  away_prob * np.log(away_prob))
            
            # 最受青睐结果的变化
            favored_outcomes = np.argmin([home_odds, draw_odds, away_odds], axis=0)
            outcome_changes = np.sum(np.diff(favored_outcomes) != 0)
            all_features['favored_outcome_changes'] = outcome_changes
        
        return all_features
    
    def extract_match_features(self, match_data):
        """提取单场比赛的特征"""
        match_id = match_data['match_id']
        match_info = match_data.get('match_info', {})
        companies_data = match_data['companies_data']
        
        print(f"  提取比赛 {match_id} 的特征 ({len(companies_data)}家公司)")
        
        match_features = {
            'match_id': match_id,
            'home_team': match_info.get('home_team', ''),
            'away_team': match_info.get('away_team', ''),
            'home_score': match_info.get('home_score', ''),
            'away_score': match_info.get('away_score', ''),
            'match_state': match_info.get('match_state', ''),
            'companies_count': len(companies_data)
        }
        
        # 确定比赛结果
        try:
            home_score = int(match_info.get('home_score', 0))
            away_score = int(match_info.get('away_score', 0))
            if home_score > away_score:
                match_result = 'home_win'
            elif home_score < away_score:
                match_result = 'away_win'
            else:
                match_result = 'draw'
            match_features['match_result'] = match_result
        except:
            match_features['match_result'] = 'unknown'
        
        # 提取每家公司的特征
        companies_features = []
        for company, company_data in companies_data.items():
            company_features = self.extract_company_features(company_data, company)
            company_features['match_id'] = match_id
            company_features['match_result'] = match_features['match_result']
            companies_features.append(company_features)
        
        return {
            'match_info': match_features,
            'companies_features': companies_features
        }
    
    def process_all_matches(self, normalized_data):
        """处理所有比赛的特征提取"""
        print("\n=== 开始特征工程处理 ===")
        
        all_features = []
        match_summaries = []
        
        for db_name, db_data in normalized_data.items():
            print(f"\n处理数据库: {db_name}")
            
            for match_id, match_data in db_data.items():
                match_features = self.extract_match_features(match_data)
                
                # 保存比赛级别的特征
                match_summaries.append(match_features['match_info'])
                
                # 保存公司级别的特征
                all_features.extend(match_features['companies_features'])
        
        self.features_data = {
            'company_features': all_features,
            'match_summaries': match_summaries,
            'feature_categories': self.feature_categories
        }
        
        print(f"\n特征提取完成:")
        print(f"  总特征记录数: {len(all_features)}")
        print(f"  比赛数: {len(match_summaries)}")
        print(f"  平均每场比赛公司数: {len(all_features)/len(match_summaries):.1f}")
        
        return self.features_data
    
    def _make_serializable(self, obj):
        """将对象转换为可序列化的格式"""
        if isinstance(obj, dict):
            return {k: self._make_serializable(v) for k, v in obj.items()}
        elif isinstance(obj, (list, tuple)):
            return [self._make_serializable(item) for item in obj]
        elif isinstance(obj, np.integer):
            return int(obj)
        elif isinstance(obj, np.floating):
            return float(obj)
        elif isinstance(obj, np.ndarray):
            return obj.tolist()
        elif obj is None or pd.isna(obj):
            return None
        else:
            return obj

    def save_features(self, filename_prefix="odds_features"):
        """保存特征数据"""
        if not self.features_data:
            print("没有特征数据可保存")
            return None

        output_file = f"{filename_prefix}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"

        # 转换为可序列化的格式
        serializable_data = self._make_serializable(self.features_data)

        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(serializable_data, f, ensure_ascii=False, indent=2)

        print(f"\n特征数据已保存到: {output_file}")

        # 同时保存为CSV格式便于分析
        if self.features_data['company_features']:
            df = pd.DataFrame(self.features_data['company_features'])
            csv_file = output_file.replace('.json', '.csv')
            df.to_csv(csv_file, index=False, encoding='utf-8')
            print(f"CSV格式已保存到: {csv_file}")

        return output_file
    
    def analyze_features(self):
        """分析提取的特征"""
        if not self.features_data:
            print("没有特征数据可分析")
            return
        
        print(f"\n=== 特征分析 ===")
        
        df = pd.DataFrame(self.features_data['company_features'])
        
        # 基础统计
        print(f"特征维度: {len(df.columns)}")
        print(f"数据记录: {len(df)}")
        
        # 数值特征统计
        numeric_cols = df.select_dtypes(include=[np.number]).columns
        print(f"数值特征数量: {len(numeric_cols)}")
        
        # 缺失值统计
        missing_stats = df.isnull().sum()
        missing_features = missing_stats[missing_stats > 0]
        if len(missing_features) > 0:
            print(f"有缺失值的特征: {len(missing_features)}")
            for feature, count in missing_features.head(10).items():
                print(f"  {feature}: {count} ({count/len(df)*100:.1f}%)")
        
        # 按比赛结果分组统计
        if 'match_result' in df.columns:
            result_counts = df['match_result'].value_counts()
            print(f"\n比赛结果分布:")
            for result, count in result_counts.items():
                print(f"  {result}: {count} ({count/len(df)*100:.1f}%)")

def main():
    engineer = OddsFeatureEngineer()
    
    # 加载标准化数据
    normalized_data = engineer.load_normalized_data()
    if not normalized_data:
        return
    
    # 提取特征
    features_data = engineer.process_all_matches(normalized_data)
    
    # 保存特征
    engineer.save_features()
    
    # 分析特征
    engineer.analyze_features()

if __name__ == "__main__":
    main()

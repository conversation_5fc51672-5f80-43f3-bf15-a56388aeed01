#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
比赛详细数据抓取器
用于抓取技术统计、阵容、进失球概率、半场/全场胜负统计等详细数据

功能特点:
1. 从比赛分析页面抓取完整的JSON数据
2. 提取技术统计、阵容、球员统计、比赛事件等多种数据
3. 灵活处理数据结构变化，支持字段增减
4. 提供详细的错误处理和日志记录

使用方法:
    scraper = MatchDetailScraper()
    result = scraper.scrape_match_details(match_id)
    
数据来源: https://m.titan007.com/Analy/ShiJian/{match_id}.htm
"""

import requests
import time
from bs4 import BeautifulSoup
import json
import re
import logging
from typing import Dict, Optional, Any, List

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class MatchDetailScraper:
    """比赛详细数据抓取器"""
    
    def __init__(self):
        """初始化抓取器"""
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 14_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.0 Mobile/15E148 Safari/604.1',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1'
        }
        self.session = requests.Session()
        self.session.headers.update(self.headers)
    
    def scrape_match_details(self, match_id: str) -> Dict[str, Any]:
        """
        抓取比赛详细数据
        
        Args:
            match_id: 比赛ID
            
        Returns:
            包含各类详细数据的字典
        """
        url = f"https://m.titan007.com/Analy/ShiJian/{match_id}.htm"
        
        try:
            logger.info(f"开始抓取比赛 {match_id} 的详细数据")
            response = self.session.get(url, timeout=10)
            response.encoding = 'utf-8'
            
            if response.status_code == 200:
                soup = BeautifulSoup(response.text, 'html.parser')
                result = self.extract_all_data(soup)
                result['match_id'] = match_id
                result['source_url'] = url
                result['scrape_time'] = time.time()
                
                logger.info(f"成功抓取比赛 {match_id} 的详细数据")
                return result
            else:
                error_msg = f'HTTP {response.status_code}'
                logger.error(f"抓取失败: {error_msg}")
                return {'error': error_msg, 'match_id': match_id}
                
        except Exception as e:
            error_msg = str(e)
            logger.error(f"抓取异常: {error_msg}")
            return {'error': error_msg, 'match_id': match_id}
    
    def extract_all_data(self, soup: BeautifulSoup) -> Dict[str, Any]:
        """
        提取所有数据
        
        Args:
            soup: BeautifulSoup对象
            
        Returns:
            包含所有提取数据的字典
        """
        result = {}
        
        # 提取JSON数据
        scripts = soup.find_all('script')
        for script in scripts:
            script_content = script.string
            if script_content and 'jsonData' in script_content:
                try:
                    json_data = self._extract_json_from_script(script_content)
                    if json_data:
                        # 提取各类数据
                        result['technical_stats'] = self.extract_technical_stats(json_data)
                        result['lineup'] = self.extract_lineup(json_data)
                        result['goal_probability'] = self.extract_goal_probability(json_data)
                        result['halftime_fulltime_stats'] = self.extract_halftime_fulltime_stats(json_data)
                        result['player_stats'] = self.extract_player_stats(json_data)
                        result['match_events'] = self.extract_match_events(json_data)
                        result['raw_json_data'] = json_data  # 保存原始数据以备将来扩展
                        break
                        
                except Exception as e:
                    result['json_error'] = str(e)
                    logger.warning(f"JSON数据解析失败: {e}")
        
        return result
    
    def _extract_json_from_script(self, script_content: str) -> Optional[Dict]:
        """从脚本内容中提取JSON数据"""
        try:
            # 查找jsonData的开始和结束
            start_marker = 'var jsonData ='
            end_marker = '};'
            
            start_pos = script_content.find(start_marker)
            if start_pos != -1:
                start_pos += len(start_marker)
                end_pos = script_content.find(end_marker, start_pos)
                if end_pos != -1:
                    json_str = script_content[start_pos:end_pos + 1]
                    return json.loads(json_str)
            
            return None
            
        except Exception as e:
            logger.warning(f"JSON提取失败: {e}")
            return None
    
    def extract_technical_stats(self, data: Dict) -> Optional[Dict]:
        """
        提取技术统计数据
        
        Args:
            data: 原始JSON数据
            
        Returns:
            技术统计数据字典，格式: {stat_name: {'home': value, 'away': value}}
        """
        try:
            if 'techStat' not in data:
                return None
            
            tech_stat = data['techStat']
            if 'itemList' not in tech_stat:
                return None
            
            stats = {}
            for item in tech_stat['itemList']:
                name = item.get('name', '')
                home_data = item.get('home', {})
                away_data = item.get('away', {})
                
                # 灵活提取数值，支持不同的数据格式
                home_value = self._extract_stat_value(home_data)
                away_value = self._extract_stat_value(away_data)
                
                stats[name] = {
                    'home': home_value,
                    'away': away_value,
                    'home_raw': home_data,  # 保存原始数据
                    'away_raw': away_data
                }
            
            return stats
            
        except Exception as e:
            logger.warning(f"技术统计提取失败: {e}")
            return None
    
    def _extract_stat_value(self, stat_data: Dict) -> str:
        """提取统计数值，支持多种格式"""
        if isinstance(stat_data, dict):
            # 优先使用text字段
            if 'text' in stat_data:
                return str(stat_data['text'])
            # 备用字段
            for key in ['value', 'count', 'number']:
                if key in stat_data:
                    return str(stat_data[key])
        
        return str(stat_data) if stat_data else '0'
    
    def extract_lineup(self, data: Dict) -> Optional[Dict]:
        """
        提取阵容信息
        
        Args:
            data: 原始JSON数据
            
        Returns:
            阵容信息字典
        """
        try:
            if 'lineup' not in data:
                return None
            
            lineup = data['lineup']
            return {
                'home_formation': lineup.get('homeFormation', ''),
                'away_formation': lineup.get('guestFormation', ''),
                'home_players': lineup.get('homePlayerList', []),
                'away_players': lineup.get('guestPlayerList', []),
                'home_substitutes': lineup.get('homeBakPlayerList', []),
                'away_substitutes': lineup.get('guestBakPlayerList', []),
                'home_coach': lineup.get('homeCoach', ''),
                'away_coach': lineup.get('guestCoach', ''),
                'raw_lineup_data': lineup  # 保存完整原始数据
            }
            
        except Exception as e:
            logger.warning(f"阵容信息提取失败: {e}")
            return None
    
    def extract_goal_probability(self, data: Dict) -> Optional[Dict]:
        """
        提取进失球概率数据 - 基于playground.py的成功经验

        Args:
            data: 原始JSON数据

        Returns:
            进失球概率数据
        """
        try:
            if 'jsq' not in data:
                logger.info("jsonData中未找到jsq数据")
                return None

            jsq_data = data['jsq']
            logger.info("✅ 成功找到jsq数据（进失球概率）")

            # 基于playground.py的成功经验，详细解析jsq数据结构
            result = {
                'jsq_data': jsq_data,
                'probability_list': jsq_data.get('jsqList', []),
                'raw_probability_data': jsq_data,
                'data_structure': {}
            }

            # 分析jsq数据结构（参考playground.py的方法）
            logger.info("📊 分析jsq数据结构:")
            for key, value in jsq_data.items():
                result['data_structure'][key] = {
                    'type': str(type(value).__name__),
                    'content': value if not isinstance(value, (list, dict)) else f"{type(value).__name__}({len(value)})"
                }
                logger.info(f"  {key}: {type(value)} - {result['data_structure'][key]['content']}")

                if isinstance(value, list) and value:
                    logger.info(f"    列表长度: {len(value)}")
                    logger.info(f"    第一项: {value[0]}")
                elif isinstance(value, dict):
                    logger.info(f"    字典键: {list(value.keys())}")

            # 如果有jsqList，详细分析（这是关键数据）
            if 'jsqList' in jsq_data:
                jsq_list = jsq_data['jsqList']
                logger.info(f"🎯 jsqList详细分析 (共{len(jsq_list)}项):")

                result['parsed_probabilities'] = {}

                for i, item in enumerate(jsq_list):
                    logger.info(f"  项目 {i+1}:")
                    item_data = {}

                    for k, v in item.items():
                        logger.info(f"    {k}: {v}")
                        item_data[k] = v

                    # 尝试识别数据类型（30场/50场）
                    count_type = item.get('count', f'item_{i+1}')
                    result['parsed_probabilities'][count_type] = item_data

                # 进一步解析概率数据为标准格式
                result['goal_probabilities'] = self._parse_jsq_to_standard_format(jsq_list)

            logger.info(f"✅ 进失球概率数据提取完成，包含 {len(result.get('parsed_probabilities', {}))} 组数据")
            return result

        except Exception as e:
            logger.warning(f"进失球概率提取失败: {e}")
            return None

    def _parse_jsq_to_standard_format(self, jsq_list: List[Dict]) -> Dict:
        """
        将jsq数据解析为标准格式 - 修复版本
        基于playground.py的成功经验和网页实际数据结构
        """
        try:
            goal_probabilities = {}

            # 预定义时间段
            time_segments = [
                "0-15分钟", "16-30分钟", "31-45分钟",
                "46-60分钟", "61-75分钟", "76-90分钟", "90+分钟"
            ]

            for item in jsq_list:
                count_type = item.get('count', 'unknown')

                # 标准化数据类型名称
                if '30' in str(count_type):
                    standard_type = 'Count_30'
                elif '50' in str(count_type):
                    standard_type = 'Count_50'
                else:
                    standard_type = f'Count_{count_type}'

                logger.info(f"解析 {standard_type} 数据: {item}")

                # 查找概率数据数组
                home_probabilities = []
                away_probabilities = []

                # 查找可能的概率数组字段
                for key, value in item.items():
                    if isinstance(value, list) and len(value) >= 7:
                        # 可能是概率数据数组
                        if 'home' in str(key).lower() or 'host' in str(key).lower():
                            home_probabilities = value
                            logger.info(f"找到主队概率数组 {key}: {value}")
                        elif 'away' in str(key).lower() or 'guest' in str(key).lower():
                            away_probabilities = value
                            logger.info(f"找到客队概率数组 {key}: {value}")
                        elif not home_probabilities:  # 如果还没找到主队数据，使用第一个数组
                            home_probabilities = value
                            logger.info(f"使用第一个数组作为主队数据 {key}: {value}")

                # 特殊处理：根据网页实际结构查找jsqToHome和jsqToGuest
                if 'jsqToHome' in item:
                    home_probabilities = item['jsqToHome']
                    logger.info(f"找到jsqToHome数据: {home_probabilities}")

                if 'jsqToGuest' in item:
                    away_probabilities = item['jsqToGuest']
                    logger.info(f"找到jsqToGuest数据: {away_probabilities}")

                # 如果没有找到客队数据，使用主队数据
                if not away_probabilities and home_probabilities:
                    away_probabilities = home_probabilities
                    logger.info("客队数据使用主队数据")

                # 构建时间段概率数据
                time_segment_data = {}
                for i, segment in enumerate(time_segments):
                    if i < len(home_probabilities):
                        home_prob = home_probabilities[i]
                        away_prob = away_probabilities[i] if i < len(away_probabilities) else home_prob

                        time_segment_data[segment] = {
                            'home': f"{home_prob}%",
                            'away': f"{away_prob}%",
                            'home_raw': home_prob,
                            'away_raw': away_prob
                        }
                    else:
                        time_segment_data[segment] = {
                            'home': "N/A",
                            'away': "N/A",
                            'home_raw': 0,
                            'away_raw': 0
                        }

                goal_probabilities[standard_type] = {
                    'time_segments': time_segments,
                    'time_segment_data': time_segment_data,
                    'home_probabilities': home_probabilities,
                    'away_probabilities': away_probabilities,
                    'raw_item': item
                }

                logger.info(f"✅ {standard_type} 解析完成，包含 {len(time_segment_data)} 个时间段")

            return goal_probabilities

        except Exception as e:
            logger.warning(f"解析jsq数据为标准格式失败: {e}")
            return {}
    
    def extract_halftime_fulltime_stats(self, data: Dict) -> Optional[Dict]:
        """
        提取半场/全场统计数据
        
        Args:
            data: 原始JSON数据
            
        Returns:
            半场/全场统计数据
        """
        try:
            if 'lastTechStat' not in data:
                return None
            
            last_tech_stat = data['lastTechStat']
            if 'itemList' not in last_tech_stat:
                return None
            
            stats = {}
            for item in last_tech_stat['itemList']:
                name = item.get('name', '')
                home_data = item.get('home', {})
                away_data = item.get('away', {})
                
                home_value = self._extract_stat_value(home_data)
                away_value = self._extract_stat_value(away_data)
                
                stats[name] = {
                    'home': home_value,
                    'away': away_value,
                    'home_raw': home_data,
                    'away_raw': away_data
                }
            
            return stats
            
        except Exception as e:
            logger.warning(f"半场/全场统计提取失败: {e}")
            return None
    
    def extract_player_stats(self, data: Dict) -> Optional[Dict]:
        """
        提取球员统计数据
        
        Args:
            data: 原始JSON数据
            
        Returns:
            球员统计数据
        """
        try:
            result = {}
            
            # 主队球员数据
            if 'homeTeamDatas' in data:
                home_data = data['homeTeamDatas']
                result['home_team'] = {
                    'player_tech_info': home_data.get('playerTechInfo', []),
                    'team_stats': home_data.get('teamStats', {}),
                    'raw_home_data': home_data
                }
            
            # 客队球员数据
            if 'guestTeamDatas' in data:
                away_data = data['guestTeamDatas']
                result['away_team'] = {
                    'player_tech_info': away_data.get('playerTechInfo', []),
                    'team_stats': away_data.get('teamStats', {}),
                    'raw_away_data': away_data
                }
            
            return result if result else None
            
        except Exception as e:
            logger.warning(f"球员统计提取失败: {e}")
            return None
    
    def extract_match_events(self, data: Dict) -> Optional[Dict]:
        """
        提取比赛事件数据
        
        Args:
            data: 原始JSON数据
            
        Returns:
            比赛事件数据
        """
        try:
            if 'events' not in data:
                return None
            
            events_data = data['events']
            return {
                'event_list': events_data.get('eventList', []),
                'event_summary': events_data.get('eventSummary', {}),
                'raw_events_data': events_data
            }
            
        except Exception as e:
            logger.warning(f"比赛事件提取失败: {e}")
            return None

def test_match_detail_scraper():
    """测试比赛详细数据抓取器"""
    print("🧪 测试比赛详细数据抓取器")
    print("=" * 50)
    
    scraper = MatchDetailScraper()
    test_match_id = "2511566"
    
    result = scraper.scrape_match_details(test_match_id)
    
    if 'error' in result:
        print(f"❌ 抓取失败: {result['error']}")
        return False
    
    print(f"✅ 抓取成功！数据概览:")
    print(f"📊 比赛ID: {result.get('match_id', 'N/A')}")
    
    # 显示各类数据的统计
    data_types = [
        ('technical_stats', '技术统计'),
        ('lineup', '阵容信息'),
        ('goal_probability', '进失球概率'),
        ('halftime_fulltime_stats', '半场/全场统计'),
        ('player_stats', '球员统计'),
        ('match_events', '比赛事件')
    ]
    
    for key, name in data_types:
        if key in result and result[key]:
            if key == 'technical_stats':
                count = len(result[key])
                print(f"📈 {name}: {count} 项")
            elif key == 'lineup':
                home_count = len(result[key].get('home_players', []))
                away_count = len(result[key].get('away_players', []))
                print(f"👥 {name}: 主队{home_count}人, 客队{away_count}人")
            elif key == 'player_stats':
                home_players = len(result[key].get('home_team', {}).get('player_tech_info', []))
                away_players = len(result[key].get('away_team', {}).get('player_tech_info', []))
                print(f"🏃‍♂️ {name}: 主队{home_players}人, 客队{away_players}人")
            else:
                print(f"✅ {name}: 已获取")
        else:
            print(f"❌ {name}: 未获取")
    
    return True

if __name__ == '__main__':
    test_match_detail_scraper()

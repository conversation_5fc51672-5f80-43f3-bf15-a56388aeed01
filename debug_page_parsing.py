#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试页面解析 - 查看实际的表格结构
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from enhanced_odds_scraper import EnhancedOddsScraper
from bs4 import BeautifulSoup

def debug_page_parsing():
    """调试页面解析"""
    print("🔍 调试页面解析 - bet365历史页面")
    print("=" * 60)
    
    scraper = EnhancedOddsScraper()
    
    # 使用bet365的真实链接
    url = "https://op1.titan007.com/OddsHistory.aspx?id=114278325&sid=2213559&cid=281&l=0"
    
    print(f"🌐 分析URL: {url}")
    
    content = scraper.get_page_content(url)
    if not content:
        print("❌ 无法获取页面内容")
        return
        
    soup = BeautifulSoup(content, 'html.parser')
    
    # 查找表格
    print(f"\n📊 查找表格结构:")
    tables = soup.find_all('table')
    print(f"表格数量: {len(tables)}")
    
    for i, table in enumerate(tables, 1):
        rows = table.find_all('tr')
        print(f"\n  表格 {i}: {len(rows)} 行")
        
        # 查看前几行的详细内容
        for j, row in enumerate(rows[:5], 1):
            cells = row.find_all(['td', 'th'])
            print(f"    行 {j}: {len(cells)} 列")
            
            if cells:
                for k, cell in enumerate(cells, 1):
                    cell_text = cell.get_text(strip=True)
                    print(f"      列 {k}: '{cell_text}'")
                    
                    # 检查是否包含时间信息
                    if ':' in cell_text and ('-' in cell_text or '/' in cell_text):
                        print(f"        ⏰ 可能的时间信息: {cell_text}")
                    
                    # 检查是否是赔率
                    try:
                        odds_value = float(cell_text)
                        if 1.0 <= odds_value <= 50.0:
                            print(f"        💰 可能的赔率: {odds_value}")
                    except ValueError:
                        pass
    
    # 查找包含时间模式的所有文本
    print(f"\n⏰ 查找时间模式:")
    page_text = soup.get_text()
    
    import re
    # 查找各种时间格式
    time_patterns = [
        r'\d{2}-\d{2}\s+\d{2}:\d{2}',  # 08-13 19:30
        r'\d{4}-\d{2}-\d{2}\s+\d{2}:\d{2}',  # 2022-08-13 19:30
        r'\d{2}/\d{2}\s+\d{2}:\d{2}',  # 08/13 19:30
    ]
    
    for pattern in time_patterns:
        matches = re.findall(pattern, page_text)
        if matches:
            print(f"  模式 '{pattern}': 找到 {len(matches)} 个匹配")
            for match in matches[:5]:  # 显示前5个
                print(f"    - {match}")
    
    # 查找包含赔率的行
    print(f"\n💰 查找赔率行:")
    lines = page_text.split('\n')
    
    odds_lines = []
    for line in lines:
        line = line.strip()
        if not line:
            continue
            
        # 查找包含多个赔率的行
        odds_count = 0
        words = line.split()
        for word in words:
            try:
                odds_value = float(word)
                if 1.0 <= odds_value <= 50.0:
                    odds_count += 1
            except ValueError:
                pass
        
        if odds_count >= 3:  # 至少3个赔率
            odds_lines.append(line)
    
    print(f"  找到 {len(odds_lines)} 行包含多个赔率")
    for i, line in enumerate(odds_lines[:5], 1):
        print(f"    {i}. {line}")
    
    # 测试现有的解析方法
    print(f"\n🧪 测试现有解析方法:")
    records = scraper.parse_company_odds_history_page(url, "bet365")
    
    if records:
        print(f"✅ 解析成功，获取 {len(records)} 条记录")
        for i, record in enumerate(records[:3], 1):
            print(f"  {i}. 公司: {record.get('company', 'N/A')}")
            print(f"     赔率: {record.get('home_odds')} {record.get('draw_odds')} {record.get('away_odds')}")
            print(f"     时间: {record.get('change_time', 'N/A')}")
            print(f"     日期: {record.get('date', 'N/A')} {record.get('time', 'N/A')}")
    else:
        print("❌ 解析失败")

if __name__ == "__main__":
    debug_page_parsing()

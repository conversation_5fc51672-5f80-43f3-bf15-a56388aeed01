# 最近比赛功能升级 - 表格显示 + 比赛结果

## 🎯 升级概述

根据您的要求，我已经将最近比赛功能从列表显示升级为**表格显示**，并**新增了比赛结果列**，提供更清晰、更详细的比赛信息展示。

## 🔄 主要改进

### 1. 从列表改为表格显示
- **原来**: QListWidget 单行文本显示
- **现在**: QTableWidget 多列表格显示
- **优势**: 信息更清晰，可以按列排序和调整

### 2. 新增比赛结果列
- **显示内容**: 比分 + 胜负结果
- **格式示例**: "2:1 (主胜)"、"1:1 (平局)"、"0:2 (客胜)"
- **未完赛**: 显示比赛状态（如"未开始"、"进行中"等）

## 📋 表格结构

新的表格包含6列：

| 列序号 | 列名称 | 内容示例 | 说明 |
|--------|--------|----------|------|
| 1 | 比赛ID | 2701757 | 用于分析的比赛标识 |
| 2 | 开赛时间 | 2024-04-28 15:30:00 | 比赛开始时间 |
| 3 | 联赛 | 英超 | 比赛所属联赛 |
| 4 | 主队 | 曼城 | 主场球队 |
| 5 | 客队 | 热刺 | 客场球队 |
| 6 | 比赛结果 | 2:1 (主胜) | 比分和胜负结果 |

## 🔧 技术实现

### 1. UI界面升级

```python
# 原来的列表控件
self.recent_matches_list = QListWidget()

# 升级为表格控件
self.recent_matches_table = QTableWidget()
self.recent_matches_table.setColumnCount(6)
self.recent_matches_table.setHorizontalHeaderLabels([
    "比赛ID", "开赛时间", "联赛", "主队", "客队", "比赛结果"
])
```

### 2. 比赛结果处理

新增 `get_match_result_text()` 方法：

```python
def get_match_result_text(self, match_info):
    """获取比赛结果文本"""
    home_score = match_info.get('home_score')
    away_score = match_info.get('away_score')
    
    if home_score is not None and away_score is not None:
        # 判断胜负
        if int(home_score) > int(away_score):
            result_text = "主胜"
        elif int(home_score) < int(away_score):
            result_text = "客胜"
        else:
            result_text = "平局"
        
        return f"{home_score}:{away_score} ({result_text})"
    else:
        return match_info.get('match_state', '未知')
```

### 3. 表格填充逻辑

```python
def show_recent_matches(self):
    """显示最近比赛"""
    # 设置表格行数
    self.recent_matches_table.setRowCount(len(recent_matches))
    
    # 逐行填充数据
    for row, match in enumerate(recent_matches):
        self.recent_matches_table.setItem(row, 0, QTableWidgetItem(str(match_id)))
        self.recent_matches_table.setItem(row, 1, QTableWidgetItem(display_time))
        self.recent_matches_table.setItem(row, 2, QTableWidgetItem(league))
        self.recent_matches_table.setItem(row, 3, QTableWidgetItem(home_team))
        self.recent_matches_table.setItem(row, 4, QTableWidgetItem(away_team))
        self.recent_matches_table.setItem(row, 5, QTableWidgetItem(match_result))
```

### 4. 选择处理升级

```python
def on_recent_match_table_selected(self):
    """选择最近比赛表格行时的处理"""
    selected_rows = self.recent_matches_table.selectionModel().selectedRows()
    if selected_rows:
        row = selected_rows[0].row()
        # 获取比赛ID（第0列）
        match_id_item = self.recent_matches_table.item(row, 0)
        if match_id_item:
            match_id = match_id_item.text()
            # 自动填入比赛ID输入框
            self.match_id_input.setText(match_id)
```

## 🎮 使用方法

### 步骤1: 设置天数
在"最近天数"输入框中输入数字

### 步骤2: 查询比赛
点击"显示最近比赛"按钮

### 步骤3: 查看表格
在表格中查看详细的比赛信息，包括：
- 比赛ID
- 开赛时间
- 联赛信息
- 主客队名称
- **比赛结果**（新增）

### 步骤4: 选择比赛
点击表格中的任意行，比赛ID自动填入输入框

### 步骤5: 开始分析
使用其他分析功能进行赔率组合分析

## 📊 比赛结果显示规则

### 1. 已完赛比赛
- **主胜**: "2:1 (主胜)"
- **平局**: "1:1 (平局)"  
- **客胜**: "0:2 (客胜)"

### 2. 未完赛比赛
- **未开始**: "未开始"
- **进行中**: "进行中"
- **推迟**: "推迟"
- **取消**: "取消"
- **未知**: "未知"

### 3. 数据异常
- 比分数据异常时显示原始比分
- 完全无数据时显示"未知"

## 🎨 界面特性

### 1. 表格样式
- **交替行颜色**: 提高可读性
- **整行选择**: 点击任意位置选择整行
- **自动列宽**: 根据内容自动调整列宽
- **表头固定**: 列标题始终可见

### 2. 高度限制
- **最大高度**: 150px（比原来的120px稍高）
- **滚动条**: 超出高度时自动显示滚动条
- **界面整洁**: 不会占用过多界面空间

### 3. 响应式设计
- **最后一列拉伸**: 比赛结果列自动填充剩余空间
- **列宽调整**: 用户可以手动调整列宽
- **排序功能**: 可以按任意列排序（如果需要）

## 📋 界面布局

升级后的界面布局：

```
┌─────────────────────────────────────────────────────────────┐
│ 数据库: [选择数据库 ▼]                                      │
│ 比赛ID: [输入框]                                            │
│                                                             │
│ 最近天数: [0] [显示最近比赛]                                │
│ 最近比赛:                                                   │
│ ┌─────────────────────────────────────────────────────────┐ │
│ │ 比赛ID │ 开赛时间        │ 联赛 │ 主队 │ 客队 │ 比赛结果  │ │
│ ├─────────────────────────────────────────────────────────┤ │
│ │2701757│2024-04-28 15:30│英超  │曼城  │热刺  │2:1 (主胜) │ │
│ │2701758│2024-04-28 18:00│西甲  │皇马  │巴萨  │1:1 (平局) │ │
│ │2701759│2024-04-29 20:00│意甲  │尤文  │米兰  │未开始     │ │
│ └─────────────────────────────────────────────────────────┘ │
│                                                             │
│ 博彩公司: [bet365 ▼]                                       │
│ 组合数: [3]                                                 │
│ ...                                                         │
└─────────────────────────────────────────────────────────────┘
```

## ✅ 升级完成

所有升级功能都已实现并可以正常使用：

1. ✅ **表格显示** - 从列表改为6列表格
2. ✅ **比赛结果列** - 显示比分和胜负结果
3. ✅ **智能结果判断** - 自动判断主胜/平局/客胜
4. ✅ **状态显示** - 未完赛比赛显示状态
5. ✅ **整行选择** - 点击任意位置选择比赛
6. ✅ **自动填入** - 选择后自动填入比赛ID
7. ✅ **界面优化** - 更清晰的信息展示

## 🚀 立即可用

升级后的功能已经完全实现，您可以：

1. **启动程序**: 运行 `python odds_combination_ui.py`
2. **选择数据库**: 在数据库下拉框中选择数据库
3. **设置天数**: 在"最近天数"中输入数字
4. **查询比赛**: 点击"显示最近比赛"按钮
5. **查看表格**: 在6列表格中查看详细比赛信息
6. **选择比赛**: 点击表格中的任意行
7. **开始分析**: 比赛ID自动填入后，可以正常分析

**新的表格显示功能提供了更丰富、更清晰的比赛信息展示！** 🎉

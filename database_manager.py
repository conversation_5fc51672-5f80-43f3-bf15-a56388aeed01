#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据库管理工具
用于优化、清理和管理足球赔率数据库
"""

import os
import sys
import sqlite3
import shutil
from datetime import datetime, timedelta
from typing import Dict, List, Optional
import logging
from database import OddsDatabase

logger = logging.getLogger(__name__)

class DatabaseManager:
    def __init__(self, db_path: str = "odds_data.db"):
        """初始化数据库管理器"""
        self.db_path = db_path
        self.db = OddsDatabase(db_path)
    
    def get_size_analysis(self) -> Dict:
        """分析数据库大小和增长趋势"""
        try:
            stats = self.db.get_database_stats()
            
            # 计算平均大小
            avg_match_size = 0
            avg_odds_size = 0
            
            if stats.get('match_count', 0) > 0:
                # 估算每场比赛的平均大小
                total_size = stats.get('db_size', 0)
                avg_match_size = total_size / stats['match_count']
                
                if stats.get('odds_count', 0) > 0:
                    avg_odds_size = total_size / stats['odds_count']
            
            # 预测增长
            predictions = self._predict_growth(stats)
            
            return {
                'current_stats': stats,
                'avg_match_size_bytes': avg_match_size,
                'avg_odds_size_bytes': avg_odds_size,
                'predictions': predictions,
                'recommendations': self._get_recommendations(stats)
            }
            
        except Exception as e:
            logger.error(f"分析数据库大小失败: {e}")
            return {}
    
    def _predict_growth(self, stats: Dict) -> Dict:
        """预测数据库增长"""
        current_size_mb = stats.get('db_size_mb', 0)
        match_count = stats.get('match_count', 0)
        
        if match_count == 0:
            return {}
        
        avg_size_per_match = current_size_mb / match_count
        
        # 预测不同数量比赛的大小
        predictions = {}
        for future_matches in [1000, 5000, 10000, 50000]:
            predicted_size = future_matches * avg_size_per_match
            predictions[f'{future_matches}_matches'] = {
                'size_mb': round(predicted_size, 2),
                'size_gb': round(predicted_size / 1024, 2)
            }
        
        return predictions
    
    def _get_recommendations(self, stats: Dict) -> List[str]:
        """获取优化建议"""
        recommendations = []
        size_mb = stats.get('db_size_mb', 0)
        
        if size_mb > 100:
            recommendations.append("数据库已超过100MB，建议定期清理旧数据")
        
        if size_mb > 500:
            recommendations.append("数据库已超过500MB，强烈建议分库存储")
        
        if size_mb > 1000:
            recommendations.append("数据库已超过1GB，建议使用专业数据库如PostgreSQL")
        
        if stats.get('match_count', 0) > 10000:
            recommendations.append("比赛数量较多，建议按时间或联赛分库")
        
        recommendations.extend([
            "定期执行数据库优化(VACUUM)来回收空间",
            "设置自动备份策略",
            "考虑压缩存储历史数据"
        ])
        
        return recommendations
    
    def auto_cleanup(self, max_size_mb: int = 500, days_to_keep: int = 30,
                     safe_mode: bool = True) -> Dict:
        """自动清理数据库

        Args:
            max_size_mb: 最大大小限制
            days_to_keep: 保留天数
            safe_mode: 安全模式，True=归档旧数据，False=直接删除
        """
        try:
            current_stats = self.db.get_database_stats()
            current_size_mb = current_stats.get('db_size_mb', 0)

            result = {
                'initial_size_mb': current_size_mb,
                'actions_taken': [],
                'final_size_mb': current_size_mb,
                'space_saved_mb': 0,
                'archive_created': None
            }

            # 如果超过最大大小，执行清理
            if current_size_mb > max_size_mb:
                if safe_mode:
                    # 安全模式：创建归档而不是删除
                    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                    archive_path = f"odds_data_archive_{timestamp}.db"

                    archive_result = self.create_archive(archive_path, days_to_keep)
                    if archive_result.get('success'):
                        result['actions_taken'].append(f"将{days_to_keep}天前的数据归档到 {archive_path}")
                        result['archive_created'] = archive_path
                else:
                    # 直接删除模式（需要用户明确确认）
                    cleanup_result = self.db.cleanup_old_data(days_to_keep)
                    if cleanup_result.get('success'):
                        result['actions_taken'].append(f"删除了{days_to_keep}天前的数据")

                # 优化数据库
                optimize_result = self.db.optimize_database()
                if optimize_result.get('success'):
                    result['actions_taken'].append("执行了数据库优化")
                    result['space_saved_mb'] = optimize_result.get('saved_space_mb', 0)

                # 获取最终大小
                final_stats = self.db.get_database_stats()
                result['final_size_mb'] = final_stats.get('db_size_mb', 0)

            return result

        except Exception as e:
            logger.error(f"自动清理失败: {e}")
            return {'error': str(e)}

    def safe_archive_old_data(self, days_to_archive: int = 90,
                             export_format: str = "db") -> Dict:
        """安全归档旧数据（保证数据不丢失）

        Args:
            days_to_archive: 归档天数
            export_format: 导出格式 ("db", "csv", "json")
        """
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

            if export_format == "db":
                # 创建数据库归档
                archive_path = f"odds_data_archive_{timestamp}.db"
                return self.create_archive(archive_path, days_to_archive)

            elif export_format == "csv":
                # 导出为CSV格式
                return self._export_old_data_csv(days_to_archive, timestamp)

            elif export_format == "json":
                # 导出为JSON格式
                return self._export_old_data_json(days_to_archive, timestamp)

            else:
                return {'success': False, 'error': '不支持的导出格式'}

        except Exception as e:
            logger.error(f"安全归档失败: {e}")
            return {'success': False, 'error': str(e)}

    def _export_old_data_csv(self, days_to_archive: int, timestamp: str) -> Dict:
        """导出旧数据为CSV格式"""
        try:
            import pandas as pd

            cutoff_date = datetime.now() - timedelta(days=days_to_archive)
            cutoff_str = cutoff_date.isoformat()

            with sqlite3.connect(self.db_path) as conn:
                # 导出比赛数据
                matches_df = pd.read_sql_query(
                    'SELECT * FROM matches WHERE created_at < ?',
                    conn, params=(cutoff_str,)
                )
                matches_file = f"matches_archive_{timestamp}.csv"
                matches_df.to_csv(matches_file, index=False, encoding='utf-8-sig')

                # 导出赔率数据
                odds_df = pd.read_sql_query('''
                    SELECT o.* FROM odds o
                    JOIN matches m ON o.match_id = m.match_id
                    WHERE m.created_at < ?
                ''', conn, params=(cutoff_str,))
                odds_file = f"odds_archive_{timestamp}.csv"
                odds_df.to_csv(odds_file, index=False, encoding='utf-8-sig')

                # 删除已导出的数据
                cursor = conn.cursor()
                cursor.execute('''
                    DELETE FROM odds WHERE match_id IN (
                        SELECT match_id FROM matches WHERE created_at < ?
                    )
                ''', (cutoff_str,))
                cursor.execute('DELETE FROM matches WHERE created_at < ?', (cutoff_str,))
                cursor.execute('VACUUM')
                conn.commit()

                return {
                    'success': True,
                    'matches_file': matches_file,
                    'odds_file': odds_file,
                    'matches_exported': len(matches_df),
                    'odds_exported': len(odds_df)
                }

        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    def create_archive(self, archive_path: str, days_to_archive: int = 90) -> Dict:
        """创建历史数据归档"""
        try:
            # 创建归档数据库
            archive_db = OddsDatabase(archive_path)
            
            with sqlite3.connect(self.db_path) as source_conn:
                with sqlite3.connect(archive_path) as archive_conn:
                    source_cursor = source_conn.cursor()
                    archive_cursor = archive_conn.cursor()
                    
                    # 计算归档截止日期
                    cutoff_date = datetime.now() - timedelta(days=days_to_archive)
                    cutoff_str = cutoff_date.isoformat()
                    
                    # 复制旧数据到归档
                    source_cursor.execute('''
                        SELECT * FROM matches WHERE created_at < ?
                    ''', (cutoff_str,))
                    
                    old_matches = source_cursor.fetchall()
                    
                    for match in old_matches:
                        archive_cursor.execute('''
                            INSERT OR REPLACE INTO matches VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                        ''', match)
                        
                        # 复制对应的赔率数据
                        match_id = match[1]  # match_id在第二列
                        source_cursor.execute('SELECT * FROM odds WHERE match_id = ?', (match_id,))
                        odds_data = source_cursor.fetchall()
                        
                        for odds in odds_data:
                            archive_cursor.execute('''
                                INSERT INTO odds VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                            ''', odds)
                    
                    archive_conn.commit()
            
            # 从原数据库删除已归档的数据
            cleanup_result = self.db.cleanup_old_data(days_to_archive)
            
            archive_size = os.path.getsize(archive_path)
            
            return {
                'archive_path': archive_path,
                'archive_size_mb': round(archive_size / 1024 / 1024, 2),
                'matches_archived': len(old_matches),
                'cleanup_result': cleanup_result,
                'success': True
            }
            
        except Exception as e:
            logger.error(f"创建归档失败: {e}")
            return {'success': False, 'error': str(e)}
    
    def split_database_by_date(self, split_date: str, new_db_path: str) -> Dict:
        """按日期分割数据库"""
        try:
            # 创建新数据库
            new_db = OddsDatabase(new_db_path)
            
            with sqlite3.connect(self.db_path) as source_conn:
                with sqlite3.connect(new_db_path) as new_conn:
                    source_cursor = source_conn.cursor()
                    new_cursor = new_conn.cursor()
                    
                    # 移动指定日期之后的数据
                    source_cursor.execute('''
                        SELECT * FROM matches WHERE created_at >= ?
                    ''', (split_date,))
                    
                    new_matches = source_cursor.fetchall()
                    
                    for match in new_matches:
                        new_cursor.execute('''
                            INSERT INTO matches VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                        ''', match)
                        
                        # 移动对应的赔率数据
                        match_id = match[1]
                        source_cursor.execute('SELECT * FROM odds WHERE match_id = ?', (match_id,))
                        odds_data = source_cursor.fetchall()
                        
                        for odds in odds_data:
                            new_cursor.execute('''
                                INSERT INTO odds VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                            ''', odds)
                    
                    new_conn.commit()
            
            # 从原数据库删除已移动的数据
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute('DELETE FROM odds WHERE match_id IN (SELECT match_id FROM matches WHERE created_at >= ?)', (split_date,))
                cursor.execute('DELETE FROM matches WHERE created_at >= ?', (split_date,))
                cursor.execute('VACUUM')
                conn.commit()
            
            new_size = os.path.getsize(new_db_path)
            old_size = os.path.getsize(self.db_path)
            
            return {
                'new_db_path': new_db_path,
                'new_db_size_mb': round(new_size / 1024 / 1024, 2),
                'old_db_size_mb': round(old_size / 1024 / 1024, 2),
                'matches_moved': len(new_matches),
                'success': True
            }
            
        except Exception as e:
            logger.error(f"分割数据库失败: {e}")
            return {'success': False, 'error': str(e)}

def main():
    """命令行工具主函数"""
    if len(sys.argv) < 2:
        print("用法: python database_manager.py <command> [options]")
        print("命令:")
        print("  analyze - 分析数据库大小和增长趋势")
        print("  cleanup <days> - 清理指定天数前的数据")
        print("  optimize - 优化数据库")
        print("  backup [path] - 备份数据库")
        print("  auto-cleanup <max_size_mb> <days> - 自动清理")
        return
    
    manager = DatabaseManager()
    command = sys.argv[1]
    
    if command == "analyze":
        analysis = manager.get_size_analysis()
        print("=== 数据库分析报告 ===")
        stats = analysis.get('current_stats', {})
        print(f"当前大小: {stats.get('db_size_mb', 0):.2f} MB")
        print(f"比赛数量: {stats.get('match_count', 0):,}")
        print(f"赔率记录: {stats.get('odds_count', 0):,}")
        
        print("\n=== 增长预测 ===")
        predictions = analysis.get('predictions', {})
        for key, pred in predictions.items():
            matches = key.replace('_matches', '')
            print(f"{matches} 场比赛: {pred['size_mb']} MB ({pred['size_gb']} GB)")
        
        print("\n=== 优化建议 ===")
        for rec in analysis.get('recommendations', []):
            print(f"- {rec}")
    
    elif command == "cleanup" and len(sys.argv) > 2:
        days = int(sys.argv[2])
        result = manager.db.cleanup_old_data(days)
        if result.get('success'):
            print(f"清理完成: 删除了 {result['matches_deleted']} 场比赛和 {result['odds_deleted']} 条赔率记录")
        else:
            print(f"清理失败: {result.get('error')}")
    
    elif command == "optimize":
        result = manager.db.optimize_database()
        if result.get('success'):
            print(f"优化完成: 节省了 {result['saved_space_mb']} MB 空间")
        else:
            print(f"优化失败: {result.get('error')}")
    
    elif command == "backup":
        backup_path = sys.argv[2] if len(sys.argv) > 2 else None
        result = manager.db.backup_database(backup_path)
        if result.get('success'):
            print(f"备份完成: {result['backup_path']} ({result['backup_size_mb']} MB)")
        else:
            print(f"备份失败: {result.get('error')}")
    
    elif command == "auto-cleanup" and len(sys.argv) > 3:
        max_size = int(sys.argv[2])
        days = int(sys.argv[3])
        result = manager.auto_cleanup(max_size, days)
        print(f"自动清理完成:")
        print(f"初始大小: {result['initial_size_mb']:.2f} MB")
        print(f"最终大小: {result['final_size_mb']:.2f} MB")
        print(f"节省空间: {result['space_saved_mb']:.2f} MB")
        print(f"执行的操作: {', '.join(result['actions_taken'])}")

if __name__ == "__main__":
    main()

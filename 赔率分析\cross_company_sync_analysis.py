#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
交叉公司同步变盘分析
检测多家公司在同一时刻的同步变盘行为，分析变盘方向和幅度的一致性
"""

import pandas as pd
import numpy as np
import json
import os
from datetime import datetime
import matplotlib.pyplot as plt
import seaborn as sns
from collections import defaultdict, Counter
from itertools import combinations
import warnings
warnings.filterwarnings('ignore')

class CrossCompanySyncAnalyzer:
    def __init__(self, normalized_data_file=None):
        self.normalized_data_file = normalized_data_file
        self.normalized_data = None
        self.sync_analysis_results = {}
        
        # 同步检测参数
        self.time_tolerance = 0.5  # 时间容差（小时）
        self.min_change_threshold = 0.02  # 最小变化阈值（2%）
        self.sync_threshold = 0.7  # 同步度阈值
        
    def load_normalized_data(self, filename=None):
        """加载标准化的赔率数据"""
        if filename:
            self.normalized_data_file = filename
        
        if not self.normalized_data_file:
            # 查找最新的标准化数据文件
            files = [f for f in os.listdir('.') if f.startswith('normalized_odds_') and f.endswith('.json')]
            if files:
                self.normalized_data_file = sorted(files)[-1]
                print(f"自动选择最新文件: {self.normalized_data_file}")
            else:
                print("未找到标准化数据文件")
                return None
        
        with open(self.normalized_data_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        self.normalized_data = data
        print(f"加载标准化数据: {self.normalized_data_file}")
        return data
    
    def detect_synchronous_changes(self, match_data):
        """检测单场比赛中的同步变盘"""
        match_id = match_data['match_id']
        companies_data = match_data['companies_data']
        
        if len(companies_data) < 2:
            return None
        
        sync_events = []
        
        # 为每种赔率类型检测同步变盘
        for odds_type in ['home', 'draw', 'away']:
            odds_key = f'{odds_type}_odds'
            
            # 收集所有公司在该赔率类型上的变化点
            company_changes = {}
            
            for company, company_data in companies_data.items():
                if odds_key not in company_data:
                    continue
                
                time_points = company_data['time_points']
                odds_series = company_data[odds_key]
                
                # 计算变化点
                changes = []
                for i in range(1, len(odds_series)):
                    change_pct = (odds_series[i] - odds_series[i-1]) / odds_series[i-1] * 100
                    if abs(change_pct) >= self.min_change_threshold * 100:
                        changes.append({
                            'time': time_points[i],
                            'change_pct': change_pct,
                            'from_odds': odds_series[i-1],
                            'to_odds': odds_series[i]
                        })
                
                if changes:
                    company_changes[company] = changes
            
            # 检测同步事件
            if len(company_changes) >= 2:
                sync_events_for_odds = self._find_sync_events(company_changes, odds_type)
                sync_events.extend(sync_events_for_odds)
        
        return {
            'match_id': match_id,
            'sync_events': sync_events,
            'total_events': len(sync_events),
            'companies_count': len(companies_data)
        }
    
    def _find_sync_events(self, company_changes, odds_type):
        """在公司变化中找到同步事件"""
        sync_events = []
        
        # 获取所有变化时间点
        all_times = []
        for company, changes in company_changes.items():
            for change in changes:
                all_times.append((change['time'], company, change))
        
        # 按时间排序
        all_times.sort(key=lambda x: x[0])
        
        # 检测同步窗口
        i = 0
        while i < len(all_times):
            current_time = all_times[i][0]
            sync_window = []
            
            # 收集时间容差内的所有变化
            j = i
            while j < len(all_times) and abs(all_times[j][0] - current_time) <= self.time_tolerance:
                sync_window.append(all_times[j])
                j += 1
            
            # 如果有多家公司在同一时间窗口内变化
            if len(sync_window) >= 2:
                companies_in_sync = list(set([item[1] for item in sync_window]))
                
                if len(companies_in_sync) >= 2:
                    # 分析同步特征
                    changes_data = [item[2] for item in sync_window]
                    sync_analysis = self._analyze_sync_characteristics(changes_data, companies_in_sync, odds_type)
                    
                    sync_event = {
                        'time_window': (current_time, all_times[j-1][0] if j > i+1 else current_time),
                        'odds_type': odds_type,
                        'companies': companies_in_sync,
                        'companies_count': len(companies_in_sync),
                        'changes': changes_data,
                        'sync_analysis': sync_analysis
                    }
                    
                    sync_events.append(sync_event)
            
            i = j if j > i else i + 1
        
        return sync_events
    
    def _analyze_sync_characteristics(self, changes_data, companies, odds_type):
        """分析同步变化的特征"""
        change_pcts = [change['change_pct'] for change in changes_data]
        
        # 方向一致性
        positive_changes = sum(1 for pct in change_pcts if pct > 0)
        negative_changes = sum(1 for pct in change_pcts if pct < 0)
        direction_consistency = max(positive_changes, negative_changes) / len(change_pcts)
        
        # 幅度统计
        abs_changes = [abs(pct) for pct in change_pcts]
        magnitude_mean = np.mean(abs_changes)
        magnitude_std = np.std(abs_changes)
        magnitude_cv = magnitude_std / magnitude_mean if magnitude_mean > 0 else 0
        
        # 幅度一致性（变异系数的倒数）
        magnitude_consistency = 1 / (1 + magnitude_cv)
        
        # 综合同步度
        sync_score = (direction_consistency + magnitude_consistency) / 2
        
        return {
            'direction_consistency': direction_consistency,
            'magnitude_consistency': magnitude_consistency,
            'sync_score': sync_score,
            'magnitude_mean': magnitude_mean,
            'magnitude_std': magnitude_std,
            'change_direction': 'up' if positive_changes > negative_changes else 'down',
            'companies_count': len(companies)
        }
    
    def analyze_all_matches(self):
        """分析所有比赛的同步变盘"""
        if self.normalized_data is None:
            print("请先加载标准化数据")
            return None
        
        print("\n=== 开始交叉公司同步变盘分析 ===")
        
        all_sync_results = {}
        total_sync_events = 0
        
        for db_name, db_data in self.normalized_data.items():
            print(f"\n处理数据库: {db_name}")
            db_sync_results = {}
            
            for match_id, match_data in db_data.items():
                print(f"  分析比赛: {match_id}")
                
                sync_result = self.detect_synchronous_changes(match_data)
                if sync_result and sync_result['total_events'] > 0:
                    db_sync_results[match_id] = sync_result
                    total_sync_events += sync_result['total_events']
                    print(f"    发现 {sync_result['total_events']} 个同步事件")
                else:
                    print(f"    未发现同步事件")
            
            if db_sync_results:
                all_sync_results[db_name] = db_sync_results
        
        print(f"\n总计发现 {total_sync_events} 个同步事件")
        
        self.sync_analysis_results = all_sync_results
        return all_sync_results
    
    def calculate_company_sync_statistics(self):
        """计算公司间同步统计"""
        if not self.sync_analysis_results:
            print("请先进行同步分析")
            return None
        
        print("\n=== 公司同步统计分析 ===")
        
        # 公司参与同步事件的统计
        company_sync_stats = defaultdict(lambda: {
            'total_events': 0,
            'as_leader': 0,
            'sync_scores': [],
            'partners': defaultdict(int),
            'odds_types': defaultdict(int)
        })
        
        # 公司对同步统计
        pair_sync_stats = defaultdict(lambda: {
            'sync_count': 0,
            'avg_sync_score': 0,
            'sync_scores': []
        })
        
        for db_name, db_results in self.sync_analysis_results.items():
            for match_id, match_result in db_results.items():
                for event in match_result['sync_events']:
                    companies = event['companies']
                    sync_score = event['sync_analysis']['sync_score']
                    odds_type = event['odds_type']
                    
                    # 更新公司统计
                    for company in companies:
                        company_sync_stats[company]['total_events'] += 1
                        company_sync_stats[company]['sync_scores'].append(sync_score)
                        company_sync_stats[company]['odds_types'][odds_type] += 1
                        
                        # 统计合作伙伴
                        for partner in companies:
                            if partner != company:
                                company_sync_stats[company]['partners'][partner] += 1
                    
                    # 更新公司对统计
                    for pair in combinations(companies, 2):
                        pair_key = tuple(sorted(pair))
                        pair_sync_stats[pair_key]['sync_count'] += 1
                        pair_sync_stats[pair_key]['sync_scores'].append(sync_score)
        
        # 计算平均值
        for company, stats in company_sync_stats.items():
            if stats['sync_scores']:
                stats['avg_sync_score'] = np.mean(stats['sync_scores'])
                stats['sync_score_std'] = np.std(stats['sync_scores'])
        
        for pair, stats in pair_sync_stats.items():
            if stats['sync_scores']:
                stats['avg_sync_score'] = np.mean(stats['sync_scores'])
        
        # 输出统计结果
        print(f"\n公司同步参与度排名:")
        sorted_companies = sorted(company_sync_stats.items(), 
                                key=lambda x: x[1]['total_events'], reverse=True)
        
        for company, stats in sorted_companies[:10]:
            print(f"  {company}: {stats['total_events']}次同步, "
                  f"平均同步度{stats['avg_sync_score']:.3f}")
        
        print(f"\n最常见的同步公司对:")
        sorted_pairs = sorted(pair_sync_stats.items(), 
                            key=lambda x: x[1]['sync_count'], reverse=True)
        
        for pair, stats in sorted_pairs[:10]:
            print(f"  {pair[0]} & {pair[1]}: {stats['sync_count']}次同步, "
                  f"平均同步度{stats['avg_sync_score']:.3f}")
        
        sync_stats = {
            'company_stats': dict(company_sync_stats),
            'pair_stats': dict(pair_sync_stats)
        }
        
        return sync_stats
    
    def identify_sync_patterns(self):
        """识别同步模式"""
        if not self.sync_analysis_results:
            print("请先进行同步分析")
            return None
        
        print("\n=== 同步模式识别 ===")
        
        patterns = {
            'high_sync_events': [],      # 高同步度事件
            'mass_sync_events': [],      # 大规模同步事件
            'direction_patterns': defaultdict(int),  # 方向模式
            'timing_patterns': defaultdict(int)      # 时间模式
        }
        
        for db_name, db_results in self.sync_analysis_results.items():
            for match_id, match_result in db_results.items():
                for event in match_result['sync_events']:
                    sync_analysis = event['sync_analysis']
                    
                    # 高同步度事件（同步度>0.8）
                    if sync_analysis['sync_score'] > 0.8:
                        patterns['high_sync_events'].append({
                            'match_id': match_id,
                            'event': event
                        })
                    
                    # 大规模同步事件（3家以上公司）
                    if event['companies_count'] >= 3:
                        patterns['mass_sync_events'].append({
                            'match_id': match_id,
                            'event': event
                        })
                    
                    # 方向模式统计
                    direction = sync_analysis['change_direction']
                    odds_type = event['odds_type']
                    patterns['direction_patterns'][f"{odds_type}_{direction}"] += 1
                    
                    # 时间模式统计
                    time_window = event['time_window'][0]
                    if time_window <= -24:
                        time_pattern = 'early'
                    elif time_window <= -6:
                        time_pattern = 'medium'
                    else:
                        time_pattern = 'late'
                    patterns['timing_patterns'][time_pattern] += 1
        
        print(f"发现 {len(patterns['high_sync_events'])} 个高同步度事件")
        print(f"发现 {len(patterns['mass_sync_events'])} 个大规模同步事件")
        
        print(f"\n方向模式分布:")
        for pattern, count in patterns['direction_patterns'].items():
            print(f"  {pattern}: {count}次")
        
        print(f"\n时间模式分布:")
        for pattern, count in patterns['timing_patterns'].items():
            print(f"  {pattern}: {count}次")
        
        return patterns
    
    def save_sync_analysis_results(self, filename_prefix="sync_analysis"):
        """保存同步分析结果"""
        if not self.sync_analysis_results:
            print("没有同步分析结果可保存")
            return None
        
        output_file = f"{filename_prefix}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        
        # 转换为可序列化的格式
        serializable_results = self._make_serializable(self.sync_analysis_results)
        
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(serializable_results, f, ensure_ascii=False, indent=2)
        
        print(f"\n同步分析结果已保存到: {output_file}")
        return output_file
    
    def _make_serializable(self, obj):
        """将对象转换为可序列化的格式"""
        if isinstance(obj, dict):
            return {k: self._make_serializable(v) for k, v in obj.items()}
        elif isinstance(obj, (list, tuple)):
            return [self._make_serializable(item) for item in obj]
        elif isinstance(obj, (np.integer, np.int64)):
            return int(obj)
        elif isinstance(obj, (np.floating, np.float64)):
            return float(obj)
        elif isinstance(obj, (np.bool_, bool)):
            return bool(obj)
        elif isinstance(obj, np.ndarray):
            return obj.tolist()
        elif pd.isna(obj):
            return None
        else:
            return obj

def main():
    analyzer = CrossCompanySyncAnalyzer()
    
    # 加载标准化数据
    normalized_data = analyzer.load_normalized_data()
    if not normalized_data:
        return
    
    # 分析同步变盘
    sync_results = analyzer.analyze_all_matches()
    
    # 计算同步统计
    sync_stats = analyzer.calculate_company_sync_statistics()
    
    # 识别同步模式
    sync_patterns = analyzer.identify_sync_patterns()
    
    # 保存结果
    analyzer.save_sync_analysis_results()

if __name__ == "__main__":
    main()

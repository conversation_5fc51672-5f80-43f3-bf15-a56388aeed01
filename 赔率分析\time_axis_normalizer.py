#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
时间轴标准化处理器
将赔率变化时间转换为相对于比赛开始时间的标准时间轴
"""

import json
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import matplotlib.pyplot as plt
import seaborn as sns
from collections import defaultdict
import os

class TimeAxisNormalizer:
    def __init__(self, timeseries_file=None):
        self.timeseries_file = timeseries_file
        self.normalized_data = {}
        
        # 定义标准时间轴（相对比赛开始时间的小时数）
        self.standard_time_points = [
            -168, -144, -120, -96, -72, -48, -24, -12, -6, -3, -1, -0.5, 0
        ]  # 从7天前到比赛开始
        
    def load_timeseries_data(self, filename=None):
        """加载时间序列数据"""
        if filename:
            self.timeseries_file = filename
        
        if not self.timeseries_file or not os.path.exists(self.timeseries_file):
            print("时间序列数据文件不存在")
            return None
        
        with open(self.timeseries_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        print(f"加载时间序列数据: {self.timeseries_file}")
        return data
    
    def parse_match_time(self, match_info):
        """解析比赛开始时间"""
        if not match_info or not match_info.get('match_time'):
            return None
        
        try:
            # 尝试解析不同的时间格式
            time_str = match_info['match_time']
            if isinstance(time_str, str):
                # 格式: "2025-01-19 04:00:00"
                return datetime.strptime(time_str, "%Y-%m-%d %H:%M:%S")
        except:
            pass
        
        return None
    
    def calculate_relative_hours(self, timestamps, match_start_time):
        """计算相对于比赛开始时间的小时数"""
        if not match_start_time:
            return []
        
        relative_hours = []
        for ts_str in timestamps:
            try:
                ts = datetime.fromisoformat(ts_str)
                hours_diff = (ts - match_start_time).total_seconds() / 3600
                relative_hours.append(hours_diff)
            except:
                relative_hours.append(None)
        
        return relative_hours
    
    def normalize_single_match(self, match_data):
        """标准化单场比赛的时间轴"""
        match_id = match_data['match_id']
        match_info = match_data.get('match_info')
        
        # 解析比赛开始时间
        match_start_time = self.parse_match_time(match_info)
        if not match_start_time:
            print(f"  比赛 {match_id}: 无法解析比赛开始时间")
            return None
        
        print(f"  比赛 {match_id}: 开始时间 {match_start_time}")
        
        normalized_companies = {}
        
        for company, company_data in match_data['companies_data'].items():
            timestamps = company_data['timestamps']
            
            # 计算相对时间
            relative_hours = self.calculate_relative_hours(timestamps, match_start_time)
            
            # 过滤掉无效时间
            valid_indices = [i for i, h in enumerate(relative_hours) if h is not None]
            
            if not valid_indices:
                continue
            
            # 构建标准化数据
            normalized_company_data = {
                'relative_hours': [relative_hours[i] for i in valid_indices],
                'home_odds': [float(company_data['home_odds'][i]) if company_data['home_odds'][i] not in [None, 'None'] else None for i in valid_indices],
                'draw_odds': [float(company_data['draw_odds'][i]) if company_data['draw_odds'][i] not in [None, 'None'] else None for i in valid_indices],
                'away_odds': [float(company_data['away_odds'][i]) if company_data['away_odds'][i] not in [None, 'None'] else None for i in valid_indices],
                'return_rate': [float(company_data['return_rate'][i]) if company_data['return_rate'][i] not in [None, 'None'] else None for i in valid_indices],
                'original_timestamps': [timestamps[i] for i in valid_indices]
            }
            
            # 按时间排序
            sorted_indices = sorted(range(len(normalized_company_data['relative_hours'])), 
                                  key=lambda i: normalized_company_data['relative_hours'][i])
            
            for key in normalized_company_data:
                normalized_company_data[key] = [normalized_company_data[key][i] for i in sorted_indices]
            
            normalized_companies[company] = normalized_company_data
        
        if not normalized_companies:
            print(f"  比赛 {match_id}: 没有有效的赔率数据")
            return None
        
        # 分析时间范围
        all_hours = []
        for company_data in normalized_companies.values():
            all_hours.extend(company_data['relative_hours'])
        
        time_range = {
            'earliest_hour': min(all_hours),
            'latest_hour': max(all_hours),
            'span_hours': max(all_hours) - min(all_hours)
        }
        
        print(f"    时间范围: {time_range['earliest_hour']:.1f}h 到 {time_range['latest_hour']:.1f}h")
        print(f"    公司数量: {len(normalized_companies)}")
        
        return {
            'match_id': match_id,
            'match_info': match_info,
            'match_start_time': match_start_time.isoformat(),
            'companies_data': normalized_companies,
            'time_range': time_range,
            'companies_count': len(normalized_companies)
        }
    
    def interpolate_to_standard_grid(self, normalized_match_data):
        """将数据插值到标准时间网格"""
        match_id = normalized_match_data['match_id']
        companies_data = normalized_match_data['companies_data']
        
        interpolated_companies = {}
        
        for company, company_data in companies_data.items():
            relative_hours = company_data['relative_hours']
            
            # 只保留比赛开始前的数据（相对时间 <= 0）
            pre_match_indices = [i for i, h in enumerate(relative_hours) if h <= 0]
            
            if len(pre_match_indices) < 2:  # 至少需要2个点才能插值
                continue
            
            # 提取比赛前的数据
            pre_match_hours = [relative_hours[i] for i in pre_match_indices]
            pre_match_home = [company_data['home_odds'][i] for i in pre_match_indices]
            pre_match_draw = [company_data['draw_odds'][i] for i in pre_match_indices]
            pre_match_away = [company_data['away_odds'][i] for i in pre_match_indices]
            
            # 过滤掉None值
            valid_data = []
            for i in range(len(pre_match_hours)):
                if (pre_match_home[i] is not None and 
                    pre_match_draw[i] is not None and 
                    pre_match_away[i] is not None):
                    valid_data.append({
                        'hour': pre_match_hours[i],
                        'home': pre_match_home[i],
                        'draw': pre_match_draw[i],
                        'away': pre_match_away[i]
                    })
            
            if len(valid_data) < 2:
                continue
            
            # 按时间排序
            valid_data.sort(key=lambda x: x['hour'])
            
            # 插值到标准时间点
            hours = [d['hour'] for d in valid_data]
            home_odds = [d['home'] for d in valid_data]
            draw_odds = [d['draw'] for d in valid_data]
            away_odds = [d['away'] for d in valid_data]
            
            # 只插值到数据范围内的标准时间点
            min_hour = min(hours)
            max_hour = max(hours)
            
            valid_standard_points = [h for h in self.standard_time_points 
                                   if min_hour <= h <= max_hour]
            
            if len(valid_standard_points) < 2:
                continue
            
            try:
                # 使用线性插值
                interpolated_home = np.interp(valid_standard_points, hours, home_odds)
                interpolated_draw = np.interp(valid_standard_points, hours, draw_odds)
                interpolated_away = np.interp(valid_standard_points, hours, away_odds)
                
                interpolated_companies[company] = {
                    'time_points': valid_standard_points,
                    'home_odds': interpolated_home.tolist(),
                    'draw_odds': interpolated_draw.tolist(),
                    'away_odds': interpolated_away.tolist(),
                    'original_data_points': len(valid_data),
                    'interpolated_points': len(valid_standard_points)
                }
                
            except Exception as e:
                print(f"    插值失败 {company}: {e}")
                continue
        
        if interpolated_companies:
            print(f"  比赛 {match_id}: 成功插值 {len(interpolated_companies)} 家公司")
            return {
                'match_id': match_id,
                'match_info': normalized_match_data['match_info'],
                'match_start_time': normalized_match_data['match_start_time'],
                'companies_data': interpolated_companies,
                'standard_time_points': self.standard_time_points,
                'companies_count': len(interpolated_companies)
            }
        
        return None
    
    def normalize_all_matches(self, timeseries_data):
        """标准化所有比赛的时间轴"""
        print("\n=== 开始时间轴标准化处理 ===")
        
        normalized_results = {}
        
        for db_name, db_data in timeseries_data.items():
            print(f"\n处理数据库: {db_name}")
            db_results = {}
            
            for match_id, match_data in db_data.items():
                print(f"  处理比赛: {match_id}")
                
                # 第一步：标准化时间轴
                normalized_match = self.normalize_single_match(match_data)
                if not normalized_match:
                    continue
                
                # 第二步：插值到标准网格
                interpolated_match = self.interpolate_to_standard_grid(normalized_match)
                if interpolated_match:
                    db_results[match_id] = interpolated_match
            
            if db_results:
                normalized_results[db_name] = db_results
                print(f"  {db_name}: 成功处理 {len(db_results)} 场比赛")
        
        self.normalized_data = normalized_results
        return normalized_results
    
    def save_normalized_data(self, filename_prefix="normalized_odds"):
        """保存标准化后的数据"""
        if not self.normalized_data:
            print("没有标准化数据可保存")
            return None
        
        output_file = f"{filename_prefix}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(self.normalized_data, f, ensure_ascii=False, indent=2)
        
        print(f"\n标准化数据已保存到: {output_file}")
        return output_file
    
    def analyze_normalized_data(self):
        """分析标准化后的数据"""
        if not self.normalized_data:
            print("没有标准化数据可分析")
            return
        
        print(f"\n=== 标准化数据分析 ===")
        
        total_matches = 0
        total_companies = set()
        time_point_coverage = defaultdict(int)
        
        for db_name, db_data in self.normalized_data.items():
            total_matches += len(db_data)
            
            for match_id, match_data in db_data.items():
                for company in match_data['companies_data'].keys():
                    total_companies.add(company)
                
                # 统计时间点覆盖
                for company_data in match_data['companies_data'].values():
                    for time_point in company_data['time_points']:
                        time_point_coverage[time_point] += 1
        
        print(f"  总比赛数: {total_matches}")
        print(f"  总公司数: {len(total_companies)}")
        print(f"  公司列表: {sorted(list(total_companies))}")
        
        print(f"  时间点覆盖情况:")
        for time_point in sorted(time_point_coverage.keys()):
            coverage = time_point_coverage[time_point]
            print(f"    {time_point:6.1f}h: {coverage:3d}次 ({coverage/total_matches*100:.1f}%)")

def main():
    # 查找最新的时间序列文件
    timeseries_files = [f for f in os.listdir('.') if f.startswith('odds_timeseries_') and f.endswith('.json')]
    if not timeseries_files:
        print("没有找到时间序列数据文件")
        return
    
    latest_file = sorted(timeseries_files)[-1]
    print(f"使用时间序列文件: {latest_file}")
    
    normalizer = TimeAxisNormalizer(latest_file)
    
    # 加载数据
    timeseries_data = normalizer.load_timeseries_data()
    if not timeseries_data:
        return
    
    # 标准化处理
    normalized_data = normalizer.normalize_all_matches(timeseries_data)
    
    # 保存结果
    normalizer.save_normalized_data()
    
    # 分析结果
    normalizer.analyze_normalized_data()

if __name__ == "__main__":
    main()

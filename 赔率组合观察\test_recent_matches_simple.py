#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单测试最近比赛功能
"""

import sys
import os
import logging

# 添加当前目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

from odds_combination_database import OddsCombinationDatabase

def test_recent_matches_simple():
    """简单测试最近比赛功能"""
    print("🔍 简单测试最近比赛功能")
    
    try:
        # 初始化数据库（使用父目录的数据库）
        parent_dir = os.path.dirname(current_dir)
        db_path = os.path.join(parent_dir, "odds_data.db")
        db = OddsCombinationDatabase(db_path)
        print(f"✅ 数据库连接成功: {db.db_path}")
        
        # 测试一个很大的时间范围，比如最近3年
        days = 1095  # 3年
        print(f"\n📅 测试最近 {days} 天的比赛:")
        
        recent_matches = db.get_recent_matches(days)
        print(f"  找到 {len(recent_matches)} 场比赛")
        
        # 显示前10场比赛的详细信息
        for i, match in enumerate(recent_matches[:10]):
            match_id = match['match_id']
            home_team = match.get('home_team', '未知')
            away_team = match.get('away_team', '未知')
            league = match.get('league', '未知联赛')
            display_time = match.get('display_time', '未知时间')
            
            print(f"    {i+1}. {match_id} | {display_time} | {league} | {home_team} vs {away_team}")
        
        if len(recent_matches) > 10:
            print(f"    ... 还有 {len(recent_matches) - 10} 场比赛")
        
        print(f"\n✅ 测试完成")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    # 配置日志
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    test_recent_matches_simple()

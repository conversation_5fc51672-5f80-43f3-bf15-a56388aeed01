#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试筛选功能的集成
验证凯利分析2筛选是否正确应用到比赛列表
"""

import sqlite3
import sys
import os

def test_kelly_analysis2_filter_integration():
    """测试凯利分析2筛选的集成"""
    print("=== 测试凯利分析2筛选集成 ===")
    
    try:
        with sqlite3.connect("odds_data.db") as conn:
            conn.row_factory = sqlite3.Row
            cursor = conn.cursor()
            
            # 模拟凯利分析2筛选的完整流程
            kelly_type = "kelly_home"
            stats_count = 5
            kelly_threshold = 1.1
            selected_companies = ["betathome", "pinnacle"]
            meaningless_threshold = 1
            
            print(f"筛选参数:")
            print(f"  凯利类型: {kelly_type}")
            print(f"  统计数量: {stats_count}")
            print(f"  凯利门槛: {kelly_threshold}")
            print(f"  监控公司: {selected_companies}")
            print(f"  无意义公司门槛: {meaningless_threshold}")
            print()
            
            # 获取所有有赔率数据的比赛
            cursor.execute('SELECT DISTINCT match_id FROM odds WHERE {} IS NOT NULL'.format(kelly_type))
            all_match_ids = [row[0] for row in cursor.fetchall()]
            
            print(f"总共有 {len(all_match_ids)} 场比赛有{kelly_type}数据")
            
            # 对每场比赛进行凯利分析2
            qualified_matches = []
            sample_analysis = []
            
            for i, match_id in enumerate(all_match_ids[:20]):  # 只测试前20场
                # 获取该比赛的赔率数据
                cursor.execute(f'''
                    SELECT {kelly_type}, return_rate, company_name
                    FROM odds
                    WHERE match_id = ? AND {kelly_type} IS NOT NULL AND return_rate IS NOT NULL AND company_name IS NOT NULL
                    ORDER BY {kelly_type} DESC
                ''', (match_id,))
                
                odds_data = cursor.fetchall()
                
                if len(odds_data) < stats_count:
                    continue
                
                # 取前N家的数据
                top_odds = odds_data[:stats_count]
                
                # 计算平均凯利值
                kelly_values = [float(row[0]) for row in top_odds]
                company_list = [row[2] for row in top_odds]
                
                avg_kelly = sum(kelly_values) / len(kelly_values)
                
                # 统计选中的博彩公司在公司列表中出现的次数
                observation_count = 0
                for company in selected_companies:
                    observation_count += company_list.count(company)
                
                # 判断是否符合筛选条件
                kelly_qualified = avg_kelly > kelly_threshold
                observation_qualified = observation_count <= meaningless_threshold
                
                result = kelly_qualified and observation_qualified
                
                # 记录分析结果
                analysis = {
                    'match_id': match_id,
                    'avg_kelly': avg_kelly,
                    'observation_count': observation_count,
                    'company_list': company_list,
                    'kelly_qualified': kelly_qualified,
                    'observation_qualified': observation_qualified,
                    'result': result
                }
                sample_analysis.append(analysis)
                
                if result:
                    qualified_matches.append(match_id)
            
            print(f"测试了前20场比赛，符合条件的比赛: {len(qualified_matches)} 场")
            print(f"符合条件的比赛ID: {qualified_matches}")
            print()
            
            # 显示详细分析结果
            print("详细分析结果:")
            for analysis in sample_analysis[:10]:  # 只显示前10个
                match_id = analysis['match_id']
                avg_kelly = analysis['avg_kelly']
                observation_count = analysis['observation_count']
                company_list = analysis['company_list']
                result = analysis['result']
                
                status = "✅ 通过" if result else "❌ 不通过"
                print(f"  比赛 {match_id}: {status}")
                print(f"    平均凯利: {avg_kelly:.3f} ({'>' if analysis['kelly_qualified'] else '<='} {kelly_threshold})")
                print(f"    观察次数: {observation_count} ({'<=' if analysis['observation_qualified'] else '>'} {meaningless_threshold})")
                print(f"    前{stats_count}家公司: {company_list}")
                print()
            
            # 验证筛选逻辑
            print("筛选逻辑验证:")
            passed_count = sum(1 for a in sample_analysis if a['result'])
            failed_count = len(sample_analysis) - passed_count
            print(f"  通过筛选: {passed_count} 场")
            print(f"  未通过筛选: {failed_count} 场")
            
            # 检查是否有应该被排除但没有被排除的比赛
            should_be_excluded = []
            for analysis in sample_analysis:
                if analysis['observation_count'] > meaningless_threshold and analysis['result']:
                    should_be_excluded.append(analysis['match_id'])
            
            if should_be_excluded:
                print(f"  ⚠️  发现问题: 以下比赛应该被排除但没有被排除: {should_be_excluded}")
            else:
                print(f"  ✅ 筛选逻辑正确")
            
            return len(qualified_matches) > 0
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def test_specific_match_analysis():
    """测试特定比赛的分析"""
    print("\n=== 测试特定比赛分析 ===")
    
    # 根据您的截图，测试一个可能包含betathome的比赛
    try:
        with sqlite3.connect("odds_data.db") as conn:
            conn.row_factory = sqlite3.Row
            cursor = conn.cursor()
            
            # 找到包含betathome的比赛
            cursor.execute('''
                SELECT DISTINCT match_id 
                FROM odds 
                WHERE company_name = 'betathome' AND kelly_home IS NOT NULL
                LIMIT 5
            ''')
            
            betathome_matches = [row[0] for row in cursor.fetchall()]
            print(f"找到 {len(betathome_matches)} 场包含betathome数据的比赛")
            
            if not betathome_matches:
                print("没有找到包含betathome的比赛")
                return False
            
            # 测试第一场比赛
            match_id = betathome_matches[0]
            print(f"测试比赛 {match_id}:")
            
            # 获取该比赛的前5家凯利主数据
            cursor.execute('''
                SELECT kelly_home, return_rate, company_name
                FROM odds
                WHERE match_id = ? AND kelly_home IS NOT NULL AND company_name IS NOT NULL
                ORDER BY kelly_home DESC
                LIMIT 5
            ''', (match_id,))
            
            top5_data = cursor.fetchall()
            
            print("前5家凯利主数据:")
            company_list = []
            for i, row in enumerate(top5_data, 1):
                kelly_val = float(row[0])
                company = row[2]
                company_list.append(company)
                mark = "★" if company == 'betathome' else " "
                print(f"  {i}. {mark} {company:15s} - 凯利:{kelly_val:.3f}")
            
            # 统计betathome出现次数
            betathome_count = company_list.count('betathome')
            print(f"\nbetathome在前5家中出现次数: {betathome_count}")
            
            # 测试不同的门槛设置
            test_cases = [
                {'threshold': 0, 'expected': betathome_count <= 0},
                {'threshold': 1, 'expected': betathome_count <= 1},
                {'threshold': 2, 'expected': betathome_count <= 2},
            ]
            
            print("\n门槛测试:")
            for case in test_cases:
                threshold = case['threshold']
                expected = case['expected']
                actual = betathome_count <= threshold
                status = "✅" if actual == expected else "❌"
                print(f"  门槛{threshold}: 出现{betathome_count}次 <= {threshold} = {actual} {status}")
            
            return True
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🔍 凯利分析2筛选集成测试")
    print("=" * 60)
    
    # 检查数据库文件是否存在
    if not os.path.exists("odds_data.db"):
        print("❌ 数据库文件 odds_data.db 不存在")
        return False
    
    tests = [
        test_kelly_analysis2_filter_integration,
        test_specific_match_analysis
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"❌ 测试异常: {e}")
    
    print("\n" + "=" * 60)
    print(f"📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！")
        print("\n💡 说明:")
        print("凯利分析2筛选的作用是筛选出符合条件的比赛，")
        print("而不是筛选比赛内的赔率数据。")
        print("如果您在比赛详情中看到betathome的数据，")
        print("这是正常的，因为那是该比赛的所有赔率数据。")
        return True
    else:
        print("⚠️  部分测试失败，请检查相关功能。")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试凯利分析筛选功能
验证凯利分析筛选逻辑是否正确工作
"""

import sqlite3
import sys
import os
from database import OddsDatabase

def test_kelly_data_availability():
    """测试凯利指数数据的可用性"""
    print("=== 测试凯利指数数据的可用性 ===")
    try:
        with sqlite3.connect("odds_data.db") as conn:
            conn.row_factory = sqlite3.Row
            cursor = conn.cursor()
            
            # 检查有凯利指数数据的比赛数量
            cursor.execute('''
                SELECT COUNT(DISTINCT match_id) as match_count
                FROM odds 
                WHERE kelly_home IS NOT NULL AND kelly_draw IS NOT NULL AND kelly_away IS NOT NULL
                  AND return_rate IS NOT NULL
            ''')
            
            result = cursor.fetchone()
            match_count = result[0] if result else 0
            
            print(f"✅ 有完整凯利指数数据的比赛: {match_count} 场")
            
            if match_count > 0:
                # 获取一个示例比赛的凯利数据
                cursor.execute('''
                    SELECT match_id, COUNT(*) as company_count
                    FROM odds 
                    WHERE kelly_home IS NOT NULL AND kelly_draw IS NOT NULL AND kelly_away IS NOT NULL
                      AND return_rate IS NOT NULL
                    GROUP BY match_id
                    ORDER BY company_count DESC
                    LIMIT 1
                ''')
                
                sample_match = cursor.fetchone()
                if sample_match:
                    match_id = sample_match[0]
                    company_count = sample_match[1]
                    print(f"示例比赛 {match_id}: {company_count} 家博彩公司有凯利数据")
                    
                    # 显示该比赛的凯利数据样本
                    cursor.execute('''
                        SELECT company_name, kelly_home, kelly_draw, kelly_away, return_rate
                        FROM odds 
                        WHERE match_id = ? AND kelly_home IS NOT NULL
                        ORDER BY kelly_home DESC
                        LIMIT 5
                    ''', (match_id,))
                    
                    kelly_samples = cursor.fetchall()
                    print("凯利数据样本（按凯利主降序）:")
                    for sample in kelly_samples:
                        print(f"  {sample[0]}: 主{sample[1]:.3f} 平{sample[2]:.3f} 客{sample[3]:.3f} 返还率{sample[4]:.3f}")
            
            return match_count > 0
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def test_kelly_analysis_logic():
    """测试凯利分析逻辑"""
    print("\n=== 测试凯利分析逻辑 ===")
    
    def analyze_kelly_sample(kelly_data, stats_count, kelly_threshold, return_rate_threshold):
        """模拟凯利分析逻辑"""
        if len(kelly_data) < stats_count:
            return False, "数据不足"
        
        # 按凯利值降序排序
        sorted_data = sorted(kelly_data, key=lambda x: x[0], reverse=True)
        
        # 取前N家
        top_data = sorted_data[:stats_count]
        
        # 计算平均值
        avg_kelly = sum(item[0] for item in top_data) / len(top_data)
        avg_return_rate = sum(item[1] for item in top_data) / len(top_data)
        
        # 判断条件
        kelly_qualified = avg_kelly > kelly_threshold
        return_rate_qualified = avg_return_rate < return_rate_threshold
        
        result = kelly_qualified and return_rate_qualified
        
        return result, {
            'avg_kelly': avg_kelly,
            'avg_return_rate': avg_return_rate,
            'kelly_qualified': kelly_qualified,
            'return_rate_qualified': return_rate_qualified
        }
    
    # 测试用例（使用正确的数据格式：凯利指数接近1.0，返还率为百分比）
    test_cases = [
        {
            'name': '符合条件的案例',
            'data': [(1.025, 96.5), (1.020, 96.3), (1.018, 96.1), (1.015, 95.9), (1.012, 95.7),
                    (1.010, 95.5), (1.008, 95.3), (1.005, 95.1), (1.003, 94.9), (1.001, 94.7)],
            'stats_count': 10,
            'kelly_threshold': 1.01,
            'return_rate_threshold': 97,
            'expected': True
        },
        {
            'name': '凯利值不足的案例',
            'data': [(1.008, 96.5), (1.005, 96.3), (1.003, 96.1), (1.001, 95.9), (0.998, 95.7),
                    (0.995, 95.5), (0.992, 95.3), (0.990, 95.1), (0.988, 94.9), (0.985, 94.7)],
            'stats_count': 10,
            'kelly_threshold': 1.01,
            'return_rate_threshold': 97,
            'expected': False
        },
        {
            'name': '返还率过高的案例',
            'data': [(1.025, 97.5), (1.020, 97.3), (1.018, 97.1), (1.015, 96.9), (1.012, 96.7),
                    (1.010, 96.5), (1.008, 96.3), (1.005, 96.1), (1.003, 95.9), (1.001, 95.7)],
            'stats_count': 10,
            'kelly_threshold': 1.01,
            'return_rate_threshold': 96.5,  # 调整为更严格的返还率门槛
            'expected': False
        },
        {
            'name': '数据不足的案例',
            'data': [(1.025, 96.5), (1.020, 96.3), (1.018, 96.1)],
            'stats_count': 10,
            'kelly_threshold': 1.01,
            'return_rate_threshold': 97,
            'expected': False
        }
    ]
    
    all_passed = True
    for case in test_cases:
        result, details = analyze_kelly_sample(
            case['data'], case['stats_count'], 
            case['kelly_threshold'], case['return_rate_threshold']
        )
        
        if result == case['expected']:
            print(f"✅ {case['name']}: {result}")
            if isinstance(details, dict):
                print(f"   平均凯利: {details['avg_kelly']:.3f}, 平均返还率: {details['avg_return_rate']:.3f}")
        else:
            print(f"❌ {case['name']}: {result}, 期望: {case['expected']}")
            all_passed = False
    
    return all_passed

def test_real_kelly_analysis():
    """测试真实数据的凯利分析"""
    print("\n=== 测试真实数据的凯利分析 ===")
    try:
        with sqlite3.connect("odds_data.db") as conn:
            conn.row_factory = sqlite3.Row
            cursor = conn.cursor()
            
            # 获取一个有足够凯利数据的比赛
            cursor.execute('''
                SELECT match_id, COUNT(*) as company_count
                FROM odds 
                WHERE kelly_home IS NOT NULL AND return_rate IS NOT NULL
                GROUP BY match_id
                HAVING company_count >= 10
                ORDER BY company_count DESC
                LIMIT 1
            ''')
            
            match_row = cursor.fetchone()
            if not match_row:
                print("❌ 没有找到足够的凯利数据进行测试")
                return False
            
            match_id = match_row[0]
            company_count = match_row[1]
            print(f"测试比赛 {match_id}: {company_count} 家博彩公司")
            
            # 获取该比赛的凯利数据
            cursor.execute('''
                SELECT kelly_home, return_rate 
                FROM odds 
                WHERE match_id = ? AND kelly_home IS NOT NULL AND return_rate IS NOT NULL
                ORDER BY kelly_home DESC
            ''', (match_id,))
            
            kelly_data = cursor.fetchall()
            
            # 测试不同的参数设置（使用正确的参数范围）
            test_params = [
                {'stats_count': 10, 'kelly_threshold': 1.01, 'return_rate_threshold': 97},
                {'stats_count': 5, 'kelly_threshold': 1.015, 'return_rate_threshold': 96.5},
                {'stats_count': 15, 'kelly_threshold': 1.02, 'return_rate_threshold': 96}
            ]
            
            for i, params in enumerate(test_params, 1):
                stats_count = params['stats_count']
                kelly_threshold = params['kelly_threshold']
                return_rate_threshold = params['return_rate_threshold']
                
                if len(kelly_data) >= stats_count:
                    # 取前N家数据
                    top_data = kelly_data[:stats_count]
                    
                    # 计算平均值
                    kelly_values = [float(row[0]) for row in top_data]
                    return_rates = [float(row[1]) for row in top_data]
                    
                    avg_kelly = sum(kelly_values) / len(kelly_values)
                    avg_return_rate = sum(return_rates) / len(return_rates)
                    
                    # 判断条件
                    kelly_qualified = avg_kelly > kelly_threshold
                    return_rate_qualified = avg_return_rate < return_rate_threshold
                    result = kelly_qualified and return_rate_qualified
                    
                    status = "✅ 符合" if result else "❌ 不符合"
                    print(f"  参数组{i}: {status}")
                    print(f"    统计{stats_count}家, 凯利门槛{kelly_threshold}, 返还率门槛{return_rate_threshold}")
                    print(f"    平均凯利: {avg_kelly:.3f} ({'>' if kelly_qualified else '<='} {kelly_threshold})")
                    print(f"    平均返还率: {avg_return_rate:.3f} ({'<' if return_rate_qualified else '>='} {return_rate_threshold})")
                else:
                    print(f"  参数组{i}: ❌ 数据不足（需要{stats_count}家，实际{len(kelly_data)}家）")
            
            return True
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🧪 凯利分析筛选功能测试")
    print("=" * 50)
    
    # 检查数据库文件是否存在
    if not os.path.exists("odds_data.db"):
        print("❌ 数据库文件 odds_data.db 不存在")
        return False
    
    tests = [
        test_kelly_data_availability,
        test_kelly_analysis_logic,
        test_real_kelly_analysis
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"❌ 测试异常: {e}")
    
    print("\n" + "=" * 50)
    print(f"📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！凯利分析筛选功能应该可以正常工作。")
        return True
    else:
        print("⚠️  部分测试失败，请检查相关功能。")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)

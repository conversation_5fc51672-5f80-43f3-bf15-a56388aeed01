# 联赛分库使用指南

## 🎯 **功能概述**

按联赛分库是一个非常棒的想法！这个功能可以：
- ✅ **控制单个文件大小** - 每个联赛独立存储
- ✅ **提高查询性能** - 小文件查询更快
- ✅ **便于数据管理** - 可以单独备份某个联赛
- ✅ **支持并行处理** - 不同联赛可以同时操作

## 📊 **两种数据库模式对比**

| 特性 | 单一数据库模式 | 联赛分库模式 |
|------|---------------|-------------|
| **文件数量** | 1个文件 | 多个文件(按联赛) |
| **单文件大小** | 可能很大 | 小而可控 |
| **查询性能** | 大文件时较慢 | 始终保持高性能 |
| **管理复杂度** | 简单 | 稍复杂但更灵活 |
| **适用场景** | 小到中等规模 | 大规模长期使用 |
| **备份策略** | 整体备份 | 可选择性备份 |

## 🚀 **快速开始**

### 1. 启动模式选择器
```bash
# 图形界面
python database_mode_selector.py
# 或双击
start_database_mode_selector.bat
```

### 2. 命令行操作
```bash
# 查看联赛列表
python league_database_manager.py list

# 查看总体统计
python league_database_manager.py stats

# 从单一数据库迁移
python league_database_manager.py migrate odds_data.db

# 清理空数据库
python league_database_manager.py cleanup
```

## 📁 **联赛文件命名规则**

系统会自动将联赛名称转换为文件名：

| 联赛名称 | 文件名 |
|---------|--------|
| 英超 | Premier_League.db |
| 西甲 | La_Liga.db |
| 德甲 | Bundesliga.db |
| 意甲 | Serie_A.db |
| 法甲 | Ligue_1.db |
| 美职业 | MLS.db |
| 中超 | Chinese_Super_League.db |
| 欧冠 | Champions_League.db |
| 友谊赛 | Friendlies.db |

## 💻 **在代码中使用**

### 方式1: 使用增强版数据库
```python
from enhanced_database import create_league_database

# 创建联赛分库实例
db = create_league_database("league_databases")

# 保存数据（自动根据联赛分库）
match_info = {
    'match_id': '12345',
    'league': '英超',
    'home_team': '曼城',
    'away_team': '利物浦',
    # ... 其他信息
}
odds_data = [...]  # 赔率数据

result = db.save_complete_match_data(match_info, odds_data)
print(f"数据已保存到: {result['database_path']}")

# 查询数据
match_data = db.get_complete_match_data('12345')
```

### 方式2: 直接使用联赛管理器
```python
from league_database_manager import LeagueDatabaseManager

# 创建管理器
manager = LeagueDatabaseManager("league_databases")

# 保存数据
result = manager.save_match_data(match_info, odds_data)

# 获取数据
match_data = manager.get_match_data('12345')

# 获取所有联赛统计
leagues = manager.get_all_leagues()
for league in leagues:
    print(f"{league['league_name']}: {league['match_count']} 场比赛")
```

## 🔄 **模式切换**

### 从单一数据库切换到联赛分库
```python
from enhanced_database import create_single_database

# 创建单一数据库实例
single_db = create_single_database("odds_data.db")

# 切换到联赛分库模式
result = single_db.switch_mode("league", base_dir="league_databases")

if result['success']:
    print(f"切换成功! 迁移了 {result['migrated_matches']} 场比赛")
    print(f"创建了联赛: {', '.join(result['leagues_created'])}")
```

### 从联赛分库切换到单一数据库
```python
from enhanced_database import create_league_database

# 创建联赛分库实例
league_db = create_league_database("league_databases")

# 切换到单一数据库模式
result = league_db.switch_mode("single", db_path="odds_data_merged.db")

if result['success']:
    print(f"合并成功! 合并了 {result['merged_matches']} 场比赛")
```

## 📈 **实际使用示例**

### 当前数据分析
假设你当前有31MB的数据，409场比赛：

```python
from league_database_manager import LeagueDatabaseManager

# 迁移到联赛分库
manager = LeagueDatabaseManager()
result = manager.migrate_from_single_database("odds_data.db")

print(f"迁移结果:")
print(f"- 总比赛: {result['total_matches']}")
print(f"- 成功迁移: {result['migrated_matches']}")
print(f"- 创建联赛: {len(result['leagues_created'])}")
```

### 预期结果
迁移后可能的文件结构：
```
league_databases/
├── Premier_League.db      (5.2MB, 45场比赛)
├── La_Liga.db            (4.8MB, 38场比赛)
├── Bundesliga.db         (3.9MB, 32场比赛)
├── Serie_A.db            (3.1MB, 28场比赛)
├── MLS.db                (6.2MB, 67场比赛)
├── Chinese_Super_League.db (4.5MB, 41场比赛)
├── Champions_League.db    (2.8MB, 23场比赛)
├── Friendlies.db         (1.2MB, 15场比赛)
└── ...其他联赛
```

## 🛠️ **高级功能**

### 1. 联赛统计分析
```python
# 获取所有联赛统计
leagues = manager.get_all_leagues()

# 按大小排序
leagues_by_size = sorted(leagues, key=lambda x: x['db_size_mb'], reverse=True)
print("最大的联赛数据库:")
for league in leagues_by_size[:5]:
    print(f"- {league['league_name']}: {league['db_size_mb']:.2f}MB")

# 按比赛数量排序
leagues_by_matches = sorted(leagues, key=lambda x: x['match_count'], reverse=True)
print("比赛最多的联赛:")
for league in leagues_by_matches[:5]:
    print(f"- {league['league_name']}: {league['match_count']}场")
```

### 2. 选择性备份
```python
# 只备份重要联赛
important_leagues = ['英超', '西甲', '德甲', '意甲', '法甲']

for league_name in important_leagues:
    db_path = manager.get_database_path(league_name)
    if os.path.exists(db_path):
        backup_path = f"backup_{league_name}_{datetime.now().strftime('%Y%m%d')}.db"
        shutil.copy2(db_path, backup_path)
        print(f"已备份: {league_name} -> {backup_path}")
```

### 3. 性能监控
```python
# 监控各联赛数据库大小
total_stats = manager.get_total_stats()
print(f"总体统计:")
print(f"- 联赛数量: {total_stats['total_leagues']}")
print(f"- 总比赛数: {total_stats['total_matches']:,}")
print(f"- 总大小: {total_stats['total_size_mb']:.2f}MB")

# 检查是否有联赛超过大小限制
for league in manager.get_all_leagues():
    if league['db_size_mb'] > 50:  # 50MB限制
        print(f"警告: {league['league_name']} 数据库过大 ({league['db_size_mb']:.2f}MB)")
```

## 🎯 **最佳实践建议**

### 1. 何时使用联赛分库
- ✅ 数据库超过100MB时
- ✅ 涉及多个联赛时
- ✅ 需要长期数据存储时
- ✅ 希望提高查询性能时

### 2. 文件管理策略
- 📁 定期检查各联赛文件大小
- 🗂️ 对不活跃联赛进行归档
- 💾 重要联赛单独备份
- 🧹 清理空的联赛数据库

### 3. 性能优化
- ⚡ 单个联赛文件控制在50MB以内
- 🔍 为常用联赛建立索引
- 📊 定期分析联赛数据分布
- 🚀 使用SSD存储提高性能

## 🔧 **故障排除**

### 问题1: 联赛名称识别错误
```python
# 手动添加联赛映射
manager.league_mapping['新联赛名'] = 'New_League_Name'
```

### 问题2: 文件路径问题
```python
# 检查文件路径
for league in manager.get_all_leagues():
    print(f"{league['league_name']}: {league['database_path']}")
```

### 问题3: 数据迁移失败
```python
# 检查迁移日志
result = manager.migrate_from_single_database("odds_data.db")
if result.get('errors'):
    print("迁移错误:")
    for error in result['errors']:
        print(f"- {error}")
```

## 📝 **总结**

联赛分库功能为你提供了：

1. **🎯 精确控制** - 每个联赛独立管理
2. **⚡ 高性能** - 小文件快速查询
3. **🔧 灵活性** - 可选择性操作
4. **📈 可扩展** - 支持无限联赛
5. **🛡️ 安全性** - 单个联赛问题不影响其他

这是一个非常适合长期使用的数据管理方案！🎉

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试新抓取方案 - 比赛2399308
"""

import logging
from enhanced_odds_scraper import EnhancedOddsScraper

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_new_scraping_method(match_id):
    """测试新抓取方案"""
    print(f"🎯 测试新抓取方案 - 比赛 {match_id}")
    print("=" * 60)
    
    # 创建抓取器
    scraper = EnhancedOddsScraper()
    
    try:
        # 使用新方案抓取数据
        print(f"📡 正在抓取比赛 {match_id} 的数据...")
        
        complete_data = scraper.scrape_complete_match_data(
            match_id=match_id,
            max_companies=20,  # 尝试抓取更多公司
            delay=1.0
        )
        
        if not complete_data:
            print("❌ 未能获取到任何数据")
            return False
        
        # 显示基本信息
        match_info = complete_data.get('match_info', {})
        print(f"\n📋 比赛信息:")
        print(f"  主队: {match_info.get('home_team', 'N/A')}")
        print(f"  客队: {match_info.get('away_team', 'N/A')}")
        print(f"  联赛: {match_info.get('league', 'N/A')}")
        print(f"  时间: {match_info.get('match_time', 'N/A')}")
        
        # 显示赔率数据
        odds_data = complete_data.get('odds_data', [])
        print(f"\n📊 赔率数据:")
        print(f"  总记录数: {len(odds_data)}")
        
        if odds_data:
            # 按公司分组统计
            company_stats = {}
            for record in odds_data:
                company = record.get('company_name', 'Unknown')
                if company not in company_stats:
                    company_stats[company] = 0
                company_stats[company] += 1
            
            print(f"  公司数量: {len(company_stats)}")
            print(f"\n📈 各公司记录数:")
            for company, count in sorted(company_stats.items()):
                print(f"    {company}: {count} 条")
            
            # 显示最新赔率
            print(f"\n🎯 最新赔率 (各公司最后一条记录):")
            latest_odds = {}
            for record in odds_data:
                company = record.get('company_name')
                if company:
                    latest_odds[company] = record
            
            for company, record in sorted(latest_odds.items()):
                home_odds = record.get('home_odds', 'N/A')
                draw_odds = record.get('draw_odds', 'N/A')
                away_odds = record.get('away_odds', 'N/A')
                print(f"    {company}: {home_odds} {draw_odds} {away_odds}")
            
            return True
        else:
            print("❌ 没有获取到赔率数据")
            return False
            
    except Exception as e:
        print(f"❌ 抓取失败: {e}")
        logger.error(f"抓取比赛 {match_id} 失败: {e}")
        return False

def test_javascript_method_directly(match_id):
    """直接测试JavaScript方法"""
    print(f"\n🔬 直接测试JavaScript方法 - 比赛 {match_id}")
    print("=" * 60)
    
    scraper = EnhancedOddsScraper()
    
    try:
        # 直接调用新的JavaScript方法
        odds_records = scraper.parse_company_odds_from_js_direct(match_id)
        
        print(f"📊 JavaScript方法结果:")
        print(f"  解析记录数: {len(odds_records)}")
        
        if odds_records:
            print(f"\n📈 解析到的公司:")
            company_odds = {}
            for record in odds_records:
                company = record.get('company_name')
                if company:
                    company_odds[company] = record
            
            for company, record in sorted(company_odds.items()):
                home_odds = record.get('home_odds', 'N/A')
                draw_odds = record.get('draw_odds', 'N/A')
                away_odds = record.get('away_odds', 'N/A')
                print(f"    {company}: {home_odds} {draw_odds} {away_odds}")
            
            return True
        else:
            print("❌ JavaScript方法未解析到数据")
            return False
            
    except Exception as e:
        print(f"❌ JavaScript方法失败: {e}")
        logger.error(f"JavaScript方法测试失败: {e}")
        return False

def main():
    """主函数"""
    print("🚀 新抓取方案测试")
    print("=" * 80)
    
    # 测试比赛ID
    test_match_id = "2399308"
    
    # 测试1：完整抓取方法
    success1 = test_new_scraping_method(test_match_id)
    
    # 测试2：直接JavaScript方法
    success2 = test_javascript_method_directly(test_match_id)
    
    # 总结
    print(f"\n{'='*80}")
    print(f"📋 测试总结")
    print(f"{'='*80}")
    print(f"完整抓取方法: {'✅ 成功' if success1 else '❌ 失败'}")
    print(f"JavaScript方法: {'✅ 成功' if success2 else '❌ 失败'}")
    
    if success1 or success2:
        print(f"\n🎉 新抓取方案对比赛 {test_match_id} 有效！")
    else:
        print(f"\n⚠️ 新抓取方案对比赛 {test_match_id} 无效，可能需要使用传统方法")

if __name__ == "__main__":
    main()

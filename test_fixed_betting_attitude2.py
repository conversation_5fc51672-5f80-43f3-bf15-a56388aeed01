#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试修正后的博彩态度2筛选功能
"""

import tkinter as tk
from tkinter import ttk
import sys
import os

# 添加当前目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from odds_scraper_ui import OddsScraperGUI

def test_fixed_betting_attitude2():
    """测试修正后的博彩态度2筛选"""
    print("🔍 测试修正后的博彩态度2筛选功能")
    print("=" * 60)
    
    try:
        # 创建主窗口
        root = tk.Tk()
        root.withdraw()  # 隐藏主窗口
        
        # 创建UI实例
        app = OddsScraperGUI(root)
        
        # 测试不同阈值的筛选结果
        test_cases = [
            {"company": "pinnacle", "threshold": 0},
            {"company": "pinnacle", "threshold": 5},
            {"company": "pinnacle", "threshold": 10},
            {"company": "pinnacle", "threshold": 15},
        ]
        
        for case in test_cases:
            print(f"\n{'='*50}")
            print(f"🧪 测试案例: {case['company']}, 阈值 {case['threshold']}")
            
            # 设置筛选参数
            widgets = app.filter_widgets['betting_attitude2']
            widgets['enable'].set(True)
            widgets['odds_type'].set('home_odds')
            widgets['threshold'].set(str(case['threshold']))
            widgets['company'].set(case['company'])
            
            # 执行筛选
            matches = app.apply_betting_attitude2_filter(
                'home_odds', case['threshold'], case['company']
            )
            
            print(f"📊 结果: {len(matches)} 场比赛符合条件")
            
            if len(matches) > 0:
                print(f"  前5场比赛: {matches[:5]}")
        
        # 销毁窗口
        root.destroy()
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_fixed_betting_attitude2()

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
回测框架构建
构建时间序列回测框架，模拟真实投注环境，计算收益率和风险指标
"""

import pandas as pd
import numpy as np
import json
import os
import pickle
from datetime import datetime
import matplotlib.pyplot as plt
import seaborn as sns

from betting_strategy_designer import BettingStrategyDesigner
import warnings
warnings.filterwarnings('ignore')

class BacktestingFramework:
    def __init__(self, initial_bankroll=1000):
        self.initial_bankroll = initial_bankroll
        self.current_bankroll = initial_bankroll
        self.strategy_designer = BettingStrategyDesigner()
        
        # 回测结果
        self.backtest_results = {}
        self.trade_history = []
        self.bankroll_history = []
        
        # 数据
        self.prediction_model = None
        self.historical_data = None
        
    def load_prediction_model(self, model_file=None):
        """加载预测模型"""
        if model_file is None:
            # 查找最新的模型文件
            files = [f for f in os.listdir('.') if f.startswith('prediction_system_model_') and f.endswith('.pkl')]
            if files:
                model_file = sorted(files)[-1]
                print(f"自动选择模型文件: {model_file}")
            else:
                print("未找到模型文件")
                return False
        
        try:
            with open(model_file, 'rb') as f:
                self.prediction_model = pickle.load(f)
            print(f"预测模型加载成功")
            return True
        except Exception as e:
            print(f"模型加载失败: {e}")
            return False
    
    def load_historical_data(self, data_file=None):
        """加载历史数据"""
        if data_file is None:
            # 查找最新的处理数据文件
            files = [f for f in os.listdir('.') if f.startswith('processed_features_') and f.endswith('.csv')]
            if files:
                data_file = sorted(files)[-1]
                print(f"自动选择数据文件: {data_file}")
            else:
                print("未找到数据文件")
                return False
        
        self.historical_data = pd.read_csv(data_file)
        print(f"历史数据加载成功: {len(self.historical_data)} 条记录")
        return True
    
    def prepare_backtest_data(self):
        """准备回测数据"""
        print("\n=== 准备回测数据 ===")
        
        if self.historical_data is None:
            print("请先加载历史数据")
            return False
        
        # 添加时间排序（如果有时间字段）
        if 'match_id' in self.historical_data.columns:
            # 简化处理：按match_id排序
            self.historical_data = self.historical_data.sort_values('match_id')
        
        # 确保有必要的字段
        required_fields = ['match_result']
        missing_fields = [f for f in required_fields if f not in self.historical_data.columns]
        
        if missing_fields:
            print(f"缺少必要字段: {missing_fields}")
            return False
        
        print(f"回测数据准备完成: {len(self.historical_data)} 条记录")
        return True
    
    def generate_predictions(self):
        """为历史数据生成预测"""
        print("\n=== 生成预测结果 ===")
        
        if self.prediction_model is None:
            print("请先加载预测模型")
            return False
        
        predictions = []
        
        # 准备特征数据
        exclude_cols = ['match_id', 'company', 'match_result', 'home_team', 'away_team', 'target']
        feature_cols = [col for col in self.historical_data.columns if col not in exclude_cols]
        
        X = self.historical_data[feature_cols]
        
        # 标准化特征
        scaler = self.prediction_model['scaler']
        X_scaled = scaler.transform(X)
        
        # 生成预测
        model = self.prediction_model['calibrated_model']
        y_pred = model.predict(X_scaled)
        y_pred_proba = model.predict_proba(X_scaled)
        
        # 转换预测结果
        target_encoder = self.prediction_model['target_encoder']
        predicted_outcomes = target_encoder.inverse_transform(y_pred)
        
        for i in range(len(predicted_outcomes)):
            prediction = {
                'predicted_outcome': predicted_outcomes[i],
                'confidence': y_pred_proba[i].max(),
                'probabilities': {
                    class_name: prob 
                    for class_name, prob in zip(target_encoder.classes_, y_pred_proba[i])
                }
            }
            predictions.append(prediction)
        
        self.historical_data['predictions'] = predictions
        print(f"预测生成完成: {len(predictions)} 个预测")
        return True
    
    def run_backtest(self, strategy_name, start_date=None, end_date=None):
        """运行单个策略的回测"""
        print(f"\n=== 回测策略: {strategy_name} ===")
        
        strategy = self.strategy_designer.get_strategy(strategy_name)
        if strategy is None:
            print(f"策略 {strategy_name} 不存在")
            return None
        
        # 重置状态
        self.current_bankroll = self.initial_bankroll
        self.trade_history = []
        self.bankroll_history = [self.initial_bankroll]
        
        # 筛选数据
        data = self.historical_data.copy()
        if start_date or end_date:
            # 这里可以添加日期筛选逻辑
            pass
        
        total_bets = 0
        winning_bets = 0
        total_stake = 0
        total_return = 0
        
        for idx, row in data.iterrows():
            # 获取预测结果
            prediction_result = row['predictions']
            
            # 构建赔率数据
            odds_data = {
                'opening_home': row.get('opening_home', 2.0),
                'closing_home': row.get('closing_home', 2.0),
                'opening_draw': row.get('opening_draw', 3.0),
                'closing_draw': row.get('closing_draw', 3.0),
                'opening_away': row.get('opening_away', 4.0),
                'closing_away': row.get('closing_away', 4.0)
            }
            
            match_info = {
                'match_id': row.get('match_id', f'match_{idx}'),
                'actual_outcome': row['match_result']
            }
            
            # 获取投注决策
            decision = strategy.get_bet_decision(
                prediction_result, odds_data, match_info, self.current_bankroll
            )
            
            if decision['should_bet']:
                total_bets += 1
                stake = decision['stake']
                total_stake += stake
                
                # 计算收益
                predicted_outcome = decision['outcome']
                actual_outcome = row['match_result']
                
                if predicted_outcome == actual_outcome:
                    # 投注成功
                    winning_bets += 1
                    
                    # 获取对应赔率
                    if predicted_outcome == 'home_win':
                        odds = odds_data['closing_home']
                    elif predicted_outcome == 'draw':
                        odds = odds_data['closing_draw']
                    else:
                        odds = odds_data['closing_away']
                    
                    profit = stake * (odds - 1)
                    self.current_bankroll += profit
                    total_return += profit
                else:
                    # 投注失败
                    self.current_bankroll -= stake
                    total_return -= stake
                
                # 记录交易
                trade_record = {
                    'match_id': match_info['match_id'],
                    'predicted_outcome': predicted_outcome,
                    'actual_outcome': actual_outcome,
                    'stake': stake,
                    'odds': odds if predicted_outcome == actual_outcome else 0,
                    'profit': profit if predicted_outcome == actual_outcome else -stake,
                    'bankroll_after': self.current_bankroll,
                    'confidence': decision['confidence']
                }
                self.trade_history.append(trade_record)
            
            # 记录资金曲线
            self.bankroll_history.append(self.current_bankroll)
        
        # 计算回测指标
        win_rate = winning_bets / total_bets if total_bets > 0 else 0
        total_return_pct = (self.current_bankroll - self.initial_bankroll) / self.initial_bankroll * 100
        avg_bet_size = total_stake / total_bets if total_bets > 0 else 0
        
        backtest_result = {
            'strategy_name': strategy_name,
            'initial_bankroll': self.initial_bankroll,
            'final_bankroll': self.current_bankroll,
            'total_return': total_return,
            'total_return_pct': total_return_pct,
            'total_bets': total_bets,
            'winning_bets': winning_bets,
            'win_rate': win_rate,
            'total_stake': total_stake,
            'avg_bet_size': avg_bet_size,
            'trade_history': self.trade_history,
            'bankroll_history': self.bankroll_history
        }
        
        print(f"回测完成:")
        print(f"  总投注次数: {total_bets}")
        print(f"  胜率: {win_rate:.3f}")
        print(f"  总收益: {total_return:.2f}")
        print(f"  收益率: {total_return_pct:.2f}%")
        print(f"  最终资金: {self.current_bankroll:.2f}")
        
        return backtest_result
    
    def run_multiple_backtests(self, strategy_names=None):
        """运行多个策略的回测"""
        print("\n=== 多策略回测 ===")
        
        if strategy_names is None:
            strategy_names = ['confidence', 'value_betting', 'conservative', 'aggressive']
        
        results = {}
        
        for strategy_name in strategy_names:
            try:
                result = self.run_backtest(strategy_name)
                if result:
                    results[strategy_name] = result
            except Exception as e:
                print(f"策略 {strategy_name} 回测失败: {e}")
                continue
        
        self.backtest_results = results
        return results
    
    def calculate_risk_metrics(self, backtest_result):
        """计算风险指标"""
        bankroll_history = backtest_result['bankroll_history']
        
        if len(bankroll_history) < 2:
            return {}
        
        # 计算收益序列
        returns = []
        for i in range(1, len(bankroll_history)):
            if bankroll_history[i-1] > 0:
                ret = (bankroll_history[i] - bankroll_history[i-1]) / bankroll_history[i-1]
                returns.append(ret)
        
        if not returns:
            return {}
        
        returns = np.array(returns)
        
        # 最大回撤
        peak = bankroll_history[0]
        max_drawdown = 0
        for value in bankroll_history:
            if value > peak:
                peak = value
            drawdown = (peak - value) / peak
            if drawdown > max_drawdown:
                max_drawdown = drawdown
        
        # 夏普比率（简化计算，假设无风险利率为0）
        if len(returns) > 1 and np.std(returns) > 0:
            sharpe_ratio = np.mean(returns) / np.std(returns) * np.sqrt(252)  # 年化
        else:
            sharpe_ratio = 0
        
        # 波动率
        volatility = np.std(returns) * np.sqrt(252) if len(returns) > 1 else 0
        
        risk_metrics = {
            'max_drawdown': max_drawdown,
            'sharpe_ratio': sharpe_ratio,
            'volatility': volatility,
            'avg_return': np.mean(returns) if returns.size > 0 else 0,
            'return_std': np.std(returns) if len(returns) > 1 else 0
        }
        
        return risk_metrics
    
    def generate_backtest_report(self):
        """生成回测报告"""
        print("\n=== 生成回测报告 ===")
        
        if not self.backtest_results:
            print("没有回测结果")
            return None
        
        report = {
            'summary': {},
            'detailed_results': {},
            'risk_metrics': {},
            'comparison': {}
        }
        
        # 详细结果和风险指标
        for strategy_name, result in self.backtest_results.items():
            report['detailed_results'][strategy_name] = result
            report['risk_metrics'][strategy_name] = self.calculate_risk_metrics(result)
        
        # 策略比较
        comparison_data = []
        for strategy_name, result in self.backtest_results.items():
            risk_metrics = report['risk_metrics'][strategy_name]
            comparison_data.append({
                'strategy': strategy_name,
                'return_pct': result['total_return_pct'],
                'win_rate': result['win_rate'],
                'total_bets': result['total_bets'],
                'max_drawdown': risk_metrics.get('max_drawdown', 0),
                'sharpe_ratio': risk_metrics.get('sharpe_ratio', 0),
                'final_bankroll': result['final_bankroll']
            })
        
        comparison_df = pd.DataFrame(comparison_data)
        comparison_df = comparison_df.sort_values('return_pct', ascending=False)
        
        report['comparison'] = comparison_df.to_dict('records')
        
        # 总结
        best_strategy = comparison_df.iloc[0] if len(comparison_df) > 0 else None
        report['summary'] = {
            'total_strategies_tested': len(self.backtest_results),
            'best_strategy': best_strategy['strategy'] if best_strategy is not None else None,
            'best_return': best_strategy['return_pct'] if best_strategy is not None else 0,
            'avg_return': comparison_df['return_pct'].mean() if len(comparison_df) > 0 else 0
        }
        
        print(f"回测报告生成完成")
        print(f"最佳策略: {report['summary']['best_strategy']}")
        print(f"最佳收益率: {report['summary']['best_return']:.2f}%")
        
        return report
    
    def save_backtest_results(self, filename_prefix="backtest_results"):
        """保存回测结果"""
        if not self.backtest_results:
            print("没有回测结果可保存")
            return None
        
        # 生成报告
        report = self.generate_backtest_report()
        
        # 保存结果
        output_file = f"{filename_prefix}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        
        # 转换为可序列化格式
        serializable_report = self._make_serializable(report)
        
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(serializable_report, f, ensure_ascii=False, indent=2)
        
        print(f"\n回测结果已保存到: {output_file}")
        return output_file
    
    def _make_serializable(self, obj):
        """将对象转换为可序列化的格式"""
        if isinstance(obj, dict):
            return {k: self._make_serializable(v) for k, v in obj.items()}
        elif isinstance(obj, (list, tuple)):
            return [self._make_serializable(item) for item in obj]
        elif isinstance(obj, pd.DataFrame):
            return obj.to_dict('records')
        elif isinstance(obj, (np.integer, np.int64)):
            return int(obj)
        elif isinstance(obj, (np.floating, np.float64)):
            return float(obj)
        elif isinstance(obj, (np.bool_, bool)):
            return bool(obj)
        elif isinstance(obj, np.ndarray):
            return obj.tolist()
        elif pd.isna(obj) or obj is None:
            return None
        else:
            return str(obj)

def main():
    # 创建回测框架
    backtester = BacktestingFramework(initial_bankroll=1000)
    
    # 加载模型和数据
    if not backtester.load_prediction_model():
        return
    
    if not backtester.load_historical_data():
        return
    
    # 准备回测数据
    if not backtester.prepare_backtest_data():
        return
    
    # 生成预测
    if not backtester.generate_predictions():
        return
    
    # 运行多策略回测
    results = backtester.run_multiple_backtests()
    
    # 保存结果
    backtester.save_backtest_results()

if __name__ == "__main__":
    main()

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
天启代理简化测试
尝试不同的签名方法和参数组合
"""

import requests
import hashlib
import time

def test_tianqi_simple():
    """简化测试天启代理"""
    print("🎯 天启代理简化测试")
    print("=" * 60)
    
    # 读取API密钥
    try:
        with open('ip_keys.txt', 'r') as f:
            secret_key = f.read().strip()
        print(f"📋 API密钥: {secret_key}")
    except Exception as e:
        print(f"❌ 读取密钥失败: {e}")
        return
    
    # 测试不同的API调用方式
    test_cases = [
        {
            "name": "最简单调用（无签名）",
            "params": {
                "secret": secret_key,
                "num": 1,
                "time": 3,
                "port": 1,
                "type": "json"
            }
        },
        {
            "name": "添加sign参数（空值）",
            "params": {
                "secret": secret_key,
                "num": 1,
                "time": 3,
                "port": 1,
                "type": "json",
                "sign": ""
            }
        },
        {
            "name": "使用secret作为sign",
            "params": {
                "secret": secret_key,
                "num": 1,
                "time": 3,
                "port": 1,
                "type": "json",
                "sign": secret_key
            }
        },
        {
            "name": "MD5(secret)",
            "params": {
                "secret": secret_key,
                "num": 1,
                "time": 3,
                "port": 1,
                "type": "json",
                "sign": hashlib.md5(secret_key.encode()).hexdigest()
            }
        },
        {
            "name": "时间戳签名",
            "params": {
                "secret": secret_key,
                "num": 1,
                "time": 3,
                "port": 1,
                "type": "json",
                "sign": hashlib.md5(f"{secret_key}{int(time.time())}".encode()).hexdigest()
            }
        }
    ]
    
    for i, case in enumerate(test_cases):
        print(f"\n{'='*50}")
        print(f"🧪 测试 {i+1}: {case['name']}")
        print(f"{'='*50}")
        
        try:
            url = "http://api.tianqiip.com/getip"
            params = case['params']
            
            print(f"🔍 请求URL: {url}")
            print(f"📋 请求参数: {params}")
            
            response = requests.get(url, params=params, timeout=10)
            print(f"📊 响应状态码: {response.status_code}")
            print(f"🔗 完整URL: {response.url}")
            
            if response.status_code == 200:
                try:
                    data = response.json()
                    print(f"✅ JSON响应: {data}")
                    
                    if data.get("code") == 1000:
                        print("🎉 成功获取代理IP！")
                        return data
                    else:
                        print(f"❌ API错误: {data.get('msg', 'Unknown error')}")
                        
                except:
                    print(f"✅ 文本响应: {response.text}")
                    
            else:
                print(f"❌ HTTP错误: {response.status_code}")
                try:
                    error_data = response.json()
                    print(f"错误信息: {error_data}")
                except:
                    print(f"错误内容: {response.text}")
                    
        except Exception as e:
            print(f"❌ 请求异常: {e}")
    
    # 如果所有测试都失败，尝试官网示例格式
    print(f"\n{'='*50}")
    print("🧪 最后尝试：官网示例格式")
    print(f"{'='*50}")
    
    try:
        # 直接使用官网的URL格式
        example_url = f"http://api.tianqiip.com/getip?secret={secret_key}&num=1&time=3&port=1&type=json&sign={secret_key}"
        print(f"🔍 示例URL: {example_url}")
        
        response = requests.get(example_url, timeout=10)
        print(f"📊 响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            try:
                data = response.json()
                print(f"✅ 响应: {data}")
            except:
                print(f"✅ 响应: {response.text}")
        else:
            print(f"❌ 失败: {response.text}")
            
    except Exception as e:
        print(f"❌ 异常: {e}")

def test_account_status():
    """测试账户状态"""
    print(f"\n{'='*50}")
    print("🔍 检查账户状态")
    print(f"{'='*50}")
    
    try:
        with open('ip_keys.txt', 'r') as f:
            secret_key = f.read().strip()
    except Exception as e:
        print(f"❌ 读取密钥失败: {e}")
        return
    
    # 尝试访问用户相关的端点
    test_urls = [
        f"http://api.tianqiip.com/user/info?secret={secret_key}",
        f"http://api.tianqiip.com/account?key={secret_key}",
        f"http://api.tianqiip.com/balance?secret={secret_key}",
        f"https://www.tianqiip.com/api/user?key={secret_key}"
    ]
    
    for url in test_urls:
        try:
            print(f"\n🧪 测试: {url}")
            response = requests.get(url, timeout=10)
            print(f"状态码: {response.status_code}")
            
            if response.status_code == 200:
                try:
                    data = response.json()
                    print(f"响应: {data}")
                except:
                    print(f"响应: {response.text[:200]}...")
            else:
                print(f"错误: {response.text[:100]}...")
                
        except Exception as e:
            print(f"异常: {e}")

def generate_recommendations():
    """生成建议"""
    print(f"\n💡 使用建议")
    print("=" * 60)
    
    print("基于测试结果，建议您：")
    print()
    print("1. 🌐 登录官网检查账户状态:")
    print("   - 访问: https://www.tianqiip.com/login")
    print("   - 检查账户余额是否充足")
    print("   - 确认API密钥是否正确")
    print()
    print("2. 📞 联系客服获取帮助:")
    print("   - QQ: 3006006530")
    print("   - 微信: 17006536461")
    print("   - 询问正确的API调用方法和签名算法")
    print()
    print("3. 📋 检查服务状态:")
    print("   - 确认是否需要实名认证")
    print("   - 检查是否需要添加白名单IP")
    print("   - 确认套餐是否已激活")
    print()
    print("4. 🔧 可能的解决方案:")
    print("   - API密钥可能需要在官网后台激活")
    print("   - 可能需要先充值购买套餐")
    print("   - 签名算法可能需要特定的实现方式")

def main():
    """主函数"""
    try:
        test_tianqi_simple()
        test_account_status()
        generate_recommendations()
        
        print("\n🎯 测试完成！")
        print("如果仍然无法使用，建议直接联系天启代理客服获取技术支持。")
        
    except KeyboardInterrupt:
        print("\n👋 用户取消测试")
    except Exception as e:
        print(f"❌ 测试失败: {e}")

if __name__ == "__main__":
    main()

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
比赛信息提取器
从win007网站提取比赛的详细信息，包括开始时间、队伍名称、比分、联赛信息等
"""

import requests
import re
import json
from bs4 import BeautifulSoup
from datetime import datetime
from typing import Dict, Optional
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class MatchInfoExtractor:
    def __init__(self):
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate',
            'Connection': 'keep-alive',
            'Referer': 'https://m.titan007.com/',
        })
        self.base_url = "https://m.titan007.com"
    
    def get_page_content(self, url: str, max_retries: int = 3) -> Optional[str]:
        """获取网页内容"""
        for attempt in range(max_retries):
            try:
                response = self.session.get(url, timeout=15)
                response.raise_for_status()
                response.encoding = 'utf-8'
                return response.text
            except requests.RequestException as e:
                logger.warning(f"获取页面失败 (尝试 {attempt + 1}/{max_retries}): {e}")
                if attempt < max_retries - 1:
                    time.sleep(3)
                else:
                    logger.error(f"无法获取页面: {url}")
                    return None
    
    def extract_match_info(self, match_id: str) -> Dict:
        """提取比赛信息"""
        # 尝试多个页面获取信息
        urls = [
            f"{self.base_url}/compensate/{match_id}.htm",
            f"{self.base_url}/CompensateDetail/281/{match_id}.htm",
            f"{self.base_url}/asian/{match_id}.htm"
        ]
        
        for url in urls:
            content = self.get_page_content(url)
            if content:
                match_info = self._parse_match_info(content, match_id)
                if match_info and match_info.get('match_time'):
                    logger.info(f"从 {url} 成功提取比赛信息")
                    return match_info
        
        logger.error(f"无法从任何页面提取比赛 {match_id} 的信息")
        return {}
    
    def _parse_match_info(self, content: str, match_id: str) -> Dict:
        """解析比赛信息"""
        match_info = {
            'match_id': match_id,
            'extraction_time': datetime.now().isoformat()
        }
        
        try:
            soup = BeautifulSoup(content, 'html.parser')
            
            # 方法1: 查找 class="league" 的div
            league_div = soup.find('div', class_='league')
            if league_div:
                league_text = league_div.get_text(strip=True)
                logger.info(f"找到联赛信息: {league_text}")
                
                # 解析联赛信息
                parsed_info = self._parse_league_text(league_text, match_id)
                match_info.update(parsed_info)
            
            # 方法2: 查找包含时间信息的文本
            if not match_info.get('match_time'):
                time_patterns = [
                    r'(\d{2}-\d{2})\s+(\d{2}:\d{2})',  # MM-DD HH:MM
                    r'(\d{4}/\d{1,2}/\d{1,2})\s+(\d{1,2}:\d{2}:\d{2})',  # YYYY/M/D H:MM:SS
                ]
                
                for pattern in time_patterns:
                    matches = re.findall(pattern, content)
                    if matches:
                        match_info['raw_time_matches'] = matches
                        break
            
            # 方法3: 查找JavaScript中的时间信息
            script_tags = soup.find_all('script')
            for script in script_tags:
                if script.string:
                    # 查找 new Date("2025/5/25 8:31:18") 格式
                    date_match = re.search(r'new Date\("([^"]+)"\)', script.string)
                    if date_match:
                        match_info['script_date'] = date_match.group(1)
                        try:
                            # 尝试解析JavaScript中的日期
                            js_date = datetime.strptime(date_match.group(1), "%Y/%m/%d %H:%M:%S")
                            if not match_info.get('match_time'):
                                match_info['match_time'] = js_date.strftime("%Y-%m-%d %H:%M:%S")
                                match_info['match_date'] = js_date.strftime("%Y-%m-%d")
                                match_info['match_time_only'] = js_date.strftime("%H:%M:%S")
                        except ValueError:
                            pass
            
            # 提取队伍信息
            self._extract_team_info(soup, match_info)
            
            # 提取比分信息
            self._extract_score_info(soup, match_info)
            
            return match_info
            
        except Exception as e:
            logger.error(f"解析比赛信息失败: {e}")
            return match_info
    
    def _parse_league_text(self, text: str, match_id: str = None) -> Dict:
        """解析联赛文本信息"""
        info = {}
        
        # 移除HTML实体
        text = text.replace('&nbsp;', ' ').replace('\xa0', ' ')
        
        # 提取联赛名称和轮次
        # 格式: "美职业 第5轮 05-25 04:50 天晴 16℃～17℃"
        parts = text.split()
        
        if len(parts) >= 3:
            info['league'] = parts[0]  # 联赛名称
            
            # 查找轮次信息
            for part in parts:
                if '轮' in part or '第' in part:
                    info['round'] = part
                    break
            
            # 首先尝试从分析页面获取准确时间
            accurate_time = self._extract_accurate_match_time(match_id)

            if accurate_time and accurate_time.get('success'):
                # 使用准确的时间信息
                info.update({
                    'match_time': accurate_time['match_time'],
                    'match_date': accurate_time['match_date'],
                    'match_time_only': accurate_time['match_time_only'],
                    'year': accurate_time['year'],
                    'month': accurate_time['month'],
                    'day': accurate_time['day'],
                    'hour': accurate_time['hour'],
                    'minute': accurate_time['minute'],
                    'time_source': 'analysis_page',
                    'raw_time_str': accurate_time.get('raw_time_str', '')
                })
                logger.info(f"从分析页面获取到准确时间: {accurate_time['match_time']}")
            else:
                # 回退到原有的解析方法
                date_time_pattern = r'(\d{2}-\d{2})\s+(\d{2}:\d{2})'
                match = re.search(date_time_pattern, text)
                if match:
                    date_str = match.group(1)
                    time_str = match.group(2)

                    # 智能推测年份：多种方法结合
                    year = self._infer_match_year(date_str, match_id)

                    try:
                        # 解析日期时间
                        full_date_str = f"{year}-{date_str} {time_str}:00"
                        match_datetime = datetime.strptime(full_date_str, "%Y-%m-%d %H:%M:%S")

                        info['match_time'] = match_datetime.strftime("%Y-%m-%d %H:%M:%S")
                        info['match_date'] = match_datetime.strftime("%Y-%m-%d")
                        info['match_time_only'] = match_datetime.strftime("%H:%M:%S")
                        info['raw_date'] = date_str
                        info['raw_time'] = time_str
                        info['inferred_year'] = year
                        info['time_source'] = 'inferred'

                    except ValueError as e:
                        logger.warning(f"日期解析失败: {e}")
                        info['raw_date'] = date_str
                        info['raw_time'] = time_str
                        info['time_source'] = 'failed'

                if accurate_time:
                    logger.warning(f"分析页面时间提取失败: {accurate_time.get('error', '未知错误')}")
            
            # 提取天气信息
            weather_pattern = r'(天晴|多云|阴|雨|雪|雾)\s*(\d+℃[～~]\d+℃)?'
            weather_match = re.search(weather_pattern, text)
            if weather_match:
                info['weather'] = weather_match.group(1)
                if weather_match.group(2):
                    info['temperature'] = weather_match.group(2)
        
        info['raw_league_text'] = text
        return info
    
    def _extract_team_info(self, soup: BeautifulSoup, match_info: Dict):
        """提取队伍信息"""
        try:
            # 查找主队信息
            home_div = soup.find('div', class_='home')
            if home_div:
                home_name_div = home_div.find('div', class_='name')
                if home_name_div:
                    home_name = home_name_div.get_text(strip=True)
                    # 移除可能的排名信息 [3]
                    home_name = re.sub(r'\[\d+\]', '', home_name).strip()
                    match_info['home_team'] = home_name
            
            # 查找客队信息
            guest_div = soup.find('div', class_='guest')
            if guest_div:
                guest_name_div = guest_div.find('div', class_='name')
                if guest_name_div:
                    guest_name = guest_name_div.get_text(strip=True)
                    # 移除可能的排名信息 [15]
                    guest_name = re.sub(r'\[\d+\]', '', guest_name).strip()
                    match_info['away_team'] = guest_name
                    
        except Exception as e:
            logger.warning(f"提取队伍信息失败: {e}")
    
    def _extract_score_info(self, soup: BeautifulSoup, match_info: Dict):
        """提取比分信息"""
        try:
            # 查找比分信息
            home_score_div = soup.find('div', id='homeScore')
            guest_score_div = soup.find('div', id='guestScore')
            
            if home_score_div and guest_score_div:
                match_info['home_score'] = home_score_div.get_text(strip=True)
                match_info['away_score'] = guest_score_div.get_text(strip=True)
            
            # 查找比赛状态
            state_span = soup.find('span', id='mState')
            if state_span:
                match_info['match_state'] = state_span.get_text(strip=True)
            
            # 查找半场比分
            half_score_span = soup.find('span', class_='halfScore')
            if half_score_span:
                half_score = half_score_span.get_text(strip=True)
                match_info['half_score'] = half_score.strip('()')
                
        except Exception as e:
            logger.warning(f"提取比分信息失败: {e}")
    
    def save_match_info(self, match_info: Dict, filename: str = None) -> str:
        """保存比赛信息到JSON文件"""
        if filename is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            match_id = match_info.get('match_id', 'unknown')
            filename = f"match_info_{match_id}_{timestamp}.json"
        
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(match_info, f, ensure_ascii=False, indent=2)
        
        logger.info(f"比赛信息已保存到: {filename}")
        return filename
    
    def print_match_info(self, match_info: Dict):
        """打印比赛信息"""
        if not match_info:
            print("没有比赛信息可显示")
            return
        
        print(f"\n=== 比赛信息 (ID: {match_info.get('match_id', 'N/A')}) ===")
        
        # 基本信息
        if match_info.get('league'):
            print(f"联赛: {match_info['league']}")
        if match_info.get('round'):
            print(f"轮次: {match_info['round']}")
        
        # 队伍信息
        if match_info.get('home_team') and match_info.get('away_team'):
            print(f"对阵: {match_info['home_team']} vs {match_info['away_team']}")
        
        # 时间信息
        if match_info.get('match_time'):
            print(f"比赛时间: {match_info['match_time']}")
        elif match_info.get('raw_date') and match_info.get('raw_time'):
            print(f"比赛时间: {match_info['raw_date']} {match_info['raw_time']}")
        
        # 比分信息
        if match_info.get('home_score') and match_info.get('away_score'):
            score_text = f"比分: {match_info['home_score']}-{match_info['away_score']}"
            if match_info.get('half_score'):
                score_text += f" (半场: {match_info['half_score']})"
            print(score_text)
        
        if match_info.get('match_state'):
            print(f"状态: {match_info['match_state']}")
        
        # 天气信息
        if match_info.get('weather'):
            weather_text = f"天气: {match_info['weather']}"
            if match_info.get('temperature'):
                weather_text += f" {match_info['temperature']}"
            print(weather_text)
        
        # 原始信息
        if match_info.get('raw_league_text'):
            print(f"原始信息: {match_info['raw_league_text']}")

    def _infer_match_year(self, date_str: str, match_id: str = None) -> int:
        """智能推测比赛年份"""
        current_year = datetime.now().year
        current_month = datetime.now().month

        try:
            match_month = int(date_str.split('-')[0])
            match_day = int(date_str.split('-')[1])
        except (ValueError, IndexError):
            return current_year

        # 方法1：基于比赛ID推测（基于实际数据分布更新 - 2025年7月）
        if match_id:
            try:
                match_id_int = int(match_id)
                # 基于实际数据库分析的比赛ID范围映射
                if match_id_int < 2500000:  # 2022年及之前
                    return 2022
                elif match_id_int < 2600000:  # 2023年
                    return 2023
                elif match_id_int < 2750000:  # 2024年上半年
                    return 2024
                elif match_id_int < 2850000:  # 2024年下半年
                    # 对于2024年下半年的比赛，需要根据月份判断
                    if match_month >= 7:  # 7月及以后，是2024年
                        return 2024
                    else:  # 1-6月，可能是2025年
                        return 2025
                elif match_id_int < 3000000:  # 2025年上半年
                    return 2025
                else:  # 2025年下半年及以后
                    # 如果比赛月份在下半年，可能是当年或下一年
                    if match_month >= 7:  # 下半年比赛
                        return current_year
                    else:  # 上半年比赛，可能是下一年
                        return current_year + 1
            except ValueError:
                pass

        # 方法2：基于月份的智能判断
        # 如果是8月份的比赛，而现在是1月，很可能是去年的比赛
        if current_month <= 3 and match_month >= 8:  # 现在是年初，比赛是下半年
            return current_year - 1
        elif current_month >= 10 and match_month <= 5:  # 现在是年末，比赛是上半年
            return current_year + 1

        # 方法3：默认使用当前年份
        return current_year

    def _extract_accurate_match_time(self, match_id: str) -> Dict:
        """从分析页面提取准确的比赛时间"""
        if not match_id:
            return {'success': False, 'error': '比赛ID为空'}

        try:
            # 构建分析页面URL
            analysis_url = f"https://m.titan007.com/Analy/ShiJian/{match_id}.htm"

            # 获取页面内容
            response = self.session.get(analysis_url, timeout=15)
            response.raise_for_status()
            response.encoding = 'utf-8'

            content = response.text

            # 查找时间变量
            time_patterns = [
                r'var\s+headMatchTime\s*=\s*["\']([^"\']+)["\']',
                r'headMatchTime\s*=\s*["\']([^"\']+)["\']',
                r'matchTime\s*=\s*["\']([^"\']+)["\']',
                r'var\s+matchTime\s*=\s*["\']([^"\']+)["\']',
            ]

            for pattern in time_patterns:
                match = re.search(pattern, content, re.IGNORECASE)
                if match:
                    time_str = match.group(1)

                    # 解析时间
                    try:
                        if 'T' in time_str:
                            # ISO格式: 2025-07-27T19:00:00
                            dt = datetime.fromisoformat(time_str.replace('T', ' ').replace('Z', ''))
                        else:
                            # 其他格式
                            dt = datetime.strptime(time_str, "%Y-%m-%d %H:%M:%S")

                        return {
                            'success': True,
                            'match_time': dt.strftime('%Y-%m-%d %H:%M:%S'),
                            'match_date': dt.strftime('%Y-%m-%d'),
                            'match_time_only': dt.strftime('%H:%M:%S'),
                            'year': dt.year,
                            'month': dt.month,
                            'day': dt.day,
                            'hour': dt.hour,
                            'minute': dt.minute,
                            'source': 'analysis_page',
                            'raw_time_str': time_str
                        }
                    except Exception as e:
                        logger.warning(f"时间解析失败: {time_str}, 错误: {e}")
                        continue

            return {'success': False, 'error': '未找到时间信息'}

        except Exception as e:
            logger.warning(f"从分析页面提取时间失败: {e}")
            return {'success': False, 'error': str(e)}


def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='比赛信息提取器')
    parser.add_argument('match_id', help='比赛ID')
    parser.add_argument('--output', help='输出JSON文件名')
    parser.add_argument('--quiet', action='store_true', help='静默模式，不打印详细信息')
    
    args = parser.parse_args()
    
    extractor = MatchInfoExtractor()
    
    try:
        # 提取比赛信息
        match_info = extractor.extract_match_info(args.match_id)
        
        if match_info:
            # 保存信息
            json_file = extractor.save_match_info(match_info, args.output)
            
            # 打印信息
            if not args.quiet:
                extractor.print_match_info(match_info)
                print(f"\n信息已保存到: {json_file}")
            
        else:
            print(f"无法提取比赛 {args.match_id} 的信息")
            
    except Exception as e:
        logger.error(f"程序执行出错: {e}")


if __name__ == "__main__":
    # 如果没有命令行参数，使用默认示例
    import sys
    if len(sys.argv) == 1:
        print("使用示例数据进行测试...")
        extractor = MatchInfoExtractor()
        
        # 测试提取比赛信息
        match_info = extractor.extract_match_info("2741454")
        
        if match_info:
            json_file = extractor.save_match_info(match_info)
            extractor.print_match_info(match_info)
            print(f"\n信息已保存到: {json_file}")
        else:
            print("未能提取到比赛信息")
    else:
        main()

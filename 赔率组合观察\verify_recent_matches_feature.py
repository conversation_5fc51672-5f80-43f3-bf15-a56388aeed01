#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证最近比赛功能
"""

import sys
import os
import sqlite3
from datetime import datetime, timedelta

def verify_recent_matches_feature():
    """验证最近比赛功能"""
    print("🔍 验证最近比赛功能")
    
    try:
        # 数据库路径
        current_dir = os.path.dirname(os.path.abspath(__file__))
        parent_dir = os.path.dirname(current_dir)
        db_path = os.path.join(parent_dir, "odds_data.db")
        
        print(f"数据库路径: {db_path}")
        
        if not os.path.exists(db_path):
            print("❌ 数据库文件不存在")
            return
        
        with sqlite3.connect(db_path) as conn:
            cursor = conn.cursor()
            
            # 检查数据库中的比赛总数
            cursor.execute("SELECT COUNT(*) FROM matches")
            total_matches = cursor.fetchone()[0]
            print(f"📊 数据库中总比赛数: {total_matches}")
            
            if total_matches == 0:
                print("❌ 数据库中没有比赛数据")
                return
            
            # 测试不同的时间范围查询
            test_ranges = [
                (30, "最近30天"),
                (90, "最近3个月"), 
                (365, "最近1年"),
                (1095, "最近3年")
            ]
            
            for days, description in test_ranges:
                print(f"\n📅 测试{description}的比赛:")
                
                # 计算日期范围
                today = datetime.now().date()
                start_date = today - timedelta(days=days)
                end_date = today
                
                start_date_str = start_date.strftime('%Y-%m-%d')
                end_date_str = end_date.strftime('%Y-%m-%d')
                
                # 执行查询（模拟get_recent_matches方法的逻辑）
                cursor.execute('''
                    SELECT match_id, league, home_team, away_team, 
                           match_time, match_date, accurate_date, accurate_datetime
                    FROM matches 
                    WHERE (
                        (accurate_date IS NOT NULL AND accurate_date BETWEEN ? AND ?) OR
                        (accurate_date IS NULL AND match_date IS NOT NULL AND match_date BETWEEN ? AND ?)
                    )
                    ORDER BY 
                        COALESCE(accurate_datetime, accurate_date || ' ' || COALESCE(match_time, '00:00:00'), 
                                match_date || ' ' || COALESCE(match_time, '00:00:00')) ASC
                    LIMIT 5
                ''', (start_date_str, end_date_str, start_date_str, end_date_str))
                
                matches = cursor.fetchall()
                print(f"  找到 {len(matches)} 场比赛（显示前5场）")
                
                for i, match in enumerate(matches):
                    match_id, league, home_team, away_team, match_time, match_date, accurate_date, accurate_datetime = match
                    
                    # 格式化显示时间
                    if accurate_datetime:
                        display_time = accurate_datetime
                    elif accurate_date:
                        time_part = match_time or '00:00:00'
                        display_time = f"{accurate_date} {time_part}"
                    elif match_date:
                        time_part = match_time or '00:00:00'
                        display_time = f"{match_date} {time_part}"
                    else:
                        display_time = "未知时间"
                    
                    print(f"    {i+1}. {match_id} | {display_time} | {league or '未知联赛'} | {home_team or '未知'} vs {away_team or '未知'}")
                
                if len(matches) == 0:
                    print(f"  ⚠️ 在{description}内没有找到比赛")
                else:
                    break  # 找到数据就停止测试更大范围
            
            # 测试最新的几场比赛（不限时间范围）
            print(f"\n🔍 数据库中最新的5场比赛:")
            cursor.execute('''
                SELECT match_id, league, home_team, away_team, 
                       match_time, match_date, accurate_date, accurate_datetime
                FROM matches 
                WHERE match_date IS NOT NULL OR accurate_date IS NOT NULL
                ORDER BY 
                    COALESCE(accurate_datetime, accurate_date || ' ' || COALESCE(match_time, '00:00:00'), 
                            match_date || ' ' || COALESCE(match_time, '00:00:00')) DESC
                LIMIT 5
            ''')
            
            latest_matches = cursor.fetchall()
            for i, match in enumerate(latest_matches):
                match_id, league, home_team, away_team, match_time, match_date, accurate_date, accurate_datetime = match
                
                # 格式化显示时间
                if accurate_datetime:
                    display_time = accurate_datetime
                elif accurate_date:
                    time_part = match_time or '00:00:00'
                    display_time = f"{accurate_date} {time_part}"
                elif match_date:
                    time_part = match_time or '00:00:00'
                    display_time = f"{match_date} {time_part}"
                else:
                    display_time = "未知时间"
                
                print(f"  {i+1}. {match_id} | {display_time} | {league or '未知联赛'} | {home_team or '未知'} vs {away_team or '未知'}")
        
        print(f"\n✅ 验证完成")
        print(f"💡 新功能已实现，可以在UI中使用'显示最近比赛'功能")
        
    except Exception as e:
        print(f"❌ 验证失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    verify_recent_matches_feature()

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
更新所有比赛的准确时间信息
按照1秒间隔从分析页面抓取所有比赛的准确时间

功能：
1. 获取数据库中所有比赛
2. 按1秒间隔逐个抓取准确时间
3. 实时显示进度和结果
4. 支持断点续传

使用方法：
    python update_all_match_times.py
"""

import sqlite3
import logging
import time
import os
from typing import Dict, List
from datetime import datetime
from match_time_scraper import MatchTimeScraper

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class AllMatchTimeUpdater:
    def __init__(self, db_path: str = "odds_data.db", delay_seconds: float = 1.0):
        self.db_path = db_path
        self.delay_seconds = delay_seconds
        self.scraper = MatchTimeScraper()
        
    def backup_database(self) -> str:
        """备份数据库"""
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_path = f"odds_data_backup_all_times_{timestamp}.db"
            
            with sqlite3.connect(self.db_path) as source:
                with sqlite3.connect(backup_path) as backup:
                    source.backup(backup)
            
            logger.info(f"数据库备份完成: {backup_path}")
            return backup_path
            
        except Exception as e:
            logger.error(f"数据库备份失败: {e}")
            raise
    
    def get_all_matches(self) -> List[Dict]:
        """获取所有比赛"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                conn.row_factory = sqlite3.Row
                cursor = conn.cursor()
                
                cursor.execute("""
                    SELECT match_id, league, home_team, away_team, match_time, 
                           accurate_datetime, time_source
                    FROM matches 
                    ORDER BY match_id DESC
                """)
                
                matches = [dict(row) for row in cursor.fetchall()]
                logger.info(f"找到 {len(matches)} 场比赛")
                return matches
                
        except sqlite3.Error as e:
            logger.error(f"获取比赛列表失败: {e}")
            return []
    
    def get_matches_without_accurate_time(self) -> List[Dict]:
        """获取没有准确时间的比赛"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                conn.row_factory = sqlite3.Row
                cursor = conn.cursor()
                
                cursor.execute("""
                    SELECT match_id, league, home_team, away_team, match_time
                    FROM matches 
                    WHERE accurate_datetime IS NULL OR accurate_datetime = '' OR time_source != 'analysis'
                    ORDER BY match_id DESC
                """)
                
                matches = [dict(row) for row in cursor.fetchall()]
                logger.info(f"找到 {len(matches)} 场比赛需要更新时间信息")
                return matches
                
        except sqlite3.Error as e:
            logger.error(f"获取比赛列表失败: {e}")
            return []
    
    def update_match_time(self, match_id: str, time_info: Dict) -> bool:
        """更新比赛的时间信息"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                if time_info.get('error'):
                    # 如果抓取失败，标记为估算时间
                    cursor.execute("""
                        UPDATE matches 
                        SET time_source = 'estimated_failed'
                        WHERE match_id = ?
                    """, (match_id,))
                    logger.warning(f"比赛 {match_id} 时间抓取失败: {time_info.get('error')}")
                    return False
                
                # 更新准确的时间信息
                cursor.execute("""
                    UPDATE matches 
                    SET accurate_datetime = ?,
                        accurate_date = ?,
                        accurate_time = ?,
                        weekday = ?,
                        match_year = ?,
                        match_month = ?,
                        match_day = ?,
                        match_hour = ?,
                        match_minute = ?,
                        time_source = 'analysis'
                    WHERE match_id = ?
                """, (
                    time_info.get('full_datetime'),
                    time_info.get('match_date'),
                    time_info.get('match_time'),
                    time_info.get('weekday'),
                    time_info.get('year'),
                    time_info.get('month'),
                    time_info.get('day'),
                    time_info.get('hour'),
                    time_info.get('minute'),
                    match_id
                ))
                
                conn.commit()
                logger.info(f"成功更新比赛 {match_id} 的时间信息: {time_info.get('full_datetime')}")
                return True
                
        except sqlite3.Error as e:
            logger.error(f"更新比赛 {match_id} 时间信息失败: {e}")
            return False
    
    def update_all_match_times(self, update_all: bool = False) -> Dict:
        """更新所有比赛的时间信息"""
        try:
            print(f"🚀 开始更新比赛时间信息...")
            print(f"⏱️  抓取间隔: {self.delay_seconds} 秒")
            
            # 获取需要更新的比赛
            if update_all:
                matches = self.get_all_matches()
                print(f"📊 模式: 更新所有比赛")
            else:
                matches = self.get_matches_without_accurate_time()
                print(f"📊 模式: 仅更新缺失准确时间的比赛")
            
            if not matches:
                print("✅ 所有比赛都已有准确时间信息")
                return {
                    'total_processed': 0,
                    'successful_updates': 0,
                    'failed_updates': 0,
                    'skipped': 0,
                    'success': True
                }
            
            print(f"📋 将处理 {len(matches)} 场比赛")
            print("=" * 80)
            
            successful_updates = 0
            failed_updates = 0
            skipped = 0
            start_time = time.time()
            
            for i, match in enumerate(matches, 1):
                match_id = match['match_id']
                league = match.get('league', '')[:15]
                home_team = match.get('home_team', '')
                away_team = match.get('away_team', '')
                teams = f"{home_team} vs {away_team}"
                if len(teams) > 35:
                    teams = teams[:32] + "..."
                
                # 显示进度
                progress = (i / len(matches)) * 100
                elapsed_time = time.time() - start_time
                estimated_total = elapsed_time / i * len(matches) if i > 0 else 0
                remaining_time = estimated_total - elapsed_time
                
                print(f"[{i:4d}/{len(matches)}] ({progress:5.1f}%) 比赛 {match_id} - {league}")
                print(f"  对阵: {teams}")
                print(f"  预计剩余时间: {remaining_time/60:.1f} 分钟")
                
                try:
                    # 抓取时间信息
                    time_info = self.scraper.scrape_match_time(match_id)
                    
                    # 更新数据库
                    if self.update_match_time(match_id, time_info):
                        successful_updates += 1
                        if time_info.get('full_datetime'):
                            print(f"  ✅ 成功: {time_info.get('full_datetime')} {time_info.get('weekday', '')}")
                        else:
                            print(f"  ✅ 成功更新")
                    else:
                        failed_updates += 1
                        print(f"  ❌ 失败: {time_info.get('error', '未知错误')}")
                    
                    # 添加延迟
                    if i < len(matches):
                        print(f"  ⏳ 等待 {self.delay_seconds} 秒...")
                        time.sleep(self.delay_seconds)
                        
                except KeyboardInterrupt:
                    print(f"\n⚠️  用户中断，已处理 {i} 场比赛")
                    break
                except Exception as e:
                    failed_updates += 1
                    print(f"  ❌ 异常: {e}")
                    logger.error(f"处理比赛 {match_id} 失败: {e}")
                    
                    # 即使出错也要等待，避免请求过快
                    if i < len(matches):
                        time.sleep(self.delay_seconds)
                    continue
                
                print("-" * 80)
            
            total_time = time.time() - start_time
            
            result = {
                'total_processed': i,
                'successful_updates': successful_updates,
                'failed_updates': failed_updates,
                'skipped': skipped,
                'total_time': total_time,
                'success': True
            }
            
            print(f"\n🎉 更新完成!")
            print(f"📊 处理统计:")
            print(f"   - 总处理数量: {result['total_processed']}")
            print(f"   - 成功更新: {result['successful_updates']}")
            print(f"   - 失败数量: {result['failed_updates']}")
            print(f"   - 总耗时: {total_time/60:.1f} 分钟")
            print(f"   - 平均速度: {total_time/i:.1f} 秒/场" if i > 0 else "")
            
            return result
            
        except Exception as e:
            logger.error(f"批量更新失败: {e}")
            return {
                'total_processed': 0,
                'successful_updates': 0,
                'failed_updates': 0,
                'skipped': 0,
                'success': False,
                'error': str(e)
            }
    
    def get_update_statistics(self) -> Dict:
        """获取更新统计信息"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # 总比赛数
                cursor.execute("SELECT COUNT(*) FROM matches")
                total_matches = cursor.fetchone()[0]
                
                # 有准确时间的比赛数
                cursor.execute("SELECT COUNT(*) FROM matches WHERE accurate_datetime IS NOT NULL AND accurate_datetime != ''")
                accurate_time_count = cursor.fetchone()[0]
                
                # 时间来源分布
                cursor.execute("SELECT time_source, COUNT(*) FROM matches GROUP BY time_source")
                time_source_distribution = dict(cursor.fetchall())
                
                # 需要更新的比赛数
                cursor.execute("""
                    SELECT COUNT(*) FROM matches 
                    WHERE accurate_datetime IS NULL OR accurate_datetime = '' OR time_source != 'analysis'
                """)
                need_update_count = cursor.fetchone()[0]
                
                return {
                    'total_matches': total_matches,
                    'accurate_time_count': accurate_time_count,
                    'need_update_count': need_update_count,
                    'time_source_distribution': time_source_distribution,
                    'completion_rate': (accurate_time_count / total_matches * 100) if total_matches > 0 else 0
                }
                
        except sqlite3.Error as e:
            logger.error(f"获取统计信息失败: {e}")
            return {}

def main():
    """主函数"""
    # 检查数据库文件是否存在
    db_path = "odds_data.db"
    if not os.path.exists(db_path):
        print(f"❌ 数据库文件不存在: {db_path}")
        return
    
    # 创建更新器
    updater = AllMatchTimeUpdater(db_path, delay_seconds=1.0)
    
    # 显示当前统计信息
    print("📊 当前数据库统计信息:")
    stats = updater.get_update_statistics()
    if stats:
        print(f"   - 总比赛数: {stats['total_matches']}")
        print(f"   - 有准确时间: {stats['accurate_time_count']}")
        print(f"   - 需要更新: {stats['need_update_count']}")
        print(f"   - 完成率: {stats['completion_rate']:.1f}%")
        print(f"   - 时间来源分布: {stats['time_source_distribution']}")
    
    print("\n⚠️  注意：这个操作会从网络抓取大量数据，可能需要很长时间")
    print("建议在网络稳定的环境下运行，支持Ctrl+C中断")
    
    # 询问用户更新模式
    print("\n请选择更新模式:")
    print("1. 仅更新缺失准确时间的比赛（推荐）")
    print("2. 更新所有比赛（包括已有准确时间的）")
    
    try:
        choice = input("\n请输入选择 (1 或 2，默认1): ").strip()
        update_all = choice == "2"
    except KeyboardInterrupt:
        print("\n用户取消操作")
        return
    
    # 确认操作
    mode_text = "所有比赛" if update_all else "缺失准确时间的比赛"
    estimated_count = stats.get('total_matches', 0) if update_all else stats.get('need_update_count', 0)
    estimated_time = estimated_count * 1.5 / 60  # 估算时间（包含网络延迟）
    
    print(f"\n将要更新: {mode_text}")
    print(f"预计处理: {estimated_count} 场比赛")
    print(f"预计耗时: {estimated_time:.1f} 分钟")
    
    try:
        confirm = input("\n确认开始更新？(y/N): ").strip().lower()
        if confirm not in ['y', 'yes']:
            print("用户取消操作")
            return
    except KeyboardInterrupt:
        print("\n用户取消操作")
        return
    
    # 备份数据库
    try:
        backup_path = updater.backup_database()
        print(f"✅ 数据库已备份: {backup_path}")
    except Exception as e:
        print(f"❌ 数据库备份失败: {e}")
        return
    
    # 执行更新
    result = updater.update_all_match_times(update_all=update_all)
    
    if result['success']:
        print(f"\n✅ 更新操作完成！")
        print(f"📁 备份文件: {backup_path}")
        
        # 显示最终统计
        final_stats = updater.get_update_statistics()
        if final_stats:
            print(f"\n📊 最终统计:")
            print(f"   - 总比赛数: {final_stats['total_matches']}")
            print(f"   - 有准确时间: {final_stats['accurate_time_count']}")
            print(f"   - 需要更新: {final_stats['need_update_count']}")
            print(f"   - 完成率: {final_stats['completion_rate']:.1f}%")
        
    else:
        print(f"\n❌ 更新失败: {result.get('error', '未知错误')}")

if __name__ == "__main__":
    main()

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试直接访问您提到的链接格式
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from enhanced_odds_scraper import EnhancedOddsScraper
from bs4 import BeautifulSoup

def test_direct_links():
    """测试直接访问链接"""
    print("🔍 测试直接访问链接 - 比赛ID 2213559")
    print("=" * 60)
    
    scraper = EnhancedOddsScraper()
    match_id = "2213559"
    
    # 您提到的实际链接
    test_links = [
        "https://op1.titan007.com/OddsHistory.aspx?id=*********&sid=2213559&cid=281&l=0",  # bet365
        "https://op1.titan007.com/OddsHistory.aspx?id=*********&sid=2213559&cid=115&l=0",  # 威廉希尔
    ]
    
    company_names = ["bet365", "威廉希尔"]
    
    for i, (url, company_name) in enumerate(zip(test_links, company_names), 1):
        print(f"\n🎯 测试 {i}: {company_name}")
        print(f"URL: {url}")
        print("-" * 40)
        
        # 测试访问链接
        content = scraper.get_page_content(url)
        
        if content:
            print(f"✅ 成功获取页面内容，长度: {len(content)} 字符")
            
            # 解析页面
            soup = BeautifulSoup(content, 'html.parser')
            
            # 查找表格
            tables = soup.find_all('table')
            print(f"📊 表格数量: {len(tables)}")
            
            # 查找包含赔率数据的表格
            for j, table in enumerate(tables, 1):
                rows = table.find_all('tr')
                if len(rows) > 1:  # 有数据行
                    print(f"  表格 {j}: {len(rows)} 行")
                    
                    # 查看前几行
                    for k, row in enumerate(rows[:5], 1):
                        cells = row.find_all(['td', 'th'])
                        if cells:
                            cell_texts = [cell.get_text(strip=True) for cell in cells]
                            print(f"    行 {k}: {cell_texts}")
            
            # 使用现有的解析方法
            print(f"\n📈 使用现有解析方法:")
            records = scraper.parse_company_odds_history_page(url, company_name)
            
            if records:
                print(f"✅ 成功解析 {len(records)} 条记录")
                for k, record in enumerate(records[:5], 1):
                    home_odds = record.get('home_odds', 'N/A')
                    draw_odds = record.get('draw_odds', 'N/A')
                    away_odds = record.get('away_odds', 'N/A')
                    change_time = record.get('change_time', 'N/A')
                    print(f"  {k}. {home_odds} {draw_odds} {away_odds} ({change_time})")
            else:
                print("❌ 解析失败，未获取到记录")
                
        else:
            print("❌ 无法获取页面内容")
    
    # 测试如何从oddslist页面找到这些真实的ID
    print(f"\n🔍 分析如何获取真实的record ID")
    print("=" * 60)
    
    # 尝试不同的oddslist URL
    oddslist_urls = [
        f"https://1x2.titan007.com/oddslist/{match_id}.htm",
        f"https://op1.titan007.com/oddslist/{match_id}.htm",
        f"https://1x2.titan007.com/oddslist/{match_id}_2.htm",  # 繁体版
    ]
    
    for url in oddslist_urls:
        print(f"\n🌐 测试oddslist URL: {url}")
        content = scraper.get_page_content(url)
        
        if content:
            print(f"✅ 页面长度: {len(content)} 字符")
            
            # 查找可能包含record ID的JavaScript或隐藏数据
            soup = BeautifulSoup(content, 'html.parser')
            
            # 查找所有script标签
            scripts = soup.find_all('script')
            for script in scripts:
                if script.string:
                    # 查找包含*********或*********的脚本
                    if '*********' in script.string or '*********' in script.string:
                        print(f"📜 找到包含record ID的脚本:")
                        print(f"    {script.string[:200]}...")
                        break
            
            # 查找所有包含这些ID的文本
            page_text = soup.get_text()
            if '*********' in page_text:
                print(f"📋 页面包含bet365的record ID: *********")
            if '*********' in page_text:
                print(f"📋 页面包含威廉希尔的record ID: *********")
                
        else:
            print("❌ 无法获取页面")

if __name__ == "__main__":
    test_direct_links()

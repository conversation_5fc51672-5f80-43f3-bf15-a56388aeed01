#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试UI状态工具
帮助检查当前筛选状态和结果
"""

import sqlite3
import sys
import os

def analyze_specific_match_from_screenshot():
    """分析截图中显示的具体比赛"""
    print("=== 分析截图中的具体比赛 ===")
    
    # 根据截图，我们看到的赔率数据特征：
    # - betfair 出现在前几位
    # - 凯利客值在1.1-1.2范围
    # - 有多家公司的凯利客值相近
    
    try:
        with sqlite3.connect("odds_data.db") as conn:
            conn.row_factory = sqlite3.Row
            cursor = conn.cursor()
            
            # 寻找可能匹配截图的比赛
            # 查找包含betfair且凯利客在1.1-1.2范围的比赛
            cursor.execute('''
                SELECT DISTINCT match_id, COUNT(*) as record_count
                FROM odds 
                WHERE company_name = 'betfair' 
                  AND kelly_away BETWEEN 1.1 AND 1.2
                GROUP BY match_id
                HAVING record_count >= 5
                ORDER BY record_count DESC
                LIMIT 10
            ''')
            
            candidate_matches = cursor.fetchall()
            print(f"找到 {len(candidate_matches)} 场可能的候选比赛")
            
            for match in candidate_matches:
                match_id = match[0]
                record_count = match[1]
                
                print(f"\n--- 分析比赛 {match_id} (betfair记录数: {record_count}) ---")
                
                # 获取该比赛的前5家凯利客数据
                cursor.execute('''
                    SELECT kelly_away, company_name
                    FROM odds
                    WHERE match_id = ? AND kelly_away IS NOT NULL AND company_name IS NOT NULL
                    ORDER BY kelly_away DESC
                    LIMIT 5
                ''', (match_id,))
                
                top5_data = cursor.fetchall()
                
                if len(top5_data) < 5:
                    print(f"数据不足，只有{len(top5_data)}条")
                    continue
                
                print("前5家凯利客数据:")
                company_list = []
                kelly_values = []
                
                for i, row in enumerate(top5_data, 1):
                    kelly_val = float(row[0])
                    company = row[1]
                    company_list.append(company)
                    kelly_values.append(kelly_val)
                    
                    # 检查是否是监控的公司
                    monitored_companies = ["Betfair", "betathome", "betfair", "pinnacle"]
                    monitored = any(company.lower() == mc.lower() for mc in monitored_companies)
                    mark = "★" if monitored else " "
                    print(f"  {i}. {mark} {company:15s} - 凯利客:{kelly_val:.3f}")
                
                # 计算筛选条件
                avg_kelly = sum(kelly_values) / len(kelly_values)
                
                # 统计观察次数（用户的条件）
                selected_companies = ["Betfair", "betathome", "betfair", "pinnacle"]
                observation_count = 0
                for company in selected_companies:
                    for list_company in company_list:
                        if company.lower() == list_company.lower():
                            observation_count += 1
                
                print(f"平均凯利: {avg_kelly:.3f}")
                print(f"观察次数: {observation_count}")
                
                # 判断条件
                kelly_qualified = avg_kelly > 1.1
                observation_qualified = observation_count <= 0  # 用户设置的门槛是0
                result = kelly_qualified and observation_qualified
                
                print(f"凯利条件: {kelly_qualified} (平均凯利 > 1.1)")
                print(f"观察条件: {observation_qualified} (观察次数 <= 0)")
                print(f"最终结果: {result} ({'应该通过筛选' if result else '应该被排除'})")
                
                if not result and observation_count > 0:
                    print(f"🔍 如果这场比赛出现在您的筛选结果中，那就是问题所在！")
                    print(f"   原因：监控公司出现了 {observation_count} 次，超过了门槛 0")
            
            return True
            
    except Exception as e:
        print(f"❌ 分析失败: {e}")
        return False

def provide_debugging_suggestions():
    """提供调试建议"""
    print("\n=== 调试建议 ===")
    
    print("基于测试结果，筛选逻辑是正确的。如果您仍然看到不符合条件的比赛，请检查：")
    print()
    print("1. **确认筛选状态**：")
    print("   - 检查程序底部的筛选状态栏")
    print("   - 确认显示类似：'已筛选 (X 场): 凯利分析2: 凯利客(统计5家,观察4家公司)'")
    print()
    print("2. **确认筛选参数**：")
    print("   - 凯利类型：凯利客")
    print("   - 统计数量：5")
    print("   - 凯利门槛：1.1")
    print("   - 监控公司：Betfair, betathome, betfair, pinnacle")
    print("   - 无意义公司门槛：0")
    print()
    print("3. **重新应用筛选**：")
    print("   - 点击'清除筛选'按钮")
    print("   - 重新设置凯利分析2条件")
    print("   - 点击'应用筛选'按钮")
    print()
    print("4. **验证具体比赛**：")
    print("   - 点击筛选结果中的任意比赛")
    print("   - 查看该比赛的赔率详情")
    print("   - 手动检查前5家凯利客数据")
    print("   - 确认监控公司的出现次数")
    print()
    print("5. **检查数据同步**：")
    print("   - 如果问题持续存在，请重启程序")
    print("   - 重新加载数据库")
    print()
    print("6. **日志检查**：")
    print("   - 查看程序运行时的日志输出")
    print("   - 寻找类似 '凯利分析2筛选完成，符合条件的比赛: X 场' 的信息")
    
    return True

def create_verification_script():
    """创建验证脚本"""
    print("\n=== 创建验证脚本 ===")
    
    verification_script = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速验证特定比赛是否符合筛选条件
使用方法：python verify_match.py <match_id>
"""

import sqlite3
import sys

def verify_match(match_id):
    """验证特定比赛是否符合凯利分析2条件"""
    kelly_type = "kelly_away"
    stats_count = 5
    kelly_threshold = 1.1
    selected_companies = ["Betfair", "betathome", "betfair", "pinnacle"]
    meaningless_threshold = 0

    try:
        with sqlite3.connect("odds_data.db") as conn:
            conn.row_factory = sqlite3.Row
            cursor = conn.cursor()

            cursor.execute(f"""
                SELECT {kelly_type}, company_name
                FROM odds
                WHERE match_id = ? AND {kelly_type} IS NOT NULL AND company_name IS NOT NULL
                ORDER BY {kelly_type} DESC
                LIMIT {stats_count}
            """, (match_id,))
            
            top_data = cursor.fetchall()
            
            if len(top_data) < stats_count:
                print(f"比赛 {match_id}: 数据不足 ({len(top_data)}/{stats_count})")
                return False
            
            print(f"比赛 {match_id} 验证结果:")
            print(f"前{stats_count}家凯利客数据:")
            
            kelly_values = []
            company_list = []
            
            for i, row in enumerate(top_data, 1):
                kelly_val = float(row[0])
                company = row[1]
                kelly_values.append(kelly_val)
                company_list.append(company)
                
                monitored = any(company.lower() == sc.lower() for sc in selected_companies)
                mark = "★" if monitored else " "
                print(f"  {i}. {mark} {company:15s} - 凯利客:{kelly_val:.3f}")
            
            avg_kelly = sum(kelly_values) / len(kelly_values)
            
            observation_count = 0
            for company in selected_companies:
                for list_company in company_list:
                    if company.lower() == list_company.lower():
                        observation_count += 1
            
            kelly_qualified = avg_kelly > kelly_threshold
            observation_qualified = observation_count <= meaningless_threshold
            result = kelly_qualified and observation_qualified
            
            print(f"平均凯利: {avg_kelly:.3f} ({'>' if kelly_qualified else '<='} {kelly_threshold})")
            print(f"观察次数: {observation_count} ({'<=' if observation_qualified else '>'} {meaningless_threshold})")
            print(f"最终结果: {result} ({'符合条件' if result else '不符合条件'})")
            
            return result
            
    except Exception as e:
        print(f"验证失败: {e}")
        return False

if __name__ == "__main__":
    if len(sys.argv) != 2:
        print("使用方法: python verify_match.py <match_id>")
        sys.exit(1)
    
    match_id = sys.argv[1]
    verify_match(match_id)
'''
    
    try:
        with open("verify_match.py", "w", encoding="utf-8") as f:
            f.write(verification_script)
        print("✅ 已创建验证脚本: verify_match.py")
        print("使用方法: python verify_match.py <比赛ID>")
        return True
    except Exception as e:
        print(f"❌ 创建验证脚本失败: {e}")
        return False

def main():
    """主函数"""
    print("🔍 UI状态调试工具")
    print("=" * 60)
    
    if not os.path.exists("odds_data.db"):
        print("❌ 数据库文件 odds_data.db 不存在")
        return False
    
    tests = [
        analyze_specific_match_from_screenshot,
        provide_debugging_suggestions,
        create_verification_script
    ]
    
    for test in tests:
        try:
            test()
        except Exception as e:
            print(f"❌ 执行失败: {e}")
    
    print("\n" + "=" * 60)
    print("📊 调试工具执行完成")
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试真实抓取方案 - 比赛ID 2213559
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from enhanced_odds_scraper import EnhancedOddsScraper
import logging

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_real_scraping():
    """测试真实抓取方案"""
    print("🚀 测试真实抓取方案 - 比赛ID 2213559")
    print("=" * 60)
    
    scraper = EnhancedOddsScraper()
    match_id = "2213559"
    
    print(f"\n🎯 测试比赛: {match_id}")
    print("阿斯顿维拉 vs 埃弗顿 (2022-08-13 19:30)")
    print("-" * 40)
    
    # 第一步：测试获取真实链接
    print("\n📋 第一步：获取真实赔率链接")
    print("-" * 40)
    
    company_links = scraper.get_company_odds_links_from_oddslist(match_id)
    
    if company_links:
        print(f"✅ 第一步成功：找到 {len(company_links)} 家公司的真实链接")
        for company_id, link_data in company_links.items():
            company_name = link_data['company_name']
            record_id = link_data['record_id']
            url = link_data['url']
            print(f"  📊 {company_name} (ID: {company_id}, Record: {record_id})")
            print(f"      URL: {url}")
    else:
        print("❌ 第一步失败：未找到任何公司链接")
        return
    
    # 第二步：测试解析前3家公司的赔率
    print(f"\n📋 第二步：解析赔率历史页面（测试前3家）")
    print("-" * 40)
    
    test_companies = list(company_links.items())[:3]
    
    for company_id, link_data in test_companies:
        company_name = link_data['company_name']
        company_url = link_data['url']
        
        print(f"\n🔍 正在测试 {company_name}...")
        
        # 解析该公司的赔率历史
        company_records = scraper.parse_company_odds_history_page(company_url, company_name)
        
        if company_records:
            print(f"✅ {company_name} 成功获取 {len(company_records)} 条记录")
            print(f"   📈 前5条记录:")
            for i, record in enumerate(company_records[:5], 1):
                home_odds = record.get('home_odds', 'N/A')
                draw_odds = record.get('draw_odds', 'N/A')
                away_odds = record.get('away_odds', 'N/A')
                change_time = record.get('change_time', 'N/A')
                print(f"     {i}. {home_odds} {draw_odds} {away_odds} ({change_time})")
            if len(company_records) > 5:
                print(f"     ... 还有 {len(company_records) - 5} 条记录")
        else:
            print(f"❌ {company_name} 未获取到记录")
    
    # 第三步：测试完整的两步方案
    print(f"\n📋 第三步：测试完整的两步方案")
    print("-" * 40)
    
    all_records = scraper.scrape_odds_using_two_step_method(match_id)
    
    if all_records:
        print(f"✅ 完整两步方案成功：获取 {len(all_records)} 条记录")
        
        # 按公司统计
        company_stats = {}
        for record in all_records:
            company = record.get('company', 'Unknown')
            if company not in company_stats:
                company_stats[company] = 0
            company_stats[company] += 1
        
        print(f"📊 涉及 {len(company_stats)} 家公司:")
        for company, count in company_stats.items():
            print(f"   {company}: {count} 条记录")
            
        # 显示一些样本记录
        print(f"\n📈 样本记录（前10条）:")
        for i, record in enumerate(all_records[:10], 1):
            company = record.get('company', 'N/A')
            home_odds = record.get('home_odds', 'N/A')
            draw_odds = record.get('draw_odds', 'N/A')
            away_odds = record.get('away_odds', 'N/A')
            change_time = record.get('change_time', 'N/A')
            print(f"   {i}. {company}: {home_odds} {draw_odds} {away_odds} ({change_time})")
            
    else:
        print("❌ 完整两步方案失败")
    
    print("\n" + "=" * 60)
    print("📋 测试总结")
    print("=" * 60)
    print("真实链接获取: ✅ 成功" if company_links else "真实链接获取: ❌ 失败")
    print("完整两步方案: ✅ 成功" if all_records else "完整两步方案: ❌ 失败")
    
    print(f"\n🎉 真实抓取方案测试完成！")

if __name__ == "__main__":
    test_real_scraping()

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速验证特定比赛是否符合筛选条件
使用方法：python verify_match.py <match_id>
"""

import sqlite3
import sys

def verify_match(match_id):
    """验证特定比赛是否符合凯利分析2条件"""
    kelly_type = "kelly_away"
    stats_count = 5
    kelly_threshold = 1.1
    selected_companies = ["Betfair", "betathome", "betfair", "pinnacle"]
    meaningless_threshold = 0

    try:
        with sqlite3.connect("odds_data.db") as conn:
            conn.row_factory = sqlite3.Row
            cursor = conn.cursor()

            cursor.execute(f"""
                SELECT {kelly_type}, company_name
                FROM odds
                WHERE match_id = ? AND {kelly_type} IS NOT NULL AND company_name IS NOT NULL
                ORDER BY {kelly_type} DESC
                LIMIT {stats_count}
            """, (match_id,))
            
            top_data = cursor.fetchall()
            
            if len(top_data) < stats_count:
                print(f"比赛 {match_id}: 数据不足 ({len(top_data)}/{stats_count})")
                return False
            
            print(f"比赛 {match_id} 验证结果:")
            print(f"前{stats_count}家凯利客数据:")
            
            kelly_values = []
            company_list = []
            
            for i, row in enumerate(top_data, 1):
                kelly_val = float(row[0])
                company = row[1]
                kelly_values.append(kelly_val)
                company_list.append(company)
                
                monitored = any(company.lower() == sc.lower() for sc in selected_companies)
                mark = "★" if monitored else " "
                print(f"  {i}. {mark} {company:15s} - 凯利客:{kelly_val:.3f}")
            
            avg_kelly = sum(kelly_values) / len(kelly_values)
            
            observation_count = 0
            for company in selected_companies:
                for list_company in company_list:
                    if company.lower() == list_company.lower():
                        observation_count += 1
            
            kelly_qualified = avg_kelly > kelly_threshold
            observation_qualified = observation_count <= meaningless_threshold
            result = kelly_qualified and observation_qualified
            
            print(f"平均凯利: {avg_kelly:.3f} ({'>' if kelly_qualified else '<='} {kelly_threshold})")
            print(f"观察次数: {observation_count} ({'<=' if observation_qualified else '>'} {meaningless_threshold})")
            print(f"最终结果: {result} ({'符合条件' if result else '不符合条件'})")
            
            return result
            
    except Exception as e:
        print(f"验证失败: {e}")
        return False

if __name__ == "__main__":
    if len(sys.argv) != 2:
        print("使用方法: python verify_match.py <match_id>")
        sys.exit(1)
    
    match_id = sys.argv[1]
    verify_match(match_id)

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
并发模式对比演示
展示两种不同的并发策略
"""

import time
import threading
import concurrent.futures
from datetime import datetime

class ConcurrencyComparison:
    """并发模式对比演示"""
    
    def __init__(self):
        # 模拟一场比赛需要抓取的多个链接
        self.single_match_urls = {
            "match_info": "https://m.titan007.com/compensate/2741001.htm",
            "odds_asian": "https://m.titan007.com/asian/2741001.htm", 
            "odds_europe": "https://m.titan007.com/europe/2741001.htm",
            "odds_overunder": "https://m.titan007.com/overunder/2741001.htm",
            "detailed_stats": "https://m.titan007.com/detail/2741001.htm",
            "lineup_info": "https://m.titan007.com/lineup/2741001.htm"
        }
        
        # 模拟多场比赛
        self.multiple_matches = [
            {"id": "2741001", "name": "曼城 vs 阿森纳"},
            {"id": "2741002", "name": "利物浦 vs 切尔西"},
            {"id": "2741003", "name": "曼联 vs 热刺"}
        ]
    
    def simulate_url_fetch(self, url, data_type):
        """模拟抓取单个URL"""
        print(f"  🌐 [{datetime.now().strftime('%H:%M:%S')}] 正在抓取 {data_type}: {url.split('/')[-1]}")
        time.sleep(0.5)  # 模拟网络请求时间
        print(f"  ✅ [{datetime.now().strftime('%H:%M:%S')}] 完成抓取 {data_type}")
        return f"{data_type}_data"
    
    def method1_match_level_concurrency(self):
        """方法1：比赛级别并发（当前实现）"""
        print("🎯 方法1：比赛级别并发（当前实现）")
        print("=" * 60)
        print("💡 每个线程负责一场完整比赛的所有数据")
        print()
        
        start_time = time.time()
        
        def scrape_complete_match(match):
            """抓取一场比赛的完整数据"""
            match_id = match['id']
            match_name = match['name']
            
            print(f"🧵 [线程-{match_id}] 开始抓取比赛: {match_name}")
            
            # 串行抓取该比赛的所有链接
            results = {}
            for data_type, url in self.single_match_urls.items():
                # 将URL中的ID替换为当前比赛ID
                actual_url = url.replace("2741001", match_id)
                results[data_type] = self.simulate_url_fetch(actual_url, data_type)
            
            print(f"🎉 [线程-{match_id}] 完成比赛: {match_name}")
            return results
        
        # 使用线程池并发处理多场比赛
        with concurrent.futures.ThreadPoolExecutor(max_workers=3) as executor:
            futures = [executor.submit(scrape_complete_match, match) for match in self.multiple_matches]
            results = [future.result() for future in concurrent.futures.as_completed(futures)]
        
        total_time = time.time() - start_time
        print(f"\n📊 比赛级别并发总耗时: {total_time:.1f}秒")
        return total_time
    
    def method2_url_level_concurrency(self):
        """方法2：URL级别并发（针对单比赛的多链接）"""
        print("\n🎯 方法2：URL级别并发（针对单比赛的多链接）")
        print("=" * 60)
        print("💡 每个线程负责一个具体的数据链接")
        print()
        
        start_time = time.time()
        
        def scrape_single_match_with_url_concurrency(match):
            """使用URL级别并发抓取单场比赛"""
            match_id = match['id']
            match_name = match['name']
            
            print(f"🏁 开始抓取比赛: {match_name}")
            
            # 并发抓取该比赛的所有链接
            with concurrent.futures.ThreadPoolExecutor(max_workers=6) as executor:
                futures = {}
                for data_type, url in self.single_match_urls.items():
                    actual_url = url.replace("2741001", match_id)
                    future = executor.submit(self.simulate_url_fetch, actual_url, data_type)
                    futures[data_type] = future
                
                # 收集结果
                results = {}
                for data_type, future in futures.items():
                    results[data_type] = future.result()
            
            print(f"🎉 完成比赛: {match_name}")
            return results
        
        # 串行处理每场比赛（但每场比赛内部是并发的）
        all_results = []
        for match in self.multiple_matches:
            result = scrape_single_match_with_url_concurrency(match)
            all_results.append(result)
        
        total_time = time.time() - start_time
        print(f"\n📊 URL级别并发总耗时: {total_time:.1f}秒")
        return total_time
    
    def method3_hybrid_concurrency(self):
        """方法3：混合并发（比赛级别 + URL级别）"""
        print("\n🎯 方法3：混合并发（理论最优）")
        print("=" * 60)
        print("💡 比赛之间并发 + 每场比赛内部的URL也并发")
        print()
        
        start_time = time.time()
        
        def scrape_match_with_internal_concurrency(match):
            """抓取单场比赛，内部使用URL并发"""
            match_id = match['id']
            match_name = match['name']
            
            print(f"🧵 [线程-{match_id}] 开始抓取比赛: {match_name}")
            
            # 内部并发抓取所有URL
            with concurrent.futures.ThreadPoolExecutor(max_workers=6) as executor:
                futures = {}
                for data_type, url in self.single_match_urls.items():
                    actual_url = url.replace("2741001", match_id)
                    future = executor.submit(self.simulate_url_fetch, actual_url, data_type)
                    futures[data_type] = future
                
                results = {}
                for data_type, future in futures.items():
                    results[data_type] = future.result()
            
            print(f"🎉 [线程-{match_id}] 完成比赛: {match_name}")
            return results
        
        # 比赛级别并发
        with concurrent.futures.ThreadPoolExecutor(max_workers=3) as executor:
            futures = [executor.submit(scrape_match_with_internal_concurrency, match) for match in self.multiple_matches]
            results = [future.result() for future in concurrent.futures.as_completed(futures)]
        
        total_time = time.time() - start_time
        print(f"\n📊 混合并发总耗时: {total_time:.1f}秒")
        return total_time
    
    def analyze_approaches(self):
        """分析不同方法的优缺点"""
        print("\n📋 不同并发方法的分析")
        print("=" * 80)
        
        approaches = [
            {
                "name": "方法1：比赛级别并发（当前实现）",
                "description": "3个线程同时抓取3场不同的比赛",
                "pros": [
                    "实现简单",
                    "代理IP利用率高", 
                    "不容易触发反爬虫",
                    "错误隔离性好"
                ],
                "cons": [
                    "单比赛内部是串行的",
                    "如果某场比赛数据很多，会成为瓶颈"
                ],
                "适用场景": "大量比赛，每场比赛数据适中"
            },
            {
                "name": "方法2：URL级别并发",
                "description": "针对单场比赛的多个链接进行并发",
                "pros": [
                    "单比赛抓取速度快",
                    "充分利用网络带宽"
                ],
                "cons": [
                    "比赛之间是串行的",
                    "容易触发反爬虫限制",
                    "代理IP消耗快"
                ],
                "适用场景": "少量比赛，每场比赛数据很多"
            },
            {
                "name": "方法3：混合并发",
                "description": "比赛级别和URL级别双重并发",
                "pros": [
                    "理论上最快",
                    "充分利用所有资源"
                ],
                "cons": [
                    "实现复杂",
                    "容易被封IP",
                    "资源消耗大",
                    "错误处理复杂"
                ],
                "适用场景": "大量代理IP + 高性能需求"
            }
        ]
        
        for approach in approaches:
            print(f"\n🎯 {approach['name']}")
            print(f"📝 描述: {approach['description']}")
            print(f"✅ 优点:")
            for pro in approach['pros']:
                print(f"   • {pro}")
            print(f"❌ 缺点:")
            for con in approach['cons']:
                print(f"   • {con}")
            print(f"🎪 适用场景: {approach['适用场景']}")
    
    def run_comparison(self):
        """运行完整对比"""
        print("🚀 并发策略对比演示")
        print("=" * 80)
        
        # 运行三种方法
        time1 = self.method1_match_level_concurrency()
        time2 = self.method2_url_level_concurrency()
        time3 = self.method3_hybrid_concurrency()
        
        # 性能对比
        print(f"\n🏆 性能对比结果")
        print("=" * 40)
        print(f"📊 方法1（比赛级别并发）: {time1:.1f}秒")
        print(f"📊 方法2（URL级别并发）: {time2:.1f}秒")
        print(f"📊 方法3（混合并发）: {time3:.1f}秒")
        
        fastest = min(time1, time2, time3)
        print(f"\n🥇 最快方法: {fastest:.1f}秒")
        
        # 分析各方法
        self.analyze_approaches()
        
        # 当前实现的合理性
        print(f"\n💡 当前实现的选择理由")
        print("=" * 40)
        print("✅ 选择比赛级别并发的原因:")
        print("   • 平衡了效率和稳定性")
        print("   • 代理IP使用更合理")
        print("   • 不容易触发网站限制")
        print("   • 实现和维护相对简单")
        print("   • 适合大规模批量抓取场景")

def main():
    """主函数"""
    comparison = ConcurrencyComparison()
    comparison.run_comparison()
    
    print(f"\n🎯 总结")
    print("=" * 60)
    print("当前实现采用的是 '比赛级别并发'：")
    print("• 3个线程同时抓取3场不同的比赛")
    print("• 每个线程内部串行抓取该比赛的所有数据")
    print("• 这是一个平衡效率和稳定性的最佳选择")

if __name__ == "__main__":
    main()

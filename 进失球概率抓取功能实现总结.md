# 🎯 进失球概率抓取功能实现总结

## ✅ 功能已实现

我已经在 `league_match_extractor.py` 中成功实现了进失球概率抓取功能！

## 🚀 实现的功能

### 1. 主要抓取方法
```python
def scrape_goal_probability_data(self, match_id: str) -> Dict[str, any]:
    """抓取进失球概率数据"""
```

**功能特点**:
- ✅ 支持任意比赛ID的概率数据抓取
- ✅ 自动构造分析页面URL: `https://m.titan007.com/analy/shijian/{match_id}.htm`
- ✅ 多种数据源解析：表格、JavaScript、div容器
- ✅ 智能识别30场/50场数据
- ✅ 提取时间段信息
- ✅ 完整的错误处理

### 2. 数据解析策略

#### 📋 表格数据解析
```python
def _parse_probability_table(self, table, probability_data: Dict, table_id: str):
```
- 查找包含"30场"、"50场"、"概率"、"%"的表格行
- 提取百分比数据和原始文本
- 按数据类型分类存储

#### 📜 JavaScript数据解析
```python
def _parse_probability_script(self, script_content: str, probability_data: Dict):
```
- 查找 `Count_30`、`Count_50` 等变量
- 提取包含百分比的数组数据
- 识别概率相关的JavaScript变量

#### 🏷️ HTML容器解析
```python
def _parse_probability_div(self, div, probability_data: Dict):
```
- 解析特定div中的概率数据
- 提取class和id信息
- 保存结构化数据

### 3. 数据输出格式

```python
{
    'match_id': '2511566',
    'analysis_url': 'https://m.titan007.com/analy/shijian/2511566.htm',
    'goal_probabilities': {
        'Count_30': {
            'percentages': ['25%', '30%', '45%'],
            'raw_text': '30场数据统计...'
        },
        'Count_50': {
            'percentages': ['28%', '32%', '40%'],
            'raw_text': '50场数据统计...'
        }
    },
    'time_segments': ['0-15', '16-30', '31-45', '46-60', '61-75', '76-90'],
    'raw_percentages': ['25%', '30%', '45%', '28%', '32%', '40%'],
    'raw_data': {
        'script_variables': '...',
        'arrays': ['...']
    }
}
```

## 🧪 测试功能

### 测试方法
```python
def test_goal_probability_scraping():
    """测试进失球概率抓取功能"""
```

**测试内容**:
- ✅ 使用示例比赛ID: 2511566
- ✅ 验证网络连接和页面访问
- ✅ 检查数据提取结果
- ✅ 显示详细的抓取信息

### 运行测试
```bash
python league_match_extractor.py
```

## 🎯 使用方法

### 1. 在现有项目中使用
```python
from league_match_extractor import LeagueMatchURLExtractor

# 创建提取器
extractor = LeagueMatchURLExtractor()

# 抓取概率数据
result = extractor.scrape_goal_probability_data("2511566")

# 检查结果
if 'error' not in result:
    goal_probs = result['goal_probabilities']
    print(f"找到 {len(goal_probs)} 组概率数据")
else:
    print(f"抓取失败: {result['error']}")
```

### 2. 集成到UI程序
可以在 `enhanced_match_data_system.py` 中调用此功能：

```python
# 在数据抓取过程中添加
probability_data = self.league_extractor.scrape_goal_probability_data(match_id)
if 'error' not in probability_data:
    # 保存到数据库
    self.save_probability_data(probability_data)
```

## 🔍 数据识别策略

### 关键词匹配
- **30场数据**: "30场", "Count_30"
- **50场数据**: "50场", "Count_50"  
- **概率相关**: "概率", "probability", "进球", "失球"
- **百分比**: 正则表达式 `\d+(?:\.\d+)?%`

### 时间段识别
- **分钟格式**: "0-15分钟", "16-30分钟"
- **简化格式**: "0-15", "16-30"
- **单分钟**: "第15分钟", "90分钟内"

### 数据验证
- ✅ 百分比数值合理性检查
- ✅ 数据完整性验证
- ✅ 重复数据去除
- ✅ 异常数据过滤

## 🛡️ 错误处理

### 网络错误
- 连接超时处理
- HTTP状态码检查
- 重试机制（可扩展）

### 解析错误
- HTML结构变化适应
- JavaScript格式兼容
- 数据缺失处理

### 数据错误
- 无效百分比过滤
- 空数据检查
- 格式异常处理

## 🔮 扩展建议

### 1. 数据存储优化
- 将抓取的概率数据保存到数据库
- 建立时间段和概率的关联表
- 支持历史数据对比

### 2. 解析精度提升
- 根据实际页面结构优化解析逻辑
- 添加更多数据源识别
- 提高数据提取准确率

### 3. 功能增强
- 支持批量比赛概率抓取
- 添加数据可视化
- 实现概率趋势分析

## 🎊 实现完成

进失球概率抓取功能已经完全实现并集成到 `league_match_extractor.py` 中！

### 主要特点
- ✅ **完整功能**: 支持完整的概率数据抓取
- ✅ **智能解析**: 多种数据源自动识别
- ✅ **结构化输出**: 标准化的数据格式
- ✅ **错误处理**: 完善的异常处理机制
- ✅ **易于集成**: 可直接在现有项目中使用

### 立即可用
您现在可以：
1. 运行 `python league_match_extractor.py` 测试功能
2. 在UI程序中集成此功能
3. 根据实际需求调整解析逻辑

**🎯 进失球概率抓取功能实现完成，可以开始使用了！**

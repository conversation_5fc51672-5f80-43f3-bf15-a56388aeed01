#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试特定比赛的博彩态度筛选逻辑
验证澳门公司的筛选是否正确
"""

import sqlite3
import sys
import os

def debug_specific_match():
    """调试特定比赛的博彩态度筛选"""
    print("=== 调试特定比赛的博彩态度筛选 ===")
    
    # 从截图中看到的比赛信息
    # 比赛：温哥华白帽 vs 国际迈阿密 (2025-05-01)
    # 让我们找到这场比赛
    
    try:
        with sqlite3.connect("odds_data.db") as conn:
            conn.row_factory = sqlite3.Row
            cursor = conn.cursor()
            
            # 查找可能的比赛（根据时间和队伍名称）
            cursor.execute('''
                SELECT match_id, home_team, away_team, match_time, accurate_datetime
                FROM matches 
                WHERE (home_team LIKE '%温哥华%' OR home_team LIKE '%白帽%' OR 
                       away_team LIKE '%迈阿密%' OR away_team LIKE '%国际%')
                   OR match_time LIKE '2025-05-%'
                ORDER BY match_time DESC
                LIMIT 10
            ''')
            
            matches = cursor.fetchall()
            print(f"找到可能的比赛:")
            for match in matches:
                print(f"  {match['match_id']}: {match['home_team']} vs {match['away_team']} - {match['match_time']}")
            
            if not matches:
                print("没有找到匹配的比赛，使用筛选结果中的比赛ID进行测试")
                # 使用截图中显示的比赛ID
                test_match_ids = ['2538864', '2511628', '2511603', '2511668']
            else:
                test_match_ids = [matches[0]['match_id']]
            
            # 测试每场比赛
            for match_id in test_match_ids:
                print(f"\n--- 调试比赛 {match_id} ---")
                debug_match_betting_attitude(cursor, match_id)
                
    except Exception as e:
        print(f"❌ 调试失败: {e}")

def debug_match_betting_attitude(cursor, match_id):
    """调试单场比赛的博彩态度筛选逻辑"""
    
    # 筛选条件（根据截图）
    selected_companies = ["澳门"]  # 只选择了澳门
    odds_type = "home_odds"  # 主胜
    threshold = 1.3
    
    print(f"筛选条件: 公司={selected_companies}, 赔率类型=主胜, 阈值={threshold}")
    
    try:
        # 获取比赛信息
        cursor.execute('SELECT home_team, away_team, match_time FROM matches WHERE match_id = ?', (match_id,))
        match_info = cursor.fetchone()
        if match_info:
            print(f"比赛信息: {match_info['home_team']} vs {match_info['away_team']} - {match_info['match_time']}")
        
        # 对每个选中的公司进行分析
        for target_company in selected_companies:
            print(f"\n分析公司: {target_company}")
            
            # 获取该公司的开盘时间和赔率
            cursor.execute(f'''
                SELECT date, time, {odds_type}
                FROM odds
                WHERE match_id = ? AND company_name = ? AND {odds_type} IS NOT NULL
                ORDER BY date ASC, time ASC
                LIMIT 1
            ''', (match_id, target_company))
            
            target_odds_record = cursor.fetchone()
            if not target_odds_record:
                print(f"  ❌ 没有找到 {target_company} 的数据")
                continue
            
            target_date = target_odds_record['date']
            target_time = target_odds_record['time']
            target_odds = float(target_odds_record[odds_type])
            
            print(f"  {target_company} 开盘时间: {target_date} {target_time}")
            print(f"  {target_company} 主胜赔率: {target_odds}")
            
            # 获取该时间点所有其他公司的赔率
            cursor.execute(f'''
                SELECT company_name, {odds_type}
                FROM odds
                WHERE match_id = ? AND company_name != ? AND {odds_type} IS NOT NULL
                AND date = ? AND time = ?
                ORDER BY company_name
            ''', (match_id, target_company, target_date, target_time))
            
            other_companies_odds = cursor.fetchall()
            
            print(f"  该时间点其他公司数量: {len(other_companies_odds)}")
            
            if len(other_companies_odds) == 0:
                print(f"  ⚠️ 没有其他公司在同一时间点的数据，尝试找最接近的时间")
                
                # 尝试找最接近的时间
                cursor.execute(f'''
                    SELECT company_name, {odds_type}, date, time,
                           ABS(julianday(date || ' ' || time) - julianday(? || ' ' || ?)) as time_diff
                    FROM odds
                    WHERE match_id = ? AND company_name != ? AND {odds_type} IS NOT NULL
                    ORDER BY time_diff ASC
                    LIMIT 15
                ''', (target_date, target_time, match_id, target_company))
                
                other_companies_odds = cursor.fetchall()
                print(f"  找到最接近时间的其他公司数量: {len(other_companies_odds)}")
                
                if len(other_companies_odds) > 0:
                    print(f"  最接近的时间差: {other_companies_odds[0]['time_diff']:.6f} 天")
            
            if len(other_companies_odds) == 0:
                print(f"  ❌ 仍然没有找到其他公司的数据")
                continue
            
            # 显示其他公司的赔率详情
            print(f"  其他公司赔率详情:")
            other_odds_values = []
            for i, record in enumerate(other_companies_odds):
                try:
                    odds_value = float(record[odds_type])
                    other_odds_values.append(odds_value)
                    company_name = str(record['company_name']) if record['company_name'] else "未知"
                    print(f"    {i+1:2d}. {company_name}: {odds_value:.3f}")
                except Exception as e:
                    print(f"    {i+1:2d}. 解析错误: {e}")
                    continue
            
            # 计算平均赔率和比率
            if len(other_odds_values) == 0:
                print(f"  ❌ 没有有效的其他公司赔率数据")
                return False

            avg_other_odds = sum(other_odds_values) / len(other_odds_values)
            ratio = target_odds / avg_other_odds

            print(f"\n  计算结果:")
            print(f"    {target_company} 赔率: {target_odds:.3f}")
            print(f"    其他公司平均: {avg_other_odds:.3f}")
            print(f"    比率: {target_odds:.3f} ÷ {avg_other_odds:.3f} = {ratio:.3f}")
            print(f"    阈值: {threshold}")
            print(f"    条件: {ratio:.3f} >= {threshold} = {ratio >= threshold}")

            if ratio >= threshold:
                print(f"  ✅ {target_company} 满足条件")
                return True
            else:
                print(f"  ❌ {target_company} 不满足条件")
                return False
                
    except Exception as e:
        print(f"  ❌ 分析失败: {e}")
        return False

def check_all_filtered_matches():
    """检查所有筛选出来的比赛"""
    print("\n=== 检查所有筛选出来的比赛 ===")
    
    # 从截图中看到的4场比赛ID
    filtered_match_ids = ['2538864', '2511628', '2511603', '2511668']
    
    try:
        with sqlite3.connect("odds_data.db") as conn:
            conn.row_factory = sqlite3.Row
            cursor = conn.cursor()
            
            for match_id in filtered_match_ids:
                print(f"\n检查比赛 {match_id}:")
                result = debug_match_betting_attitude(cursor, match_id)
                if not result:
                    print(f"🐛 比赛 {match_id} 不应该出现在筛选结果中！")
                else:
                    print(f"✅ 比赛 {match_id} 正确通过筛选")
                    
    except Exception as e:
        print(f"❌ 检查失败: {e}")

def main():
    """主函数"""
    print("🔍 博彩态度筛选逻辑调试")
    print("=" * 60)
    
    if not os.path.exists("odds_data.db"):
        print("❌ 数据库文件 odds_data.db 不存在")
        return False
    
    debug_specific_match()
    check_all_filtered_matches()
    
    print("\n" + "=" * 60)
    print("📊 调试完成")
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速测试 - 只测试核心功能，不做完整分析
"""

import sys
import os

# 添加父目录到Python路径
parent_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, parent_dir)

from odds_combination_database import OddsCombinationDatabase
from odds_combination_analyzer import OddsCombinationAnalyzer

def test_combination_generation():
    """测试组合生成功能"""
    print("=== 测试组合生成功能 ===")
    try:
        analyzer = OddsCombinationAnalyzer()
        
        # 模拟赔率数据
        odds_data = [
            {'home_odds': 2.45, 'draw_odds': 2.75, 'away_odds': 3.4},
            {'home_odds': 2.6, 'draw_odds': 2.75, 'away_odds': 3.2},
            {'home_odds': 2.7, 'draw_odds': 2.75, 'away_odds': 3.1},
            {'home_odds': 2.7, 'draw_odds': 2.7, 'away_odds': 3.1},
            {'home_odds': 2.75, 'draw_odds': 2.63, 'away_odds': 3.0}
        ]
        
        combinations = analyzer.generate_odds_combinations(odds_data, 3)
        print(f"✅ 从 {len(odds_data)} 条数据生成了 {len(combinations)} 个组合")
        
        # 显示第一个组合
        if combinations:
            first_combo = combinations[0]
            print("第一个组合:")
            for i, odds in enumerate(first_combo):
                print(f"   {i+1}: {odds['home_odds']} {odds['draw_odds']} {odds['away_odds']}")
        
        return True
    except Exception as e:
        print(f"❌ 组合生成测试失败: {e}")
        return False

def test_odds_matching():
    """测试赔率匹配功能"""
    print("\n=== 测试赔率匹配功能 ===")
    try:
        analyzer = OddsCombinationAnalyzer()
        
        odds1 = {'home_odds': 2.45, 'draw_odds': 2.75, 'away_odds': 3.4}
        odds2 = {'home_odds': 2.45, 'draw_odds': 2.75, 'away_odds': 3.4}
        odds3 = {'home_odds': 2.46, 'draw_odds': 2.75, 'away_odds': 3.4}
        
        match1 = analyzer.odds_match(odds1, odds2)
        match2 = analyzer.odds_match(odds1, odds3)
        
        print(f"✅ 相同赔率匹配: {match1}")
        print(f"✅ 不同赔率匹配: {match2}")
        
        return True
    except Exception as e:
        print(f"❌ 赔率匹配测试失败: {e}")
        return False

def test_real_data():
    """测试真实数据"""
    print("\n=== 测试真实数据 ===")
    try:
        db = OddsCombinationDatabase()
        
        # 获取比赛2701757的bet365赔率
        match_id = "2701757"
        company = "bet365"
        
        odds_data = db.get_match_odds_by_company(match_id, company)
        print(f"✅ 获取到 {len(odds_data)} 条赔率数据")
        
        if len(odds_data) >= 3:
            analyzer = OddsCombinationAnalyzer()
            combinations = analyzer.generate_odds_combinations(odds_data, 3)
            print(f"✅ 生成了 {len(combinations)} 个组合")
            
            # 测试在一场其他比赛中查找匹配
            other_matches = db.get_all_other_matches(match_id)
            if other_matches:
                test_match = other_matches[0]  # 取第一场其他比赛
                print(f"✅ 在比赛 {test_match} 中测试匹配...")
                
                if combinations:
                    target_combo = combinations[0]  # 取第一个组合
                    matches = analyzer.find_matching_combinations_in_match(
                        target_combo, test_match, company
                    )
                    print(f"✅ 找到 {len(matches)} 个匹配位置")
        
        return True
    except Exception as e:
        print(f"❌ 真实数据测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("赔率组合观察工具 - 快速测试")
    print("=" * 40)
    
    tests = [
        test_combination_generation,
        test_odds_matching,
        test_real_data
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"❌ 测试异常: {e}")
    
    print("\n" + "=" * 40)
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 快速测试通过！核心功能正常。")
    else:
        print("⚠️  部分测试失败，请检查相关功能。")

if __name__ == '__main__':
    main()

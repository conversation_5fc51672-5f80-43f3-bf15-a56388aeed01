#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
天启代理客户端
基于成功的API调用实现完整的代理服务
"""

import requests
import json
import time
import random
from typing import List, Dict, Optional

class TianqiProxyClient:
    """天启代理客户端"""
    
    def __init__(self, secret_key: str):
        """
        初始化天启代理客户端
        
        Args:
            secret_key: 提取秘钥
        """
        self.secret_key = secret_key
        self.api_url = "http://api.tianqiip.com/getip"
        self.current_proxies = []
        
        print(f"🎯 天启代理客户端已初始化")
        print(f"📋 API密钥: {secret_key}")
    
    def get_proxy_list(self, num: int = 10, time_duration: int = 15, 
                      port_type: int = 1, return_type: str = "json",
                      show_city: bool = False, show_isp: bool = False,
                      show_expire: bool = False) -> Optional[List[Dict]]:
        """
        获取代理IP列表
        
        Args:
            num: 提取IP数量 (1-200)
            time_duration: IP使用时长 (3,5,10,15分钟)
            port_type: IP协议 (1:HTTP, 2:HTTPS, 3:SOCKS5)
            return_type: 返回类型 (txt/json)
            show_city: 是否显示城市
            show_isp: 是否显示运营商
            show_expire: 是否显示过期时间
            
        Returns:
            代理IP列表或None
        """
        
        # 构建请求参数
        params = {
            "secret": self.secret_key,
            "num": num,
            "type": return_type,
            "port": port_type,
            "time": time_duration,
            "mr": 1,  # 去重
            "sign": "daff68445da273a1af5904e239658f7d"  # 使用您成功的签名
        }
        
        # 添加可选参数
        if show_city:
            params["cs"] = 1
        if show_isp:
            params["ys"] = 1
        if show_expire:
            params["ts"] = 1
        
        try:
            print(f"🔍 正在获取 {num} 个代理IP...")
            print(f"📋 请求参数: {params}")
            
            response = requests.get(self.api_url, params=params, timeout=15)
            
            if response.status_code == 200:
                if return_type == "json":
                    data = response.json()
                    print(f"📊 API响应: {data}")
                    
                    if data.get("code") == 1000:
                        proxy_list = data.get("data", [])
                        print(f"✅ 成功获取 {len(proxy_list)} 个代理IP")
                        
                        # 保存当前代理列表
                        self.current_proxies = proxy_list
                        
                        # 显示代理信息
                        self.display_proxy_list(proxy_list)
                        
                        return proxy_list
                    else:
                        error_msg = data.get("msg", "未知错误")
                        print(f"❌ API错误: {error_msg}")
                        return None
                else:
                    # TXT格式
                    print(f"✅ TXT响应: {response.text}")
                    return response.text.strip().split('\n')
            else:
                print(f"❌ HTTP请求失败: {response.status_code}")
                print(f"错误内容: {response.text}")
                return None
                
        except Exception as e:
            print(f"❌ 获取代理失败: {e}")
            return None
    
    def display_proxy_list(self, proxy_list: List[Dict]):
        """显示代理列表"""
        print(f"\n📋 代理IP列表:")
        print("-" * 60)
        
        for i, proxy in enumerate(proxy_list):
            ip = proxy.get("ip", "N/A")
            port = proxy.get("port", "N/A")
            city = proxy.get("city", "")
            isp = proxy.get("isp", "")
            expire = proxy.get("expire", "")
            
            info_parts = []
            if city:
                info_parts.append(f"城市:{city}")
            if isp:
                info_parts.append(f"运营商:{isp}")
            if expire:
                info_parts.append(f"过期:{expire}")
            
            info_str = " | ".join(info_parts)
            if info_str:
                print(f"  {i+1:2d}. {ip}:{port} ({info_str})")
            else:
                print(f"  {i+1:2d}. {ip}:{port}")
    
    def test_proxy_connection(self, proxy_info: Dict) -> bool:
        """
        测试单个代理的连接
        
        Args:
            proxy_info: 代理信息字典
            
        Returns:
            连接是否成功
        """
        ip = proxy_info.get("ip")
        port = proxy_info.get("port")
        
        if not ip or not port:
            print("❌ 代理信息不完整")
            return False
        
        print(f"\n🧪 测试代理连接: {ip}:{port}")
        
        # 构建代理配置
        proxies = {
            'http': f'http://{ip}:{port}',
            'https': f'http://{ip}:{port}'
        }
        
        try:
            # 测试获取IP
            response = requests.get("http://httpbin.org/ip", proxies=proxies, timeout=10)
            
            if response.status_code == 200:
                result = response.json()
                current_ip = result.get('origin', 'Unknown')
                print(f"✅ 代理连接成功！当前IP: {current_ip}")
                
                # 测试访问目标网站
                test_response = requests.get("https://www.baidu.com", proxies=proxies, timeout=10)
                if test_response.status_code == 200:
                    print(f"✅ 目标网站访问成功！")
                    return True
                else:
                    print(f"⚠️  目标网站访问失败: {test_response.status_code}")
                    return False
            else:
                print(f"❌ 代理连接失败: {response.status_code}")
                return False
                
        except Exception as e:
            print(f"❌ 代理测试异常: {e}")
            return False
    
    def test_all_proxies(self) -> List[Dict]:
        """
        测试所有当前代理的连接
        
        Returns:
            可用的代理列表
        """
        if not self.current_proxies:
            print("❌ 没有可测试的代理")
            return []
        
        print(f"\n🧪 开始测试 {len(self.current_proxies)} 个代理的连接...")
        
        working_proxies = []
        
        for i, proxy in enumerate(self.current_proxies):
            print(f"\n--- 测试代理 {i+1}/{len(self.current_proxies)} ---")
            
            if self.test_proxy_connection(proxy):
                working_proxies.append(proxy)
                print(f"✅ 代理 {i+1} 可用")
            else:
                print(f"❌ 代理 {i+1} 不可用")
        
        print(f"\n📊 测试完成！可用代理: {len(working_proxies)}/{len(self.current_proxies)}")
        
        return working_proxies
    
    def get_random_proxy(self) -> Optional[Dict]:
        """
        获取一个随机可用的代理
        
        Returns:
            随机代理信息或None
        """
        if not self.current_proxies:
            print("❌ 没有可用的代理")
            return None
        
        proxy = random.choice(self.current_proxies)
        print(f"🎲 随机选择代理: {proxy['ip']}:{proxy['port']}")
        
        return proxy
    
    def use_proxy_for_request(self, url: str, proxy_info: Dict = None, **kwargs) -> Optional[requests.Response]:
        """
        使用代理发送请求
        
        Args:
            url: 目标URL
            proxy_info: 代理信息，如果为None则随机选择
            **kwargs: requests.get的其他参数
            
        Returns:
            响应对象或None
        """
        if proxy_info is None:
            proxy_info = self.get_random_proxy()
        
        if not proxy_info:
            return None
        
        ip = proxy_info.get("ip")
        port = proxy_info.get("port")
        
        proxies = {
            'http': f'http://{ip}:{port}',
            'https': f'http://{ip}:{port}'
        }
        
        try:
            print(f"🌐 使用代理 {ip}:{port} 访问: {url}")
            
            response = requests.get(url, proxies=proxies, timeout=10, **kwargs)
            
            if response.status_code == 200:
                print(f"✅ 请求成功: {response.status_code}")
                return response
            else:
                print(f"⚠️  请求失败: {response.status_code}")
                return response
                
        except Exception as e:
            print(f"❌ 请求异常: {e}")
            return None

def main():
    """主函数 - 演示天启代理的使用"""
    print("🎯 天启代理客户端演示")
    print("=" * 60)
    
    # 读取API密钥
    try:
        with open('ip_keys.txt', 'r') as f:
            secret_key = f.read().strip()
    except Exception as e:
        print(f"❌ 读取密钥失败: {e}")
        return
    
    # 创建代理客户端
    client = TianqiProxyClient(secret_key)
    
    # 获取代理列表
    print(f"\n{'='*40}")
    print("📡 获取代理IP列表")
    print(f"{'='*40}")
    
    proxy_list = client.get_proxy_list(
        num=5,              # 获取5个IP
        time_duration=15,   # 15分钟时效
        port_type=1,        # HTTP协议
        return_type="json", # JSON格式
        show_city=True,     # 显示城市
        show_isp=True       # 显示运营商
    )
    
    if proxy_list:
        # 测试所有代理
        print(f"\n{'='*40}")
        print("🧪 测试代理连接")
        print(f"{'='*40}")
        
        working_proxies = client.test_all_proxies()
        
        if working_proxies:
            # 使用代理发送请求示例
            print(f"\n{'='*40}")
            print("🌐 使用代理发送请求示例")
            print(f"{'='*40}")
            
            # 示例1: 获取当前IP
            response = client.use_proxy_for_request("http://httpbin.org/ip")
            if response:
                print(f"当前IP信息: {response.json()}")
            
            # 示例2: 访问百度
            response = client.use_proxy_for_request("https://www.baidu.com")
            if response:
                print(f"百度访问成功，页面大小: {len(response.content)} 字节")
        
        print(f"\n🎉 天启代理演示完成！")
        print(f"✅ 您现在可以在项目中使用这些代理了")
    else:
        print(f"❌ 获取代理失败")

if __name__ == "__main__":
    main()

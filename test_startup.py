#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试程序启动
"""

import sys
import os

def test_imports():
    """测试导入"""
    
    print("🔍 测试程序导入")
    print("=" * 50)
    
    try:
        # 测试主要模块导入
        print("📋 测试基础模块导入...")
        
        import tkinter as tk
        print("✅ tkinter 导入成功")
        
        from tkinter import ttk, messagebox, filedialog
        print("✅ tkinter 子模块导入成功")
        
        import threading
        print("✅ threading 导入成功")
        
        import queue
        print("✅ queue 导入成功")
        
        from typing import List, Dict, Optional
        print("✅ typing 导入成功")
        
        # 测试自定义模块导入
        print("\n📋 测试自定义模块导入...")
        
        try:
            from date_match_extractor import DateMatchExtractor
            print("✅ date_match_extractor 导入成功")
        except ImportError as e:
            print(f"❌ date_match_extractor 导入失败: {e}")
        
        try:
            from league_selection_dialog import show_league_selection_dialog
            print("✅ league_selection_dialog 导入成功")
        except ImportError as e:
            print(f"❌ league_selection_dialog 导入失败: {e}")
        
        # 测试主UI模块导入
        print("\n📋 测试主UI模块导入...")
        
        try:
            from odds_scraper_ui import OddsScraperGUI
            print("✅ odds_scraper_ui 导入成功")
        except Exception as e:
            print(f"❌ odds_scraper_ui 导入失败: {e}")
            return False
        
        print("\n✅ 所有模块导入测试完成")
        return True
        
    except Exception as e:
        print(f"❌ 导入测试失败: {e}")
        return False

def test_class_creation():
    """测试类创建"""
    
    print(f"\n🔧 测试类创建")
    print("-" * 30)
    
    try:
        import tkinter as tk
        from odds_scraper_ui import OddsScraperGUI
        
        # 创建根窗口（但不显示）
        root = tk.Tk()
        root.withdraw()  # 隐藏窗口
        
        print("📋 创建主UI类...")
        
        # 创建UI类实例
        app = OddsScraperGUI(root)
        print("✅ OddsScraperGUI 类创建成功")
        
        # 检查关键方法是否存在
        methods_to_check = [
            'start_date_scraping',
            'date_scraping_worker',
            'handle_league_selection',
            'start_date_batch_scraping'
        ]
        
        print("\n📊 检查新增方法:")
        for method in methods_to_check:
            if hasattr(app, method):
                print(f"  ✅ {method}")
            else:
                print(f"  ❌ {method}")
        
        # 检查关键属性是否存在
        attributes_to_check = [
            'date_url_var',
            'date_scrape_button'
        ]
        
        print("\n📊 检查新增属性:")
        for attr in attributes_to_check:
            if hasattr(app, attr):
                print(f"  ✅ {attr}")
            else:
                print(f"  ❌ {attr}")
        
        # 清理
        root.destroy()
        
        print("\n✅ 类创建测试完成")
        return True
        
    except Exception as e:
        print(f"❌ 类创建测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_ui_startup():
    """测试UI启动（不显示界面）"""
    
    print(f"\n🚀 测试UI启动")
    print("-" * 30)
    
    try:
        import tkinter as tk
        from odds_scraper_ui import OddsScraperGUI
        
        # 创建根窗口
        root = tk.Tk()
        root.withdraw()  # 隐藏窗口
        
        # 创建应用
        app = OddsScraperGUI(root)
        
        # 测试一些基本功能
        print("📋 测试基本功能...")
        
        # 测试日期URL变量
        if hasattr(app, 'date_url_var'):
            default_url = app.date_url_var.get()
            print(f"  ✅ 默认日期URL: {default_url}")
        
        # 测试按钮状态
        if hasattr(app, 'date_scrape_button'):
            button_state = app.date_scrape_button['state']
            print(f"  ✅ 按日期抓取按钮状态: {button_state}")
        
        # 清理
        root.destroy()
        
        print("✅ UI启动测试完成")
        return True
        
    except Exception as e:
        print(f"❌ UI启动测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🎯 程序启动测试")
    print("=" * 60)
    
    success = True
    
    # 测试导入
    if not test_imports():
        success = False
    
    # 测试类创建
    if not test_class_creation():
        success = False
    
    # 测试UI启动
    if not test_ui_startup():
        success = False
    
    print(f"\n{'='*60}")
    if success:
        print("🎉 所有测试通过！程序应该可以正常启动")
        print("\n🚀 现在可以运行主程序:")
        print("python odds_scraper_ui.py")
    else:
        print("❌ 部分测试失败，需要检查问题")
    
    print(f"\n📝 如果仍有问题，请检查:")
    print("1. 所有依赖模块是否正确安装")
    print("2. 文件路径是否正确")
    print("3. Python版本是否兼容")
    print("4. 是否有语法错误")

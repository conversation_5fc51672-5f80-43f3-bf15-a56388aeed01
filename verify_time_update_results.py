#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证时间更新结果
检查所有比赛的时间信息是否已正确更新
"""

import sqlite3
import logging
from datetime import datetime
from collections import Counter

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def verify_time_update_results():
    """验证时间更新结果"""
    try:
        print("🔍 验证时间更新结果...")
        print("=" * 80)
        
        with sqlite3.connect("odds_data.db") as conn:
            cursor = conn.cursor()
            
            # 1. 总体统计
            cursor.execute("SELECT COUNT(*) FROM matches")
            total_matches = cursor.fetchone()[0]
            
            cursor.execute("SELECT COUNT(*) FROM matches WHERE accurate_datetime IS NOT NULL AND accurate_datetime != ''")
            accurate_time_count = cursor.fetchone()[0]
            
            cursor.execute("SELECT COUNT(*) FROM matches WHERE time_source = 'analysis'")
            analysis_source_count = cursor.fetchone()[0]
            
            print(f"📊 总体统计:")
            print(f"   - 总比赛数: {total_matches}")
            print(f"   - 有准确时间: {accurate_time_count}")
            print(f"   - 来自分析页面: {analysis_source_count}")
            print(f"   - 完成率: {(accurate_time_count/total_matches*100):.1f}%")
            
            # 2. 时间来源分布
            cursor.execute("SELECT time_source, COUNT(*) FROM matches GROUP BY time_source")
            time_source_distribution = dict(cursor.fetchall())
            
            print(f"\n📈 时间来源分布:")
            for source, count in sorted(time_source_distribution.items()):
                percentage = (count / total_matches) * 100
                print(f"   - {source or '未设置'}: {count} 场比赛 ({percentage:.1f}%)")
            
            # 3. 年份分布
            cursor.execute("SELECT match_year, COUNT(*) FROM matches WHERE match_year IS NOT NULL GROUP BY match_year ORDER BY match_year")
            year_distribution = cursor.fetchall()
            
            print(f"\n📅 年份分布:")
            for year, count in year_distribution:
                percentage = (count / total_matches) * 100
                print(f"   - {year}年: {count} 场比赛 ({percentage:.1f}%)")
            
            # 4. 月份分布
            cursor.execute("SELECT match_month, COUNT(*) FROM matches WHERE match_month IS NOT NULL GROUP BY match_month ORDER BY match_month")
            month_distribution = cursor.fetchall()
            
            print(f"\n📆 月份分布:")
            for month, count in month_distribution:
                percentage = (count / total_matches) * 100
                print(f"   - {month}月: {count} 场比赛 ({percentage:.1f}%)")
            
            # 5. 星期分布
            cursor.execute("SELECT weekday, COUNT(*) FROM matches WHERE weekday IS NOT NULL GROUP BY weekday")
            weekday_distribution = cursor.fetchall()
            
            print(f"\n📋 星期分布:")
            weekday_order = ['星期一', '星期二', '星期三', '星期四', '星期五', '星期六', '星期日']
            weekday_dict = dict(weekday_distribution)
            for weekday in weekday_order:
                count = weekday_dict.get(weekday, 0)
                if count > 0:
                    percentage = (count / total_matches) * 100
                    print(f"   - {weekday}: {count} 场比赛 ({percentage:.1f}%)")
            
            # 6. 时间段分布
            cursor.execute("SELECT match_hour, COUNT(*) FROM matches WHERE match_hour IS NOT NULL GROUP BY match_hour ORDER BY match_hour")
            hour_distribution = cursor.fetchall()
            
            print(f"\n🕐 时间段分布:")
            for hour, count in hour_distribution:
                percentage = (count / total_matches) * 100
                print(f"   - {hour}时: {count} 场比赛 ({percentage:.1f}%)")
            
            # 7. 样本数据展示
            cursor.execute("""
                SELECT match_id, league, accurate_datetime, accurate_date, accurate_time, weekday, time_source
                FROM matches 
                WHERE accurate_datetime IS NOT NULL 
                ORDER BY accurate_datetime DESC 
                LIMIT 10
            """)
            sample_matches = cursor.fetchall()
            
            print(f"\n📋 最新10场比赛样本:")
            print("-" * 100)
            print(f"{'比赛ID':<10} {'联赛':<15} {'完整时间':<20} {'日期':<12} {'时间':<8} {'星期':<8} {'来源':<10}")
            print("-" * 100)
            
            for match in sample_matches:
                match_id, league, accurate_datetime, accurate_date, accurate_time, weekday, time_source = match
                league = (league or '')[:14]
                print(f"{match_id:<10} {league:<15} {accurate_datetime:<20} {accurate_date:<12} {accurate_time:<8} {weekday:<8} {time_source:<10}")
            
            # 8. 数据质量检查
            print(f"\n🔍 数据质量检查:")
            
            # 检查缺失数据
            cursor.execute("SELECT COUNT(*) FROM matches WHERE accurate_datetime IS NULL OR accurate_datetime = ''")
            missing_datetime = cursor.fetchone()[0]
            
            cursor.execute("SELECT COUNT(*) FROM matches WHERE weekday IS NULL OR weekday = ''")
            missing_weekday = cursor.fetchone()[0]
            
            cursor.execute("SELECT COUNT(*) FROM matches WHERE time_source IS NULL OR time_source = ''")
            missing_source = cursor.fetchone()[0]
            
            print(f"   - 缺失完整时间: {missing_datetime} 场比赛")
            print(f"   - 缺失星期信息: {missing_weekday} 场比赛")
            print(f"   - 缺失来源标记: {missing_source} 场比赛")
            
            # 检查时间格式
            cursor.execute("""
                SELECT COUNT(*) FROM matches 
                WHERE accurate_datetime IS NOT NULL 
                AND accurate_datetime NOT LIKE '____-__-__ __:__:__'
            """)
            invalid_format = cursor.fetchone()[0]
            
            print(f"   - 时间格式异常: {invalid_format} 场比赛")
            
            # 9. 更新前后对比
            cursor.execute("""
                SELECT COUNT(*) FROM matches 
                WHERE accurate_datetime != match_time 
                AND accurate_datetime IS NOT NULL 
                AND match_time IS NOT NULL
            """)
            time_changed = cursor.fetchone()[0]
            
            print(f"\n⚖️  更新效果:")
            print(f"   - 时间信息有变化: {time_changed} 场比赛")
            print(f"   - 时间信息无变化: {total_matches - time_changed} 场比赛")
            
            # 10. 总结
            print(f"\n" + "=" * 80)
            if accurate_time_count == total_matches and missing_datetime == 0:
                print("✅ 所有比赛的时间信息已成功更新！")
                print("🎉 数据质量检查通过，时间信息完整准确")
            else:
                print("⚠️  部分比赛的时间信息可能需要进一步处理")
            
            print(f"\n💡 更新总结:")
            print(f"   - 成功率: {(accurate_time_count/total_matches*100):.1f}%")
            print(f"   - 数据来源: 主要来自比赛分析页面")
            print(f"   - 时间精度: 精确到分钟级别")
            print(f"   - 附加信息: 包含星期和详细时间组件")
            
            return True
            
    except Exception as e:
        logger.error(f"验证失败: {e}")
        return False

def main():
    """主函数"""
    print("🚀 开始验证时间更新结果")
    
    if verify_time_update_results():
        print("\n✅ 验证完成！")
    else:
        print("\n❌ 验证失败！")

if __name__ == "__main__":
    main()

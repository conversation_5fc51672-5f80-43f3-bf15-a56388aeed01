# 最近比赛功能实现说明

## 🎯 功能概述

根据您的要求，我已经成功为赔率组合观察工具添加了"最近比赛"功能，包含以下组件：

1. **最近天数输入控件** - 可以输入整数，默认值为0
2. **最近比赛列表** - 显示查询到的比赛
3. **显示最近比赛按钮** - 执行查询操作
4. **点击选择功能** - 点击比赛自动填入比赛ID

## 🔧 实现细节

### 1. 数据库层面新增方法

在 `odds_combination_database.py` 中新增了 `get_recent_matches()` 方法：

```python
def get_recent_matches(self, days: int) -> List[Dict]:
    """
    获取最近指定天数内的比赛
    
    Args:
        days: 天数，0表示今天，1表示昨天和今天，以此类推
        
    Returns:
        比赛信息列表，按开赛时间从早到晚排序
    """
```

**查询逻辑：**
- `days = 0`: 只查询今天的比赛
- `days = 1`: 查询昨天和今天的比赛
- `days = N`: 查询从(今天-N天)到今天的比赛

**时间字段优先级：**
1. 优先使用 `accurate_date` 字段
2. 备用 `match_date` 字段
3. 按 `accurate_datetime` 或组合时间排序

### 2. UI界面新增控件

在 `odds_combination_ui.py` 中的控制面板添加了：

```python
# 最近天数输入
self.recent_days_input = QSpinBox()
self.recent_days_input.setMinimum(0)
self.recent_days_input.setMaximum(365)
self.recent_days_input.setValue(0)  # 默认值0

# 显示最近比赛按钮
self.show_recent_matches_button = QPushButton("显示最近比赛")

# 最近比赛列表
self.recent_matches_list = QListWidget()
self.recent_matches_list.setMaximumHeight(120)  # 限制高度
```

### 3. 功能处理方法

新增了两个主要方法：

#### `show_recent_matches()`
- 获取用户输入的天数
- 调用数据库查询方法
- 格式化显示比赛信息
- 更新状态信息

#### `on_recent_match_selected()`
- 处理用户点击比赛列表项
- 自动提取比赛ID
- 填入"比赛ID"输入框
- 更新状态提示

## 📋 界面布局

新功能在现有界面中的位置：

```
数据库: [选择数据库 ▼]
比赛ID: [输入框]

最近天数: [0] [显示最近比赛]
最近比赛: [比赛列表 - 最大高度120px]

博彩公司: [bet365 ▼]
组合数: [3]
...
```

## 🎮 使用方法

### 步骤1：设置天数
在"最近天数"输入框中输入数字：
- `0` - 查询今天的比赛
- `1` - 查询昨天和今天的比赛
- `7` - 查询最近一周的比赛
- `30` - 查询最近一个月的比赛

### 步骤2：查询比赛
点击"显示最近比赛"按钮，系统会：
- 查询数据库中符合条件的比赛
- 在列表中显示比赛信息
- 在状态栏显示查询结果

### 步骤3：选择比赛
在比赛列表中点击任意比赛，系统会：
- 自动提取该比赛的ID
- 填入"比赛ID"输入框
- 显示选择确认信息

### 步骤4：开始分析
比赛ID填入后，可以正常使用其他分析功能。

## 📊 显示格式

比赛列表中每项的显示格式：
```
比赛ID | 开赛时间 | 联赛 | 主队 vs 客队
```

示例：
```
2701757 | 2024-04-28 15:30:00 | 英超 | 曼城 vs 热刺
2701758 | 2024-04-28 18:00:00 | 西甲 | 皇马 vs 巴萨
```

## ⚠️ 注意事项

### 1. 数据依赖
- 功能依赖数据库中的时间字段
- 如果数据库中没有最近的比赛，列表会为空
- 建议先用较大的天数（如30、90）测试

### 2. 性能考虑
- 查询大时间范围可能较慢
- 列表高度限制为120px，避免界面过长
- 只显示必要的比赛信息

### 3. 兼容性
- 完全不影响现有功能
- 所有原有操作保持不变
- 新功能为可选使用

## 🔍 技术实现亮点

### 1. 智能时间处理
```python
# 优先使用准确时间，备用原始时间
WHERE (
    (accurate_date IS NOT NULL AND accurate_date BETWEEN ? AND ?) OR
    (accurate_date IS NULL AND match_date IS NOT NULL AND match_date BETWEEN ? AND ?)
)
```

### 2. 用户友好的显示
```python
# 格式化显示用的开赛时间
if match_info.get('accurate_datetime'):
    match_info['display_time'] = match_info['accurate_datetime']
elif match_info.get('accurate_date'):
    time_part = match_info.get('match_time', '00:00:00')
    match_info['display_time'] = f"{match_info['accurate_date']} {time_part}"
```

### 3. 数据存储和提取
```python
# 在列表项中存储比赛ID
item.setData(Qt.UserRole, match_id)

# 点击时提取比赛ID
match_id = item.data(Qt.UserRole)
```

## ✅ 功能验证

新功能已经实现并可以正常使用：

1. ✅ 最近天数输入控件（默认值0）
2. ✅ 比赛列表显示
3. ✅ 显示最近比赛按钮
4. ✅ 数据库查询功能
5. ✅ 点击选择自动填入比赛ID
6. ✅ 按开赛时间排序
7. ✅ 不影响现有功能

## 🚀 后续优化建议

1. **缓存机制** - 对查询结果进行缓存
2. **分页显示** - 当比赛数量很多时分页显示
3. **筛选功能** - 按联赛、球队等条件筛选
4. **快捷按钮** - 添加"今天"、"本周"等快捷选择

新功能已经完全按照您的要求实现，可以立即使用！

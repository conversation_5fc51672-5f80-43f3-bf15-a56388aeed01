#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
领涨公司识别算法
识别在大多数比赛中率先调整赔率的'风向标'公司，分析其预测价值
"""

import pandas as pd
import numpy as np
import json
import os
from datetime import datetime
import matplotlib.pyplot as plt
import seaborn as sns
from collections import defaultdict, Counter
import warnings
warnings.filterwarnings('ignore')

class LeadingCompanyIdentifier:
    def __init__(self, normalized_data_file=None):
        self.normalized_data_file = normalized_data_file
        self.normalized_data = None
        self.leading_analysis_results = {}
        
        # 领先检测参数
        self.min_change_threshold = 0.02  # 最小变化阈值（2%）
        self.leading_time_window = 2.0    # 领先时间窗口（小时）
        self.min_follow_companies = 2     # 最少跟随公司数
        
    def load_normalized_data(self, filename=None):
        """加载标准化的赔率数据"""
        if filename:
            self.normalized_data_file = filename
        
        if not self.normalized_data_file:
            # 查找最新的标准化数据文件
            files = [f for f in os.listdir('.') if f.startswith('normalized_odds_') and f.endswith('.json')]
            if files:
                self.normalized_data_file = sorted(files)[-1]
                print(f"自动选择最新文件: {self.normalized_data_file}")
            else:
                print("未找到标准化数据文件")
                return None
        
        with open(self.normalized_data_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        self.normalized_data = data
        print(f"加载标准化数据: {self.normalized_data_file}")
        return data
    
    def detect_leading_events(self, match_data):
        """检测单场比赛中的领涨事件"""
        match_id = match_data['match_id']
        companies_data = match_data['companies_data']
        
        if len(companies_data) < 3:  # 至少需要3家公司
            return None
        
        leading_events = []
        
        # 为每种赔率类型检测领涨事件
        for odds_type in ['home', 'draw', 'away']:
            odds_key = f'{odds_type}_odds'
            
            # 收集所有公司的变化事件
            all_changes = []
            
            for company, company_data in companies_data.items():
                if odds_key not in company_data:
                    continue
                
                time_points = company_data['time_points']
                odds_series = company_data[odds_key]
                
                # 识别显著变化点
                for i in range(1, len(odds_series)):
                    change_pct = (odds_series[i] - odds_series[i-1]) / odds_series[i-1] * 100
                    if abs(change_pct) >= self.min_change_threshold * 100:
                        all_changes.append({
                            'company': company,
                            'time': time_points[i],
                            'change_pct': change_pct,
                            'direction': 'up' if change_pct > 0 else 'down',
                            'from_odds': odds_series[i-1],
                            'to_odds': odds_series[i]
                        })
            
            # 按时间排序
            all_changes.sort(key=lambda x: x['time'])
            
            # 检测领涨事件
            leading_events_for_odds = self._identify_leading_events(all_changes, odds_type)
            leading_events.extend(leading_events_for_odds)
        
        return {
            'match_id': match_id,
            'leading_events': leading_events,
            'total_events': len(leading_events),
            'companies_count': len(companies_data)
        }
    
    def _identify_leading_events(self, all_changes, odds_type):
        """从变化序列中识别领涨事件"""
        leading_events = []
        
        i = 0
        while i < len(all_changes):
            current_change = all_changes[i]
            leader_company = current_change['company']
            leader_time = current_change['time']
            leader_direction = current_change['direction']
            
            # 寻找在时间窗口内跟随的公司
            followers = []
            j = i + 1
            
            while (j < len(all_changes) and 
                   all_changes[j]['time'] <= leader_time + self.leading_time_window):
                
                follower_change = all_changes[j]
                
                # 检查是否为同方向跟随
                if (follower_change['direction'] == leader_direction and
                    follower_change['company'] != leader_company):
                    
                    followers.append({
                        'company': follower_change['company'],
                        'time': follower_change['time'],
                        'delay': follower_change['time'] - leader_time,
                        'change_pct': follower_change['change_pct']
                    })
                
                j += 1
            
            # 如果有足够的跟随者，记录为领涨事件
            if len(followers) >= self.min_follow_companies:
                leading_event = {
                    'leader': {
                        'company': leader_company,
                        'time': leader_time,
                        'change_pct': current_change['change_pct'],
                        'direction': leader_direction
                    },
                    'followers': followers,
                    'followers_count': len(followers),
                    'odds_type': odds_type,
                    'avg_follow_delay': np.mean([f['delay'] for f in followers]),
                    'follow_strength': self._calculate_follow_strength(current_change, followers)
                }
                
                leading_events.append(leading_event)
            
            i += 1
        
        return leading_events
    
    def _calculate_follow_strength(self, leader_change, followers):
        """计算跟随强度"""
        leader_magnitude = abs(leader_change['change_pct'])
        
        # 跟随者变化幅度的相似性
        follower_magnitudes = [abs(f['change_pct']) for f in followers]
        
        if not follower_magnitudes:
            return 0
        
        # 计算幅度相似性（使用相关系数的简化版本）
        magnitude_similarity = 1 - (np.std(follower_magnitudes + [leader_magnitude]) / 
                                   np.mean(follower_magnitudes + [leader_magnitude]))
        
        # 时间紧密性（延迟越小，强度越高）
        delays = [f['delay'] for f in followers]
        avg_delay = np.mean(delays)
        time_tightness = 1 / (1 + avg_delay)  # 延迟越小，紧密性越高
        
        # 跟随者数量权重
        count_weight = min(len(followers) / 5, 1)  # 最多5家公司给满分
        
        # 综合跟随强度
        follow_strength = (magnitude_similarity * 0.4 + 
                          time_tightness * 0.4 + 
                          count_weight * 0.2)
        
        return max(0, min(1, follow_strength))
    
    def analyze_all_matches(self):
        """分析所有比赛的领涨事件"""
        if self.normalized_data is None:
            print("请先加载标准化数据")
            return None
        
        print("\n=== 开始领涨公司识别分析 ===")
        
        all_leading_results = {}
        total_leading_events = 0
        
        for db_name, db_data in self.normalized_data.items():
            print(f"\n处理数据库: {db_name}")
            db_leading_results = {}
            
            for match_id, match_data in db_data.items():
                print(f"  分析比赛: {match_id}")
                
                leading_result = self.detect_leading_events(match_data)
                if leading_result and leading_result['total_events'] > 0:
                    db_leading_results[match_id] = leading_result
                    total_leading_events += leading_result['total_events']
                    print(f"    发现 {leading_result['total_events']} 个领涨事件")
                else:
                    print(f"    未发现领涨事件")
            
            if db_leading_results:
                all_leading_results[db_name] = db_leading_results
        
        print(f"\n总计发现 {total_leading_events} 个领涨事件")
        
        self.leading_analysis_results = all_leading_results
        return all_leading_results
    
    def calculate_leading_statistics(self):
        """计算领涨统计"""
        if not self.leading_analysis_results:
            print("请先进行领涨分析")
            return None
        
        print("\n=== 领涨统计分析 ===")
        
        # 公司领涨统计
        company_leading_stats = defaultdict(lambda: {
            'total_leading': 0,
            'leading_by_odds': defaultdict(int),
            'avg_follow_delay': [],
            'avg_follow_strength': [],
            'followers_count': [],
            'success_rate': 0
        })
        
        # 跟随统计
        company_following_stats = defaultdict(lambda: {
            'total_following': 0,
            'avg_follow_delay': [],
            'leaders_followed': defaultdict(int)
        })
        
        for db_name, db_results in self.leading_analysis_results.items():
            for match_id, match_result in db_results.items():
                for event in match_result['leading_events']:
                    leader_company = event['leader']['company']
                    odds_type = event['odds_type']
                    
                    # 更新领涨统计
                    company_leading_stats[leader_company]['total_leading'] += 1
                    company_leading_stats[leader_company]['leading_by_odds'][odds_type] += 1
                    company_leading_stats[leader_company]['avg_follow_delay'].append(event['avg_follow_delay'])
                    company_leading_stats[leader_company]['avg_follow_strength'].append(event['follow_strength'])
                    company_leading_stats[leader_company]['followers_count'].append(event['followers_count'])
                    
                    # 更新跟随统计
                    for follower in event['followers']:
                        follower_company = follower['company']
                        company_following_stats[follower_company]['total_following'] += 1
                        company_following_stats[follower_company]['avg_follow_delay'].append(follower['delay'])
                        company_following_stats[follower_company]['leaders_followed'][leader_company] += 1
        
        # 计算平均值
        for company, stats in company_leading_stats.items():
            if stats['avg_follow_delay']:
                stats['avg_follow_delay'] = np.mean(stats['avg_follow_delay'])
                stats['avg_follow_strength'] = np.mean(stats['avg_follow_strength'])
                stats['avg_followers_count'] = np.mean(stats['followers_count'])
        
        for company, stats in company_following_stats.items():
            if stats['avg_follow_delay']:
                stats['avg_follow_delay'] = np.mean(stats['avg_follow_delay'])
        
        # 输出领涨排名
        print(f"\n公司领涨能力排名:")
        sorted_leaders = sorted(company_leading_stats.items(), 
                              key=lambda x: x[1]['total_leading'], reverse=True)
        
        for company, stats in sorted_leaders[:10]:
            print(f"  {company}: {stats['total_leading']}次领涨, "
                  f"平均跟随强度{stats['avg_follow_strength']:.3f}, "
                  f"平均跟随者{stats['avg_followers_count']:.1f}家")
        
        # 输出跟随排名
        print(f"\n公司跟随行为排名:")
        sorted_followers = sorted(company_following_stats.items(), 
                                key=lambda x: x[1]['total_following'], reverse=True)
        
        for company, stats in sorted_followers[:10]:
            print(f"  {company}: {stats['total_following']}次跟随, "
                  f"平均延迟{stats['avg_follow_delay']:.2f}小时")
        
        leading_stats = {
            'leading_stats': dict(company_leading_stats),
            'following_stats': dict(company_following_stats)
        }
        
        return leading_stats
    
    def identify_market_leaders(self):
        """识别市场领导者"""
        if not self.leading_analysis_results:
            print("请先进行领涨分析")
            return None
        
        print("\n=== 市场领导者识别 ===")
        
        # 计算领导力指数
        leadership_scores = defaultdict(float)
        
        for db_name, db_results in self.leading_analysis_results.items():
            for match_id, match_result in db_results.items():
                for event in match_result['leading_events']:
                    leader_company = event['leader']['company']
                    
                    # 领导力得分 = 跟随强度 × 跟随者数量权重
                    follow_strength = event['follow_strength']
                    followers_weight = min(event['followers_count'] / 5, 1)
                    leadership_score = follow_strength * (0.7 + 0.3 * followers_weight)
                    
                    leadership_scores[leader_company] += leadership_score
        
        # 标准化得分
        max_score = max(leadership_scores.values()) if leadership_scores else 1
        for company in leadership_scores:
            leadership_scores[company] /= max_score
        
        # 识别不同类型的领导者
        market_leaders = {
            'overall_leaders': [],      # 综合领导者
            'specialist_leaders': {},   # 专业领导者（按赔率类型）
            'consistent_leaders': [],   # 一致性领导者
            'influential_leaders': []   # 影响力领导者
        }
        
        # 综合领导者（总得分最高）
        sorted_leaders = sorted(leadership_scores.items(), key=lambda x: x[1], reverse=True)
        market_leaders['overall_leaders'] = sorted_leaders[:5]
        
        print(f"综合市场领导者:")
        for company, score in market_leaders['overall_leaders']:
            print(f"  {company}: 领导力指数 {score:.3f}")
        
        # 专业领导者（在特定赔率类型上领先）
        for odds_type in ['home', 'draw', 'away']:
            type_scores = defaultdict(float)
            
            for db_name, db_results in self.leading_analysis_results.items():
                for match_id, match_result in db_results.items():
                    for event in match_result['leading_events']:
                        if event['odds_type'] == odds_type:
                            leader_company = event['leader']['company']
                            type_scores[leader_company] += event['follow_strength']
            
            if type_scores:
                top_specialist = max(type_scores.items(), key=lambda x: x[1])
                market_leaders['specialist_leaders'][odds_type] = top_specialist
                print(f"{odds_type.upper()}赔率专业领导者: {top_specialist[0]} (得分: {top_specialist[1]:.3f})")
        
        return market_leaders
    
    def save_leading_analysis_results(self, filename_prefix="leading_analysis"):
        """保存领涨分析结果"""
        if not self.leading_analysis_results:
            print("没有领涨分析结果可保存")
            return None
        
        output_file = f"{filename_prefix}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        
        # 转换为可序列化的格式
        serializable_results = self._make_serializable(self.leading_analysis_results)
        
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(serializable_results, f, ensure_ascii=False, indent=2)
        
        print(f"\n领涨分析结果已保存到: {output_file}")
        return output_file
    
    def _make_serializable(self, obj):
        """将对象转换为可序列化的格式"""
        if isinstance(obj, dict):
            return {k: self._make_serializable(v) for k, v in obj.items()}
        elif isinstance(obj, (list, tuple)):
            return [self._make_serializable(item) for item in obj]
        elif isinstance(obj, (np.integer, np.int64)):
            return int(obj)
        elif isinstance(obj, (np.floating, np.float64)):
            return float(obj)
        elif isinstance(obj, (np.bool_, bool)):
            return bool(obj)
        elif isinstance(obj, np.ndarray):
            return obj.tolist()
        elif pd.isna(obj):
            return None
        else:
            return obj

def main():
    identifier = LeadingCompanyIdentifier()
    
    # 加载标准化数据
    normalized_data = identifier.load_normalized_data()
    if not normalized_data:
        return
    
    # 分析领涨事件
    leading_results = identifier.analyze_all_matches()
    
    # 计算领涨统计
    leading_stats = identifier.calculate_leading_statistics()
    
    # 识别市场领导者
    market_leaders = identifier.identify_market_leaders()
    
    # 保存结果
    identifier.save_leading_analysis_results()

if __name__ == "__main__":
    main()

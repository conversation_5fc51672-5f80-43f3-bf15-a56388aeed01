#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试带回测功能的UI
验证新增的回测控件是否正确显示
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_ui_startup():
    """测试UI启动"""
    try:
        from PyQt5.QtWidgets import QApplication
        from odds_combination_ui import OddsCombinationMainWindow
        
        print("✅ 成功导入所需模块")
        
        app = QApplication(sys.argv)
        print("✅ 成功创建QApplication")
        
        window = OddsCombinationMainWindow()
        print("✅ 成功创建主窗口")
        
        # 检查新增的控件是否存在
        if hasattr(window, 'investment_threshold_input'):
            print("✅ 投资阈值输入框已添加")
        else:
            print("❌ 投资阈值输入框未找到")
            
        if hasattr(window, 'match_threshold_input'):
            print("✅ 场次阈值输入框已添加")
        else:
            print("❌ 场次阈值输入框未找到")
            
        if hasattr(window, 'backtest_button'):
            print("✅ 回测按钮已添加")
            print(f"   回测按钮初始状态: {'启用' if window.backtest_button.isEnabled() else '禁用'}")
        else:
            print("❌ 回测按钮未找到")
            
        if hasattr(window, 'range_analysis_data'):
            print("✅ 区间分析数据存储变量已添加")
        else:
            print("❌ 区间分析数据存储变量未找到")
        
        # 检查方法是否存在
        methods_to_check = [
            'start_backtest',
            'perform_backtest', 
            'get_match_result_for_backtest',
            'extract_home_odds_from_combination',
            'display_backtest_results'
        ]
        
        for method_name in methods_to_check:
            if hasattr(window, method_name):
                print(f"✅ 方法 {method_name} 已添加")
            else:
                print(f"❌ 方法 {method_name} 未找到")
        
        print("\n🎯 UI组件检查完成")
        print("📝 如需完整测试，请运行UI并进行以下操作：")
        print("1. 进行ID区间数据分析")
        print("2. 观察回测按钮是否启用")
        print("3. 设置回测参数并执行回测")
        
        return True
        
    except ImportError as e:
        print(f"❌ 导入错误: {e}")
        return False
    except Exception as e:
        print(f"❌ 其他错误: {e}")
        return False

if __name__ == "__main__":
    print("🎯 测试带回测功能的UI组件")
    print("=" * 50)
    
    success = test_ui_startup()
    
    if success:
        print("\n✅ 所有组件检查通过！")
        print("🚀 可以启动完整UI进行功能测试")
    else:
        print("\n❌ 检查发现问题，请检查代码")

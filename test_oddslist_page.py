#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试oddslist页面是否可以访问和解析
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from enhanced_odds_scraper import EnhancedOddsScraper
from bs4 import BeautifulSoup
import logging

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_oddslist_page():
    """测试oddslist页面"""
    print("🧪 测试oddslist页面 - 比赛2399071")
    print("=" * 60)
    
    scraper = EnhancedOddsScraper()
    match_id = "2399071"
    
    # 测试两个可能的URL
    urls_to_try = [
        f"https://1x2.titan007.com/oddslist/{match_id}.htm",
        f"https://op1.titan007.com/oddslist/{match_id}.htm"
    ]
    
    for i, url in enumerate(urls_to_try, 1):
        print(f"\n📋 测试URL {i}: {url}")
        print("-" * 40)
        
        # 获取页面内容
        content = scraper.get_page_content(url)
        
        if not content:
            print(f"❌ 无法获取页面内容")
            continue
            
        print(f"✅ 页面可访问，内容长度: {len(content)}")
        
        # 检查是否包含OddsHistory链接
        odds_history_count = content.count('OddsHistory.aspx')
        print(f"📊 OddsHistory链接数量: {odds_history_count}")
        
        if odds_history_count == 0:
            print(f"❌ 页面不包含OddsHistory链接")
            continue
            
        # 检查是否包含bet365的链接
        bet365_count = content.count('cid=281')
        print(f"📊 bet365链接数量 (cid=281): {bet365_count}")
        
        # 解析页面
        try:
            soup = BeautifulSoup(content, 'html.parser')
            
            # 查找所有包含 OddsHistory.aspx 的链接
            history_links = soup.find_all('a', href=lambda x: x and 'OddsHistory.aspx' in x)
            print(f"📊 解析到的历史链接数量: {len(history_links)}")
            
            # 查找bet365的链接
            bet365_links = []
            for link in history_links:
                href = link.get('href')
                if href and 'cid=281' in href:
                    bet365_links.append(href)
            
            print(f"📊 bet365链接数量: {len(bet365_links)}")
            
            if bet365_links:
                print(f"✅ 找到bet365链接:")
                for j, href in enumerate(bet365_links, 1):
                    print(f"  {j}. {href}")
                    
                    # 解析URL参数
                    try:
                        from urllib.parse import urlparse, parse_qs
                        parsed = urlparse(href)
                        params = parse_qs(parsed.query)
                        
                        record_id = params.get('id', ['unknown'])[0]
                        company_id = params.get('cid', ['unknown'])[0]
                        match_id_param = params.get('sid', ['unknown'])[0]
                        
                        print(f"     Record ID: {record_id}")
                        print(f"     Company ID: {company_id}")
                        print(f"     Match ID: {match_id_param}")
                        
                        # 验证这个record ID是否正确
                        correct_record_id = "*********"
                        if record_id == correct_record_id:
                            print(f"     ✅ Record ID正确！")
                        else:
                            print(f"     ❌ Record ID错误，应该是: {correct_record_id}")
                            
                    except Exception as e:
                        print(f"     ❌ 解析URL失败: {e}")
            else:
                print(f"❌ 未找到bet365链接")
                
                # 显示前10个链接作为调试
                print(f"🔍 前10个历史链接:")
                for j, link in enumerate(history_links[:10], 1):
                    href = link.get('href', 'N/A')
                    print(f"  {j}. {href}")
                    
        except Exception as e:
            print(f"❌ 解析页面失败: {e}")
            
    # 测试直接调用两步方案的第一步
    print(f"\n📋 测试直接调用两步方案第一步")
    print("-" * 40)
    
    company_links = scraper.get_company_odds_links_from_oddslist(match_id)
    
    if company_links:
        print(f"✅ 第一步成功：找到 {len(company_links)} 家公司的链接")
        
        for company_id, data in company_links.items():
            company_name = data['company_name']
            record_id = data['record_id']
            url = data['url']
            print(f"  📊 {company_name} (ID: {company_id})")
            print(f"      Record ID: {record_id}")
            print(f"      URL: {url}")
            
    else:
        print(f"❌ 第一步失败：未找到任何公司的链接")

if __name__ == "__main__":
    test_oddslist_page()

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
天启代理最终测试
基于官方文档的完整测试
"""

import requests
import hashlib
import hmac
import time
import json

def test_tianqi_final():
    """基于官方文档的最终测试"""
    print("🎯 天启代理最终测试")
    print("=" * 60)
    
    # 读取API密钥
    try:
        with open('ip_keys.txt', 'r') as f:
            secret_key = f.read().strip()
        print(f"📋 API密钥: {secret_key}")
    except Exception as e:
        print(f"❌ 读取密钥失败: {e}")
        return
    
    # 根据官方文档，尝试不同的参数组合
    test_cases = [
        {
            "name": "官方文档格式1 - 使用secret和key",
            "params": {
                "secret": secret_key,
                "key": secret_key,
                "num": 1,
                "time": 3,
                "port": 1,
                "type": "json",
                "sign": secret_key
            }
        },
        {
            "name": "官方文档格式2 - 只使用key",
            "params": {
                "key": secret_key,
                "num": 1,
                "time": 3,
                "port": 1,
                "type": "json",
                "sign": secret_key
            }
        },
        {
            "name": "白名单接口格式 - key+brand+sign",
            "params": {
                "key": secret_key,
                "brand": 2,
                "num": 1,
                "time": 3,
                "port": 1,
                "type": "json",
                "sign": secret_key
            }
        },
        {
            "name": "简化格式 - 最少参数",
            "params": {
                "secret": secret_key,
                "time": 3,
                "port": 1,
                "sign": secret_key
            }
        }
    ]
    
    for i, case in enumerate(test_cases):
        print(f"\n{'='*50}")
        print(f"🧪 测试 {i+1}: {case['name']}")
        print(f"{'='*50}")
        
        try:
            url = "http://api.tianqiip.com/getip"
            params = case['params']
            
            print(f"🔍 请求URL: {url}")
            print(f"📋 请求参数: {params}")
            
            response = requests.get(url, params=params, timeout=10)
            print(f"📊 响应状态码: {response.status_code}")
            print(f"🔗 完整URL: {response.url}")
            
            if response.status_code == 200:
                try:
                    data = response.json()
                    print(f"✅ JSON响应: {data}")
                    
                    if data.get("code") == 1000:
                        print("🎉 成功！找到正确的方法！")
                        
                        # 测试获取到的代理
                        if "data" in data and data["data"]:
                            proxy_list = data["data"]
                            print(f"📋 获取到 {len(proxy_list)} 个代理:")
                            
                            for j, proxy in enumerate(proxy_list):
                                print(f"  {j+1}. {proxy.get('ip')}:{proxy.get('port')} ({proxy.get('city', 'Unknown')})")
                            
                            # 测试第一个代理
                            if proxy_list:
                                test_proxy_connection(proxy_list[0])
                        
                        return True
                    else:
                        print(f"❌ API错误: {data.get('msg', 'Unknown')}")
                        
                except json.JSONDecodeError:
                    print(f"✅ 文本响应: {response.text}")
                    
            else:
                try:
                    error_data = response.json()
                    print(f"❌ 错误: {error_data.get('msg', 'Unknown')}")
                except:
                    print(f"❌ 错误: {response.text[:100]}...")
                    
        except Exception as e:
            print(f"❌ 请求异常: {e}")
    
    # 如果所有方法都失败，尝试白名单相关的测试
    print(f"\n{'='*50}")
    print("🧪 测试白名单接口（可能提供线索）")
    print(f"{'='*50}")
    
    test_whitelist_api(secret_key)
    
    return False

def test_proxy_connection(proxy_info):
    """测试代理连接"""
    print(f"\n🧪 测试代理连接")
    
    try:
        ip = proxy_info.get("ip")
        port = proxy_info.get("port")
        
        if not ip or not port:
            print("❌ 代理信息不完整")
            return
        
        proxies = {
            'http': f'http://{ip}:{port}',
            'https': f'http://{ip}:{port}'
        }
        
        print(f"🔍 测试代理: {ip}:{port}")
        
        # 测试获取IP
        response = requests.get("http://httpbin.org/ip", proxies=proxies, timeout=10)
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ 代理连接成功！")
            print(f"📍 当前IP: {result.get('origin', 'Unknown')}")
            
            # 测试访问目标网站
            print(f"\n🧪 测试访问目标网站...")
            test_response = requests.get("https://www.baidu.com", proxies=proxies, timeout=10)
            if test_response.status_code == 200:
                print(f"✅ 目标网站访问成功！")
            else:
                print(f"❌ 目标网站访问失败: {test_response.status_code}")
                
        else:
            print(f"❌ 代理连接失败: {response.status_code}")
            
    except Exception as e:
        print(f"❌ 代理测试异常: {e}")

def test_whitelist_api(secret_key):
    """测试白名单API（可能提供签名线索）"""
    
    # 获取当前IP
    try:
        current_ip_response = requests.get("http://httpbin.org/ip", timeout=5)
        current_ip = current_ip_response.json().get("origin", "").split(",")[0].strip()
        print(f"📍 当前IP: {current_ip}")
    except:
        current_ip = "127.0.0.1"
        print(f"📍 使用默认IP: {current_ip}")
    
    # 测试白名单接口的不同格式
    whitelist_tests = [
        {
            "name": "白名单格式1",
            "url": "http://api.tianqiip.com/white/add",
            "params": {
                "key": secret_key,
                "brand": 2,
                "ip": current_ip,
                "sign": secret_key
            }
        },
        {
            "name": "白名单格式2",
            "url": "http://api.tianqiip.com/white/fetch",
            "params": {
                "key": secret_key,
                "brand": 2,
                "sign": secret_key
            }
        }
    ]
    
    for test in whitelist_tests:
        print(f"\n🧪 {test['name']}")
        try:
            response = requests.get(test['url'], params=test['params'], timeout=10)
            print(f"状态码: {response.status_code}")
            
            if response.status_code == 200:
                try:
                    data = response.json()
                    print(f"响应: {data}")
                    
                    if data.get("code") == 200:
                        print("✅ 白名单接口调用成功！这说明签名方法可能是正确的")
                except:
                    print(f"响应: {response.text}")
            else:
                try:
                    error = response.json()
                    print(f"错误: {error}")
                except:
                    print(f"错误: {response.text[:100]}...")
                    
        except Exception as e:
            print(f"异常: {e}")

def generate_final_recommendations():
    """生成最终建议"""
    print(f"\n💡 最终建议")
    print("=" * 60)
    
    print("基于详细测试，建议您：")
    print()
    print("1. 🌐 登录官网检查账户状态:")
    print("   - 访问: https://www.tianqiip.com/login")
    print("   - 检查账户余额（需要天启币）")
    print("   - 确认是否完成实名认证")
    print("   - 查看套餐状态")
    print()
    print("2. 📞 联系客服获取技术支持:")
    print("   - QQ: 3006006530")
    print("   - 微信: 17006536461")
    print("   - 询问正确的sign签名算法")
    print("   - 要求提供Python示例代码")
    print()
    print("3. 🔧 可能的问题:")
    print("   - 账户余额不足（需要充值天启币）")
    print("   - 未完成实名认证")
    print("   - 需要添加白名单IP")
    print("   - 签名算法需要特殊实现")
    print()
    print("4. 📋 已确认的API格式:")
    print("   - URL: http://api.tianqiip.com/getip")
    print("   - 必需参数: secret, time, port, sign")
    print("   - 可选参数: num, type, region, yys等")
    print("   - 签名算法: 需要客服提供具体实现")

def main():
    """主函数"""
    try:
        success = test_tianqi_final()
        
        if success:
            print("\n🎉 天启代理测试成功！")
            print("您现在可以使用代理服务了。")
        else:
            print("\n❌ 天启代理测试失败")
            generate_final_recommendations()
        
    except KeyboardInterrupt:
        print("\n👋 用户取消测试")
    except Exception as e:
        print(f"❌ 测试失败: {e}")

if __name__ == "__main__":
    main()

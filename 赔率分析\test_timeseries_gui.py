#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
时间序列提取GUI功能测试
验证步骤2的时间序列提取功能是否正常工作
"""

import os
import sys
from datetime import datetime

def test_timeseries_extractor():
    """测试时间序列提取器功能"""
    print("=== 测试时间序列提取器功能 ===")
    
    try:
        from odds_timeseries_extractor import OddsTimeseriesExtractor
        
        # 创建提取器实例
        extractor = OddsTimeseriesExtractor()
        print("✓ 时间序列提取器创建成功")
        
        # 测试主数据库路径
        if os.path.exists(extractor.main_db_path):
            print(f"✓ 主数据库存在: {extractor.main_db_path}")
        else:
            print(f"✗ 主数据库不存在: {extractor.main_db_path}")
            return False
        
        # 测试联赛数据库目录
        if os.path.exists(extractor.league_db_dir):
            print(f"✓ 联赛数据库目录存在: {extractor.league_db_dir}")
            
            # 列出联赛数据库文件
            league_files = [f for f in os.listdir(extractor.league_db_dir) if f.endswith('.db')]
            print(f"  发现联赛数据库: {league_files}")
        else:
            print(f"✗ 联赛数据库目录不存在: {extractor.league_db_dir}")
        
        # 测试提取功能（小规模测试）
        print("正在测试时间序列提取功能...")
        
        # 提取主数据库的1场比赛
        main_results = extractor.extract_top_matches_timeseries(
            extractor.main_db_path, "主数据库测试", 1
        )
        
        if main_results:
            print(f"✓ 主数据库提取成功，获得 {len(main_results)} 场比赛")
            
            # 显示第一场比赛的详细信息
            first_match = list(main_results.values())[0]
            odds_count = len(first_match.get('odds_data', []))
            companies = len(set(item.get('company', '') for item in first_match.get('odds_data', [])))
            
            print(f"  第一场比赛统计:")
            print(f"    赔率记录数: {odds_count}")
            print(f"    博彩公司数: {companies}")
            
            # 显示时间范围
            timestamps = [item.get('timestamp', '') for item in first_match.get('odds_data', []) if item.get('timestamp')]
            if timestamps:
                print(f"    时间范围: {min(timestamps)} 至 {max(timestamps)}")
        else:
            print("✗ 主数据库提取失败")
            return False
        
        print("✓ 时间序列提取功能测试通过")
        return True
        
    except ImportError as e:
        print(f"✗ 导入错误: {e}")
        return False
    except Exception as e:
        print(f"✗ 测试过程中出错: {e}")
        return False

def test_gui_timeseries_components():
    """测试GUI时间序列组件"""
    print("\n=== 测试GUI时间序列组件 ===")
    
    try:
        # 测试PyQt5组件
        from PyQt5.QtWidgets import QApplication
        from PyQt5.QtCore import QThread
        
        print("✓ PyQt5组件导入成功")
        
        # 测试自定义组件导入
        import sys
        sys.path.append('.')
        
        # 这里不实际创建GUI，只测试导入
        print("✓ GUI组件准备就绪")
        
        return True
        
    except ImportError as e:
        print(f"✗ GUI组件导入失败: {e}")
        return False
    except Exception as e:
        print(f"✗ GUI组件测试失败: {e}")
        return False

def test_data_availability():
    """测试数据可用性"""
    print("\n=== 测试数据可用性 ===")
    
    # 检查主数据库
    main_db_path = "../odds_data.db"
    if os.path.exists(main_db_path):
        size_mb = os.path.getsize(main_db_path) / (1024 * 1024)
        print(f"✓ 主数据库: {main_db_path} ({size_mb:.1f} MB)")
        main_ok = True
    else:
        print(f"✗ 主数据库不存在: {main_db_path}")
        main_ok = False
    
    # 检查联赛数据库
    league_db_dir = "../league_databases"
    if os.path.exists(league_db_dir):
        league_files = [f for f in os.listdir(league_db_dir) if f.endswith('.db')]
        print(f"✓ 联赛数据库目录: {league_db_dir}")
        print(f"  发现文件: {league_files}")
        
        total_size = 0
        for file in league_files:
            file_path = os.path.join(league_db_dir, file)
            size_mb = os.path.getsize(file_path) / (1024 * 1024)
            total_size += size_mb
            print(f"    {file}: {size_mb:.1f} MB")
        
        print(f"  联赛数据库总大小: {total_size:.1f} MB")
        league_ok = len(league_files) > 0
    else:
        print(f"✗ 联赛数据库目录不存在: {league_db_dir}")
        league_ok = False
    
    return main_ok and league_ok

def generate_gui_test_report():
    """生成GUI测试报告"""
    print("\n=== 生成GUI测试报告 ===")
    
    # 运行所有测试
    data_ok = test_data_availability()
    extractor_ok = test_timeseries_extractor()
    gui_ok = test_gui_timeseries_components()
    
    # 生成报告
    report = {
        "test_time": datetime.now().isoformat(),
        "gui_version": "v1.1 - 步骤1+2",
        "test_results": {
            "data_availability": data_ok,
            "timeseries_extractor": extractor_ok,
            "gui_components": gui_ok
        },
        "overall_status": "PASS" if all([data_ok, extractor_ok, gui_ok]) else "FAIL",
        "recommendations": []
    }
    
    # 生成建议
    if not data_ok:
        report["recommendations"].append("请确保数据库文件存在且可访问")
    if not extractor_ok:
        report["recommendations"].append("请检查时间序列提取器模块")
    if not gui_ok:
        report["recommendations"].append("请检查PyQt5安装和GUI组件")
    
    if report["overall_status"] == "PASS":
        report["recommendations"].append("所有测试通过，GUI步骤2功能可以正常使用")
    
    # 保存报告
    import json
    report_file = f"gui_timeseries_test_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
    with open(report_file, 'w', encoding='utf-8') as f:
        json.dump(report, f, ensure_ascii=False, indent=2)
    
    print(f"\n测试报告已保存到: {report_file}")
    
    # 输出总结
    print(f"\n=== 测试总结 ===")
    print(f"GUI版本: {report['gui_version']}")
    print(f"整体状态: {report['overall_status']}")
    print(f"数据可用性: {'通过' if data_ok else '失败'}")
    print(f"提取器功能: {'通过' if extractor_ok else '失败'}")
    print(f"GUI组件: {'通过' if gui_ok else '失败'}")
    
    if report["recommendations"]:
        print(f"\n建议:")
        for i, rec in enumerate(report["recommendations"], 1):
            print(f"  {i}. {rec}")
    
    return report

def main():
    """主函数"""
    print("🏈 赔率价值分析系统 - GUI时间序列功能测试")
    print("=" * 60)
    
    # 检查当前目录
    current_dir = os.getcwd()
    print(f"当前目录: {current_dir}")
    
    if not current_dir.endswith("赔率分析"):
        print("警告: 请在'赔率分析'目录下运行此脚本")
        print("正在尝试切换目录...")
        try:
            os.chdir("赔率分析")
            print("✓ 目录切换成功")
        except:
            print("✗ 目录切换失败，请手动切换到'赔率分析'目录")
            return
    
    # 运行测试并生成报告
    report = generate_gui_test_report()
    
    # 根据测试结果给出启动建议
    if report["overall_status"] == "PASS":
        print(f"\n🎉 所有测试通过！")
        print(f"GUI步骤2功能已就绪，您可以:")
        print(f"  1. 启动GUI: python odds_analysis_gui.py")
        print(f"  2. 使用数据库分析功能（步骤1）")
        print(f"  3. 使用时间序列提取功能（步骤2）")
        print(f"  4. 导出分析结果")
    else:
        print(f"\n⚠️  测试发现问题，请先解决后再使用GUI")
        print(f"详细信息请查看测试报告")

if __name__ == "__main__":
    main()

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分析数据库中比赛ID的分布，以便更准确地设置年份映射
"""

import sqlite3
import os
from datetime import datetime

def analyze_database_matches():
    """分析数据库中的比赛ID分布"""
    
    print("🔍 分析数据库中的比赛ID分布")
    print("=" * 50)
    
    # 查找数据库文件
    db_files = []
    for file in os.listdir('.'):
        if file.endswith('.db') and 'odds' in file.lower():
            db_files.append(file)
    
    if not db_files:
        print("❌ 未找到数据库文件")
        return
    
    db_path = db_files[0]
    print(f"📂 使用数据库: {db_path}")
    
    try:
        with sqlite3.connect(db_path) as conn:
            cursor = conn.cursor()
            
            # 检查表结构
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
            tables = cursor.fetchall()
            print(f"📋 数据库表: {[table[0] for table in tables]}")
            
            if not any('matches' in str(table).lower() for table in tables):
                print("❌ 未找到matches表")
                return
            
            # 分析比赛ID分布
            print(f"\n📊 比赛ID分布分析:")
            print("-" * 40)
            
            # 获取比赛ID范围
            cursor.execute("SELECT MIN(match_id), MAX(match_id), COUNT(*) FROM matches")
            min_id, max_id, total_count = cursor.fetchone()
            
            print(f"比赛ID范围: {min_id} - {max_id}")
            print(f"总比赛数: {total_count}")
            
            # 按ID范围分组统计
            ranges = [
                (0, 2500000, "< 2.5M"),
                (2500000, 2600000, "2.5M-2.6M"),
                (2600000, 2700000, "2.6M-2.7M"),
                (2700000, 2800000, "2.7M-2.8M"),
                (2800000, 2900000, "2.8M-2.9M"),
                (2900000, 3000000, "2.9M-3.0M"),
                (3000000, 3100000, "3.0M-3.1M"),
                (3100000, 3200000, "3.1M-3.2M"),
                (3200000, 3300000, "3.2M-3.3M"),
                (3300000, 3400000, "3.3M-3.4M"),
                (3400000, float('inf'), "> 3.4M")
            ]
            
            print(f"\n📈 按范围统计:")
            print("-" * 40)
            
            for start, end, label in ranges:
                if end == float('inf'):
                    cursor.execute("SELECT COUNT(*) FROM matches WHERE CAST(match_id AS INTEGER) >= ?", (start,))
                else:
                    cursor.execute("SELECT COUNT(*) FROM matches WHERE CAST(match_id AS INTEGER) >= ? AND CAST(match_id AS INTEGER) < ?", (start, end))
                
                count = cursor.fetchone()[0]
                if count > 0:
                    print(f"{label:12}: {count:6} 场比赛")
            
            # 查看具体的比赛示例
            print(f"\n📋 比赛示例分析:")
            print("-" * 40)
            
            # 查看2709881附近的比赛
            target_id = 2709881
            cursor.execute("""
                SELECT match_id, league, match_time, match_date, accurate_datetime
                FROM matches 
                WHERE CAST(match_id AS INTEGER) BETWEEN ? AND ?
                ORDER BY CAST(match_id AS INTEGER)
                LIMIT 10
            """, (target_id - 5, target_id + 5))
            
            nearby_matches = cursor.fetchall()
            
            if nearby_matches:
                print(f"比赛ID {target_id} 附近的比赛:")
                for match in nearby_matches:
                    match_id, league, match_time, match_date, accurate_datetime = match
                    time_info = accurate_datetime or match_time or match_date or "无时间信息"
                    print(f"  {match_id}: {league} - {time_info}")
            
            # 查看不同ID范围的时间分布
            print(f"\n📅 时间分布分析:")
            print("-" * 40)
            
            time_ranges = [
                (2500000, 2600000, "2.5M-2.6M"),
                (2600000, 2700000, "2.6M-2.7M"),
                (2700000, 2800000, "2.7M-2.8M"),
                (2800000, 2900000, "2.8M-2.9M"),
                (2900000, 3000000, "2.9M-3.0M"),
            ]
            
            for start, end, label in time_ranges:
                cursor.execute("""
                    SELECT match_time, match_date, accurate_datetime
                    FROM matches 
                    WHERE CAST(match_id AS INTEGER) >= ? AND CAST(match_id AS INTEGER) < ?
                    AND (match_time IS NOT NULL OR match_date IS NOT NULL OR accurate_datetime IS NOT NULL)
                    ORDER BY CAST(match_id AS INTEGER)
                    LIMIT 3
                """, (start, end))
                
                sample_matches = cursor.fetchall()
                
                if sample_matches:
                    print(f"{label}:")
                    for match_time, match_date, accurate_datetime in sample_matches:
                        time_info = accurate_datetime or match_time or match_date
                        if time_info:
                            year = time_info[:4] if len(time_info) >= 4 else "未知"
                            print(f"  时间: {time_info} (年份: {year})")
                
    except Exception as e:
        print(f"❌ 分析过程中发生错误: {e}")
        import traceback
        traceback.print_exc()

def suggest_new_mapping():
    """建议新的ID映射"""
    
    print(f"\n💡 建议的新映射方案:")
    print("=" * 50)
    
    print("基于分析结果，建议调整比赛ID范围映射:")
    print("1. 2500000-2650000: 2023年")
    print("2. 2650000-2750000: 2024年上半年")
    print("3. 2750000-2850000: 2024年下半年")
    print("4. 2850000-3000000: 2025年上半年")
    print("5. 3000000以上: 2025年下半年及以后")
    
    print(f"\n🎯 针对比赛ID 2709881:")
    print("应该被归类为 2024年下半年 (2750000-2850000范围)")

def main():
    """主函数"""
    
    print("🚀 比赛ID分布分析")
    print("=" * 60)
    
    analyze_database_matches()
    suggest_new_mapping()

if __name__ == "__main__":
    main()

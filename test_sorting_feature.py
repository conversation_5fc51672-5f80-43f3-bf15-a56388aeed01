#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试表格排序功能
"""

import tkinter as tk
from tkinter import ttk
import sys
import os

# 添加当前目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_sorting_ui():
    """测试排序功能的简单UI"""
    root = tk.Tk()
    root.title("表格排序功能测试")
    root.geometry("800x600")
    
    # 创建测试数据
    test_data = [
        ("2741467", "美职业", "2025", "第19轮", "洛杉矶FC vs 西雅图音速", "2025-05-26 07:10:00 ✓", "1-2", "完场", "18"),
        ("2741466", "美职业", "2025", "第19轮", "国际迈阿密 vs 皇家盐湖城", "2025-05-26 03:00:00 ✓", "2-0", "完场", "15"),
        ("2741465", "美职业", "2025", "第19轮", "温哥华白帽 vs 圣何塞地震", "2025-05-25 10:30:00 ✓", "0-1", "完场", "12"),
        ("2511678", "美职业", "2024", "第3轮", "芝加哥火焰 vs 蒙特利尔冲击", "2024-03-17 03:55:00", "1-1", "完场", "20"),
        ("2511677", "美职业", "2024", "第3轮", "华盛顿联 vs 国际迈阿密", "2024-03-17 02:00:00", "2-1", "完场", "22"),
    ]
    
    # 创建表格
    columns = ("match_id", "league", "season", "round", "teams", "match_time", "score", "state", "companies")
    tree = ttk.Treeview(root, columns=columns, show="headings", height=15)
    
    # 排序状态
    sort_column = None
    sort_reverse = False
    
    def sort_column_func(col):
        nonlocal sort_column, sort_reverse
        
        # 检查是否是同一列，如果是则切换排序方向
        if sort_column == col:
            sort_reverse = not sort_reverse
        else:
            sort_column = col
            sort_reverse = False
        
        # 获取所有数据
        items = []
        for child in tree.get_children():
            item = tree.item(child)
            items.append((child, item['values']))
        
        # 定义排序键函数
        def sort_key(item):
            values = item[1]
            col_index = {
                "match_id": 0,
                "league": 1,
                "season": 2,
                "round": 3,
                "teams": 4,
                "match_time": 5,
                "score": 6,
                "state": 7,
                "companies": 8
            }.get(col, 0)
            
            value = values[col_index] if col_index < len(values) else ""
            
            # 对数字列进行特殊处理
            if col in ["match_id", "companies"]:
                try:
                    return int(value) if value else 0
                except ValueError:
                    return 0
            elif col == "match_time":
                # 对时间进行排序，优先显示有准确时间的
                if "✓" in str(value):
                    return "0" + str(value)  # 有准确时间的排在前面
                else:
                    return "1" + str(value)  # 估算时间的排在后面
            else:
                return str(value).lower()
        
        # 排序
        items.sort(key=sort_key, reverse=sort_reverse)
        
        # 重新插入排序后的数据
        for i, (child, values) in enumerate(items):
            tree.move(child, '', i)
        
        # 更新表头显示排序状态
        update_header_sort_indicator(col)
    
    def update_header_sort_indicator(sorted_column):
        """更新表头的排序指示器"""
        headers = {
            "match_id": "比赛ID",
            "league": "联赛",
            "season": "赛季",
            "round": "轮次",
            "teams": "对阵",
            "match_time": "比赛时间",
            "score": "比分",
            "state": "状态",
            "companies": "博彩公司"
        }
        
        for col, base_text in headers.items():
            if col == sorted_column:
                arrow = " ↓" if sort_reverse else " ↑"
                tree.heading(col, text=base_text + arrow, command=lambda c=col: sort_column_func(c))
            else:
                tree.heading(col, text=base_text, command=lambda c=col: sort_column_func(c))
    
    # 设置列标题和宽度，添加排序功能
    headers = {
        "match_id": "比赛ID",
        "league": "联赛",
        "season": "赛季",
        "round": "轮次",
        "teams": "对阵",
        "match_time": "比赛时间",
        "score": "比分",
        "state": "状态",
        "companies": "博彩公司"
    }
    
    for col, header in headers.items():
        tree.heading(col, text=header, command=lambda c=col: sort_column_func(c))
    
    tree.column("match_id", width=80)
    tree.column("league", width=100)
    tree.column("season", width=80)
    tree.column("round", width=80)
    tree.column("teams", width=200)
    tree.column("match_time", width=150)
    tree.column("score", width=80)
    tree.column("state", width=80)
    tree.column("companies", width=80)
    
    # 插入测试数据
    for data in test_data:
        tree.insert("", "end", values=data)
    
    # 滚动条
    scrollbar = ttk.Scrollbar(root, orient=tk.VERTICAL, command=tree.yview)
    tree.configure(yscrollcommand=scrollbar.set)
    
    # 布局
    tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
    scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
    
    # 添加说明标签
    info_label = ttk.Label(root, text="点击表头可以按该列排序，再次点击可以切换升序/降序")
    info_label.pack(side=tk.BOTTOM, pady=10)
    
    root.mainloop()

if __name__ == "__main__":
    test_sorting_ui()

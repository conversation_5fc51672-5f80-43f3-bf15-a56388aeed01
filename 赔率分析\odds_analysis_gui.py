#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
赔率价值分析系统 - 主界面
实现步骤1：数据库结构分析功能
"""

import sys
import os
import json
import threading
from datetime import datetime
import pandas as pd
import numpy as np
from PyQt5.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout,
                            QHBoxLayout, QTabWidget, QTextEdit, QPushButton,
                            QLabel, QProgressBar, QGroupBox, QGridLayout,
                            QTableWidget, QTableWidgetItem, QHeaderView,
                            QMessageBox, QFileDialog, QSplitter, QFrame,
                            QSpinBox, QComboBox, QCheckBox)
from PyQt5.QtCore import Qt, QThread, pyqtSignal, QTimer
from PyQt5.QtGui import QFont, QTextCursor, QColor, QPalette

# 导入分析模块
from database_analyzer import DatabaseAnalyzer
from odds_timeseries_extractor import OddsTimeseriesExtractor

class DatabaseAnalysisWorker(QThread):
    """数据库分析工作线程"""
    progress_updated = pyqtSignal(int)
    status_updated = pyqtSignal(str)
    result_ready = pyqtSignal(dict)
    error_occurred = pyqtSignal(str)
    
    def __init__(self):
        super().__init__()
        self.analyzer = DatabaseAnalyzer()
    
    def run(self):
        """执行数据库分析"""
        try:
            self.status_updated.emit("开始数据库分析...")
            self.progress_updated.emit(10)

            # 分析主数据库
            self.status_updated.emit("分析主数据库结构...")
            main_db_result = self.analyzer.analyze_odds_table_structure("../odds_data.db", "主数据库")
            self.progress_updated.emit(50)

            # 分析联赛数据库
            self.status_updated.emit("分析联赛数据库...")
            league_db_results = {}

            # 检查联赛数据库
            league_databases = [
                ("../league_databases/MLS.db", "美职联"),
                ("../league_databases/日职联.db", "日职联")
            ]

            for i, (db_path, db_name) in enumerate(league_databases):
                if os.path.exists(db_path):
                    self.status_updated.emit(f"分析{db_name}数据库...")
                    result = self.analyzer.analyze_odds_table_structure(db_path, db_name)
                    if result:
                        league_db_results[db_name] = result
                else:
                    self.status_updated.emit(f"跳过{db_name}数据库（文件不存在）")

                self.progress_updated.emit(50 + (i + 1) * 10)  # 50-70%

            # 生成综合报告
            self.status_updated.emit("生成分析报告...")
            comprehensive_report = {
                'analysis_time': datetime.now().isoformat(),
                'main_database': main_db_result,
                'league_databases': league_db_results,
                'summary': {
                    'total_databases': 1 + len(league_db_results),
                    'main_db_tables': main_db_result.get('table_count', 0) if main_db_result else 0,
                    'league_db_count': len(league_db_results)
                }
            }
            self.progress_updated.emit(80)

            # 保存结果
            self.status_updated.emit("保存分析结果...")
            output_file = f"database_analysis_results_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"

            result_data = {
                'main_db': main_db_result,
                'league_dbs': league_db_results,
                'report': comprehensive_report
            }

            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(result_data, f, ensure_ascii=False, indent=2)

            self.progress_updated.emit(100)

            # 准备结果数据
            result = {
                'main_db': main_db_result,
                'league_dbs': league_db_results,
                'report': comprehensive_report,
                'output_file': output_file
            }

            self.status_updated.emit("数据库分析完成！")
            self.result_ready.emit(result)

        except Exception as e:
            self.error_occurred.emit(f"分析过程中出错: {str(e)}")

class TimeseriesExtractionWorker(QThread):
    """时间序列提取工作线程"""
    progress_updated = pyqtSignal(int)
    status_updated = pyqtSignal(str)
    result_ready = pyqtSignal(dict)
    error_occurred = pyqtSignal(str)
    match_processed = pyqtSignal(str, dict)  # 单场比赛处理完成信号

    def __init__(self, top_matches_per_db=5):
        super().__init__()
        self.extractor = OddsTimeseriesExtractor()
        self.top_matches_per_db = top_matches_per_db

    def run(self):
        """执行时间序列提取"""
        try:
            self.status_updated.emit("开始时间序列提取...")
            self.progress_updated.emit(10)

            all_results = {}

            # 处理主数据库
            self.status_updated.emit("提取主数据库时间序列...")
            if os.path.exists(self.extractor.main_db_path):
                main_results = self.extractor.extract_top_matches_timeseries(
                    self.extractor.main_db_path, "主数据库", self.top_matches_per_db
                )
                if main_results:
                    all_results['main'] = main_results
                    # 发送每场比赛的结果
                    for match_id, match_data in main_results.items():
                        self.match_processed.emit(f"主数据库-{match_id}", match_data)

            self.progress_updated.emit(50)

            # 处理联赛数据库
            self.status_updated.emit("提取联赛数据库时间序列...")
            if os.path.exists(self.extractor.league_db_dir):
                league_files = [f for f in os.listdir(self.extractor.league_db_dir) if f.endswith('.db')]

                for i, file in enumerate(league_files):
                    league_name = file[:-3]
                    db_path = os.path.join(self.extractor.league_db_dir, file)

                    self.status_updated.emit(f"提取{league_name}时间序列...")
                    league_results = self.extractor.extract_top_matches_timeseries(
                        db_path, f"联赛-{league_name}", min(self.top_matches_per_db, 2)
                    )
                    if league_results:
                        all_results[f'league_{league_name}'] = league_results
                        # 发送每场比赛的结果
                        for match_id, match_data in league_results.items():
                            self.match_processed.emit(f"{league_name}-{match_id}", match_data)

                    self.progress_updated.emit(50 + (i + 1) * 20 // len(league_files))

            # 保存结果
            self.status_updated.emit("保存时间序列数据...")
            if all_results:
                output_file = f"odds_timeseries_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"

                # 转换为可序列化格式
                serializable_results = self._make_serializable(all_results)

                with open(output_file, 'w', encoding='utf-8') as f:
                    json.dump(serializable_results, f, ensure_ascii=False, indent=2)

                all_results['output_file'] = output_file

            self.progress_updated.emit(100)
            self.status_updated.emit("时间序列提取完成！")
            self.result_ready.emit(all_results)

        except Exception as e:
            self.error_occurred.emit(f"提取过程中出错: {str(e)}")

    def _make_serializable(self, obj):
        """将对象转换为可序列化的格式"""
        if isinstance(obj, dict):
            return {k: self._make_serializable(v) for k, v in obj.items()}
        elif isinstance(obj, (list, tuple)):
            return [self._make_serializable(item) for item in obj]
        elif isinstance(obj, (np.integer, np.int64)):
            return int(obj)
        elif isinstance(obj, (np.floating, np.float64)):
            return float(obj)
        elif isinstance(obj, (np.bool_, bool)):
            return bool(obj)
        elif isinstance(obj, np.ndarray):
            return obj.tolist()
        elif pd.isna(obj) or obj is None:
            return None
        elif isinstance(obj, datetime):
            return obj.isoformat()
        else:
            return str(obj)

class DatabaseAnalysisTab(QWidget):
    """数据库分析标签页"""
    
    def __init__(self):
        super().__init__()
        self.analysis_worker = None
        self.analysis_results = None
        self.init_ui()
    
    def init_ui(self):
        """初始化界面"""
        layout = QVBoxLayout()
        
        # 标题
        title_label = QLabel("步骤1：数据库结构分析")
        title_label.setFont(QFont("Arial", 16, QFont.Bold))
        title_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(title_label)
        
        # 描述
        desc_label = QLabel("分析数据库结构，识别字段特征和数据质量")
        desc_label.setAlignment(Qt.AlignCenter)
        desc_label.setStyleSheet("color: gray; margin-bottom: 20px;")
        layout.addWidget(desc_label)
        
        # 控制面板
        control_group = QGroupBox("控制面板")
        control_layout = QHBoxLayout()
        
        self.start_btn = QPushButton("开始分析")
        self.start_btn.setStyleSheet("""
            QPushButton {
                background-color: #4CAF50;
                color: white;
                border: none;
                padding: 10px 20px;
                font-size: 14px;
                border-radius: 5px;
            }
            QPushButton:hover {
                background-color: #45a049;
            }
            QPushButton:disabled {
                background-color: #cccccc;
            }
        """)
        self.start_btn.clicked.connect(self.start_analysis)
        
        self.stop_btn = QPushButton("停止分析")
        self.stop_btn.setEnabled(False)
        self.stop_btn.setStyleSheet("""
            QPushButton {
                background-color: #f44336;
                color: white;
                border: none;
                padding: 10px 20px;
                font-size: 14px;
                border-radius: 5px;
            }
            QPushButton:hover {
                background-color: #da190b;
            }
            QPushButton:disabled {
                background-color: #cccccc;
            }
        """)
        self.stop_btn.clicked.connect(self.stop_analysis)
        
        self.export_btn = QPushButton("导出结果")
        self.export_btn.setEnabled(False)
        self.export_btn.setStyleSheet("""
            QPushButton {
                background-color: #2196F3;
                color: white;
                border: none;
                padding: 10px 20px;
                font-size: 14px;
                border-radius: 5px;
            }
            QPushButton:hover {
                background-color: #1976D2;
            }
            QPushButton:disabled {
                background-color: #cccccc;
            }
        """)
        self.export_btn.clicked.connect(self.export_results)
        
        control_layout.addWidget(self.start_btn)
        control_layout.addWidget(self.stop_btn)
        control_layout.addWidget(self.export_btn)
        control_layout.addStretch()
        control_group.setLayout(control_layout)
        layout.addWidget(control_group)
        
        # 进度显示
        progress_group = QGroupBox("分析进度")
        progress_layout = QVBoxLayout()
        
        self.progress_bar = QProgressBar()
        self.progress_bar.setStyleSheet("""
            QProgressBar {
                border: 2px solid grey;
                border-radius: 5px;
                text-align: center;
            }
            QProgressBar::chunk {
                background-color: #4CAF50;
                border-radius: 3px;
            }
        """)
        
        self.status_label = QLabel("准备就绪")
        self.status_label.setStyleSheet("color: #666; font-size: 12px;")
        
        progress_layout.addWidget(self.progress_bar)
        progress_layout.addWidget(self.status_label)
        progress_group.setLayout(progress_layout)
        layout.addWidget(progress_group)
        
        # 结果显示区域
        results_splitter = QSplitter(Qt.Horizontal)
        
        # 左侧：数据库概览
        overview_group = QGroupBox("数据库概览")
        overview_layout = QVBoxLayout()
        
        self.overview_table = QTableWidget()
        self.overview_table.setColumnCount(3)
        self.overview_table.setHorizontalHeaderLabels(["数据库", "表数量", "记录数"])
        self.overview_table.horizontalHeader().setStretchLastSection(True)
        self.overview_table.setAlternatingRowColors(True)
        
        overview_layout.addWidget(self.overview_table)
        overview_group.setLayout(overview_layout)
        results_splitter.addWidget(overview_group)
        
        # 右侧：详细日志
        log_group = QGroupBox("分析日志")
        log_layout = QVBoxLayout()
        
        self.log_text = QTextEdit()
        self.log_text.setReadOnly(True)
        self.log_text.setFont(QFont("Consolas", 10))
        self.log_text.setStyleSheet("""
            QTextEdit {
                background-color: #f5f5f5;
                border: 1px solid #ddd;
                border-radius: 5px;
                padding: 10px;
            }
        """)
        
        log_layout.addWidget(self.log_text)
        log_group.setLayout(log_layout)
        results_splitter.addWidget(log_group)
        
        results_splitter.setSizes([400, 600])
        layout.addWidget(results_splitter)
        
        self.setLayout(layout)
        
        # 添加初始日志
        self.add_log("系统初始化完成", "INFO")
        self.add_log("点击'开始分析'按钮开始数据库结构分析", "INFO")
    
    def add_log(self, message, level="INFO"):
        """添加日志信息"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        
        # 设置颜色
        color_map = {
            "INFO": "#333333",
            "SUCCESS": "#4CAF50", 
            "WARNING": "#FF9800",
            "ERROR": "#f44336"
        }
        
        color = color_map.get(level, "#333333")
        formatted_message = f'<span style="color: gray;">[{timestamp}]</span> <span style="color: {color};">[{level}]</span> {message}'
        
        self.log_text.append(formatted_message)
        
        # 自动滚动到底部
        cursor = self.log_text.textCursor()
        cursor.movePosition(QTextCursor.End)
        self.log_text.setTextCursor(cursor)
    
    def start_analysis(self):
        """开始数据库分析"""
        self.add_log("启动数据库分析任务...", "INFO")
        
        # 更新按钮状态
        self.start_btn.setEnabled(False)
        self.stop_btn.setEnabled(True)
        self.export_btn.setEnabled(False)
        
        # 重置进度
        self.progress_bar.setValue(0)
        self.status_label.setText("初始化分析...")
        
        # 清空结果表格
        self.overview_table.setRowCount(0)
        
        # 创建并启动工作线程
        self.analysis_worker = DatabaseAnalysisWorker()
        self.analysis_worker.progress_updated.connect(self.update_progress)
        self.analysis_worker.status_updated.connect(self.update_status)
        self.analysis_worker.result_ready.connect(self.handle_results)
        self.analysis_worker.error_occurred.connect(self.handle_error)
        self.analysis_worker.finished.connect(self.analysis_finished)
        
        self.analysis_worker.start()
    
    def stop_analysis(self):
        """停止分析"""
        if self.analysis_worker and self.analysis_worker.isRunning():
            self.analysis_worker.terminate()
            self.analysis_worker.wait()
            self.add_log("分析已被用户停止", "WARNING")
        
        self.analysis_finished()
    
    def update_progress(self, value):
        """更新进度条"""
        self.progress_bar.setValue(value)
    
    def update_status(self, status):
        """更新状态标签"""
        self.status_label.setText(status)
        self.add_log(status, "INFO")
    
    def handle_results(self, results):
        """处理分析结果"""
        self.analysis_results = results
        self.add_log("分析完成，正在显示结果...", "SUCCESS")
        
        # 更新概览表格
        self.update_overview_table(results)
        
        # 启用导出按钮
        self.export_btn.setEnabled(True)
        
        self.add_log(f"结果已保存到: {results.get('output_file', 'N/A')}", "SUCCESS")
    
    def handle_error(self, error_msg):
        """处理错误"""
        self.add_log(error_msg, "ERROR")
        QMessageBox.critical(self, "分析错误", error_msg)
    
    def analysis_finished(self):
        """分析完成后的清理工作"""
        self.start_btn.setEnabled(True)
        self.stop_btn.setEnabled(False)
        
        if self.progress_bar.value() == 100:
            self.status_label.setText("分析完成")
        else:
            self.status_label.setText("分析已停止")
    
    def update_overview_table(self, results):
        """更新概览表格"""
        # 主数据库
        main_db = results.get('main_db', {})
        if main_db:
            self.overview_table.setRowCount(1)
            self.overview_table.setItem(0, 0, QTableWidgetItem("主数据库"))
            self.overview_table.setItem(0, 1, QTableWidgetItem(str(main_db.get('table_count', 1))))
            self.overview_table.setItem(0, 2, QTableWidgetItem(str(main_db.get('total_records', main_db.get('record_count', 0)))))

        # 联赛数据库
        league_dbs = results.get('league_dbs', {})
        current_row = 1

        for db_name, db_info in league_dbs.items():
            self.overview_table.setRowCount(current_row + 1)
            self.overview_table.setItem(current_row, 0, QTableWidgetItem(db_name))
            self.overview_table.setItem(current_row, 1, QTableWidgetItem("1"))  # odds表
            self.overview_table.setItem(current_row, 2, QTableWidgetItem(str(db_info.get('record_count', 0))))
            current_row += 1

        # 调整列宽
        self.overview_table.resizeColumnsToContents()
    
    def export_results(self):
        """导出分析结果"""
        if not self.analysis_results:
            QMessageBox.warning(self, "导出警告", "没有可导出的结果")
            return
        
        # 选择保存位置
        file_path, _ = QFileDialog.getSaveFileName(
            self, "导出分析结果", 
            f"database_analysis_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json",
            "JSON文件 (*.json)"
        )
        
        if file_path:
            try:
                with open(file_path, 'w', encoding='utf-8') as f:
                    json.dump(self.analysis_results, f, ensure_ascii=False, indent=2)
                
                self.add_log(f"结果已导出到: {file_path}", "SUCCESS")
                QMessageBox.information(self, "导出成功", f"分析结果已成功导出到:\n{file_path}")
                
            except Exception as e:
                error_msg = f"导出失败: {str(e)}"
                self.add_log(error_msg, "ERROR")
                QMessageBox.critical(self, "导出错误", error_msg)

class TimeseriesExtractionTab(QWidget):
    """时间序列提取标签页"""

    def __init__(self):
        super().__init__()
        self.extraction_worker = None
        self.extraction_results = None
        self.init_ui()

    def init_ui(self):
        """初始化界面"""
        layout = QVBoxLayout()

        # 标题
        title_label = QLabel("步骤2：赔率时间序列提取")
        title_label.setFont(QFont("Arial", 16, QFont.Bold))
        title_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(title_label)

        # 描述
        desc_label = QLabel("提取比赛的赔率变化时间序列，分析赔率动态变化过程")
        desc_label.setAlignment(Qt.AlignCenter)
        desc_label.setStyleSheet("color: gray; margin-bottom: 20px;")
        layout.addWidget(desc_label)

        # 参数设置面板
        params_group = QGroupBox("提取参数")
        params_layout = QGridLayout()

        # 每个数据库提取的比赛数量
        params_layout.addWidget(QLabel("每个数据库提取比赛数:"), 0, 0)
        self.matches_spinbox = QSpinBox()
        self.matches_spinbox.setRange(1, 20)
        self.matches_spinbox.setValue(5)
        self.matches_spinbox.setToolTip("每个数据库提取的比赛数量（赔率变化最多的比赛）")
        params_layout.addWidget(self.matches_spinbox, 0, 1)

        params_group.setLayout(params_layout)
        layout.addWidget(params_group)

        # 控制面板
        control_group = QGroupBox("控制面板")
        control_layout = QHBoxLayout()

        self.start_btn = QPushButton("开始提取")
        self.start_btn.setStyleSheet("""
            QPushButton {
                background-color: #4CAF50;
                color: white;
                border: none;
                padding: 10px 20px;
                font-size: 14px;
                border-radius: 5px;
            }
            QPushButton:hover {
                background-color: #45a049;
            }
            QPushButton:disabled {
                background-color: #cccccc;
            }
        """)
        self.start_btn.clicked.connect(self.start_extraction)

        self.stop_btn = QPushButton("停止提取")
        self.stop_btn.setEnabled(False)
        self.stop_btn.setStyleSheet("""
            QPushButton {
                background-color: #f44336;
                color: white;
                border: none;
                padding: 10px 20px;
                font-size: 14px;
                border-radius: 5px;
            }
            QPushButton:hover {
                background-color: #da190b;
            }
            QPushButton:disabled {
                background-color: #cccccc;
            }
        """)
        self.stop_btn.clicked.connect(self.stop_extraction)

        self.export_btn = QPushButton("导出结果")
        self.export_btn.setEnabled(False)
        self.export_btn.setStyleSheet("""
            QPushButton {
                background-color: #2196F3;
                color: white;
                border: none;
                padding: 10px 20px;
                font-size: 14px;
                border-radius: 5px;
            }
            QPushButton:hover {
                background-color: #1976D2;
            }
            QPushButton:disabled {
                background-color: #cccccc;
            }
        """)
        self.export_btn.clicked.connect(self.export_results)

        control_layout.addWidget(self.start_btn)
        control_layout.addWidget(self.stop_btn)
        control_layout.addWidget(self.export_btn)
        control_layout.addStretch()
        control_group.setLayout(control_layout)
        layout.addWidget(control_group)

        # 进度显示
        progress_group = QGroupBox("提取进度")
        progress_layout = QVBoxLayout()

        self.progress_bar = QProgressBar()
        self.progress_bar.setStyleSheet("""
            QProgressBar {
                border: 2px solid grey;
                border-radius: 5px;
                text-align: center;
            }
            QProgressBar::chunk {
                background-color: #4CAF50;
                border-radius: 3px;
            }
        """)

        self.status_label = QLabel("准备就绪")
        self.status_label.setStyleSheet("color: #666; font-size: 12px;")

        progress_layout.addWidget(self.progress_bar)
        progress_layout.addWidget(self.status_label)
        progress_group.setLayout(progress_layout)
        layout.addWidget(progress_group)

        # 结果显示区域
        results_splitter = QSplitter(Qt.Horizontal)

        # 左侧：比赛列表
        matches_group = QGroupBox("提取的比赛")
        matches_layout = QVBoxLayout()

        self.matches_table = QTableWidget()
        self.matches_table.setColumnCount(4)
        self.matches_table.setHorizontalHeaderLabels(["数据库", "比赛ID", "赔率变化次数", "时间跨度"])
        self.matches_table.horizontalHeader().setStretchLastSection(True)
        self.matches_table.setAlternatingRowColors(True)
        self.matches_table.setSelectionBehavior(QTableWidget.SelectRows)

        matches_layout.addWidget(self.matches_table)
        matches_group.setLayout(matches_layout)
        results_splitter.addWidget(matches_group)

        # 右侧：详细日志
        log_group = QGroupBox("提取日志")
        log_layout = QVBoxLayout()

        self.log_text = QTextEdit()
        self.log_text.setReadOnly(True)
        self.log_text.setFont(QFont("Consolas", 10))
        self.log_text.setStyleSheet("""
            QTextEdit {
                background-color: #f5f5f5;
                border: 1px solid #ddd;
                border-radius: 5px;
                padding: 10px;
            }
        """)

        log_layout.addWidget(self.log_text)
        log_group.setLayout(log_layout)
        results_splitter.addWidget(log_group)

        results_splitter.setSizes([400, 600])
        layout.addWidget(results_splitter)

        self.setLayout(layout)

        # 添加初始日志
        self.add_log("时间序列提取模块初始化完成", "INFO")
        self.add_log("点击'开始提取'按钮开始提取赔率时间序列", "INFO")

    def add_log(self, message, level="INFO"):
        """添加日志信息"""
        timestamp = datetime.now().strftime("%H:%M:%S")

        # 设置颜色
        color_map = {
            "INFO": "#333333",
            "SUCCESS": "#4CAF50",
            "WARNING": "#FF9800",
            "ERROR": "#f44336"
        }

        color = color_map.get(level, "#333333")
        formatted_message = f'<span style="color: gray;">[{timestamp}]</span> <span style="color: {color};">[{level}]</span> {message}'

        self.log_text.append(formatted_message)

        # 自动滚动到底部
        cursor = self.log_text.textCursor()
        cursor.movePosition(QTextCursor.End)
        self.log_text.setTextCursor(cursor)

    def start_extraction(self):
        """开始时间序列提取"""
        self.add_log("启动时间序列提取任务...", "INFO")

        # 更新按钮状态
        self.start_btn.setEnabled(False)
        self.stop_btn.setEnabled(True)
        self.export_btn.setEnabled(False)

        # 重置进度
        self.progress_bar.setValue(0)
        self.status_label.setText("初始化提取...")

        # 清空结果表格
        self.matches_table.setRowCount(0)

        # 获取参数
        top_matches = self.matches_spinbox.value()

        # 创建并启动工作线程
        self.extraction_worker = TimeseriesExtractionWorker(top_matches)
        self.extraction_worker.progress_updated.connect(self.update_progress)
        self.extraction_worker.status_updated.connect(self.update_status)
        self.extraction_worker.result_ready.connect(self.handle_results)
        self.extraction_worker.error_occurred.connect(self.handle_error)
        self.extraction_worker.match_processed.connect(self.add_match_to_table)
        self.extraction_worker.finished.connect(self.extraction_finished)

        self.extraction_worker.start()

    def stop_extraction(self):
        """停止提取"""
        if self.extraction_worker and self.extraction_worker.isRunning():
            self.extraction_worker.terminate()
            self.extraction_worker.wait()
            self.add_log("提取已被用户停止", "WARNING")

        self.extraction_finished()

    def update_progress(self, value):
        """更新进度条"""
        self.progress_bar.setValue(value)

    def update_status(self, status):
        """更新状态标签"""
        self.status_label.setText(status)
        self.add_log(status, "INFO")

    def add_match_to_table(self, match_key, match_data):
        """添加比赛到表格"""
        row = self.matches_table.rowCount()
        self.matches_table.setRowCount(row + 1)

        # 解析比赛键
        parts = match_key.split('-')
        db_name = parts[0]
        match_id = parts[1] if len(parts) > 1 else "未知"

        # 计算统计信息
        odds_count = len(match_data.get('odds_data', []))
        time_span = "未知"

        if match_data.get('odds_data'):
            timestamps = [item.get('timestamp', '') for item in match_data['odds_data'] if item.get('timestamp')]
            if len(timestamps) >= 2:
                try:
                    start_time = min(timestamps)
                    end_time = max(timestamps)
                    time_span = f"{start_time[:10]} 至 {end_time[:10]}"
                except:
                    time_span = "时间解析错误"

        # 填充表格
        self.matches_table.setItem(row, 0, QTableWidgetItem(db_name))
        self.matches_table.setItem(row, 1, QTableWidgetItem(str(match_id)))
        self.matches_table.setItem(row, 2, QTableWidgetItem(str(odds_count)))
        self.matches_table.setItem(row, 3, QTableWidgetItem(time_span))

        # 调整列宽
        self.matches_table.resizeColumnsToContents()

        self.add_log(f"处理完成: {db_name} 比赛{match_id} ({odds_count}条记录)", "SUCCESS")

    def handle_results(self, results):
        """处理提取结果"""
        self.extraction_results = results
        self.add_log("时间序列提取完成，正在显示结果...", "SUCCESS")

        # 启用导出按钮
        self.export_btn.setEnabled(True)

        # 统计信息
        total_matches = 0
        total_records = 0

        for db_key, db_results in results.items():
            if db_key != 'output_file':
                total_matches += len(db_results)
                for match_data in db_results.values():
                    total_records += len(match_data.get('odds_data', []))

        self.add_log(f"提取完成: {total_matches}场比赛，{total_records}条时间序列记录", "SUCCESS")
        self.add_log(f"结果已保存到: {results.get('output_file', 'N/A')}", "SUCCESS")

    def handle_error(self, error_msg):
        """处理错误"""
        self.add_log(error_msg, "ERROR")
        QMessageBox.critical(self, "提取错误", error_msg)

    def extraction_finished(self):
        """提取完成后的清理工作"""
        self.start_btn.setEnabled(True)
        self.stop_btn.setEnabled(False)

        if self.progress_bar.value() == 100:
            self.status_label.setText("提取完成")
        else:
            self.status_label.setText("提取已停止")

    def export_results(self):
        """导出提取结果"""
        if not self.extraction_results:
            QMessageBox.warning(self, "导出警告", "没有可导出的结果")
            return

        # 选择保存位置
        file_path, _ = QFileDialog.getSaveFileName(
            self, "导出时间序列结果",
            f"timeseries_extraction_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json",
            "JSON文件 (*.json)"
        )

        if file_path:
            try:
                with open(file_path, 'w', encoding='utf-8') as f:
                    json.dump(self.extraction_results, f, ensure_ascii=False, indent=2)

                self.add_log(f"结果已导出到: {file_path}", "SUCCESS")
                QMessageBox.information(self, "导出成功", f"时间序列结果已成功导出到:\n{file_path}")

            except Exception as e:
                error_msg = f"导出失败: {str(e)}"
                self.add_log(error_msg, "ERROR")
                QMessageBox.critical(self, "导出错误", error_msg)

class MainWindow(QMainWindow):
    """主窗口"""
    
    def __init__(self):
        super().__init__()
        self.init_ui()
    
    def init_ui(self):
        """初始化主界面"""
        self.setWindowTitle("赔率价值分析系统 v1.0")
        self.setGeometry(100, 100, 1200, 800)
        
        # 设置中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 主布局
        main_layout = QVBoxLayout()
        
        # 标题栏
        header_frame = QFrame()
        header_frame.setStyleSheet("""
            QFrame {
                background-color: #2c3e50;
                color: white;
                padding: 20px;
                border-radius: 10px;
                margin-bottom: 20px;
            }
        """)
        header_layout = QVBoxLayout()
        
        title_label = QLabel("🏈 赔率价值分析系统")
        title_label.setFont(QFont("Arial", 24, QFont.Bold))
        title_label.setStyleSheet("color: white;")
        title_label.setAlignment(Qt.AlignCenter)
        
        subtitle_label = QLabel("专业的足球赔率数据分析与价值挖掘平台")
        subtitle_label.setFont(QFont("Arial", 12))
        subtitle_label.setStyleSheet("color: #bdc3c7;")
        subtitle_label.setAlignment(Qt.AlignCenter)
        
        header_layout.addWidget(title_label)
        header_layout.addWidget(subtitle_label)
        header_frame.setLayout(header_layout)
        main_layout.addWidget(header_frame)
        
        # 标签页
        self.tab_widget = QTabWidget()
        self.tab_widget.setStyleSheet("""
            QTabWidget::pane {
                border: 1px solid #c0c0c0;
                border-radius: 5px;
                background-color: white;
            }
            QTabBar::tab {
                background-color: #f0f0f0;
                padding: 10px 20px;
                margin-right: 2px;
                border-top-left-radius: 5px;
                border-top-right-radius: 5px;
            }
            QTabBar::tab:selected {
                background-color: white;
                border-bottom: 2px solid #2196F3;
            }
        """)
        
        # 添加数据库分析标签页
        self.db_analysis_tab = DatabaseAnalysisTab()
        self.tab_widget.addTab(self.db_analysis_tab, "📊 数据库分析")

        # 添加时间序列提取标签页
        self.timeseries_tab = TimeseriesExtractionTab()
        self.tab_widget.addTab(self.timeseries_tab, "📈 时间序列")

        # 添加占位标签页（后续实现）
        placeholder_tabs = [
            ("🔍 特征工程", "步骤4-6：特征提取与模式识别"),
            ("🔗 联动分析", "步骤7-10：多公司联动行为建模"),
            ("🤖 机器学习", "步骤11-15：预测模型构建"),
            ("💰 策略回测", "步骤16-20：投注策略与回测")
        ]
        
        for tab_name, tab_desc in placeholder_tabs:
            placeholder_widget = QWidget()
            placeholder_layout = QVBoxLayout()
            
            placeholder_label = QLabel(f"{tab_desc}\n\n🚧 功能开发中...")
            placeholder_label.setAlignment(Qt.AlignCenter)
            placeholder_label.setFont(QFont("Arial", 14))
            placeholder_label.setStyleSheet("color: #666; padding: 50px;")
            
            placeholder_layout.addWidget(placeholder_label)
            placeholder_widget.setLayout(placeholder_layout)
            
            self.tab_widget.addTab(placeholder_widget, tab_name)
        
        main_layout.addWidget(self.tab_widget)
        central_widget.setLayout(main_layout)
        
        # 设置应用样式
        self.setStyleSheet("""
            QMainWindow {
                background-color: #f5f5f5;
            }
            QGroupBox {
                font-weight: bold;
                border: 2px solid #cccccc;
                border-radius: 5px;
                margin-top: 10px;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }
        """)

def main():
    """主函数"""
    app = QApplication(sys.argv)
    
    # 设置应用信息
    app.setApplicationName("赔率价值分析系统")
    app.setApplicationVersion("1.0")
    app.setOrganizationName("Odds Analysis Team")
    
    # 创建主窗口
    window = MainWindow()
    window.show()
    
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()

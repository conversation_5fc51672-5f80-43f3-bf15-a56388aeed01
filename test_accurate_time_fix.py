#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试准确时间提取修复
验证完赛后更新功能中的年份问题是否已彻底解决
"""

import sys
import os
from datetime import datetime

def test_accurate_time_extraction():
    """测试准确时间提取功能"""
    
    print("🔧 测试准确时间提取修复")
    print("=" * 50)
    
    try:
        from enhanced_odds_scraper import EnhancedOddsScraper
        
        # 创建抓取器
        scraper = EnhancedOddsScraper()
        print("✅ EnhancedOddsScraper 创建成功")
        
        # 测试问题比赛ID
        test_cases = [
            ("2709881", "之前显示2023年的问题比赛"),
            ("2726299", "2025年的比赛"),
            ("2213559", "2022年的老比赛"),
        ]
        
        print(f"\n📋 测试准确时间提取:")
        print("-" * 40)
        
        for match_id, description in test_cases:
            print(f"\n🎯 测试比赛ID: {match_id} ({description})")
            print("-" * 30)
            
            try:
                # 测试直接的时间提取方法
                accurate_time = scraper._extract_accurate_match_time(match_id)
                
                if accurate_time and accurate_time.get('success'):
                    print(f"✅ 准确时间提取成功")
                    print(f"  完整时间: {accurate_time['match_time']}")
                    print(f"  年份: {accurate_time['year']}")
                    print(f"  来源: {accurate_time['source']}")
                    print(f"  原始时间字符串: {accurate_time['raw_time_str']}")
                else:
                    print(f"❌ 准确时间提取失败: {accurate_time.get('error', '未知错误')}")
                
                # 测试完整的比赛信息提取
                print(f"\n📊 完整比赛信息提取:")
                match_info = scraper.extract_match_info(match_id)
                
                if match_info:
                    print(f"✅ 比赛信息提取成功")
                    print(f"  比赛时间: {match_info.get('match_time', 'N/A')}")
                    print(f"  比赛日期: {match_info.get('match_date', 'N/A')}")
                    print(f"  时间来源: {match_info.get('time_source', 'N/A')}")
                    print(f"  主队: {match_info.get('home_team', 'N/A')}")
                    print(f"  客队: {match_info.get('away_team', 'N/A')}")
                    
                    # 验证年份是否正确
                    match_time = match_info.get('match_time', '')
                    time_source = match_info.get('time_source', '')
                    
                    if time_source == 'analysis_page':
                        print(f"  ✅ 使用了准确时间提取")
                    else:
                        print(f"  ⚠️ 使用了备用方法: {time_source}")
                    
                    # 对于问题比赛ID 2709881，检查是否修复
                    if match_id == "2709881":
                        if match_time.startswith('2025'):
                            print(f"  ✅ 问题已修复：年份正确为2025年")
                        elif match_time.startswith('2023'):
                            print(f"  ❌ 问题未修复：仍显示2023年")
                        else:
                            year = match_time[:4] if match_time else 'Unknown'
                            print(f"  ⚠️ 年份: {year}")
                else:
                    print(f"❌ 比赛信息提取失败")
                    
            except Exception as e:
                print(f"❌ 测试失败: {e}")
                import traceback
                traceback.print_exc()

    except Exception as e:
        print(f"❌ 整体测试失败: {e}")

def test_match_info_extractor():
    """测试 MatchInfoExtractor 的准确时间提取功能"""
    
    print(f"\n🔧 测试 MatchInfoExtractor 准确时间提取")
    print("-" * 50)
    
    try:
        from match_info_extractor import MatchInfoExtractor
        
        # 创建提取器
        extractor = MatchInfoExtractor()
        print("✅ MatchInfoExtractor 创建成功")
        
        # 测试问题比赛ID
        match_id = "2709881"
        print(f"\n📋 测试比赛ID: {match_id}")
        print("-" * 30)
        
        # 测试准确时间提取
        accurate_time = extractor._extract_accurate_match_time(match_id)
        
        if accurate_time and accurate_time.get('success'):
            print(f"✅ 准确时间提取成功")
            print(f"  完整时间: {accurate_time['match_time']}")
            print(f"  年份: {accurate_time['year']}")
        else:
            print(f"❌ 准确时间提取失败: {accurate_time.get('error', '未知错误')}")
            
    except Exception as e:
        print(f"❌ MatchInfoExtractor 测试失败: {e}")

def simulate_update_finished_process():
    """模拟完赛后更新过程"""
    
    print(f"\n🔧 模拟完赛后更新过程")
    print("-" * 50)
    
    try:
        from enhanced_odds_scraper import EnhancedOddsScraper
        
        scraper = EnhancedOddsScraper()
        
        # 模拟完赛后更新的关键步骤
        match_id = "2709881"
        print(f"模拟更新比赛: {match_id}")
        
        # 步骤1：提取比赛信息（这是完赛后更新的第一步）
        print(f"\n📋 步骤1：提取比赛信息")
        match_info = scraper.extract_match_info(match_id)
        
        if match_info:
            print(f"✅ 比赛信息提取成功")
            
            # 检查关键时间字段
            time_fields = ['match_time', 'match_date', 'time_source']
            for field in time_fields:
                if field in match_info:
                    print(f"  {field}: {match_info[field]}")
            
            # 验证年份修复
            match_time = match_info.get('match_time', '')
            if match_time.startswith('2025'):
                print(f"  ✅ 年份修复成功：{match_time}")
                print(f"  ✅ 完赛后更新功能应该能正确处理时间")
            else:
                print(f"  ❌ 年份仍有问题：{match_time}")
                
        else:
            print(f"❌ 比赛信息提取失败")
            
        # 步骤2：模拟数据保存（检查数据结构）
        print(f"\n📋 步骤2：检查数据结构")
        if match_info:
            required_fields = ['match_id', 'match_time', 'match_date', 'home_team', 'away_team']
            missing_fields = []
            
            for field in required_fields:
                if field not in match_info or not match_info[field]:
                    missing_fields.append(field)
            
            if not missing_fields:
                print(f"  ✅ 所有必需字段都存在")
            else:
                print(f"  ⚠️ 缺少字段: {missing_fields}")
                
    except Exception as e:
        print(f"❌ 模拟过程失败: {e}")

def main():
    """主函数"""
    
    print("🚀 准确时间提取修复测试")
    print("=" * 60)
    
    # 测试1：准确时间提取功能
    test_accurate_time_extraction()
    
    # 测试2：MatchInfoExtractor
    test_match_info_extractor()
    
    # 测试3：模拟完赛后更新过程
    simulate_update_finished_process()
    
    print(f"\n" + "=" * 60)
    print("📋 测试总结")
    print("=" * 60)
    
    print("✅ 准确时间提取功能已实现")
    print("✅ 优先从分析页面获取准确时间")
    print("✅ 备用智能年份推测作为后备方案")
    print("✅ 完赛后更新功能应该能正确处理年份")
    
    print(f"\n🎯 修复说明:")
    print("1. 新增从分析页面提取准确时间的功能")
    print("2. 优先使用 var headMatchTime 获取准确年份")
    print("3. 如果分析页面失败，回退到智能年份推测")
    print("4. 完赛后更新功能现在会获得准确的时间信息")
    
    print(f"\n🚀 测试完成！现在可以测试完赛后更新功能")

if __name__ == "__main__":
    main()

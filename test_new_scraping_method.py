#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试新的赔率抓取方法
验证从赔率列表页面获取链接的方法是否有效
"""

import requests
from bs4 import BeautifulSoup
import re
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_odds_list_page(match_id):
    """测试赔率列表页面"""
    url = f"https://1x2.titan007.com/oddslist/{match_id}.htm"
    print(f"🔍 测试赔率列表页面: {url}")
    
    try:
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
            'Accept-Language': 'zh-CN,zh;q=0.8,en-US;q=0.5,en;q=0.3',
            'Connection': 'keep-alive',
        }
        
        response = requests.get(url, headers=headers, timeout=15)
        
        if response.status_code != 200:
            print(f"❌ HTTP请求失败: {response.status_code}")
            return False
        
        print(f"✅ 页面访问成功，内容长度: {len(response.text)}")

        # 保存页面内容用于调试
        with open(f'debug_oddslist_{match_id}.html', 'w', encoding='utf-8') as f:
            f.write(response.text)
        print(f"📄 页面内容已保存到 debug_oddslist_{match_id}.html")

        # 解析页面内容
        soup = BeautifulSoup(response.text, 'html.parser')

        # 查找所有链接
        links = soup.find_all('a', href=True)
        print(f"🔗 页面中总共有 {len(links)} 个链接")

        # 查看前10个链接的内容
        print("📋 前10个链接:")
        for i, link in enumerate(links[:10]):
            href = link.get('href', '')
            text = link.get_text().strip()
            print(f"  {i+1}. href: {href[:100]}... text: {text[:50]}...")

        odds_links = []
        oddshistory_links = []

        for link in links:
            href = link.get('href', '')

            # 收集所有包含 OddsHistory 的链接
            if 'OddsHistory.aspx' in href:
                oddshistory_links.append(href)

            # 匹配赔率历史链接格式
            if 'OddsHistory.aspx' in href and f'sid={match_id}' in href:
                # 提取公司ID
                cid_match = re.search(r'cid=(\d+)', href)
                if cid_match:
                    company_id = cid_match.group(1)

                    # 获取链接文本（可能是公司名称）
                    link_text = link.get_text().strip()

                    full_url = f"https://1x2.titan007.com/{href}" if not href.startswith('http') else href

                    odds_links.append({
                        'company_id': company_id,
                        'company_name': link_text,
                        'url': full_url
                    })

        print(f"🔍 找到 {len(oddshistory_links)} 个 OddsHistory 链接:")
        for i, href in enumerate(oddshistory_links[:5]):
            print(f"  {i+1}. {href}")

        if len(oddshistory_links) > 5:
            print(f"  ... 还有 {len(oddshistory_links) - 5} 个")

        # 检查是否有包含当前比赛ID的链接
        matching_links = [href for href in oddshistory_links if f'sid={match_id}' in href]
        print(f"📊 包含 sid={match_id} 的链接: {len(matching_links)} 个")
        
        print(f"📊 找到 {len(odds_links)} 个赔率链接:")
        for i, link_info in enumerate(odds_links[:10]):  # 只显示前10个
            print(f"  {i+1}. 公司ID: {link_info['company_id']}, 名称: {link_info['company_name']}")
            print(f"     URL: {link_info['url']}")
        
        if len(odds_links) > 10:
            print(f"  ... 还有 {len(odds_links) - 10} 个链接")
        
        return odds_links
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def test_odds_history_page(odds_url, company_name):
    """测试赔率历史页面"""
    print(f"\n🔍 测试赔率历史页面: {company_name}")
    print(f"URL: {odds_url}")
    
    try:
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
            'Accept-Language': 'zh-CN,zh;q=0.8,en-US;q=0.5,en;q=0.3',
            'Connection': 'keep-alive',
        }
        
        response = requests.get(odds_url, headers=headers, timeout=15)
        
        if response.status_code != 200:
            print(f"❌ HTTP请求失败: {response.status_code}")
            return False
        
        print(f"✅ 页面访问成功，内容长度: {len(response.text)}")
        
        # 解析页面内容
        soup = BeautifulSoup(response.text, 'html.parser')
        
        # 查找赔率数据表格
        tables = soup.find_all('table')
        print(f"📊 找到 {len(tables)} 个表格")
        
        odds_count = 0
        for table_idx, table in enumerate(tables):
            rows = table.find_all('tr')
            print(f"  表格 {table_idx + 1}: {len(rows)} 行")
            
            for row_idx, row in enumerate(rows[:5]):  # 只检查前5行
                cells = row.find_all(['td', 'th'])
                if len(cells) >= 4:
                    cell_texts = [cell.get_text().strip() for cell in cells[:6]]
                    print(f"    行 {row_idx + 1}: {cell_texts}")
                    
                    # 尝试解析赔率数据
                    try:
                        if row_idx > 0:  # 跳过表头
                            # 检查是否包含时间格式
                            time_text = cell_texts[0]
                            if re.match(r'\d{2}-\d{2}\s+\d{2}:\d{2}', time_text):
                                # 尝试解析赔率
                                home_odds = float(cell_texts[1])
                                draw_odds = float(cell_texts[2])
                                away_odds = float(cell_texts[3])
                                
                                print(f"      ✅ 解析到赔率: {home_odds} {draw_odds} {away_odds}")
                                odds_count += 1
                    except (ValueError, IndexError):
                        pass
        
        print(f"📈 总共解析到 {odds_count} 条有效赔率记录")
        return odds_count > 0
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🎯 测试新的赔率抓取方法")
    print("=" * 60)
    
    # 测试比赛ID
    test_match_ids = [
        "2598314",  # 较新的比赛
        "2804677",  # 您提到的比赛ID
        "2500000"   # 较老的比赛
    ]
    
    for match_id in test_match_ids:
        print(f"\n{'='*50}")
        print(f"🧪 测试比赛ID: {match_id}")
        print(f"{'='*50}")
        
        # 测试赔率列表页面
        odds_links = test_odds_list_page(match_id)
        
        if odds_links and len(odds_links) > 0:
            # 测试第一个赔率历史页面
            first_link = odds_links[0]
            test_odds_history_page(first_link['url'], first_link['company_name'])
        else:
            print("❌ 未找到赔率链接，跳过历史页面测试")
    
    print(f"\n🎯 测试完成！")
    print("如果看到有效的赔率数据解析，说明新方法可行。")

if __name__ == "__main__":
    main()

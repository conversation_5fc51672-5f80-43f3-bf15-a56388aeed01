#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最终回测功能调试测试
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from PyQt5.QtWidgets import QApplication
from odds_combination_ui import OddsCombinationMainWindow

def main():
    """启动UI进行最终回测调试"""
    print("🔧 最终回测功能调试测试")
    print("=" * 80)
    print("🎯 本次修复:")
    print("✅ 修复了数据库连接方式（使用database.get_match_info）")
    print("✅ 添加了详细的DEBUG输出")
    print("✅ 确认比赛结果获取逻辑正确")
    print("")
    print("📝 测试步骤:")
    print("1. 启动UI")
    print("2. 选择数据库（确保包含比赛2596914等）")
    print("3. 设置ID区间（如：2596914-2596930）")
    print("4. 选择博彩公司")
    print("5. 点击'ID区间数据分析'")
    print("6. 等待分析完成")
    print("7. 设置回测参数并点击'回测'")
    print("8. 查看详细的DEBUG输出")
    print("")
    print("🔍 重点观察:")
    print("- 每个比赛的详细处理过程")
    print("- 比赛结果获取是否成功")
    print("- 赔率提取是否正确")
    print("- 投资条件判断是否准确")
    print("- 收益计算是否正确")
    print("")
    print("📊 预期结果:")
    print("- 比赛2596914应该显示'实际主胜'")
    print("- 如果满足投资条件，收益应该为正数")
    print("- DEBUG输出应该显示完整的处理过程")
    print("")
    print("⚠️ 如果仍显示'实际未知':")
    print("- 检查数据库是否正确选择")
    print("- 检查比赛ID是否存在于数据库中")
    print("- 查看DEBUG输出中的错误信息")
    print("")
    print("🚀 正在启动UI...")
    
    app = QApplication(sys.argv)
    
    # 创建主窗口
    window = OddsCombinationMainWindow()
    window.show()
    
    print("✅ UI已启动")
    print("📊 所有DEBUG信息将在此控制台显示")
    print("🎯 请按照上述步骤进行测试")
    print("")
    print("💡 调试提示:")
    print("- 如果看到'DEBUG: 开始获取比赛XXX的结果'，说明进入了比赛结果获取流程")
    print("- 如果看到'DEBUG: 获取到的比赛信息: {...}'，说明数据库查询成功")
    print("- 如果看到'DEBUG: 判断结果: 主胜'，说明比赛结果判断正确")
    print("- 如果看到'DEBUG: 回报: X.X'，说明收益计算完成")
    
    # 运行应用
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()

#!/usr/bin/env python3
# -*- coding: utf-8 -*-

print("快速测试新功能")
print("=" * 30)

# 测试基本计算
home_odds = 1.95
draw_odds = 3.4
away_odds = 4.0

print(f"赔率: {home_odds}, {draw_odds}, {away_odds}")

# 计算隐含概率
home_prob_raw = 1.0 / home_odds
draw_prob_raw = 1.0 / draw_odds
away_prob_raw = 1.0 / away_odds

print(f"原始概率: {home_prob_raw:.4f}, {draw_prob_raw:.4f}, {away_prob_raw:.4f}")

# 计算返还率
return_rate = home_prob_raw + draw_prob_raw + away_prob_raw
print(f"返还率: {return_rate:.4f}")

# 归一化
p = [home_prob_raw / return_rate, draw_prob_raw / return_rate, away_prob_raw / return_rate]
print(f"归一化概率: {p[0]:.4f}, {p[1]:.4f}, {p[2]:.4f}")
print(f"概率和: {sum(p):.4f}")

# 观察结果
observed = [6, 0, 0]
print(f"观察结果: {observed}")

print("\n✅ 基本计算逻辑正确")
print("💡 UI应该能正常显示新的两列")
print("💡 如果scipy可用，会显示实际概率值")
print("💡 如果scipy不可用，会显示1.000000")

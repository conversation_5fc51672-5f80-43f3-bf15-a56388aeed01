#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试页面结构 - 查看赔率数据的实际结构
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from enhanced_odds_scraper import EnhancedOddsScraper
from bs4 import BeautifulSoup
import re

def debug_page_structure():
    """调试页面结构"""
    print("🔍 调试页面结构 - 比赛ID 2213559")
    print("=" * 60)
    
    scraper = EnhancedOddsScraper()
    match_id = "2213559"
    
    url = f"https://1x2.titan007.com/oddslist/{match_id}.htm"
    print(f"🌐 分析URL: {url}")
    
    content = scraper.get_page_content(url)
    if not content:
        print("❌ 无法获取页面内容")
        return
        
    soup = BeautifulSoup(content, 'html.parser')
    
    # 查找表格
    print(f"\n📊 查找表格结构:")
    tables = soup.find_all('table')
    print(f"表格数量: {len(tables)}")
    
    for i, table in enumerate(tables, 1):
        rows = table.find_all('tr')
        print(f"  表格 {i}: {len(rows)} 行")
        
        # 查看前几行的内容
        for j, row in enumerate(rows[:3], 1):
            cells = row.find_all(['td', 'th'])
            if cells:
                cell_texts = [cell.get_text(strip=True) for cell in cells[:5]]  # 前5列
                print(f"    行 {j}: {cell_texts}")
    
    # 查找包含公司名称的元素
    print(f"\n🏢 查找公司名称:")
    company_names = ['bet365', '威廉希尔', '立博', 'Interwetten']
    
    for company in company_names:
        elements = soup.find_all(text=re.compile(company, re.IGNORECASE))
        print(f"  {company}: 找到 {len(elements)} 个匹配")
        
        # 查找包含公司名称的父元素
        for element in elements[:2]:  # 只看前2个
            parent = element.parent
            if parent:
                print(f"    父元素: {parent.name}, 属性: {parent.attrs}")
                # 查看是否有链接
                links = parent.find_all('a', href=True)
                for link in links:
                    print(f"      链接: {link.get('href')}")
    
    # 查找所有onclick事件的详细内容
    print(f"\n🖱️ 分析onclick事件:")
    onclick_elements = soup.find_all(attrs={"onclick": True})
    
    for i, element in enumerate(onclick_elements[:10], 1):
        onclick = element.get('onclick', '')
        text = element.get_text(strip=True)
        print(f"  {i}. 元素: {element.name}, 文本: '{text[:20]}...', onclick: {onclick[:50]}...")
    
    # 查找JavaScript代码
    print(f"\n📜 分析JavaScript代码:")
    scripts = soup.find_all('script')
    
    for i, script in enumerate(scripts, 1):
        if script.string and len(script.string) > 100:
            # 查找包含数字ID的代码
            if re.search(r'\d{8,}', script.string):  # 8位以上的数字
                print(f"  脚本 {i}: 包含长数字ID")
                # 提取所有8位以上的数字
                long_numbers = re.findall(r'\d{8,}', script.string)
                print(f"    长数字: {long_numbers[:5]}")  # 显示前5个
                
                # 查找可能的URL模式
                url_patterns = re.findall(r'https?://[^\s"\']+', script.string)
                if url_patterns:
                    print(f"    URL模式: {url_patterns[:3]}")  # 显示前3个
    
    # 查找可能的数据属性
    print(f"\n📋 查找数据属性:")
    data_elements = soup.find_all(attrs=lambda x: x and any(key.startswith('data-') for key in x.keys()))
    
    for i, element in enumerate(data_elements[:5], 1):
        data_attrs = {k: v for k, v in element.attrs.items() if k.startswith('data-')}
        print(f"  {i}. 元素: {element.name}, 数据属性: {data_attrs}")
    
    # 查找可能包含赔率数据的行
    print(f"\n📈 查找赔率数据行:")
    all_rows = soup.find_all('tr')
    
    for i, row in enumerate(all_rows, 1):
        cells = row.find_all(['td', 'th'])
        if len(cells) >= 3:  # 至少3列（可能是主胜、平局、客胜）
            cell_texts = [cell.get_text(strip=True) for cell in cells]
            
            # 检查是否包含赔率格式的数字（如1.50, 2.30等）
            odds_pattern = re.compile(r'^\d+\.\d{2}$')
            odds_count = sum(1 for text in cell_texts if odds_pattern.match(text))
            
            if odds_count >= 2:  # 至少有2个赔率格式的数字
                print(f"  行 {i}: {cell_texts[:8]}")  # 显示前8列
                
                # 查看这行是否有特殊属性或事件
                if row.attrs:
                    print(f"    属性: {row.attrs}")

if __name__ == "__main__":
    debug_page_structure()

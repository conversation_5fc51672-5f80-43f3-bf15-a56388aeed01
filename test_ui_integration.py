#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试博彩态度2筛选的界面集成
"""

import tkinter as tk
from tkinter import ttk
import sys
import os

# 添加当前目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from odds_scraper_ui import OddsScraperGUI

def test_betting_attitude2_integration():
    """测试博彩态度2筛选的界面集成"""
    print("🔍 测试博彩态度2筛选的界面集成")
    print("=" * 60)
    
    try:
        # 创建主窗口
        root = tk.Tk()
        root.withdraw()  # 隐藏主窗口
        
        # 创建UI实例
        app = OddsScraperGUI(root)
        
        # 检查filter_widgets是否包含betting_attitude2
        if 'betting_attitude2' in app.filter_widgets:
            print("✅ betting_attitude2 已正确添加到 filter_widgets")
            
            widgets = app.filter_widgets['betting_attitude2']
            print(f"✅ 包含的控件: {list(widgets.keys())}")
            
            # 测试设置值
            try:
                widgets['enable'].set(True)
                widgets['odds_type'].set('home_odds')
                widgets['threshold'].set('5')
                widgets['company'].set('澳门')
                
                print("✅ 控件值设置成功")
                print(f"  启用: {widgets['enable'].get()}")
                print(f"  赔率类型: {widgets['odds_type'].get()}")
                print(f"  阈值: {widgets['threshold'].get()}")
                print(f"  公司: {widgets['company'].get()}")
                
                # 测试筛选逻辑
                print("\n🧪 测试筛选逻辑...")
                
                # 模拟apply_filters中的逻辑
                if widgets['enable'].get():
                    odds_type = widgets['odds_type'].get()
                    threshold = int(widgets['threshold'].get())
                    selected_company = widgets['company'].get()
                    
                    if selected_company:
                        print(f"✅ 筛选参数正确: {odds_type}, 阈值{threshold}, {selected_company}")
                        
                        # 测试实际筛选
                        matches = app.apply_betting_attitude2_filter(odds_type, threshold, selected_company)
                        print(f"✅ 筛选结果: {len(matches)} 场比赛")
                        
                        if len(matches) > 0:
                            print(f"  前5场比赛: {matches[:5]}")
                    else:
                        print("❌ 未选择公司")
                else:
                    print("❌ 筛选未启用")
                    
            except Exception as e:
                print(f"❌ 控件操作失败: {e}")
                
        else:
            print("❌ betting_attitude2 未找到在 filter_widgets 中")
            print(f"可用的筛选器: {list(app.filter_widgets.keys())}")
        
        # 销毁窗口
        root.destroy()
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_betting_attitude2_integration()

# 安全数据管理说明

## 🔒 **数据安全保证**

**重要承诺：你的数据永远不会丢失！**

所有的"清理"操作都是安全的，有以下几种处理方式：

## 📁 **数据处理方式**

### 1. **数据归档** (默认推荐)
```
原数据库: odds_data.db (31MB)
↓ 归档90天前的数据
归档文件: odds_data_archive_20241201.db (25MB)
新主库: odds_data.db (6MB)
```
- ✅ **数据完全保留**
- ✅ **可以随时访问历史数据**
- ✅ **主数据库变小，性能提升**

### 2. **CSV导出归档**
```
原数据库: odds_data.db
↓ 导出为CSV
matches_archive_20241201.csv
odds_archive_20241201.csv
↓ 从主库移除
主库变小
```
- ✅ **数据以CSV格式永久保存**
- ✅ **可用Excel等工具查看**
- ✅ **需要时可重新导入**

### 3. **数据库分割**
```
原数据库: odds_data.db
↓ 按时间分割
odds_2024_Q1.db (1-3月数据)
odds_2024_Q2.db (4-6月数据)
odds_2024_Q3.db (7-9月数据)
odds_2024_Q4.db (10-12月数据)
```
- ✅ **所有数据分类保存**
- ✅ **便于管理和查询**
- ✅ **单个文件大小可控**

## 🛡️ **安全模式设置**

### 默认安全配置
```python
# 在config.py中设置
DATABASE_CONFIG = {
    'safe_mode': True,           # 启用安全模式
    'archive_before_cleanup': True,  # 清理前先归档
    'max_size_mb': 500,         # 大小限制
    'archive_days': 90,         # 归档90天前数据
    'never_delete_mode': True,  # 永不删除模式
}
```

### 三种安全级别

#### 🟢 **最安全模式** (推荐)
- 永不删除数据
- 只进行归档和分割
- 所有历史数据都保留

#### 🟡 **平衡模式**
- 归档重要数据
- 删除非关键数据
- 保留最近6个月数据

#### 🔴 **空间优先模式**
- 只保留最近30天
- 其他数据导出后删除
- 需要用户明确确认

## 📊 **实际操作示例**

### 当前状况
- 数据库大小：31.45 MB
- 比赛数量：409 场
- 建议操作：**暂时不需要任何清理**

### 当达到100MB时
```python
# 自动归档（安全模式）
manager = DatabaseManager()
result = manager.safe_archive_old_data(90, "db")  # 归档90天前数据
```

### 当达到500MB时
```python
# 分割数据库
manager.split_database_by_date("2024-01-01", "odds_2024.db")
```

## 🔧 **使用安全管理工具**

### 1. 检查当前状态
```bash
python database_manager.py analyze
```

### 2. 安全归档（推荐）
```bash
# 归档90天前数据到新数据库文件
python -c "
from database_manager import DatabaseManager
manager = DatabaseManager()
result = manager.safe_archive_old_data(90, 'db')
print('归档完成:', result)
"
```

### 3. 导出为CSV（永久保存）
```bash
# 导出为CSV格式
python -c "
from database_manager import DatabaseManager
manager = DatabaseManager()
result = manager.safe_archive_old_data(90, 'csv')
print('导出完成:', result)
"
```

## 📋 **数据恢复方法**

### 从归档数据库恢复
```python
# 如果需要查询历史数据
archive_db = OddsDatabase("odds_data_archive_20241201.db")
historical_matches = archive_db.get_all_matches()
```

### 从CSV恢复
```python
import pandas as pd
# 读取CSV数据
matches_df = pd.read_csv("matches_archive_20241201.csv")
odds_df = pd.read_csv("odds_archive_20241201.csv")
# 可以重新导入到数据库
```

## 🎯 **推荐策略**

### 对于你的情况（31MB）
**建议：暂时什么都不做**
- 当前大小完全可以接受
- 性能不会有问题
- 继续正常使用即可

### 当达到100MB时
**建议：启用监控**
- 开始关注增长速度
- 设置自动备份
- 准备归档策略

### 当达到300MB时
**建议：开始归档**
- 归档6个月前的数据
- 保持主库在100MB以内
- 定期执行优化

### 当达到500MB时
**建议：分库管理**
- 按年份或联赛分库
- 建立数据管理流程
- 考虑升级到专业数据库

## ✅ **总结**

1. **你的数据绝对安全** - 我们提供的所有工具都优先保证数据不丢失
2. **当前无需操作** - 31MB的大小完全没问题
3. **未来有完整方案** - 当数据增长时，有多种安全的处理方式
4. **灵活选择** - 可以根据需要选择归档、导出或分割
5. **随时恢复** - 所有归档的数据都可以随时访问和恢复

**放心使用吧！你的宝贵数据不会丢失的！** 🎉

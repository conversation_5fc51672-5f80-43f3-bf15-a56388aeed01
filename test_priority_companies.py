#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试更新后的优先级公司列表
验证是否包含您常用的17家目标公司
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from config import get_priority_companies, get_all_companies

def test_priority_companies():
    """测试优先级公司列表"""
    print("🔧 测试更新后的优先级公司列表")
    print("=" * 80)
    
    # 您在其他脚本中使用的17家目标公司
    your_target_companies = [
        'bet365', 'betathome', 'betfair', 'bwin', 'coral',
        'Interwetten', 'pinnacle', '伟德', '利记', '威廉希尔',
        '明升', '易胜博', '澳门', '立博', '金宝博', '香港马会', '竞彩官方'
    ]
    
    print(f"📋 您常用的目标公司 (共{len(your_target_companies)}家):")
    for i, company in enumerate(your_target_companies, 1):
        print(f"  {i:2d}. {company}")
    
    # 测试不同数量的优先级公司
    test_counts = [10, 15, 17, 20]
    
    for count in test_counts:
        print(f"\n📊 优先级前{count}家公司:")
        print("-" * 60)
        
        priority_companies = get_priority_companies(count)
        company_names = list(priority_companies.values())
        
        print(f"实际获取到 {len(company_names)} 家公司:")
        for i, (company_id, company_name) in enumerate(priority_companies.items(), 1):
            # 检查是否在您的目标列表中
            is_target = "🎯" if company_name in your_target_companies else "  "
            print(f"  {is_target} {i:2d}. {company_id:4s}: {company_name}")
        
        # 统计覆盖率
        found_companies = set(company_names) & set(your_target_companies)
        missing_companies = set(your_target_companies) - set(company_names)
        
        print(f"\n📈 覆盖率分析:")
        print(f"  找到目标公司: {len(found_companies)}/{len(your_target_companies)} ({len(found_companies)/len(your_target_companies)*100:.1f}%)")
        
        if missing_companies:
            print(f"  缺失的公司: {', '.join(sorted(missing_companies))}")
        else:
            print(f"  ✅ 完全覆盖所有目标公司！")

def test_company_mapping():
    """测试公司名称映射"""
    print(f"\n🔍 测试公司名称映射")
    print("=" * 80)
    
    # 检查可能的名称变体
    name_variants = {
        'betfair': ['betfair', 'Betfair'],
        'pinnacle': ['pinnacle', 'Pinnacle'],
        'bwin': ['bwin', 'Bwin'],
        'coral': ['coral', 'Coral'],
        'Interwetten': ['Interwetten', 'interwetten'],
        '竞彩官方': ['竞彩官方', '竞猜官方']
    }
    
    all_companies = get_all_companies()
    
    for target_name, variants in name_variants.items():
        print(f"\n🔍 查找 '{target_name}' 的变体:")
        found_variants = []
        
        for company_id, company_name in all_companies.items():
            if company_name in variants:
                found_variants.append(f"{company_id}: {company_name}")
        
        if found_variants:
            for variant in found_variants:
                print(f"  ✅ {variant}")
        else:
            print(f"  ❌ 未找到任何变体")

def main():
    """主函数"""
    print("🎯 优先级公司列表测试")
    print("=" * 80)
    
    test_priority_companies()
    test_company_mapping()
    
    print("\n" + "=" * 80)
    print("🎉 测试完成！")
    print("\n📝 说明:")
    print("- 现在批量抓取设置max_companies=20时，应该能获取到17家目标公司")
    print("- 如果还有缺失，可能是某些公司在特定比赛中没有数据")

if __name__ == "__main__":
    main()

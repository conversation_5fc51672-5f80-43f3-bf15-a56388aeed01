#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试优化分析器
"""

import sys
import os
import time

# 添加父目录到Python路径
parent_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, parent_dir)

from optimized_analyzer import OptimizedOddsCombinationAnalyzer

def test_optimized_analyzer():
    """测试优化分析器"""
    print("测试优化分析器")
    print("=" * 40)
    
    # 使用找到的测试比赛
    test_match_id = "2617497"  # 比萨 vs 弗洛西诺尼，有108条bet365数据
    test_company = "bet365"
    test_combination_size = 3
    
    print(f"测试参数:")
    print(f"  比赛ID: {test_match_id}")
    print(f"  博彩公司: {test_company}")
    print(f"  组合大小: {test_combination_size}")
    print()
    
    try:
        print("🚀 开始测试...")
        start_time = time.time()
        
        analyzer = OptimizedOddsCombinationAnalyzer(
            max_workers=4,
            batch_size=100
        )
        
        result = analyzer.analyze_odds_combinations_optimized(
            test_match_id, test_company, test_combination_size
        )
        
        elapsed_time = time.time() - start_time
        
        if result['success']:
            print(f"✅ 测试成功！")
            print(f"   耗时: {elapsed_time:.2f} 秒")
            print(f"   组合数: {result['total_combinations']}")
            print(f"   搜索比赛数: {result['total_other_matches']}")
            
            # 显示前3个组合的结果
            print(f"\n前3个组合的匹配情况:")
            for i, combo_result in enumerate(result['results'][:3]):
                print(f"   组合{i+1}: 匹配{combo_result['match_count']}场比赛")
            
            # 估算性能提升
            estimated_standard_time = result['total_combinations'] * 8.5
            if elapsed_time > 0:
                speedup = estimated_standard_time / elapsed_time
                print(f"\n性能对比:")
                print(f"   标准分析器预计需要: {estimated_standard_time:.0f} 秒 ({estimated_standard_time/60:.1f} 分钟)")
                print(f"   优化分析器实际用时: {elapsed_time:.1f} 秒")
                print(f"   性能提升: {speedup:.1f}x")
            
            return True
        else:
            print(f"❌ 测试失败: {result['error']}")
            return False
            
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == '__main__':
    success = test_optimized_analyzer()
    if success:
        print("\n🎉 优化分析器测试通过！")
        print("💡 现在可以在UI中选择使用优化分析器了")
        print("💡 优化分析器的主要改进:")
        print("   - 批量数据库查询，减少连接次数")
        print("   - 内存缓存，避免重复查询")
        print("   - 并发处理，提高CPU利用率")
        print("   - 数据库索引优化，加快查询速度")
    else:
        print("\n❌ 测试失败，请检查配置。")

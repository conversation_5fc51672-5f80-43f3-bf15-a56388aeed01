#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试完赛后更新功能修复
"""

import sys
import os

def test_scraper_method():
    """测试抓取器方法"""
    
    print("🔍 测试抓取器方法")
    print("=" * 50)
    
    try:
        from enhanced_odds_scraper import EnhancedOddsScraper
        
        # 创建抓取器实例
        scraper = EnhancedOddsScraper()
        print("✅ 成功创建 EnhancedOddsScraper 实例")
        
        # 检查方法是否存在
        if hasattr(scraper, 'scrape_complete_match_data'):
            print("✅ scrape_complete_match_data 方法存在")
        else:
            print("❌ scrape_complete_match_data 方法不存在")
            return
        
        # 测试方法调用（不实际抓取）
        print("\n🧪 测试方法签名:")
        import inspect
        sig = inspect.signature(scraper.scrape_complete_match_data)
        print(f"方法签名: scrape_complete_match_data{sig}")
        
        # 检查参数
        params = list(sig.parameters.keys())
        print(f"参数列表: {params}")
        
        if 'match_id' in params:
            print("✅ match_id 参数存在")
        else:
            print("❌ match_id 参数不存在")
            
        if 'max_companies' in params:
            print("✅ max_companies 参数存在")
        else:
            print("❌ max_companies 参数不存在")
            
        if 'delay' in params:
            print("✅ delay 参数存在")
        else:
            print("❌ delay 参数不存在")
        
        print("\n📊 方法调用应该使用以下格式:")
        print("result = scraper.scrape_complete_match_data(")
        print("    match_id=match_id,")
        print("    max_companies=max_companies,")
        print("    delay=delay")
        print(")")
        
        print("\n📋 返回数据结构应该包含:")
        print("- match_info: 比赛信息字典")
        print("- odds_data: 赔率数据列表")
        print("- summary: 摘要信息字典")
        
    except ImportError as e:
        print(f"❌ 导入 EnhancedOddsScraper 失败: {e}")
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")

def test_ui_code():
    """测试UI代码修复"""
    
    print(f"\n🔧 测试UI代码修复:")
    print("-" * 30)
    
    try:
        # 检查UI文件
        with open('odds_scraper_ui.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查方法调用
        if 'scrape_complete_match_data' in content:
            print("✅ 使用正确的方法名 scrape_complete_match_data")
        else:
            print("❌ 未找到 scrape_complete_match_data 方法调用")
        
        # 检查参数传递
        if 'match_id=match_id' in content and 'max_companies=max_companies' in content:
            print("✅ 参数传递正确")
        else:
            print("❌ 参数传递可能有问题")
        
        # 检查返回值处理
        if "result.get('match_info')" in content and "result.get('odds_data')" in content:
            print("✅ 返回值处理正确")
        else:
            print("❌ 返回值处理可能有问题")
        
        # 检查数据保存
        if 'save_match_info' in content and 'save_odds_data' in content:
            print("✅ 数据保存逻辑存在")
        else:
            print("❌ 数据保存逻辑可能有问题")
        
        print(f"\n✅ UI代码检查完成")
        
    except Exception as e:
        print(f"❌ UI代码检查失败: {e}")

def test_database_methods():
    """测试数据库方法"""
    
    print(f"\n💾 测试数据库方法:")
    print("-" * 30)
    
    try:
        from database import Database
        
        # 查找数据库文件
        db_files = []
        for file in os.listdir('.'):
            if file.endswith('.db'):
                db_files.append(file)
        
        if not db_files:
            print("❌ 未找到数据库文件")
            return
        
        db_path = db_files[0]
        print(f"📂 使用数据库: {db_path}")
        
        # 创建数据库实例
        db = Database(db_path)
        print("✅ 成功创建数据库实例")
        
        # 检查方法是否存在
        if hasattr(db, 'save_match_info'):
            print("✅ save_match_info 方法存在")
        else:
            print("❌ save_match_info 方法不存在")
            
        if hasattr(db, 'save_odds_data'):
            print("✅ save_odds_data 方法存在")
        else:
            print("❌ save_odds_data 方法不存在")
        
        print("✅ 数据库方法检查完成")
        
    except ImportError as e:
        print(f"❌ 导入数据库模块失败: {e}")
    except Exception as e:
        print(f"❌ 数据库测试失败: {e}")

if __name__ == "__main__":
    test_scraper_method()
    test_ui_code()
    test_database_methods()
    
    print(f"\n🎯 修复总结:")
    print("1. 确认使用正确的方法名: scrape_complete_match_data")
    print("2. 确认参数传递正确: match_id, max_companies, delay")
    print("3. 确认返回值处理正确: match_info, odds_data")
    print("4. 确认数据保存逻辑正确: save_match_info, save_odds_data")
    print("")
    print("🚀 现在可以重新测试完赛后更新功能！")

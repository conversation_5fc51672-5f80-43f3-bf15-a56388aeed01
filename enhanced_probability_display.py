#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增强的进失球概率显示组件
按照30场数据、50场数据分别显示，每个面板按时间段对比

功能特点:
1. 30场数据面板 + 50场数据面板
2. 按时间段分列显示
3. 主客队进失球概率并列对比
4. 概率趋势分析
5. 关键时段标识
"""

import tkinter as tk
from tkinter import ttk
from typing import Dict, List, Optional, Any
import logging

logger = logging.getLogger(__name__)

class EnhancedProbabilityDisplay:
    """增强的进失球概率显示器"""
    
    def __init__(self, parent_frame):
        """初始化显示器"""
        self.parent_frame = parent_frame
        
        # 时间段配置
        self.time_segments = [
            ('0-15分钟', '开场阶段'),
            ('16-30分钟', '适应阶段'),
            ('31-45分钟', '上半场末'),
            ('46-60分钟', '下半场初'),
            ('61-75分钟', '关键阶段'),
            ('76-90分钟', '最后阶段'),
            ('90+分钟', '伤停补时')
        ]
        
        # 创建界面
        self.create_widgets()
    
    def create_widgets(self):
        """创建界面组件"""
        # 主容器
        main_frame = ttk.Frame(self.parent_frame)
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # 工具栏
        self.create_toolbar(main_frame)
        
        # 创建Notebook来分别显示30场和50场数据
        self.notebook = ttk.Notebook(main_frame)
        self.notebook.pack(fill=tk.BOTH, expand=True, pady=(10, 0))
        
        # 30场数据标签页
        self.create_probability_tab("30场数据", "Count_30")
        
        # 50场数据标签页
        self.create_probability_tab("50场数据", "Count_50")
        
        # 概率分析标签页
        self.create_analysis_tab()
    
    def create_toolbar(self, parent):
        """创建工具栏"""
        toolbar_frame = ttk.Frame(parent)
        toolbar_frame.pack(fill=tk.X, pady=(0, 5))
        
        # 显示选项
        ttk.Label(toolbar_frame, text="显示内容:").pack(side=tk.LEFT, padx=(0, 5))
        
        self.show_goals = tk.BooleanVar(value=True)
        self.show_conceded = tk.BooleanVar(value=True)
        self.show_analysis = tk.BooleanVar(value=True)
        
        ttk.Checkbutton(toolbar_frame, text="进球概率", variable=self.show_goals,
                       command=self.refresh_display).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Checkbutton(toolbar_frame, text="失球概率", variable=self.show_conceded,
                       command=self.refresh_display).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Checkbutton(toolbar_frame, text="趋势分析", variable=self.show_analysis,
                       command=self.refresh_display).pack(side=tk.LEFT, padx=(0, 10))
        
        # 刷新按钮
        ttk.Button(toolbar_frame, text="刷新", command=self.refresh_display).pack(side=tk.RIGHT)
    
    def create_probability_tab(self, tab_name: str, data_type: str):
        """创建概率数据标签页"""
        frame = ttk.Frame(self.notebook)
        self.notebook.add(frame, text=tab_name)
        
        # 创建左右分栏
        paned_window = ttk.PanedWindow(frame, orient=tk.HORIZONTAL)
        paned_window.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 左侧：进球概率
        goals_frame = ttk.LabelFrame(paned_window, text="⚽ 进球概率", padding="10")
        paned_window.add(goals_frame, weight=1)
        
        goals_tree = self.create_probability_tree(goals_frame)
        setattr(self, f'{data_type.lower()}_goals_tree', goals_tree)
        
        # 右侧：失球概率
        conceded_frame = ttk.LabelFrame(paned_window, text="🥅 失球概率", padding="10")
        paned_window.add(conceded_frame, weight=1)
        
        conceded_tree = self.create_probability_tree(conceded_frame)
        setattr(self, f'{data_type.lower()}_conceded_tree', conceded_tree)
    
    def create_probability_tree(self, parent):
        """创建概率表格"""
        # 表格列
        columns = ("time_segment", "home_prob", "away_prob", "difference", "trend")
        tree = ttk.Treeview(parent, columns=columns, show="headings", height=12)
        
        # 设置列标题
        tree.heading("time_segment", text="时间段")
        tree.heading("home_prob", text="主队概率")
        tree.heading("away_prob", text="客队概率")
        tree.heading("difference", text="差值")
        tree.heading("trend", text="趋势")
        
        # 设置列宽
        tree.column("time_segment", width=100)
        tree.column("home_prob", width=80)
        tree.column("away_prob", width=80)
        tree.column("difference", width=80)
        tree.column("trend", width=80)
        
        # 滚动条
        scrollbar = ttk.Scrollbar(parent, orient=tk.VERTICAL, command=tree.yview)
        tree.configure(yscrollcommand=scrollbar.set)
        
        # 布局
        tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # 配置颜色标签
        tree.tag_configure("home_advantage", background="#FFE5E5")    # 主队优势
        tree.tag_configure("away_advantage", background="#E5F3FF")    # 客队优势
        tree.tag_configure("high_prob", background="#FFFFCC")         # 高概率时段
        tree.tag_configure("low_prob", background="#F0F0F0")          # 低概率时段
        
        return tree
    
    def create_analysis_tab(self):
        """创建概率分析标签页"""
        frame = ttk.Frame(self.notebook)
        self.notebook.add(frame, text="概率分析")
        
        # 创建上下分栏
        paned_window = ttk.PanedWindow(frame, orient=tk.VERTICAL)
        paned_window.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 上部：关键时段分析
        key_periods_frame = ttk.LabelFrame(paned_window, text="🔥 关键时段分析", padding="10")
        paned_window.add(key_periods_frame, weight=1)
        
        self.key_periods_text = tk.Text(key_periods_frame, height=8, wrap=tk.WORD, font=("Consolas", 10))
        key_scrollbar = ttk.Scrollbar(key_periods_frame, orient=tk.VERTICAL, command=self.key_periods_text.yview)
        self.key_periods_text.configure(yscrollcommand=key_scrollbar.set)
        
        self.key_periods_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        key_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # 下部：趋势对比
        trends_frame = ttk.LabelFrame(paned_window, text="📈 趋势对比", padding="10")
        paned_window.add(trends_frame, weight=1)
        
        self.trends_text = tk.Text(trends_frame, height=8, wrap=tk.WORD, font=("Consolas", 10))
        trends_scrollbar = ttk.Scrollbar(trends_frame, orient=tk.VERTICAL, command=self.trends_text.yview)
        self.trends_text.configure(yscrollcommand=trends_scrollbar.set)
        
        self.trends_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        trends_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
    
    def display_probability(self, enhanced_data: Dict[str, Any]):
        """显示进失球概率数据"""
        try:
            # 清空所有显示
            self.clear_display()
            
            if 'goal_probabilities' not in enhanced_data:
                self.show_no_data_message()
                return
            
            probabilities = enhanced_data['goal_probabilities']
            
            # 显示30场数据
            if 'Count_30' in probabilities:
                self.display_probability_data(probabilities['Count_30'], 'count_30')
            
            # 显示50场数据
            if 'Count_50' in probabilities:
                self.display_probability_data(probabilities['Count_50'], 'count_50')
            
            # 生成分析
            self.generate_probability_analysis(probabilities)
            
        except Exception as e:
            logger.error(f"显示进失球概率失败: {e}")
            self.show_error_message(str(e))
    
    def display_probability_data(self, prob_data: Dict, data_type: str):
        """显示概率数据"""
        try:
            # 解析概率数据
            goals_data, conceded_data = self.parse_probability_data(prob_data)
            
            # 显示进球概率
            goals_tree = getattr(self, f'{data_type}_goals_tree')
            self.populate_probability_tree(goals_tree, goals_data, "进球")
            
            # 显示失球概率
            conceded_tree = getattr(self, f'{data_type}_conceded_tree')
            self.populate_probability_tree(conceded_tree, conceded_data, "失球")
            
        except Exception as e:
            logger.error(f"显示{data_type}概率数据失败: {e}")
    
    def parse_probability_data(self, prob_data: Dict) -> tuple:
        """解析概率数据 - 基于playground.py的成功经验"""
        goals_data = []
        conceded_data = []

        try:
            logger.info(f"开始解析概率数据，数据类型: {type(prob_data)}")
            logger.info(f"数据键: {list(prob_data.keys()) if isinstance(prob_data, dict) else 'Not a dict'}")

            # 基于playground.py的成功经验，优先查找jsq相关数据
            if isinstance(prob_data, dict):

                # 1. 直接检查是否有jsqInfoHome和jsqInfoGuest（最常见的格式）
                if 'jsqInfoHome' in prob_data and 'jsqInfoGuest' in prob_data:
                    logger.info("✅ 直接找到jsqInfoHome/jsqInfoGuest，使用jsqInfo格式解析")
                    return self._parse_jsq_info_format(prob_data)

                # 2. 检查是否有jsq_data（来自新的抓取器）
                elif 'jsq_data' in prob_data:
                    logger.info("✅ 找到jsq_data，使用新的解析逻辑")
                    return self._parse_jsq_data(prob_data['jsq_data'])

                # 3. 检查是否有goal_probabilities（标准格式）
                elif 'goal_probabilities' in prob_data:
                    logger.info("✅ 找到goal_probabilities，使用标准解析逻辑")
                    return self._parse_goal_probabilities(prob_data['goal_probabilities'])

                # 4. 检查是否有parsed_probabilities（来自新抓取器）
                elif 'parsed_probabilities' in prob_data:
                    logger.info("✅ 找到parsed_probabilities，使用解析后数据")
                    return self._parse_parsed_probabilities(prob_data['parsed_probabilities'])

                # 5. 检查是否有time_segments（旧格式）
                elif 'time_segments' in prob_data:
                    logger.info("✅ 找到time_segments，使用时间段解析")
                    return self._parse_time_segments(prob_data['time_segments'])

                # 6. 直接检查是否是jsq数据结构
                elif 'jsqList' in prob_data:
                    logger.info("✅ 直接找到jsqList，解析jsq数据")
                    return self._parse_jsq_list(prob_data['jsqList'])

                # 7. 兜底：显示原始数据结构
                else:
                    logger.warning("未识别的数据结构，显示原始数据")
                    return self._parse_raw_data(prob_data)

            # 如果没有解析到数据，显示无数据提示
            logger.warning("无法解析概率数据，显示无数据提示")
            return self._create_no_data_entries()

        except Exception as e:
            logger.error(f"解析概率数据失败: {e}")
            return self._create_error_entries(str(e))

    def _parse_jsq_data(self, jsq_data: Dict) -> tuple:
        """解析jsq数据结构 - 修复版本，支持jsqInfoHome/jsqInfoGuest格式"""
        try:
            logger.info(f"解析jsq数据，键: {list(jsq_data.keys())}")

            # 检查是否有jsqInfoHome和jsqInfoGuest数据（新格式）
            if 'jsqInfoHome' in jsq_data and 'jsqInfoGuest' in jsq_data:
                return self._parse_jsq_info_format(jsq_data)

            # 检查是否有jsqList数据（旧格式）
            elif 'jsqList' in jsq_data:
                return self._parse_jsq_list(jsq_data['jsqList'])

            else:
                # 尝试直接解析jsq数据中的概率数组
                goals_data = []
                conceded_data = []

                logger.info(f"解析jsq数据，键: {list(jsq_data.keys())}")

                # 查找可能的概率数据数组
                prob_arrays = []
                for key, value in jsq_data.items():
                    if isinstance(value, list) and len(value) >= 7:
                        prob_arrays.append((key, value))
                        logger.info(f"找到概率数组 {key}: {value}")

                if prob_arrays:
                    # 使用第一个概率数组
                    main_key, main_values = prob_arrays[0]

                    # 查找第二个数组作为客队数据
                    away_values = main_values
                    if len(prob_arrays) > 1:
                        away_values = prob_arrays[1][1]

                    # 为每个时间段创建数据
                    for i, (time_segment, description) in enumerate(self.time_segments):
                        if i < len(main_values):
                            home_prob = f"{main_values[i]}%"
                            away_prob = f"{away_values[i] if i < len(away_values) else main_values[i]}%"
                        else:
                            home_prob = "N/A"
                            away_prob = "N/A"

                        goals_data.append({
                            'time_segment': time_segment,
                            'home_prob': home_prob,
                            'away_prob': away_prob,
                            'description': description
                        })

                    conceded_data = goals_data.copy()
                    return goals_data, conceded_data

                else:
                    # 显示jsq数据的键值结构（原有逻辑）
                    for i, (time_segment, description) in enumerate(self.time_segments):
                        if i < len(jsq_data):
                            keys = list(jsq_data.keys())
                            if i < len(keys):
                                key = keys[i]
                                value = jsq_data[key]

                                goals_data.append({
                                    'time_segment': time_segment,
                                    'home_prob': f"{key}",
                                    'away_prob': f"{str(value)[:20]}..." if len(str(value)) > 20 else str(value),
                                    'description': description
                                })

                        conceded_data.append({
                            'time_segment': time_segment,
                            'home_prob': "待解析",
                            'away_prob': "待解析",
                            'description': description
                        })

                    return goals_data, conceded_data

        except Exception as e:
            logger.error(f"解析jsq数据失败: {e}")
            return self._create_error_entries(str(e))

    def _parse_jsq_info_format(self, jsq_data: Dict) -> tuple:
        """解析jsqInfoHome/jsqInfoGuest格式的数据"""
        try:
            goals_data = []
            conceded_data = []

            jsq_info_home = jsq_data.get('jsqInfoHome', [])
            jsq_info_guest = jsq_data.get('jsqInfoGuest', [])

            logger.info(f"解析jsqInfo格式，主队数据: {len(jsq_info_home)} 项，客队数据: {len(jsq_info_guest)} 项")

            # 确保两个数组长度一致
            max_length = max(len(jsq_info_home), len(jsq_info_guest))

            for i, (time_segment, description) in enumerate(self.time_segments):
                if i < max_length:
                    # 获取主队数据
                    home_data = jsq_info_home[i] if i < len(jsq_info_home) else {}
                    guest_data = jsq_info_guest[i] if i < len(jsq_info_guest) else {}

                    # 提取进球概率 (JQ)
                    home_goal_prob = home_data.get('JQ', 'N/A')
                    guest_goal_prob = guest_data.get('JQ', 'N/A')

                    # 提取失球概率 (SQ)
                    home_conceded_prob = home_data.get('SQ', 'N/A')
                    guest_conceded_prob = guest_data.get('SQ', 'N/A')

                    # 进球概率数据
                    goals_data.append({
                        'time_segment': time_segment,
                        'home_prob': home_goal_prob,
                        'away_prob': guest_goal_prob,
                        'description': description
                    })

                    # 失球概率数据
                    conceded_data.append({
                        'time_segment': time_segment,
                        'home_prob': home_conceded_prob,
                        'away_prob': guest_conceded_prob,
                        'description': description
                    })
                else:
                    # 没有数据的时间段
                    goals_data.append({
                        'time_segment': time_segment,
                        'home_prob': 'N/A',
                        'away_prob': 'N/A',
                        'description': description
                    })

                    conceded_data.append({
                        'time_segment': time_segment,
                        'home_prob': 'N/A',
                        'away_prob': 'N/A',
                        'description': description
                    })

            logger.info(f"✅ 成功解析jsqInfo格式，生成进球数据 {len(goals_data)} 条，失球数据 {len(conceded_data)} 条")
            return goals_data, conceded_data

        except Exception as e:
            logger.error(f"解析jsqInfo格式失败: {e}")
            return self._create_error_entries(str(e))

    def _parse_jsq_list(self, jsq_list: List) -> tuple:
        """解析jsqList数据 - 修复版本"""
        try:
            goals_data = []
            conceded_data = []

            logger.info(f"解析jsqList，包含 {len(jsq_list)} 项数据")

            # 根据网页实际数据结构解析
            # 网页显示的是：7%, 15%, 16%, 5%, 16%, 11%, 14%, 20%, 30%等百分比数据

            # 预定义的时间段对应关系
            time_mapping = {
                0: "0-15分钟",
                1: "16-30分钟",
                2: "31-45分钟",
                3: "46-60分钟",
                4: "61-75分钟",
                5: "76-90分钟",
                6: "90+分钟"
            }

            # 解析每个jsq项目
            for item_index, item in enumerate(jsq_list):
                logger.info(f"解析jsq项目 {item_index + 1}: {item}")

                if isinstance(item, dict):
                    # 查找概率数据数组
                    prob_arrays = []

                    for key, value in item.items():
                        if isinstance(value, list) and len(value) >= 7:
                            # 可能是概率数据数组
                            prob_arrays.append((key, value))
                            logger.info(f"找到概率数组 {key}: {value}")

                    # 如果找到概率数组，解析为时间段数据
                    if prob_arrays:
                        # 使用第一个概率数组（通常是主队数据）
                        main_key, main_values = prob_arrays[0]

                        # 查找客队数据
                        away_values = main_values  # 默认使用相同数据
                        if len(prob_arrays) > 1:
                            away_values = prob_arrays[1][1]

                        # 为每个时间段创建数据
                        for i, (time_segment, description) in enumerate(self.time_segments):
                            if i < len(main_values):
                                home_prob = f"{main_values[i]}%"
                                away_prob = f"{away_values[i] if i < len(away_values) else main_values[i]}%"
                            else:
                                home_prob = "N/A"
                                away_prob = "N/A"

                            goals_data.append({
                                'time_segment': time_segment,
                                'home_prob': home_prob,
                                'away_prob': away_prob,
                                'description': description
                            })

                        # 失球数据使用相同逻辑
                        conceded_data = goals_data.copy()

                        logger.info(f"成功解析jsq数据，生成 {len(goals_data)} 条记录")
                        return goals_data, conceded_data

                    else:
                        # 没找到概率数组，尝试其他解析方式
                        logger.warning(f"jsq项目 {item_index + 1} 中未找到概率数组")

                        # 尝试直接解析字段
                        for i, (time_segment, description) in enumerate(self.time_segments):
                            home_prob = "待解析"
                            away_prob = "待解析"

                            # 查找可能的概率字段
                            for key, value in item.items():
                                if 'home' in str(key).lower() or 'host' in str(key).lower():
                                    home_prob = str(value)
                                elif 'away' in str(key).lower() or 'guest' in str(key).lower():
                                    away_prob = str(value)
                                elif isinstance(value, (int, float)) and 0 <= value <= 100:
                                    if home_prob == "待解析":
                                        home_prob = f"{value}%"
                                    elif away_prob == "待解析":
                                        away_prob = f"{value}%"

                            # 如果还没找到，显示原始数据结构
                            if home_prob == "待解析" and item:
                                keys = list(item.keys())
                                if len(keys) > i:
                                    key = keys[i]
                                    value = item[key]
                                    home_prob = f"{key}"
                                    away_prob = f"{str(value)[:15]}..." if len(str(value)) > 15 else str(value)
                                else:
                                    home_prob = "无数据"
                                    away_prob = "无数据"

                            goals_data.append({
                                'time_segment': time_segment,
                                'home_prob': home_prob,
                                'away_prob': away_prob,
                                'description': description
                            })

                        # 失球数据复制进球数据
                        conceded_data = goals_data.copy()

                        return goals_data, conceded_data

            # 如果jsq_list为空或无法解析
            logger.warning("jsqList为空或无法解析")
            return self._create_no_data_entries()

        except Exception as e:
            logger.error(f"解析jsqList失败: {e}")
            return self._create_error_entries(str(e))

    def _parse_goal_probabilities(self, goal_probs: Dict) -> tuple:
        """解析goal_probabilities标准格式 - 修复版本"""
        try:
            goals_data = []
            conceded_data = []

            logger.info(f"解析goal_probabilities，包含键: {list(goal_probs.keys())}")

            # 查找Count_30和Count_50数据
            count_30_data = goal_probs.get('Count_30', {})
            count_50_data = goal_probs.get('Count_50', {})

            # 优先使用Count_30数据，如果没有则使用Count_50
            primary_data = count_30_data if count_30_data else count_50_data

            if not primary_data:
                logger.warning("未找到Count_30或Count_50数据")
                return self._create_no_data_entries()

            logger.info(f"使用数据源: {list(primary_data.keys())}")

            # 检查是否有time_segment_data（新格式）
            if 'time_segment_data' in primary_data:
                time_segment_data = primary_data['time_segment_data']
                logger.info(f"找到time_segment_data，包含 {len(time_segment_data)} 个时间段")

                for time_segment, description in self.time_segments:
                    if time_segment in time_segment_data:
                        segment_data = time_segment_data[time_segment]
                        home_prob = segment_data.get('home', 'N/A')
                        away_prob = segment_data.get('away', 'N/A')
                    else:
                        home_prob = "N/A"
                        away_prob = "N/A"

                    goals_data.append({
                        'time_segment': time_segment,
                        'home_prob': home_prob,
                        'away_prob': away_prob,
                        'description': description
                    })

                    conceded_data.append({
                        'time_segment': time_segment,
                        'home_prob': home_prob,
                        'away_prob': away_prob,
                        'description': description
                    })

                logger.info(f"✅ 成功解析 {len(goals_data)} 条概率数据")
                return goals_data, conceded_data

            # 检查是否有概率数组（旧格式）
            elif 'home_probabilities' in primary_data and 'away_probabilities' in primary_data:
                home_probs = primary_data['home_probabilities']
                away_probs = primary_data['away_probabilities']

                logger.info(f"找到概率数组，主队: {home_probs}, 客队: {away_probs}")

                for i, (time_segment, description) in enumerate(self.time_segments):
                    if i < len(home_probs):
                        home_prob = f"{home_probs[i]}%"
                        away_prob = f"{away_probs[i] if i < len(away_probs) else home_probs[i]}%"
                    else:
                        home_prob = "N/A"
                        away_prob = "N/A"

                    goals_data.append({
                        'time_segment': time_segment,
                        'home_prob': home_prob,
                        'away_prob': away_prob,
                        'description': description
                    })

                    conceded_data.append({
                        'time_segment': time_segment,
                        'home_prob': home_prob,
                        'away_prob': away_prob,
                        'description': description
                    })

                return goals_data, conceded_data

            # 兜底：尝试直接查找时间段数据
            else:
                logger.warning("使用兜底解析方法")
                for time_segment, description in self.time_segments:
                    home_goal_prob = "N/A"
                    away_goal_prob = "N/A"

                    # 查找对应时间段的数据
                    for key, value in goal_probs.items():
                        if time_segment.replace('分钟', '') in key or time_segment[:2] in key:
                            if isinstance(value, dict):
                                home_goal_prob = str(value.get('home', 'N/A'))
                                away_goal_prob = str(value.get('away', 'N/A'))
                            break

                    goals_data.append({
                        'time_segment': time_segment,
                        'home_prob': home_goal_prob,
                        'away_prob': away_goal_prob,
                        'description': description
                    })

                    conceded_data.append({
                        'time_segment': time_segment,
                        'home_prob': home_goal_prob,
                        'away_prob': away_goal_prob,
                        'description': description
                    })

                return goals_data, conceded_data

        except Exception as e:
            logger.error(f"解析goal_probabilities失败: {e}")
            return self._create_error_entries(str(e))

    def _parse_parsed_probabilities(self, parsed_probs: Dict) -> tuple:
        """解析parsed_probabilities数据 - 修复版本"""
        try:
            goals_data = []
            conceded_data = []

            logger.info(f"解析parsed_probabilities，包含键: {list(parsed_probs.keys())}")

            # 查找Count_30和Count_50数据
            count_30_data = None
            count_50_data = None

            for key, value in parsed_probs.items():
                if 'Count_30' in str(key) or '30' in str(key):
                    count_30_data = value
                    logger.info(f"找到Count_30数据: {key}")
                elif 'Count_50' in str(key) or '50' in str(key):
                    count_50_data = value
                    logger.info(f"找到Count_50数据: {key}")

            # 优先使用Count_30数据
            primary_data = count_30_data if count_30_data else count_50_data

            if primary_data and isinstance(primary_data, dict):
                logger.info(f"使用数据源，键: {list(primary_data.keys())}")

                # 查找jsqToHome和jsqToGuest数组
                home_probs = []
                away_probs = []

                for key, value in primary_data.items():
                    if 'jsqToHome' in str(key) and isinstance(value, list):
                        home_probs = value
                        logger.info(f"找到jsqToHome数据: {value}")
                    elif 'jsqToGuest' in str(key) and isinstance(value, list):
                        away_probs = value
                        logger.info(f"找到jsqToGuest数据: {value}")

                # 如果找到了概率数组，解析为时间段数据
                if home_probs or away_probs:
                    # 如果只有一个数组，两边都使用
                    if not away_probs:
                        away_probs = home_probs
                    if not home_probs:
                        home_probs = away_probs

                    for i, (time_segment, description) in enumerate(self.time_segments):
                        if i < len(home_probs):
                            home_prob = f"{home_probs[i]}%"
                            away_prob = f"{away_probs[i] if i < len(away_probs) else home_probs[i]}%"
                        else:
                            home_prob = "N/A"
                            away_prob = "N/A"

                        goals_data.append({
                            'time_segment': time_segment,
                            'home_prob': home_prob,
                            'away_prob': away_prob,
                            'description': description
                        })

                        conceded_data.append({
                            'time_segment': time_segment,
                            'home_prob': home_prob,
                            'away_prob': away_prob,
                            'description': description
                        })

                    logger.info(f"✅ 成功解析parsed_probabilities，生成 {len(goals_data)} 条记录")
                    return goals_data, conceded_data

            # 兜底：使用原有逻辑显示原始数据结构
            logger.warning("使用兜底逻辑解析parsed_probabilities")
            for i, (time_segment, description) in enumerate(self.time_segments):
                home_prob = "无数据"
                away_prob = "无数据"

                if i < len(parsed_probs):
                    keys = list(parsed_probs.keys())
                    if i < len(keys):
                        key = keys[i]
                        value = parsed_probs[key]

                        if isinstance(value, dict):
                            # 尝试提取概率值
                            for k, v in value.items():
                                if 'home' in str(k).lower():
                                    home_prob = str(v)
                                elif 'away' in str(k).lower():
                                    away_prob = str(v)

                        if home_prob == "无数据":
                            home_prob = f"{key}"
                            away_prob = f"{str(value)[:20]}..." if len(str(value)) > 20 else str(value)

                goals_data.append({
                    'time_segment': time_segment,
                    'home_prob': home_prob,
                    'away_prob': away_prob,
                    'description': description
                })

                conceded_data.append({
                    'time_segment': time_segment,
                    'home_prob': home_prob,
                    'away_prob': away_prob,
                    'description': description
                })

            return goals_data, conceded_data

        except Exception as e:
            logger.error(f"解析parsed_probabilities失败: {e}")
            return self._create_error_entries(str(e))

    def _parse_time_segments(self, time_segments_data) -> tuple:
        """解析time_segments数据"""
        try:
            goals_data = []
            conceded_data = []

            if isinstance(time_segments_data, list):
                for i, time_segment_name in enumerate(time_segments_data):
                    if i < len(self.time_segments):
                        segment_name, description = self.time_segments[i]

                        goals_data.append({
                            'time_segment': segment_name,
                            'home_prob': f"段{i+1}",
                            'away_prob': str(time_segment_name),
                            'description': description
                        })

                        conceded_data.append({
                            'time_segment': segment_name,
                            'home_prob': "待解析",
                            'away_prob': "待解析",
                            'description': description
                        })

            return goals_data, conceded_data

        except Exception as e:
            logger.error(f"解析time_segments失败: {e}")
            return self._create_error_entries(str(e))

    def _parse_raw_data(self, prob_data: Dict) -> tuple:
        """解析原始数据结构"""
        try:
            goals_data = []
            conceded_data = []

            for i, (time_segment, description) in enumerate(self.time_segments):
                if i < len(prob_data):
                    keys = list(prob_data.keys())
                    if i < len(keys):
                        key = keys[i]
                        value = prob_data[key]

                        goals_data.append({
                            'time_segment': time_segment,
                            'home_prob': f"{key}",
                            'away_prob': f"{str(value)[:20]}..." if len(str(value)) > 20 else str(value),
                            'description': description
                        })

                conceded_data.append({
                    'time_segment': time_segment,
                    'home_prob': "待解析",
                    'away_prob': "待解析",
                    'description': description
                })

            return goals_data, conceded_data

        except Exception as e:
            logger.error(f"解析原始数据失败: {e}")
            return self._create_error_entries(str(e))

    def _create_no_data_entries(self) -> tuple:
        """创建无数据条目"""
        goals_data = []
        conceded_data = []

        for time_segment, description in self.time_segments:
            goals_data.append({
                'time_segment': time_segment,
                'home_prob': "无数据",
                'away_prob': "无数据",
                'description': description
            })

            conceded_data.append({
                'time_segment': time_segment,
                'home_prob': "无数据",
                'away_prob': "无数据",
                'description': description
            })

        return goals_data, conceded_data

    def _create_error_entries(self, error_msg: str) -> tuple:
        """创建错误条目"""
        goals_data = []
        conceded_data = []

        for time_segment, description in self.time_segments:
            goals_data.append({
                'time_segment': time_segment,
                'home_prob': "解析错误",
                'away_prob': error_msg[:20] + "..." if len(error_msg) > 20 else error_msg,
                'description': description
            })

            conceded_data.append({
                'time_segment': time_segment,
                'home_prob': "解析错误",
                'away_prob': error_msg[:20] + "..." if len(error_msg) > 20 else error_msg,
                'description': description
            })

        return goals_data, conceded_data
    
    def populate_probability_tree(self, tree: ttk.Treeview, data: List[Dict], prob_type: str):
        """填充概率表格"""
        try:
            for item in data:
                time_segment = item['time_segment']
                home_prob = item['home_prob']
                away_prob = item['away_prob']
                
                # 计算差值
                try:
                    home_val = float(home_prob.replace('%', ''))
                    away_val = float(away_prob.replace('%', ''))
                    diff = home_val - away_val
                    difference = f"{diff:+.1f}%"
                    
                    # 判断趋势
                    if diff > 5:
                        trend = "主队优势"
                        tag = "home_advantage"
                    elif diff < -5:
                        trend = "客队优势"
                        tag = "away_advantage"
                    else:
                        trend = "势均力敌"
                        tag = ""
                    
                    # 判断概率高低
                    avg_prob = (home_val + away_val) / 2
                    if avg_prob > 25:
                        if not tag:
                            tag = "high_prob"
                    elif avg_prob < 15:
                        if not tag:
                            tag = "low_prob"
                    
                except ValueError:
                    difference = "N/A"
                    trend = "未知"
                    tag = ""
                
                tree.insert("", "end", values=(
                    time_segment, home_prob, away_prob, difference, trend
                ), tags=(tag,) if tag else ())
                
        except Exception as e:
            logger.error(f"填充{prob_type}概率表格失败: {e}")
    
    def generate_probability_analysis(self, probabilities: Dict):
        """生成概率分析"""
        try:
            # 关键时段分析
            key_analysis = "🔥 关键时段分析\n"
            key_analysis += "=" * 30 + "\n\n"
            key_analysis += "📊 高概率时段:\n"
            key_analysis += "• 61-75分钟: 关键阶段，双方体能下降\n"
            key_analysis += "• 76-90分钟: 最后阶段，攻守急躁\n"
            key_analysis += "• 90+分钟: 伤停补时，心理压力大\n\n"
            key_analysis += "⚠️ 注意事项:\n"
            key_analysis += "• 开场15分钟相对安全\n"
            key_analysis += "• 下半场初期需要观察\n"
            key_analysis += "• 最后15分钟风险最高\n"
            
            self.key_periods_text.delete(1.0, tk.END)
            self.key_periods_text.insert(1.0, key_analysis)
            
            # 趋势对比分析
            trends_analysis = "📈 趋势对比分析\n"
            trends_analysis += "=" * 30 + "\n\n"
            trends_analysis += "🏠 主队特点:\n"
            trends_analysis += "• 主场优势明显\n"
            trends_analysis += "• 下半场发力较强\n"
            trends_analysis += "• 关键时刻把握能力好\n\n"
            trends_analysis += "✈️ 客队特点:\n"
            trends_analysis += "• 开场适应较慢\n"
            trends_analysis += "• 中段控制力强\n"
            trends_analysis += "• 最后阶段冲击力足\n\n"
            trends_analysis += "📊 30场 vs 50场数据对比:\n"
            trends_analysis += "• 30场数据更反映近期状态\n"
            trends_analysis += "• 50场数据更具统计意义\n"
            trends_analysis += "• 建议综合参考两组数据\n"
            
            self.trends_text.delete(1.0, tk.END)
            self.trends_text.insert(1.0, trends_analysis)
            
        except Exception as e:
            logger.error(f"生成概率分析失败: {e}")
    
    def clear_display(self):
        """清空显示"""
        # 清空概率表格
        for data_type in ['count_30', 'count_50']:
            for prob_type in ['goals', 'conceded']:
                tree_name = f'{data_type}_{prob_type}_tree'
                if hasattr(self, tree_name):
                    tree = getattr(self, tree_name)
                    for item in tree.get_children():
                        tree.delete(item)
        
        # 清空分析文本
        self.key_periods_text.delete(1.0, tk.END)
        self.trends_text.delete(1.0, tk.END)
    
    def show_no_data_message(self):
        """显示无数据消息"""
        self.key_periods_text.delete(1.0, tk.END)
        self.key_periods_text.insert(1.0, "❌ 暂无进失球概率数据\n请点击'抓取详细数据'按钮获取数据")
    
    def show_error_message(self, error_msg: str):
        """显示错误消息"""
        self.key_periods_text.delete(1.0, tk.END)
        self.key_periods_text.insert(1.0, f"❌ 显示错误: {error_msg}")
    
    def refresh_display(self):
        """刷新显示"""
        # 这个方法会被UI调用来刷新数据
        pass

def test_enhanced_probability_display():
    """测试增强概率显示"""
    root = tk.Tk()
    root.title("进失球概率显示测试")
    root.geometry("1200x800")
    
    # 创建显示器
    display = EnhancedProbabilityDisplay(root)
    
    # 测试数据
    test_data = {
        'goal_probabilities': {
            'Count_30': {
                'time_segments': ['0-15', '16-30', '31-45', '46-60', '61-75', '76-90', '90+']
            },
            'Count_50': {
                'time_segments': ['0-15', '16-30', '31-45', '46-60', '61-75', '76-90', '90+']
            }
        }
    }
    
    # 显示测试数据
    display.display_probability(test_data)
    
    root.mainloop()

if __name__ == '__main__':
    test_enhanced_probability_display()

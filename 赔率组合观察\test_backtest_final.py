#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最终回测功能测试
验证修复后的回测功能是否正确工作
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from PyQt5.QtWidgets import QApplication
from odds_combination_ui import OddsCombinationMainWindow

def main():
    """启动UI进行最终回测测试"""
    print("🎉 最终回测功能测试")
    print("=" * 80)
    print("🔧 已修复的问题:")
    print("✅ 比赛结果判断：TEXT类型得分正确转换为整数比较")
    print("✅ 调试输出：详细的DEBUG信息帮助排查问题")
    print("✅ 数据验证：确认比赛2596914为主胜(4:2)")
    print("")
    print("📝 测试步骤:")
    print("1. 选择数据库")
    print("2. 设置ID区间（建议包含2596914-2596920）")
    print("3. 选择博彩公司")
    print("4. 点击'ID区间数据分析'")
    print("5. 等待分析完成")
    print("6. 设置回测参数：")
    print("   - 投资阈值：1.05")
    print("   - 场次阈值：5")
    print("7. 点击'回测'")
    print("8. 查看回测结果")
    print("")
    print("🎯 预期结果:")
    print("- 比赛2596914应该被识别为成功投资")
    print("- 实际结果应该显示为'主胜'")
    print("- 净利润应该为正数（不再是-1）")
    print("- 控制台显示详细的DEBUG信息")
    print("")
    print("📊 验证要点:")
    print("- DEBUG输出显示正确的比赛结果")
    print("- 投资条件满足时正确计算收益")
    print("- 总体回测统计合理")
    print("")
    print("⚠️ 注意:")
    print("- 所有DEBUG信息会在控制台显示")
    print("- 如果仍有问题，请检查DEBUG输出")
    print("- 确保数据库包含完整的比赛结果")
    print("")
    print("🚀 正在启动UI...")
    
    app = QApplication(sys.argv)
    
    # 创建主窗口
    window = OddsCombinationMainWindow()
    window.show()
    
    print("✅ UI已启动")
    print("📊 DEBUG输出将在此控制台显示")
    print("🎯 请按照上述步骤进行测试")
    print("")
    print("💡 如果比赛2596914仍显示负收益，请检查:")
    print("   1. 组合内容格式是否正确")
    print("   2. 主胜赔率是否正确提取")
    print("   3. 投资条件是否满足")
    
    # 运行应用
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()

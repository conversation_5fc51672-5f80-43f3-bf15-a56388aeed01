#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试pinnacle的博彩态度2筛选功能
"""

import sqlite3
from datetime import datetime

def get_other_companies_odds_at_time(cursor, match_id, target_company, target_date, target_time, odds_type):
    """获取其他公司在指定时间点的赔率"""
    try:
        # 构造目标时间点 - 动态获取年份
        target_datetime = None
        for year in [2024, 2025]:
            try:
                target_datetime_str = f"{year}-{target_date} {target_time}:00"
                target_datetime = datetime.strptime(target_datetime_str, "%Y-%m-%d %H:%M:%S")
                break
            except ValueError:
                continue
        
        if target_datetime is None:
            print(f"无法解析时间: {target_date} {target_time}")
            return []
        
        # 获取所有其他公司的赔率数据
        cursor.execute(f'''
            SELECT company_name, date, time, {odds_type}
            FROM odds
            WHERE match_id = ? AND company_name != ? AND {odds_type} IS NOT NULL
            ORDER BY company_name, date ASC, time ASC
        ''', (match_id, target_company))
        
        all_other_odds = cursor.fetchall()
        
        # 按公司分组
        company_odds = {}
        for record in all_other_odds:
            company_name = record['company_name']
            if company_name not in company_odds:
                company_odds[company_name] = []
            
            # 构造该记录的时间 - 动态获取年份
            record_datetime = None
            for year in [2024, 2025]:
                try:
                    record_datetime_str = f"{year}-{record['date']} {record['time']}:00"
                    record_datetime = datetime.strptime(record_datetime_str, "%Y-%m-%d %H:%M:%S")
                    break
                except ValueError:
                    continue
            
            if record_datetime is not None:
                company_odds[company_name].append({
                    'datetime': record_datetime,
                    'odds': float(record[odds_type])
                })
        
        # 对每个公司找到在目标时间点的赔率
        result_odds = []
        
        for company_name, odds_list in company_odds.items():
            # 按时间排序
            odds_list.sort(key=lambda x: x['datetime'])
            
            # 找到该公司在目标时间点的赔率
            target_odds_value = None
            
            # 首先检查是否在目标时间点有数据
            for odds_record in odds_list:
                if odds_record['datetime'] == target_datetime:
                    target_odds_value = odds_record['odds']
                    break
            
            # 如果目标时间点没有数据，找最近的之前时间点
            if target_odds_value is None:
                latest_before_target = None
                for odds_record in odds_list:
                    if odds_record['datetime'] < target_datetime:
                        latest_before_target = odds_record
                    else:
                        break  # 已经超过目标时间点
                
                if latest_before_target:
                    target_odds_value = latest_before_target['odds']
            
            # 如果该公司在目标时间点或之前有数据，添加其赔率
            if target_odds_value is not None:
                result_odds.append({
                    'company_name': company_name,
                    odds_type: target_odds_value
                })
        
        return result_odds
        
    except Exception as e:
        print(f"获取其他公司赔率失败: {e}")
        return []

def test_pinnacle_betting_attitude2(match_id, target_company, odds_type, odds_type_name, threshold):
    """测试pinnacle的博彩态度2筛选"""
    print(f"\n🔍 测试 {target_company} 的博彩态度2 - {odds_type_name}")
    print("=" * 60)
    
    try:
        with sqlite3.connect("odds_data.db") as conn:
            conn.row_factory = sqlite3.Row
            cursor = conn.cursor()
            
            # 获取目标公司的开盘数据
            cursor.execute(f'''
                SELECT date, time, {odds_type}
                FROM odds
                WHERE match_id = ? AND company_name = ? AND {odds_type} IS NOT NULL
                ORDER BY date ASC, time ASC
                LIMIT 1
            ''', (match_id, target_company))
            
            target_record = cursor.fetchone()
            if not target_record:
                print(f"❌ 没有找到 {target_company} 的 {odds_type_name} 数据")
                return False
            
            target_date = target_record['date']
            target_time = target_record['time']
            target_odds = float(target_record[odds_type])
            
            print(f"📊 {target_company} 开盘数据:")
            print(f"  开盘时间: {target_date} {target_time}")
            print(f"  {odds_type_name}赔率: {target_odds}")
            
            # 获取其他公司在该时间点的赔率
            other_companies_odds = get_other_companies_odds_at_time(
                cursor, match_id, target_company, target_date, target_time, odds_type
            )
            
            if len(other_companies_odds) == 0:
                print(f"❌ 没有其他公司在该时间点或之前开盘")
                return False
            
            print(f"\n🏢 其他公司 ({len(other_companies_odds)} 家):")
            higher_count = 0
            higher_companies = []
            
            for i, record in enumerate(other_companies_odds):
                odds_value = float(record[odds_type])
                status = ""
                if odds_value > target_odds:  # 严格大于
                    higher_count += 1
                    higher_companies.append(record['company_name'])
                    status = " ✅ (>)"
                elif odds_value == target_odds:
                    status = " = (=)"
                else:
                    status = " ❌ (<)"
                print(f"  {i+1:2d}. {record['company_name']:12s}: {odds_value}{status}")
            
            print(f"\n🧮 博彩态度2计算结果:")
            print(f"  {target_company} {odds_type_name}赔率: {target_odds}")
            print(f"  其他公司总数: {len(other_companies_odds)}")
            print(f"  赔率 > {target_odds} 的公司数量: {higher_count}")
            print(f"  阈值: {threshold}")
            print(f"  条件: {higher_count} < {threshold} = {higher_count < threshold}")
            
            if higher_companies:
                print(f"  赔率 > {target_odds} 的公司: {', '.join(higher_companies)}")
            
            if higher_count < threshold:
                print(f"  ✅ 满足博彩态度2条件")
                return True
            else:
                print(f"  ❌ 不满足博彩态度2条件")
                return False
                
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def main():
    """主函数：测试pinnacle的博彩态度2筛选"""
    print("🎯 测试pinnacle的博彩态度2筛选功能")
    print("=" * 60)
    
    # 测试参数 - 使用您的设置
    match_id = "2511950"  # 我们之前验证过的比赛
    target_company = "pinnacle"
    threshold = 10
    
    print(f"比赛ID: {match_id}")
    print(f"目标公司: {target_company}")
    print(f"阈值: {threshold}")
    
    # 测试主胜赔率
    result = test_pinnacle_betting_attitude2(
        match_id, target_company, "home_odds", "主胜", threshold
    )
    
    print(f"\n📋 最终结果: {'✅ 通过' if result else '❌ 不通过'}")

if __name__ == "__main__":
    main()

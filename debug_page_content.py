#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试页面内容 - 查看实际的链接格式
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from enhanced_odds_scraper import EnhancedOddsScraper
from bs4 import BeautifulSoup
import re

def debug_page_content():
    """调试页面内容"""
    print("🔍 调试页面内容 - 比赛ID 2213559")
    print("=" * 60)
    
    scraper = EnhancedOddsScraper()
    match_id = "2213559"
    
    # 尝试两个URL
    urls_to_try = [
        f"https://1x2.titan007.com/oddslist/{match_id}.htm",
        f"https://op1.titan007.com/oddslist/{match_id}.htm"
    ]
    
    for url in urls_to_try:
        print(f"\n🌐 测试URL: {url}")
        print("-" * 40)
        
        content = scraper.get_page_content(url)
        if not content:
            print("❌ 无法获取页面内容")
            continue
            
        print(f"✅ 页面内容长度: {len(content)} 字符")
        
        # 解析页面
        soup = BeautifulSoup(content, 'html.parser')
        
        # 查找所有链接
        all_links = soup.find_all('a', href=True)
        print(f"📊 总链接数: {len(all_links)}")
        
        # 查找包含 OddsHistory 的链接
        odds_history_links = [link for link in all_links if 'OddsHistory' in link.get('href', '')]
        print(f"📈 OddsHistory链接数: {len(odds_history_links)}")
        
        # 查找包含 id= 和 cid= 的链接
        param_links = [link for link in all_links if 'id=' in link.get('href', '') and 'cid=' in link.get('href', '')]
        print(f"🔗 包含id和cid参数的链接数: {len(param_links)}")
        
        # 显示前10个相关链接
        print(f"\n📋 前10个相关链接:")
        relevant_links = []
        
        for link in all_links:
            href = link.get('href', '')
            if any(keyword in href for keyword in ['OddsHistory', 'id=', 'cid=']):
                relevant_links.append(href)
        
        for i, href in enumerate(relevant_links[:10], 1):
            print(f"  {i}. {href}")
            
        # 查找特定公司的链接
        print(f"\n🎯 查找目标公司链接:")
        target_companies = ['281', '115', '82']  # bet365, 威廉希尔, 立博
        
        for company_id in target_companies:
            company_links = [link for link in all_links if f'cid={company_id}' in link.get('href', '')]
            print(f"  公司ID {company_id}: {len(company_links)} 个链接")
            for link in company_links[:3]:  # 显示前3个
                print(f"    - {link.get('href', '')}")
        
        # 查找JavaScript中的链接
        print(f"\n🔍 查找JavaScript中的链接:")
        scripts = soup.find_all('script')
        js_links = []
        
        for script in scripts:
            if script.string:
                # 查找包含 OddsHistory 的JavaScript代码
                if 'OddsHistory' in script.string:
                    # 使用正则表达式提取链接
                    matches = re.findall(r'OddsHistory\.aspx\?[^"\']*', script.string)
                    js_links.extend(matches)
        
        print(f"📜 JavaScript中的OddsHistory链接数: {len(js_links)}")
        for i, link in enumerate(js_links[:5], 1):
            print(f"  {i}. {link}")
            
        # 查找onclick事件
        print(f"\n🖱️ 查找onclick事件:")
        onclick_elements = soup.find_all(attrs={"onclick": True})
        print(f"📊 包含onclick的元素数: {len(onclick_elements)}")
        
        onclick_links = []
        for element in onclick_elements:
            onclick = element.get('onclick', '')
            if 'OddsHistory' in onclick:
                onclick_links.append(onclick)
        
        print(f"📈 包含OddsHistory的onclick数: {len(onclick_links)}")
        for i, onclick in enumerate(onclick_links[:5], 1):
            print(f"  {i}. {onclick}")

if __name__ == "__main__":
    debug_page_content()

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增强版足球赔率数据抓取器
集成了比赛信息提取功能，能够同时获取比赛详情和赔率数据
"""

import requests
import time
import re
import json
import pandas as pd
from bs4 import BeautifulSoup
from datetime import datetime
from typing import List, Dict, Optional
import logging
import argparse
from config import BETTING_COMPANIES, WEBSITE_CONFIG

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class EnhancedOddsScraper:
    def __init__(self):
        self.session = requests.Session()
        self.session.headers.update(WEBSITE_CONFIG['headers'])
        self.base_url = WEBSITE_CONFIG['base_url']

        # 使用配置文件中的博彩公司列表
        self.known_companies = BETTING_COMPANIES

    def get_page_content(self, url: str, max_retries: int = 3) -> Optional[str]:
        """获取网页内容"""
        for attempt in range(max_retries):
            try:
                response = self.session.get(url, timeout=15)
                response.raise_for_status()
                response.encoding = 'utf-8'
                return response.text
            except requests.RequestException as e:
                logger.warning(f"获取页面失败 (尝试 {attempt + 1}/{max_retries}): {e}")
                if attempt < max_retries - 1:
                    time.sleep(3)
                else:
                    logger.error(f"无法获取页面: {url}")
                    return None

    def extract_match_info(self, match_id: str) -> Dict:
        """提取比赛信息"""
        url = f"{self.base_url}/compensate/{match_id}.htm"
        content = self.get_page_content(url)

        if not content:
            return {}

        match_info = {
            'match_id': match_id,
            'extraction_time': datetime.now().isoformat()
        }

        try:
            soup = BeautifulSoup(content, 'html.parser')

            # 提取联赛信息
            league_div = soup.find('div', class_='league')
            if league_div:
                league_text = league_div.get_text(strip=True)
                parsed_info = self._parse_league_text(league_text, match_id)
                match_info.update(parsed_info)

            # 提取队伍信息
            self._extract_team_info(soup, match_info)

            # 提取比分信息
            self._extract_score_info(soup, match_info)

            logger.info(f"成功提取比赛 {match_id} 的基本信息")
            return match_info

        except Exception as e:
            logger.error(f"提取比赛信息失败: {e}")
            return match_info

    def _parse_league_text(self, text: str, match_id: str = None) -> Dict:
        """解析联赛文本信息"""
        info = {}
        text = text.replace('&nbsp;', ' ').replace('\xa0', ' ')

        parts = text.split()
        if len(parts) >= 3:
            info['league'] = parts[0]

            for part in parts:
                if '轮' in part or '第' in part:
                    info['round'] = part
                    break

            # 首先尝试从分析页面获取准确时间
            accurate_time = self._extract_accurate_match_time(match_id)

            if accurate_time and accurate_time.get('success'):
                # 使用准确的时间信息
                info.update({
                    'match_time': accurate_time['match_time'],
                    'match_date': accurate_time['match_date'],
                    'match_time_only': accurate_time['match_time_only'],
                    'year': accurate_time['year'],
                    'month': accurate_time['month'],
                    'day': accurate_time['day'],
                    'hour': accurate_time['hour'],
                    'minute': accurate_time['minute'],
                    'time_source': 'analysis_page',
                    'raw_time_str': accurate_time.get('raw_time_str', '')
                })
                logger.info(f"从分析页面获取到准确时间: {accurate_time['match_time']}")
            else:
                # 回退到原有的解析方法
                date_time_pattern = r'(\d{2}-\d{2})\s+(\d{2}:\d{2})'
                match = re.search(date_time_pattern, text)
                if match:
                    date_str = match.group(1)
                    time_str = match.group(2)

                    # 智能推测年份：多种方法结合
                    year = self._infer_match_year(date_str, match_id)

                    try:
                        full_date_str = f"{year}-{date_str} {time_str}:00"
                        match_datetime = datetime.strptime(full_date_str, "%Y-%m-%d %H:%M:%S")

                        info['match_time'] = match_datetime.strftime("%Y-%m-%d %H:%M:%S")
                        info['match_date'] = match_datetime.strftime("%Y-%m-%d")
                        info['match_time_only'] = match_datetime.strftime("%H:%M:%S")
                        info['raw_date'] = date_str
                        info['raw_time'] = time_str
                        info['inferred_year'] = year
                        info['time_source'] = 'inferred'

                    except ValueError:
                        info['raw_date'] = date_str
                        info['raw_time'] = time_str
                        info['time_source'] = 'failed'

                if accurate_time:
                    logger.warning(f"分析页面时间提取失败: {accurate_time.get('error', '未知错误')}")

            # 提取天气信息
            weather_pattern = r'(天晴|多云|阴|雨|雪|雾)\s*(\d+℃[～~]\d+℃)?'
            weather_match = re.search(weather_pattern, text)
            if weather_match:
                info['weather'] = weather_match.group(1)
                if weather_match.group(2):
                    info['temperature'] = weather_match.group(2)

        info['raw_league_text'] = text
        return info

    def _infer_match_year(self, date_str: str, match_id: str = None) -> int:
        """智能推测比赛年份"""
        current_year = datetime.now().year
        current_month = datetime.now().month

        try:
            match_month = int(date_str.split('-')[0])
            match_day = int(date_str.split('-')[1])
        except (ValueError, IndexError):
            return current_year

        # 方法1：基于比赛ID推测（基于实际数据分布更新 - 2025年7月）
        if match_id:
            try:
                match_id_int = int(match_id)
                # 基于实际数据库分析的比赛ID范围映射
                if match_id_int < 2500000:  # 2022年及之前
                    return 2022
                elif match_id_int < 2600000:  # 2023年
                    return 2023
                elif match_id_int < 2750000:  # 2024年上半年
                    return 2024
                elif match_id_int < 2850000:  # 2024年下半年
                    # 对于2024年下半年的比赛，需要根据月份判断
                    if match_month >= 7:  # 7月及以后，是2024年
                        return 2024
                    else:  # 1-6月，可能是2025年
                        return 2025
                elif match_id_int < 3000000:  # 2025年上半年
                    return 2025
                else:  # 2025年下半年及以后
                    # 如果比赛月份在下半年，可能是当年或下一年
                    if match_month >= 7:  # 下半年比赛
                        return current_year
                    else:  # 上半年比赛，可能是下一年
                        return current_year + 1
            except ValueError:
                pass

        # 方法2：基于月份的智能判断
        # 如果是8月份的比赛，而现在是1月，很可能是去年的比赛
        if current_month <= 3 and match_month >= 8:  # 现在是年初，比赛是下半年
            return current_year - 1
        elif current_month >= 10 and match_month <= 5:  # 现在是年末，比赛是上半年
            return current_year + 1

        # 方法3：默认使用当前年份
        return current_year

    def _extract_accurate_match_time(self, match_id: str) -> Dict:
        """从分析页面提取准确的比赛时间"""
        if not match_id:
            return {'success': False, 'error': '比赛ID为空'}

        try:
            # 构建分析页面URL
            analysis_url = f"https://m.titan007.com/Analy/ShiJian/{match_id}.htm"

            # 获取页面内容
            response = self.session.get(analysis_url, timeout=15)
            response.raise_for_status()
            response.encoding = 'utf-8'

            content = response.text

            # 查找时间变量
            time_patterns = [
                r'var\s+headMatchTime\s*=\s*["\']([^"\']+)["\']',
                r'headMatchTime\s*=\s*["\']([^"\']+)["\']',
                r'matchTime\s*=\s*["\']([^"\']+)["\']',
                r'var\s+matchTime\s*=\s*["\']([^"\']+)["\']',
            ]

            for pattern in time_patterns:
                match = re.search(pattern, content, re.IGNORECASE)
                if match:
                    time_str = match.group(1)

                    # 解析时间
                    try:
                        if 'T' in time_str:
                            # ISO格式: 2025-07-27T19:00:00
                            dt = datetime.fromisoformat(time_str.replace('T', ' ').replace('Z', ''))
                        else:
                            # 其他格式
                            dt = datetime.strptime(time_str, "%Y-%m-%d %H:%M:%S")

                        return {
                            'success': True,
                            'match_time': dt.strftime('%Y-%m-%d %H:%M:%S'),
                            'match_date': dt.strftime('%Y-%m-%d'),
                            'match_time_only': dt.strftime('%H:%M:%S'),
                            'year': dt.year,
                            'month': dt.month,
                            'day': dt.day,
                            'hour': dt.hour,
                            'minute': dt.minute,
                            'source': 'analysis_page',
                            'raw_time_str': time_str
                        }
                    except Exception as e:
                        logger.warning(f"时间解析失败: {time_str}, 错误: {e}")
                        continue

            return {'success': False, 'error': '未找到时间信息'}

        except Exception as e:
            logger.warning(f"从分析页面提取时间失败: {e}")
            return {'success': False, 'error': str(e)}

    def _extract_team_info(self, soup: BeautifulSoup, match_info: Dict):
        """提取队伍信息"""
        try:
            home_div = soup.find('div', class_='home')
            if home_div:
                home_name_div = home_div.find('div', class_='name')
                if home_name_div:
                    home_name = home_name_div.get_text(strip=True)
                    home_name = re.sub(r'\[\d+\]', '', home_name).strip()
                    match_info['home_team'] = home_name

            guest_div = soup.find('div', class_='guest')
            if guest_div:
                guest_name_div = guest_div.find('div', class_='name')
                if guest_name_div:
                    guest_name = guest_name_div.get_text(strip=True)
                    guest_name = re.sub(r'\[\d+\]', '', guest_name).strip()
                    match_info['away_team'] = guest_name

        except Exception as e:
            logger.warning(f"提取队伍信息失败: {e}")

    def _extract_score_info(self, soup: BeautifulSoup, match_info: Dict):
        """提取比分信息"""
        try:
            home_score_div = soup.find('div', id='homeScore')
            guest_score_div = soup.find('div', id='guestScore')

            if home_score_div and guest_score_div:
                match_info['home_score'] = home_score_div.get_text(strip=True)
                match_info['away_score'] = guest_score_div.get_text(strip=True)

            state_span = soup.find('span', id='mState')
            if state_span:
                match_info['match_state'] = state_span.get_text(strip=True)

            half_score_span = soup.find('span', class_='halfScore')
            if half_score_span:
                half_score = half_score_span.get_text(strip=True)
                match_info['half_score'] = half_score.strip('()')

        except Exception as e:
            logger.warning(f"提取比分信息失败: {e}")

    def parse_company_odds_from_js_direct(self, match_id: str) -> List[Dict]:
        """直接从JavaScript数据文件解析所有公司的赔率数据（新方案）"""
        logger.info(f"使用新方案：直接从JavaScript数据文件抓取比赛 {match_id} 的赔率数据")

        try:
            # 直接访问JavaScript数据文件
            js_url = f"https://1x2d.titan007.com/{match_id}.js"
            content = self.get_page_content(js_url)

            if not content:
                logger.error(f"无法获取JavaScript数据文件: {js_url}")
                return []

            logger.info(f"成功获取JavaScript数据文件，长度: {len(content)}")

            # 解析JavaScript中的赔率数据
            odds_records = []

            # 查找各种可能的数据变量
            import re

            # 方法1：查找game变量（使用正确的正则表达式）
            game_patterns = [
                r'var game=Array\((.*?)\);',  # 匹配整个Array内容，使用非贪婪模式
                r'var game=(.+?);',           # 备用模式
                r'game=Array\((.*?)\);',      # 无var的版本
                r'game=(.+?);'                # 无var的备用版本
            ]

            for pattern in game_patterns:
                match = re.search(pattern, content, re.DOTALL)  # 使用DOTALL模式匹配多行
                if match:
                    game_data = match.group(1)
                    logger.info(f"找到game变量，原始长度: {len(game_data)}")

                    # 如果匹配到的是Array("...")格式，提取引号内的内容
                    if game_data.startswith('Array("') and game_data.endswith('")'):
                        game_data = game_data[7:-2]  # 移除 Array(" 和 ")
                    elif game_data.startswith('"') and game_data.endswith('"'):
                        game_data = game_data[1:-1]  # 移除首尾引号

                    logger.info(f"处理后game数据长度: {len(game_data)}")
                    odds_records.extend(self.parse_game_data(game_data))
                    break

            # 方法2：查找其他数据变量
            if not odds_records:
                other_patterns = [
                    r'var jcEuropeOddsData="([^"]+)";',
                    r'var europeOddsData="([^"]+)";',
                    r'var oddsData="([^"]+)";'
                ]

                for pattern in other_patterns:
                    match = re.search(pattern, content)
                    if match:
                        odds_data = match.group(1)
                        logger.info(f"找到其他赔率数据变量，长度: {len(odds_data)}")
                        odds_records.extend(self.parse_other_odds_data(odds_data))
                        break

            # 方法3：查找数组格式的数据
            if not odds_records:
                array_patterns = [
                    r'var\s+\w+\s*=\s*\[([^\]]+)\];',
                    r'\w+\s*=\s*\[([^\]]+)\];'
                ]

                for pattern in array_patterns:
                    matches = re.findall(pattern, content)
                    for match_data in matches:
                        if '|' in match_data and len(match_data) > 100:  # 可能包含赔率数据
                            logger.info(f"找到数组格式数据，长度: {len(match_data)}")
                            odds_records.extend(self.parse_array_data(match_data))
                            if odds_records:  # 如果找到数据就停止
                                break
                    if odds_records:
                        break

            logger.info(f"新方案成功解析 {len(odds_records)} 条赔率记录")
            return odds_records

        except Exception as e:
            logger.error(f"新方案解析赔率失败: {e}")
            return []

    def parse_company_odds_from_two_step_method(self, match_id: str) -> List[Dict]:
        """使用两步方案获取完整的历史赔率数据"""
        logger.info(f"使用两步方案抓取比赛 {match_id} 的完整历史赔率数据")

        try:
            # 第一步：获取各公司的真实赔率历史链接
            company_links = self.get_company_odds_links_from_oddslist(match_id)

            if not company_links:
                logger.warning("第一步失败：未找到任何公司的赔率链接，尝试备用方案")
                # 备用方案：使用推测的链接
                company_links = self.get_company_odds_links_from_known_pattern(match_id)

            if not company_links:
                logger.error("所有方案都失败：无法获取公司赔率链接")
                return []

            logger.info(f"第一步成功：找到 {len(company_links)} 家公司的链接")

            # 第二步：逐个访问每家公司的赔率历史页面
            all_odds_records = []

            for company_id, link_data in company_links.items():
                company_name = link_data['company_name']
                company_url = link_data['url']

                logger.info(f"正在处理 {company_name} (ID: {company_id})")

                # 获取该公司的完整历史记录
                company_records = self.parse_company_odds_history_page(company_url, company_name)

                if company_records:
                    all_odds_records.extend(company_records)
                    logger.info(f"{company_name} 成功获取 {len(company_records)} 条记录")
                else:
                    logger.warning(f"{company_name} 未获取到任何记录")

                # 添加延迟避免请求过快
                import time
                time.sleep(1)

            logger.info(f"两步方案完成：总共获取 {len(all_odds_records)} 条历史记录")
            return all_odds_records

        except Exception as e:
            logger.error(f"两步方案失败: {e}")
            return []

    def get_company_odds_links_from_known_pattern(self, match_id: str) -> Dict[str, Dict]:
        """使用智能推测模式构造目标公司的赔率历史链接"""
        logger.info(f"备用方案：使用智能推测模式构造目标公司赔率链接")

        # 定义目标公司列表（只抓取这些公司）
        target_companies = {
            '281': 'bet365',
            '115': '威廉希尔',
            '82': '立博',
            '81': '伟德',
            '90': '易胜博',
            '2': 'betfair',
            '177': 'pinnacle',
            '255': 'bwin',
            '104': 'Interwetten',
            '80': '澳门',
            '432': '香港马会',
            '499': '金宝博',
            '474': '利记',
            '517': '明升',
            '1129': '竞彩官方'
        }

        odds_links = {}

        # 智能推测策略：基于发现的线性规律
        # 发现的规律：威廉希尔_record_id = ********* + (比赛ID - 2213559) * 10
        # 验证数据：
        # - 比赛2213559: 威廉希尔=*********, bet365=*********
        # - 比赛2398985: 威廉希尔=114688212, bet365=114704812

        # 已知的真实record_id映射
        known_record_ids = {}

        # 方法1：使用线性规律计算威廉希尔的基准record_id
        try:
            match_id_int = int(match_id)
            # 基于发现的线性规律
            william_base_record = ********* + (match_id_int - 2213559) * 10
            logger.info(f"使用线性规律计算威廉希尔基准record_id: {william_base_record}")

            # 为已知的几个公司计算精确的record_id
            known_record_ids = {
                '115': william_base_record,  # 威廉希尔 - 基准
                '82': william_base_record + (82 - 115) * 100,    # 立博
                '81': william_base_record + (81 - 115) * 100,    # 伟德
                '90': william_base_record + (90 - 115) * 100,    # 易胜博
            }

            # bet365需要特殊处理，因为它的规律可能不同
            if match_id == "2213559":
                # 对于已知比赛，使用真实的bet365 record_id
                known_record_ids['281'] = *********
            else:
                # 对于其他比赛，使用线性推测
                known_record_ids['281'] = william_base_record + (281 - 115) * 100

            logger.info(f"使用线性规律为比赛{match_id}计算record_id映射")

        except ValueError:
            logger.warning(f"无法解析比赛ID {match_id}，使用默认基准")
            william_base_record = *********

        # 为每个目标公司获取或推测record_id
        for company_id, company_name in target_companies.items():
            company_id_int = int(company_id)

            # 如果有已知的真实record_id，直接使用
            if company_id in known_record_ids:
                record_id = known_record_ids[company_id]
                logger.info(f"使用真实record_id: {company_name} (ID: {company_id}, Record: {record_id})")
            else:
                # 否则基于威廉希尔进行推测
                if '115' in known_record_ids:
                    william_base = known_record_ids['115']
                    record_id = william_base + (company_id_int - 115) * 100
                    logger.info(f"基于威廉希尔推测: {company_name} (ID: {company_id}, Record: {record_id})")
                else:
                    # 如果没有威廉希尔的基准，使用通用推测
                    try:
                        match_id_int = int(match_id)
                        # 基于比赛ID的通用推测
                        base_record = ********* + (match_id_int - 2213559) * 10
                        record_id = base_record + (company_id_int - 115) * 100
                        logger.info(f"通用推测: {company_name} (ID: {company_id}, Record: {record_id})")
                    except:
                        # 最后的备用方案
                        record_id = ********* + (company_id_int - 115) * 100
                        logger.warning(f"备用推测: {company_name} (ID: {company_id}, Record: {record_id})")

            # 构造URL（使用op1域名，因为测试证明有效）
            url = f"https://op1.titan007.com/OddsHistory.aspx?id={record_id}&sid={match_id}&cid={company_id}&l=0"

            odds_links[company_id] = {
                'company_name': company_name,
                'url': url,
                'record_id': str(record_id)
            }

        logger.info(f"智能推测完成：构造了 {len(odds_links)} 个目标公司的赔率链接")
        return odds_links

    def get_company_odds_links_from_oddslist(self, match_id: str) -> Dict[str, Dict]:
        """从赔率列表页面获取各公司的真实赔率历史链接"""
        logger.info(f"第一步：从赔率列表页面获取真实链接")

        # 定义目标公司列表（只抓取这些公司）
        target_companies = {
            '281': 'bet365',
            '115': '威廉希尔',
            '82': '立博',
            '81': '伟德',
            '90': '易胜博',
            '2': 'betfair',
            '177': 'pinnacle',
            '255': 'bwin',
            '104': 'Interwetten',
            '80': '澳门',
            '432': '香港马会',
            '499': '金宝博',
            '474': '利记',
            '517': '明升',
            '1129': '竞彩官方'
        }

        # 首先尝试从JavaScript数据源获取record ID
        js_links = self.get_company_odds_links_from_js(match_id, target_companies)
        if js_links:
            logger.info(f"从JavaScript数据源成功获取了 {len(js_links)} 个目标公司的真实链接")
            return js_links

        # 如果JavaScript方案失败，尝试传统的HTML解析方案
        urls_to_try = [
            f"https://1x2.titan007.com/oddslist/{match_id}.htm",
            f"https://op1.titan007.com/oddslist/{match_id}.htm"
        ]

        for url in urls_to_try:
            logger.info(f"尝试获取赔率列表页面: {url}")
            content = self.get_page_content(url)

            if not content:
                logger.warning(f"无法获取页面: {url}")
                continue

            try:
                soup = BeautifulSoup(content, 'html.parser')
                odds_links = {}

                # 查找所有包含 OddsHistory.aspx 的链接
                history_links = soup.find_all('a', href=lambda x: x and 'OddsHistory.aspx' in x)

                logger.info(f"找到 {len(history_links)} 个历史链接")

                for link in history_links:
                    href = link.get('href')
                    if not href:
                        continue

                    # 解析URL参数
                    try:
                        from urllib.parse import urlparse, parse_qs
                        parsed = urlparse(href)
                        params = parse_qs(parsed.query)

                        # 获取公司ID
                        if 'cid' in params:
                            company_id = params['cid'][0]

                            # 检查是否是目标公司
                            if company_id in target_companies:
                                company_name = target_companies[company_id]

                                # 构造完整URL
                                if href.startswith('http'):
                                    full_url = href
                                else:
                                    # 相对URL，需要补充域名
                                    if 'op1.titan007.com' in url:
                                        full_url = f"https://op1.titan007.com{href}"
                                    else:
                                        full_url = f"https://1x2.titan007.com{href}"

                                # 获取record ID
                                record_id = params.get('id', ['unknown'])[0]

                                odds_links[company_id] = {
                                    'company_name': company_name,
                                    'url': full_url,
                                    'record_id': record_id
                                }

                                logger.info(f"找到目标公司: {company_name} (ID: {company_id}, Record: {record_id})")

                    except Exception as e:
                        logger.debug(f"解析链接失败: {href}, 错误: {e}")
                        continue

                if odds_links:
                    logger.info(f"第一步成功：从页面获取了 {len(odds_links)} 个目标公司的真实链接")
                    return odds_links
                else:
                    logger.warning(f"页面中未找到目标公司的链接: {url}")

            except Exception as e:
                logger.error(f"解析页面失败: {url}, 错误: {e}")
                continue

        logger.error("所有URL都无法获取到有效的公司链接")
        return {}

    def get_company_odds_links_from_js(self, match_id: str, target_companies: Dict[str, str]) -> Dict[str, Dict]:
        """从JavaScript数据源获取各公司的真实赔率历史链接"""
        logger.info(f"尝试从JavaScript数据源获取真实链接")

        # JavaScript数据源URL
        js_url = f"https://1x2d.titan007.com/{match_id}.js"

        content = self.get_page_content(js_url)
        if not content:
            logger.warning(f"无法获取JavaScript数据源: {js_url}")
            return {}

        try:
            odds_links = {}

            # 查找game数组 - 使用多行模式和更灵活的匹配
            import re
            game_pattern = r'var game=Array\((.*?)\);'
            match = re.search(game_pattern, content, re.DOTALL)

            if not match:
                logger.warning("JavaScript中未找到game数组")
                return {}

            game_data = match.group(1)
            logger.info(f"找到game数据，长度: {len(game_data)}")

            # 解析game数据中的公司信息
            # 格式: "281|*********|Bet 365|2.8|3.4|2.4|..."
            # 移除首尾的引号和换行符，然后按","分割
            game_data = game_data.strip().strip('"')
            companies = game_data.split('","')

            for company_data in companies:
                try:
                    parts = company_data.split('|')
                    if len(parts) < 3:
                        continue

                    company_id = parts[0]
                    record_id = parts[1]
                    company_display_name = parts[2]

                    # 检查是否是目标公司
                    if company_id in target_companies:
                        company_name = target_companies[company_id]

                        # 构造完整的赔率历史URL
                        full_url = f"https://op1.titan007.com/OddsHistory.aspx?id={record_id}&sid={match_id}&cid={company_id}&l=0"

                        odds_links[company_id] = {
                            'company_name': company_name,
                            'url': full_url,
                            'record_id': record_id
                        }

                        logger.info(f"从JS找到目标公司: {company_name} (ID: {company_id}, Record: {record_id})")

                except Exception as e:
                    logger.debug(f"解析公司数据失败: {company_data[:50]}..., 错误: {e}")
                    continue

            if odds_links:
                logger.info(f"JavaScript方案成功：获取了 {len(odds_links)} 个目标公司的真实链接")
                return odds_links
            else:
                logger.warning("JavaScript中未找到目标公司的数据")
                return {}

        except Exception as e:
            logger.error(f"解析JavaScript数据失败: {e}")
            return {}

    def get_company_odds_links(self, match_id: str) -> Dict[str, str]:
        """备用方法：保持兼容性"""
        links_data = self.get_company_odds_links_from_oddslist(match_id)
        return {cid: data['url'] for cid, data in links_data.items()}

    def parse_company_odds_from_history(self, odds_url: str, company_name: str) -> List[Dict]:
        """从赔率历史页面解析公司赔率数据"""
        logger.info(f"解析 {company_name} 的赔率历史: {odds_url}")

        content = self.get_page_content(odds_url)
        if not content:
            return []

        odds_data = []

        try:
            soup = BeautifulSoup(content, 'html.parser')

            # 查找赔率数据表格
            tables = soup.find_all('table')

            for table in tables:
                rows = table.find_all('tr')

                for row in rows:
                    cells = row.find_all(['td', 'th'])

                    if len(cells) >= 6:
                        try:
                            # 提取时间信息
                            time_cell = cells[0].get_text().strip()
                            if not time_cell or time_cell in ['时间', 'Time', '']:
                                continue

                            # 解析时间格式 (MM-dd HH:mm)
                            import re
                            time_match = re.match(r'(\d{2})-(\d{2})\s+(\d{2}):(\d{2})', time_cell)
                            if not time_match:
                                continue

                            month, day, hour, minute = time_match.groups()
                            date = f"{month}-{day}"
                            time = f"{hour}:{minute}"

                            # 提取赔率数据
                            home_odds = float(cells[1].get_text().strip())
                            draw_odds = float(cells[2].get_text().strip())
                            away_odds = float(cells[3].get_text().strip())

                            # 计算返还率
                            return_rate = (1/home_odds + 1/draw_odds + 1/away_odds) * 100

                            # 计算凯利值 (简化计算)
                            kelly_home = home_odds * 0.33 - 1  # 假设概率为1/3
                            kelly_draw = draw_odds * 0.33 - 1
                            kelly_away = away_odds * 0.33 - 1

                            # 验证数据合理性
                            if (1.0 <= home_odds <= 50.0 and
                                1.0 <= draw_odds <= 50.0 and
                                1.0 <= away_odds <= 50.0 and
                                85.0 <= return_rate <= 100.0):

                                record = {
                                    'company_name': company_name,
                                    'date': date,
                                    'time': time,
                                    'home_odds': home_odds,
                                    'draw_odds': draw_odds,
                                    'away_odds': away_odds,
                                    'return_rate': return_rate,
                                    'kelly_home': kelly_home,
                                    'kelly_draw': kelly_draw,
                                    'kelly_away': kelly_away
                                }

                                odds_data.append(record)

                        except (ValueError, IndexError) as e:
                            continue

            logger.info(f"从 {company_name} 赔率历史页面提取到 {len(odds_data)} 条记录")
            return odds_data

        except Exception as e:
            logger.error(f"解析 {company_name} 赔率历史失败: {e}")
            return []

    def parse_company_odds_from_js(self, match_id: str) -> List[Dict]:
        """从JavaScript数据源解析所有公司的赔率数据"""
        logger.info(f"从JavaScript数据源抓取比赛 {match_id} 的赔率数据")

        try:
            # 访问JavaScript数据源
            js_url = f"https://1x2d.titan007.com/{match_id}.js"
            content = self.get_page_content(js_url)

            if not content:
                logger.error(f"无法获取JavaScript数据: {js_url}")
                return []

            # 查找赔率数据变量
            import re

            # 尝试多种可能的变量名和格式
            patterns = [
                r'var game=Array\("([^"]+)"\);',
                r'var jcEuropeOddsData="([^"]+)";',
                r'var europeOddsData="([^"]+)";',
                r'var oddsData="([^"]+)";'
            ]

            odds_data = None
            for pattern in patterns:
                match = re.search(pattern, content)
                if match:
                    odds_data = match.group(1)
                    logger.info(f"找到赔率数据变量，长度: {len(odds_data)}")
                    break

            if not odds_data:
                logger.error("未找到赔率数据变量")
                return []

            # 解析赔率数据
            odds_records = []

            # 如果数据包含多个公司记录，用分隔符分割
            if '","' in odds_data:
                companies = odds_data.split('","')
            else:
                # 单个记录或不同格式
                companies = [odds_data]

            for company_data in companies:
                try:
                    # 数据格式可能不同，需要灵活解析
                    parts = company_data.split('|')

                    if len(parts) < 10:  # 至少需要基本字段
                        continue

                    # 尝试解析不同的数据格式
                    company_id = None
                    company_name = None
                    home_odds = None
                    draw_odds = None
                    away_odds = None

                    # 格式1: jcEuropeOddsData格式 (从实际数据看到的)
                    # "5000|4956003||1.9|3.4|3.25|||||1.9|3.25|3.4|||||||1|2025,02-1,24,13,58,50|竞彩让*|1|0|1129"
                    if len(parts) >= 20:
                        try:
                            # 提取赔率 (位置3,4,5或位置10,11,12)
                            if parts[3] and parts[4] and parts[5]:
                                home_odds = float(parts[3])
                                draw_odds = float(parts[4])
                                away_odds = float(parts[5])
                            elif parts[10] and parts[11] and parts[12]:
                                home_odds = float(parts[10])
                                draw_odds = float(parts[11])
                                away_odds = float(parts[12])

                            # 提取公司信息
                            company_id = parts[-1] if parts[-1] else parts[0]

                            # 从倒数第三个字段提取公司名称
                            if len(parts) >= 3:
                                company_name = parts[-3]
                                # 清理公司名称
                                if '*' in company_name:
                                    company_name = company_name.replace('*', '')
                                if '让' in company_name:
                                    company_name = company_name.replace('让', '')
                                company_name = company_name.strip()

                            # 时间信息
                            time_str = parts[-4] if len(parts) >= 4 else ""
                            time_parts = time_str.split(',')

                            if len(time_parts) >= 6:
                                year = time_parts[0]
                                month_day = time_parts[1]
                                day = time_parts[2]
                                hour = time_parts[3]
                                minute = time_parts[4]

                                date = f"{month_day}-{day}"
                                time = f"{hour}:{minute}"
                            else:
                                date = "unknown"
                                time = "unknown"

                        except (ValueError, IndexError):
                            continue

                    # 格式2: 传统game格式
                    elif len(parts) >= 6:
                        try:
                            company_id = parts[0]
                            company_name = parts[2] if len(parts) > 2 else ""
                            home_odds = float(parts[3])
                            draw_odds = float(parts[4])
                            away_odds = float(parts[5])
                            date = "unknown"
                            time = "unknown"
                        except (ValueError, IndexError):
                            continue

                    # 验证数据有效性
                    if not all([home_odds, draw_odds, away_odds]):
                        continue

                    if not (1.0 <= home_odds <= 50.0 and 1.0 <= draw_odds <= 50.0 and 1.0 <= away_odds <= 50.0):
                        continue

                    # 标准化公司名称
                    if company_name:
                        # 映射已知公司名称
                        normalized_name = self.normalize_company_name(company_name)
                        if not normalized_name and company_id in self.known_companies:
                            normalized_name = self.known_companies[company_id]

                        if normalized_name:
                            # 计算返还率和凯利值
                            return_rate = (1/home_odds + 1/draw_odds + 1/away_odds) * 100
                            kelly_home = home_odds * 0.33 - 1
                            kelly_draw = draw_odds * 0.33 - 1
                            kelly_away = away_odds * 0.33 - 1

                            record = {
                                'company_name': normalized_name,
                                'date': date,
                                'time': time,
                                'home_odds': home_odds,
                                'draw_odds': draw_odds,
                                'away_odds': away_odds,
                                'return_rate': return_rate,
                                'kelly_home': kelly_home,
                                'kelly_draw': kelly_draw,
                                'kelly_away': kelly_away
                            }

                            odds_records.append(record)
                            logger.info(f"解析到 {normalized_name}: {home_odds} {draw_odds} {away_odds}")

                except Exception as e:
                    logger.warning(f"处理公司数据失败: {e}")
                    continue

            logger.info(f"从JavaScript数据源成功解析 {len(odds_records)} 条赔率记录")
            return odds_records

        except Exception as e:
            logger.error(f"从JavaScript数据源解析赔率失败: {e}")
            return []

    def normalize_company_name(self, company_name: str) -> str:
        """标准化公司名称"""
        if not company_name:
            return None

        company_name = company_name.strip()

        # 直接映射（包含英文名和中文名）
        name_mappings = {
            # 竞彩官方
            '竞彩': '竞彩官方',
            '竞彩官方': '竞彩官方',

            # bet365
            'bet365': 'bet365',
            'Bet 365': 'bet365',
            'Bet365': 'bet365',

            # 威廉希尔
            '威廉希尔': '威廉希尔',
            'William Hill': '威廉希尔',
            'WilliamHill': '威廉希尔',

            # 立博
            '立博': '立博',
            'Ladbrokes': '立博',

            # 伟德
            '伟德': '伟德',
            'Victor': '伟德',

            # 易胜博
            '易胜博': '易胜博',
            'Easybets': '易胜博',

            # betfair
            'betfair': 'betfair',
            'Betfair': 'betfair',

            # pinnacle
            'pinnacle': 'pinnacle',
            'Pinnacle': 'pinnacle',

            # bwin
            'bwin': 'bwin',
            'Bwin': 'bwin',

            # Interwetten
            'Interwetten': 'Interwetten',

            # 澳门
            '澳门': '澳门',
            'Macau': '澳门',

            # 香港马会
            '香港马会': '香港马会',
            'HKJC': '香港马会',

            # 金宝博
            '金宝博': '金宝博',
            '188bet': '金宝博',
            '188Bet': '金宝博',

            # 利记
            '利记': '利记',
            'Sbobet': '利记',
            'SBOBET': '利记',

            # 明升
            '明升': '明升',
            'Mansion88': '明升',
            'M88': '明升',

            # 其他常见公司
            'Bet-at-home': 'Bet-at-home',
            '10BET': '10BET',
            '12bet': '12bet',
            '18Bet': '18Bet',
            'Crown': 'Crown',
            'Nordicbet': 'Nordicbet',
            'Wewbet': 'Wewbet',
            'Betsson': 'Betsson',
            '10x10bet': '10x10bet',
            '1xBet': '1xBet',
            'BetInAsia': 'BetInAsia',
            'Betsafe': 'Betsafe',
            'Betway': 'Betway',
            'Evobet': 'Evobet'
        }

        # 直接匹配
        if company_name in name_mappings:
            return name_mappings[company_name]

        # 模糊匹配
        company_lower = company_name.lower()
        for key, value in name_mappings.items():
            if key.lower() in company_lower or company_lower in key.lower():
                return value

        return company_name

    def parse_company_odds_history_page(self, company_url: str, company_name: str) -> List[Dict]:
        """解析单个公司的赔率历史页面（新的两步方案第二步）"""
        logger.info(f"第二步：解析 {company_name} 的赔率历史页面")

        content = self.get_page_content(company_url)
        if not content:
            logger.error(f"无法获取 {company_name} 的赔率历史页面")
            return []

        odds_records = []

        try:
            from bs4 import BeautifulSoup
            soup = BeautifulSoup(content, 'html.parser')

            # 查找赔率数据表格
            # 赔率历史页面通常有一个包含所有历史记录的表格
            tables = soup.find_all('table')

            for table in tables:
                rows = table.find_all('tr')

                for row in rows:
                    cells = row.find_all(['td', 'th'])

                    if len(cells) >= 10:  # 确保有足够的列
                        try:
                            # 新页面格式：主胜赔率、和局赔率、客胜赔率、主胜率%、和率%、客胜率%、返还率%、凯利主胜、凯利和局、凯利客胜、时间

                            # 提取所有单元格的文本
                            cell_texts = [cell.get_text().strip() for cell in cells]

                            # 跳过表头行
                            if any(header in cell_texts[0].lower() for header in ['主胜', '卢顿', 'home']):
                                continue

                            # 尝试解析赔率数据行
                            if len(cell_texts) >= 11:  # 新格式至少需要11列
                                try:
                                    # 提取赔率 (前3列)
                                    home_odds = float(cell_texts[0])
                                    draw_odds = float(cell_texts[1])
                                    away_odds = float(cell_texts[2])

                                    # 验证赔率合理性
                                    if not all(1.0 <= odds <= 50.0 for odds in [home_odds, draw_odds, away_odds]):
                                        continue

                                    # 提取返还率 (第7列，移除%符号)
                                    return_rate_text = cell_texts[6].replace('%', '')
                                    return_rate = float(return_rate_text)

                                    # 提取凯利指数 (第8-10列)
                                    kelly_home = float(cell_texts[7])
                                    kelly_draw = float(cell_texts[8])
                                    kelly_away = float(cell_texts[9])

                                    # 提取时间信息 (最后一列)
                                    time_info = cell_texts[-1]

                                    # 解析时间信息
                                    date_part = "unknown"
                                    time_part = "unknown"

                                    if time_info:
                                        import re
                                        # 匹配格式如 "10-04 02:16" 或 "09-25 14:00(初盘)"
                                        time_match = re.search(r'(\d{2}-\d{2})\s+(\d{2}:\d{2})', time_info)
                                        if time_match:
                                            date_part = time_match.group(1)
                                            time_part = time_match.group(2)

                                    # 创建记录（使用实际提取的值，不计算）
                                    record = {
                                        'company_name': company_name,
                                        'date': date_part,
                                        'time': time_part,
                                        'home_odds': home_odds,
                                        'draw_odds': draw_odds,
                                        'away_odds': away_odds,
                                        'return_rate': return_rate,
                                        'kelly_home': kelly_home,
                                        'kelly_draw': kelly_draw,
                                        'kelly_away': kelly_away,
                                        'datetime_str': time_info,
                                        'company': company_name,
                                        'change_time': time_info if time_info else f"{date_part} {time_part}"
                                    }

                                    odds_records.append(record)

                                except (ValueError, IndexError) as e:
                                    # 如果新格式解析失败，尝试旧格式
                                    continue

                        except (ValueError, IndexError) as e:
                            continue

            # 如果表格解析失败，尝试从页面文本中直接提取
            if not odds_records:
                page_text = soup.get_text()
                lines = page_text.split('\n')

                for line in lines:
                    line = line.strip()
                    if not line:
                        continue

                    # 查找包含赔率的行
                    import re
                    # 匹配格式如 "1.57 4.50 5.25 ... 02-24 22:45"
                    odds_pattern = r'(\d+\.\d+)\s+(\d+\.\d+)\s+(\d+\.\d+).*?(\d{2}-\d{2}\s+\d{2}:\d{2})'
                    match = re.search(odds_pattern, line)

                    if match:
                        home_odds = float(match.group(1))
                        draw_odds = float(match.group(2))
                        away_odds = float(match.group(3))
                        time_info = match.group(4)

                        # 验证赔率合理性
                        if all(1.0 <= odds <= 50.0 for odds in [home_odds, draw_odds, away_odds]):
                            # 解析时间
                            time_match = re.search(r'(\d{2}-\d{2})\s+(\d{2}:\d{2})', time_info)
                            date_part = time_match.group(1) if time_match else "unknown"
                            time_part = time_match.group(2) if time_match else "unknown"

                            record = self.create_odds_record(
                                company_name, home_odds, draw_odds, away_odds
                            )
                            record['date'] = date_part
                            record['time'] = time_part
                            record['datetime_str'] = time_info
                            # 添加两步方案需要的字段
                            record['company'] = company_name
                            record['change_time'] = time_info

                            odds_records.append(record)

            logger.info(f"{company_name} 解析完成：获取到 {len(odds_records)} 条历史记录")
            return odds_records

        except Exception as e:
            logger.error(f"解析 {company_name} 赔率历史失败: {e}")
            return []

    def parse_game_data(self, game_data: str) -> List[Dict]:
        """解析game变量格式的数据"""
        odds_records = []

        try:
            logger.info(f"开始解析game数据，原始长度: {len(game_data)}")

            # game数据格式：多个公司记录用","分隔，每个记录用|分隔字段
            # 示例: "281|*********|Bet 365|8.5|5.5|1.22|...","115|*********|William Hill|10|5.5|1.22|..."

            # 分割公司记录
            companies = game_data.split('","')
            logger.info(f"分割后得到 {len(companies)} 个公司记录")

            for i, company_data in enumerate(companies):
                try:
                    # 清理引号
                    company_data = company_data.strip('"')
                    parts = company_data.split('|')

                    if len(parts) < 15:  # 确保有足够的字段
                        logger.debug(f"公司记录 {i} 字段不足: {len(parts)}")
                        continue

                    # 解析字段
                    company_id = parts[0]
                    record_id = parts[1]
                    company_name = parts[2]

                    # 赔率字段（根据实际数据结构）
                    # 位置3,4,5是初始赔率，位置10,11,12是当前赔率
                    try:
                        # 优先使用当前赔率（位置10,11,12）
                        if len(parts) > 12 and parts[10] and parts[11] and parts[12]:
                            home_odds = float(parts[10])
                            draw_odds = float(parts[11])
                            away_odds = float(parts[12])
                        # 备用初始赔率（位置3,4,5）
                        elif parts[3] and parts[4] and parts[5]:
                            home_odds = float(parts[3])
                            draw_odds = float(parts[4])
                            away_odds = float(parts[5])
                        else:
                            logger.debug(f"公司 {company_name} 无有效赔率数据")
                            continue

                        # 验证赔率合理性
                        if not (1.0 <= home_odds <= 50.0 and 1.0 <= draw_odds <= 50.0 and 1.0 <= away_odds <= 50.0):
                            logger.debug(f"公司 {company_name} 赔率超出合理范围: {home_odds} {draw_odds} {away_odds}")
                            continue

                        # 标准化公司名称
                        normalized_name = self.normalize_company_name(company_name)
                        if not normalized_name:
                            # 如果标准化失败，检查是否在已知公司列表中
                            if company_id in self.known_companies:
                                normalized_name = self.known_companies[company_id]
                            else:
                                logger.debug(f"未知公司: {company_name} (ID: {company_id})")
                                continue

                        # 创建记录
                        record = self.create_odds_record(normalized_name, home_odds, draw_odds, away_odds)
                        odds_records.append(record)

                        logger.info(f"成功解析: {normalized_name} - {home_odds:.2f} {draw_odds:.2f} {away_odds:.2f}")

                    except (ValueError, IndexError) as e:
                        logger.debug(f"解析公司 {company_name} 赔率失败: {e}")
                        continue

                except Exception as e:
                    logger.warning(f"处理公司记录 {i} 失败: {e}")
                    continue

            logger.info(f"game数据解析完成，成功解析 {len(odds_records)} 条记录")

        except Exception as e:
            logger.error(f"解析game数据失败: {e}")

        return odds_records

    def parse_other_odds_data(self, odds_data: str) -> List[Dict]:
        """解析其他格式的赔率数据"""
        odds_records = []

        try:
            # 如果数据包含多个记录，用分隔符分割
            if '","' in odds_data:
                records = odds_data.split('","')
            else:
                records = [odds_data]

            for record in records:
                record = record.strip('"')
                parts = record.split('|')

                if len(parts) >= 15:
                    try:
                        # 尝试不同的字段位置
                        home_odds = None
                        draw_odds = None
                        away_odds = None
                        company_name = ""

                        # 查找赔率数据（通常在前几个字段）
                        for i in range(min(10, len(parts))):
                            try:
                                val = float(parts[i])
                                if 1.0 <= val <= 50.0:  # 合理的赔率范围
                                    if home_odds is None:
                                        home_odds = val
                                    elif draw_odds is None:
                                        draw_odds = val
                                    elif away_odds is None:
                                        away_odds = val
                                        break
                            except ValueError:
                                continue

                        # 查找公司名称（通常在后面的字段）
                        for i in range(len(parts)-5, len(parts)):
                            if i >= 0 and parts[i] and not parts[i].isdigit():
                                text = parts[i].strip()
                                if text and len(text) > 1:
                                    company_name = text
                                    break

                        if all([home_odds, draw_odds, away_odds]) and company_name:
                            normalized_name = self.normalize_company_name(company_name)
                            if normalized_name:
                                record = self.create_odds_record(normalized_name, home_odds, draw_odds, away_odds)
                                odds_records.append(record)

                    except (ValueError, IndexError):
                        continue

        except Exception as e:
            logger.warning(f"解析其他赔率数据失败: {e}")

        return odds_records

    def parse_array_data(self, array_data: str) -> List[Dict]:
        """解析数组格式的数据"""
        odds_records = []

        try:
            # 移除引号并分割
            array_data = array_data.replace('"', '').replace("'", "")
            items = array_data.split(',')

            for item in items:
                if '|' in item:
                    parts = item.split('|')
                    if len(parts) >= 6:
                        try:
                            # 尝试提取赔率
                            odds_values = []
                            company_name = ""

                            for part in parts:
                                part = part.strip()
                                try:
                                    val = float(part)
                                    if 1.0 <= val <= 50.0:
                                        odds_values.append(val)
                                except ValueError:
                                    if part and not part.isdigit() and len(part) > 1:
                                        company_name = part

                            if len(odds_values) >= 3 and company_name:
                                normalized_name = self.normalize_company_name(company_name)
                                if normalized_name:
                                    record = self.create_odds_record(
                                        normalized_name,
                                        odds_values[0],
                                        odds_values[1],
                                        odds_values[2]
                                    )
                                    odds_records.append(record)

                        except (ValueError, IndexError):
                            continue

        except Exception as e:
            logger.warning(f"解析数组数据失败: {e}")

        return odds_records

    def create_odds_record(self, company_name: str, home_odds: float, draw_odds: float, away_odds: float) -> Dict:
        """创建标准的赔率记录"""
        return_rate = (1/home_odds + 1/draw_odds + 1/away_odds) * 100
        kelly_home = home_odds * 0.33 - 1
        kelly_draw = draw_odds * 0.33 - 1
        kelly_away = away_odds * 0.33 - 1

        return {
            'company_name': company_name,
            'date': "unknown",
            'time': "unknown",
            'home_odds': home_odds,
            'draw_odds': draw_odds,
            'away_odds': away_odds,
            'return_rate': return_rate,
            'kelly_home': kelly_home,
            'kelly_draw': kelly_draw,
            'kelly_away': kelly_away
        }

    def parse_company_odds(self, match_id: str, company_id: str, company_name: str) -> List[Dict]:
        """解析单个公司的赔率数据（兼容新旧方法）"""
        # 首先尝试JavaScript数据源方法
        logger.info(f"尝试从JavaScript数据源抓取 {company_name} 的赔率数据")
        js_odds = self.parse_company_odds_from_js(match_id)

        # 筛选出目标公司的数据
        company_odds = [record for record in js_odds if record['company_name'] == company_name]

        if company_odds:
            logger.info(f"JavaScript方法成功，获取到 {len(company_odds)} 条记录")
            return company_odds

        # 如果JavaScript方法失败，尝试赔率历史页面方法
        odds_links = self.get_company_odds_links(match_id)

        if company_id in odds_links:
            logger.info(f"使用赔率历史页面方法抓取 {company_name} 的赔率数据")
            odds_url = odds_links[company_id]['url']
            history_odds = self.parse_company_odds_from_history(odds_url, company_name)
            if history_odds:
                return history_odds

        # 最后回退到旧方法
        logger.info(f"使用传统方法抓取 {company_name} 的赔率数据")
        return self.parse_company_odds_legacy(match_id, company_id, company_name)

    def parse_company_odds_legacy(self, match_id: str, company_id: str, company_name: str) -> List[Dict]:
        """解析单个公司的赔率数据（旧方法）"""
        url = f"{self.base_url}/CompensateDetail/{company_id}/{match_id}.htm"
        content = self.get_page_content(url)

        if not content:
            return []

        odds_data = []

        try:
            soup = BeautifulSoup(content, 'html.parser')
            tables = soup.find_all('table', class_='mytable3')

            for table in tables:
                rows = table.find_all('tr')

                for row in rows:
                    cells = row.find_all('td')

                    if len(cells) >= 6:
                        try:
                            home_odds = float(cells[0].get_text(strip=True))
                            draw_odds = float(cells[1].get_text(strip=True))
                            away_odds = float(cells[2].get_text(strip=True))
                            return_rate = float(cells[3].get_text(strip=True))

                            kelly_spans = cells[4].find_all('span', class_='odds')
                            if len(kelly_spans) >= 3:
                                kelly_home = float(kelly_spans[0].get_text(strip=True))
                                kelly_draw = float(kelly_spans[1].get_text(strip=True))
                                kelly_away = float(kelly_spans[2].get_text(strip=True))
                            else:
                                continue

                            time_divs = cells[5].find_all('div')
                            if len(time_divs) >= 2:
                                date = time_divs[0].get_text(strip=True)
                                time = time_divs[1].get_text(strip=True)
                            else:
                                continue

                            if (1.0 <= home_odds <= 50.0 and
                                1.0 <= draw_odds <= 50.0 and
                                1.0 <= away_odds <= 50.0 and
                                85.0 <= return_rate <= 100.0):

                                record = {
                                    'company_name': company_name,
                                    'company_id': company_id,
                                    'date': date,
                                    'time': time,
                                    'home_odds': home_odds,
                                    'draw_odds': draw_odds,
                                    'away_odds': away_odds,
                                    'return_rate': return_rate,
                                    'kelly_home': kelly_home,
                                    'kelly_draw': kelly_draw,
                                    'kelly_away': kelly_away
                                }

                                odds_data.append(record)

                        except (ValueError, IndexError):
                            continue

            logger.info(f"从 {company_name} (ID: {company_id}) 提取到 {len(odds_data)} 条记录")
            return odds_data

        except Exception as e:
            logger.error(f"解析 {company_name} 数据失败: {e}")
            return []

    def scrape_complete_match_data(self, match_id: str, max_companies: int = None, delay: float = 2.0) -> Dict:
        """抓取完整的比赛数据（信息+赔率）"""
        logger.info(f"开始抓取比赛 {match_id} 的完整数据")

        # 1. 提取比赛基本信息
        match_info = self.extract_match_info(match_id)

        # 2. 首先尝试两步方案：获取完整的历史赔率数据
        logger.info("尝试两步方案：获取完整的历史赔率数据")
        all_odds_data = self.parse_company_odds_from_two_step_method(match_id)

        # 初始化变量 - 使用优先级公司列表
        from config import get_priority_companies, get_all_companies

        # 获取目标公司列表（按优先级排序）
        if max_companies:
            target_companies = get_priority_companies(max_companies)
            logger.info(f"使用优先级公司列表，最多抓取 {max_companies} 家公司")
        else:
            target_companies = get_all_companies()
            logger.info(f"使用全部目标公司列表，共 {len(target_companies)} 家公司")

        # 打印目标公司列表
        company_names = list(target_companies.values())
        logger.info(f"目标公司: {', '.join(company_names)}")

        companies = list(target_companies.items())
        scraping_method = "未知方法"

        if all_odds_data:
            logger.info(f"两步方案成功，获取到 {len(all_odds_data)} 条赔率记录")
            scraping_method = "两步方案"

            # 统计成功的公司数量
            successful_companies = len(set(record['company_name'] for record in all_odds_data))

            # 如果指定了最大公司数量，按优先级筛选目标公司
            if max_companies:
                # 按照config.py中定义的优先级顺序筛选公司
                priority_company_names = list(target_companies.values())

                # 筛选出在优先级列表中的公司数据
                filtered_odds_data = []
                for company_name in priority_company_names:
                    company_records = [record for record in all_odds_data if record['company_name'] == company_name]
                    if company_records:
                        filtered_odds_data.extend(company_records)
                        logger.info(f"保留目标公司 {company_name} 的 {len(company_records)} 条记录")

                all_odds_data = filtered_odds_data
                successful_companies = len(set(record['company_name'] for record in all_odds_data))
                logger.info(f"按优先级筛选后保留 {successful_companies} 家目标公司的数据")

        else:
            # 如果两步方案失败，尝试JavaScript备用方案
            logger.info("两步方案失败，尝试JavaScript备用方案")

            all_odds_data = self.parse_company_odds_from_js_direct(match_id)

            if all_odds_data:
                logger.info(f"JavaScript方法成功，获取到 {len(all_odds_data)} 条赔率记录")
                scraping_method = "JavaScript方案"
                # 统计成功的公司数量
                successful_companies = len(set(record['company_name'] for record in all_odds_data))

                # 如果指定了最大公司数量，按优先级筛选目标公司
                if max_companies:
                    # 按照config.py中定义的优先级顺序筛选公司
                    priority_company_names = list(target_companies.values())

                    # 筛选出在优先级列表中的公司数据
                    filtered_odds_data = []
                    for company_name in priority_company_names:
                        company_records = [record for record in all_odds_data if record['company_name'] == company_name]
                        if company_records:
                            filtered_odds_data.extend(company_records)
                            logger.info(f"保留目标公司 {company_name} 的 {len(company_records)} 条记录")

                    all_odds_data = filtered_odds_data
                    successful_companies = len(set(record['company_name'] for record in all_odds_data))
                    logger.info(f"按优先级筛选后保留 {successful_companies} 家目标公司的数据")
            else:
                # 如果JavaScript方法也失败，回退到逐个公司抓取
                logger.info("JavaScript方法也失败，回退到传统方法")
                scraping_method = "传统方法"

                all_odds_data = []
                successful_companies = 0

                for i, (company_id, company_name) in enumerate(companies, 1):
                    logger.info(f"正在抓取第 {i}/{len(companies)} 家公司: {company_name}")

                    try:
                        odds_data = self.parse_company_odds(match_id, company_id, company_name)
                        if odds_data:
                            all_odds_data.extend(odds_data)
                            successful_companies += 1

                        time.sleep(delay)

                    except Exception as e:
                        logger.error(f"抓取公司 {company_name} 数据失败: {e}")
                        continue

        # 3. 组合结果
        complete_data = {
            'match_info': match_info,
            'odds_data': all_odds_data,
            'summary': {
                'total_odds_records': len(all_odds_data),
                'successful_companies': successful_companies,
                'total_companies_attempted': len(companies),
                'scraping_method': scraping_method,
                'extraction_time': datetime.now().isoformat()
            }
        }

        logger.info(f"完成抓取：比赛信息 + {len(all_odds_data)} 条赔率记录")
        return complete_data

    def save_complete_data(self, complete_data: Dict, base_filename: str = None) -> Dict[str, str]:
        """保存完整数据到多个文件"""
        if base_filename is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            match_id = complete_data['match_info'].get('match_id', 'unknown')
            base_filename = f"match_{match_id}_{timestamp}"

        saved_files = {}

        # 保存比赛信息
        match_info_file = f"{base_filename}_info.json"
        with open(match_info_file, 'w', encoding='utf-8') as f:
            json.dump(complete_data['match_info'], f, ensure_ascii=False, indent=2)
        saved_files['match_info'] = match_info_file

        # 保存赔率数据
        if complete_data['odds_data']:
            # CSV格式
            odds_csv_file = f"{base_filename}_odds.csv"
            df = pd.DataFrame(complete_data['odds_data'])
            columns_order = [
                'company_name', 'company_id', 'date', 'time',
                'home_odds', 'draw_odds', 'away_odds',
                'return_rate', 'kelly_home', 'kelly_draw', 'kelly_away'
            ]
            existing_columns = [col for col in columns_order if col in df.columns]
            df = df[existing_columns]
            df = df.sort_values(['company_name', 'date', 'time'])
            df.to_csv(odds_csv_file, index=False, encoding='utf-8-sig')
            saved_files['odds_csv'] = odds_csv_file

            # JSON格式
            odds_json_file = f"{base_filename}_odds.json"
            with open(odds_json_file, 'w', encoding='utf-8') as f:
                json.dump(complete_data['odds_data'], f, ensure_ascii=False, indent=2)
            saved_files['odds_json'] = odds_json_file

        # 保存完整数据
        complete_file = f"{base_filename}_complete.json"
        with open(complete_file, 'w', encoding='utf-8') as f:
            json.dump(complete_data, f, ensure_ascii=False, indent=2)
        saved_files['complete'] = complete_file

        logger.info(f"数据已保存到 {len(saved_files)} 个文件")
        return saved_files

    def print_summary(self, complete_data: Dict):
        """打印数据摘要"""
        match_info = complete_data['match_info']
        odds_data = complete_data['odds_data']
        summary = complete_data['summary']

        print(f"\n=== 比赛完整数据摘要 ===")

        # 比赛基本信息
        if match_info.get('league') and match_info.get('home_team') and match_info.get('away_team'):
            print(f"比赛: {match_info['league']} - {match_info['home_team']} vs {match_info['away_team']}")

        if match_info.get('match_time'):
            print(f"时间: {match_info['match_time']}")

        if match_info.get('home_score') and match_info.get('away_score'):
            score_text = f"比分: {match_info['home_score']}-{match_info['away_score']}"
            if match_info.get('match_state'):
                score_text += f" ({match_info['match_state']})"
            print(score_text)

        # 赔率数据统计
        print(f"\n=== 赔率数据统计 ===")
        print(f"成功抓取公司: {summary['successful_companies']}/{summary['total_companies_attempted']}")
        print(f"总赔率记录: {summary['total_odds_records']}")

        if odds_data:
            df = pd.DataFrame(odds_data)

            # 按公司统计
            company_stats = df.groupby('company_name').size().sort_values(ascending=False)
            print(f"\n各公司记录数:")
            for company, count in company_stats.head(5).items():
                print(f"  {company}: {count} 条")

            # 赔率范围
            if all(col in df.columns for col in ['home_odds', 'draw_odds', 'away_odds']):
                print(f"\n赔率范围:")
                print(f"  主胜: {df['home_odds'].min():.2f} - {df['home_odds'].max():.2f}")
                print(f"  平局: {df['draw_odds'].min():.2f} - {df['draw_odds'].max():.2f}")
                print(f"  客胜: {df['away_odds'].min():.2f} - {df['away_odds'].max():.2f}")

            # 时间范围
            if 'date' in df.columns:
                print(f"  时间范围: {df['date'].min()} 到 {df['date'].max()}")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='增强版足球赔率数据抓取器')
    parser.add_argument('match_id', help='比赛ID')
    parser.add_argument('--max-companies', type=int, default=5, help='最大抓取公司数量')
    parser.add_argument('--delay', type=float, default=2.0, help='请求间隔时间（秒）')
    parser.add_argument('--output-base', help='输出文件基础名称')
    parser.add_argument('--quiet', action='store_true', help='静默模式')

    args = parser.parse_args()

    scraper = EnhancedOddsScraper()

    try:
        # 抓取完整数据
        complete_data = scraper.scrape_complete_match_data(
            match_id=args.match_id,
            max_companies=args.max_companies,
            delay=args.delay
        )

        # 保存数据
        saved_files = scraper.save_complete_data(complete_data, args.output_base)

        # 显示摘要
        if not args.quiet:
            scraper.print_summary(complete_data)

            print(f"\n=== 保存的文件 ===")
            for file_type, filename in saved_files.items():
                print(f"{file_type}: {filename}")

    except Exception as e:
        logger.error(f"程序执行出错: {e}")


if __name__ == "__main__":
    # 如果没有命令行参数，使用默认示例
    import sys
    if len(sys.argv) == 1:
        print("使用示例数据进行测试...")
        scraper = EnhancedOddsScraper()

        # 测试抓取完整数据
        complete_data = scraper.scrape_complete_match_data("2741454", max_companies=3)

        # 保存数据
        saved_files = scraper.save_complete_data(complete_data)

        # 显示摘要
        scraper.print_summary(complete_data)

        print(f"\n=== 保存的文件 ===")
        for file_type, filename in saved_files.items():
            print(f"{file_type}: {filename}")
    else:
        main()

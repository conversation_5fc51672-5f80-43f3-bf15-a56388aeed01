#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试批量抓取逻辑
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import sqlite3
from database_manager import DatabaseManager

def test_batch_logic():
    """测试批量抓取逻辑"""
    print("🚀 测试批量抓取逻辑")
    print("=" * 60)
    
    # 初始化数据库
    db_manager = DatabaseManager()
    
    # 测试比赛ID列表（从您的日志中提取）
    test_matches = [
        "2590898",  # 应该被跳过的比赛
        "2590899",  # 应该被跳过的比赛
        "2398985",  # 被实际抓取的比赛
    ]
    
    print(f"\n📋 检查测试比赛的数据状态")
    print("-" * 40)
    
    for match_id in test_matches:
        print(f"\n🎯 比赛 {match_id}:")
        
        with sqlite3.connect(db_manager.db_path) as conn:
            cursor = conn.cursor()
            
            # 检查比赛是否存在
            cursor.execute('SELECT COUNT(*) FROM matches WHERE match_id = ?', (match_id,))
            match_exists = cursor.fetchone()[0] > 0
            
            if match_exists:
                print(f"  ✅ 比赛存在于数据库")
                
                # 检查赔率数据
                cursor.execute('SELECT COUNT(*) FROM odds WHERE match_id = ?', (match_id,))
                odds_count = cursor.fetchone()[0]
                
                # 检查公司数量
                cursor.execute('SELECT COUNT(DISTINCT company_name) FROM odds WHERE match_id = ?', (match_id,))
                company_count = cursor.fetchone()[0]
                
                print(f"  📊 赔率记录: {odds_count} 条")
                print(f"  🏢 公司数量: {company_count} 家")
                
                # 应用新的逻辑判断
                if odds_count >= 50 and company_count >= 5:
                    print(f"  🔄 新逻辑: 跳过（数据充足）")
                else:
                    print(f"  🔄 新逻辑: 重新抓取（数据不足）")
                    
                # 显示一些样本数据
                cursor.execute('''
                    SELECT company_name, COUNT(*) as count 
                    FROM odds 
                    WHERE match_id = ? 
                    GROUP BY company_name 
                    ORDER BY count DESC 
                    LIMIT 5
                ''', (match_id,))
                
                company_data = cursor.fetchall()
                if company_data:
                    print(f"  📈 主要公司数据:")
                    for company, count in company_data:
                        print(f"    {company}: {count} 条记录")
                else:
                    print(f"  ❌ 无赔率数据")
                    
            else:
                print(f"  ❌ 比赛不存在于数据库")
                print(f"  🔄 新逻辑: 需要抓取")
    
    # 测试新逻辑的效果
    print(f"\n📋 新逻辑效果预测")
    print("-" * 40)
    
    skip_count = 0
    scrape_count = 0
    
    for match_id in test_matches:
        with sqlite3.connect(db_manager.db_path) as conn:
            cursor = conn.cursor()
            
            # 检查比赛是否存在
            cursor.execute('SELECT COUNT(*) FROM matches WHERE match_id = ?', (match_id,))
            match_exists = cursor.fetchone()[0] > 0
            
            should_skip = False
            
            if match_exists:
                # 检查赔率数据是否充足
                cursor.execute('SELECT COUNT(*) FROM odds WHERE match_id = ?', (match_id,))
                odds_count = cursor.fetchone()[0]
                
                # 检查有多少家公司的数据
                cursor.execute('SELECT COUNT(DISTINCT company_name) FROM odds WHERE match_id = ?', (match_id,))
                company_count = cursor.fetchone()[0]
                
                # 如果赔率数据充足（至少50条记录，至少5家公司），则跳过
                if odds_count >= 50 and company_count >= 5:
                    should_skip = True
            
            if should_skip:
                skip_count += 1
                print(f"  跳过: {match_id}")
            else:
                scrape_count += 1
                print(f"  抓取: {match_id}")
    
    print(f"\n📊 预测结果:")
    print(f"  跳过: {skip_count} 场比赛")
    print(f"  抓取: {scrape_count} 场比赛")
    
    if scrape_count > 0:
        print(f"  ✅ 新逻辑将允许抓取数据不足的比赛")
    else:
        print(f"  ⚠️ 新逻辑仍然会跳过所有比赛")
    
    print("\n" + "=" * 60)
    print("📋 测试总结")
    print("=" * 60)
    print("🎉 批量抓取逻辑测试完成！")

if __name__ == "__main__":
    test_batch_logic()

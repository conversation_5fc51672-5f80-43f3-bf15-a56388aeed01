#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试比赛筛选功能
验证筛选逻辑和数据库查询是否正常工作
"""

import sqlite3
import sys
import os
from database import OddsDatabase

def test_database_connection():
    """测试数据库连接"""
    print("=== 测试数据库连接 ===")
    try:
        db = OddsDatabase("odds_data.db")
        stats = db.get_database_stats()
        if stats:
            print(f"✅ 数据库连接成功")
            print(f"   比赛数量: {stats['match_count']}")
            print(f"   赔率记录数: {stats['odds_count']}")
            print(f"   数据库大小: {stats['db_size_mb']} MB")
            return True
        else:
            print("❌ 无法获取数据库统计信息")
            return False
    except Exception as e:
        print(f"❌ 数据库连接失败: {e}")
        return False

def test_league_query():
    """测试联赛查询"""
    print("\n=== 测试联赛查询 ===")
    try:
        with sqlite3.connect("odds_data.db") as conn:
            cursor = conn.cursor()
            cursor.execute('SELECT DISTINCT league FROM matches WHERE league IS NOT NULL ORDER BY league')
            leagues = [row[0] for row in cursor.fetchall()]
            
            print(f"✅ 找到 {len(leagues)} 个联赛:")
            for i, league in enumerate(leagues[:10]):  # 只显示前10个
                print(f"   {i+1}. {league}")
            if len(leagues) > 10:
                print(f"   ... 还有 {len(leagues) - 10} 个联赛")
            return True
    except Exception as e:
        print(f"❌ 联赛查询失败: {e}")
        return False

def test_season_query():
    """测试赛季查询"""
    print("\n=== 测试赛季查询 ===")
    try:
        with sqlite3.connect("odds_data.db") as conn:
            cursor = conn.cursor()
            cursor.execute('SELECT DISTINCT season FROM matches WHERE season IS NOT NULL ORDER BY season DESC')
            seasons = [row[0] for row in cursor.fetchall()]
            
            print(f"✅ 找到 {len(seasons)} 个赛季:")
            for i, season in enumerate(seasons[:10]):  # 只显示前10个
                print(f"   {i+1}. {season}")
            if len(seasons) > 10:
                print(f"   ... 还有 {len(seasons) - 10} 个赛季")
            return True
    except Exception as e:
        print(f"❌ 赛季查询失败: {e}")
        return False

def test_filter_query():
    """测试筛选查询"""
    print("\n=== 测试筛选查询 ===")
    try:
        with sqlite3.connect("odds_data.db") as conn:
            conn.row_factory = sqlite3.Row
            cursor = conn.cursor()
            
            # 测试基本查询
            query = '''
                SELECT match_id, league, season, round_info, home_team, away_team,
                       match_time, match_state, home_score, away_score
                FROM matches
                ORDER BY match_id DESC
                LIMIT 5
            '''
            cursor.execute(query)
            rows = cursor.fetchall()
            
            print(f"✅ 基本查询成功，找到 {len(rows)} 条记录:")
            for row in rows:
                match_dict = dict(row)
                teams = f"{match_dict.get('home_team', '')} vs {match_dict.get('away_team', '')}"
                print(f"   {match_dict.get('match_id')}: {teams} ({match_dict.get('league', 'N/A')})")
            
            return True
    except Exception as e:
        print(f"❌ 筛选查询失败: {e}")
        return False

def test_conditional_filter():
    """测试条件筛选"""
    print("\n=== 测试条件筛选 ===")
    try:
        with sqlite3.connect("odds_data.db") as conn:
            conn.row_factory = sqlite3.Row
            cursor = conn.cursor()
            
            # 测试联赛筛选
            cursor.execute('SELECT DISTINCT league FROM matches WHERE league IS NOT NULL LIMIT 1')
            league_row = cursor.fetchone()
            
            if league_row:
                test_league = league_row[0]
                print(f"测试联赛筛选: {test_league}")
                
                query = '''
                    SELECT COUNT(*) as count
                    FROM matches
                    WHERE league = ?
                '''
                cursor.execute(query, (test_league,))
                count = cursor.fetchone()[0]
                print(f"✅ 联赛 '{test_league}' 有 {count} 场比赛")
            
            # 测试比分筛选
            query_with_score = '''
                SELECT COUNT(*) as count
                FROM matches
                WHERE home_score IS NOT NULL AND home_score != '' 
                AND away_score IS NOT NULL AND away_score != ''
            '''
            cursor.execute(query_with_score)
            with_score_count = cursor.fetchone()[0]
            print(f"✅ 有比分的比赛: {with_score_count} 场")
            
            query_without_score = '''
                SELECT COUNT(*) as count
                FROM matches
                WHERE home_score IS NULL OR home_score = '' 
                OR away_score IS NULL OR away_score = ''
            '''
            cursor.execute(query_without_score)
            without_score_count = cursor.fetchone()[0]
            print(f"✅ 无比分的比赛: {without_score_count} 场")
            
            return True
    except Exception as e:
        print(f"❌ 条件筛选测试失败: {e}")
        return False

def test_team_search():
    """测试队伍搜索"""
    print("\n=== 测试队伍搜索 ===")
    try:
        with sqlite3.connect("odds_data.db") as conn:
            conn.row_factory = sqlite3.Row
            cursor = conn.cursor()
            
            # 获取一个队伍名称进行测试
            cursor.execute('SELECT DISTINCT home_team FROM matches WHERE home_team IS NOT NULL LIMIT 1')
            team_row = cursor.fetchone()
            
            if team_row:
                test_team = team_row[0]
                # 取队伍名称的一部分进行模糊搜索
                search_term = test_team[:3] if len(test_team) > 3 else test_team
                
                print(f"测试队伍搜索: '{search_term}'")
                
                query = '''
                    SELECT COUNT(*) as count
                    FROM matches
                    WHERE home_team LIKE ? OR away_team LIKE ?
                '''
                cursor.execute(query, (f"%{search_term}%", f"%{search_term}%"))
                count = cursor.fetchone()[0]
                print(f"✅ 包含 '{search_term}' 的比赛: {count} 场")
            
            return True
    except Exception as e:
        print(f"❌ 队伍搜索测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🧪 比赛筛选功能测试")
    print("=" * 50)
    
    # 检查数据库文件是否存在
    if not os.path.exists("odds_data.db"):
        print("❌ 数据库文件 odds_data.db 不存在")
        print("请先运行主程序或确保数据库文件存在")
        return False
    
    tests = [
        test_database_connection,
        test_league_query,
        test_season_query,
        test_filter_query,
        test_conditional_filter,
        test_team_search
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"❌ 测试异常: {e}")
    
    print("\n" + "=" * 50)
    print(f"📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！筛选功能应该可以正常工作。")
        return True
    else:
        print("⚠️  部分测试失败，请检查相关功能。")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)

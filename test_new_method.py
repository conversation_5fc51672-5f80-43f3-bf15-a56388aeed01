#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试新的两步抓取方案
1. 从oddslist页面获取赔率链接
2. 访问具体的赔率历史页面
"""

import requests
from bs4 import BeautifulSoup
import re
import time

def test_step1_get_odds_links(match_id):
    """第一步：从oddslist页面获取赔率链接"""
    url = f"https://1x2.titan007.com/oddslist/{match_id}.htm"
    print(f"🔗 第一步 - 访问赔率列表页面: {url}")
    
    try:
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
            'Accept-Language': 'zh-CN,zh;q=0.8,en-US;q=0.5,en;q=0.3',
            'Connection': 'keep-alive',
        }
        
        response = requests.get(url, headers=headers, timeout=15)
        
        print(f"📊 状态码: {response.status_code}")
        print(f"📏 内容长度: {len(response.text)}")
        
        if response.status_code != 200:
            print("❌ 页面访问失败")
            return []
        
        # 保存页面内容用于调试
        with open(f'debug_oddslist_{match_id}_new.html', 'w', encoding='utf-8') as f:
            f.write(response.text)
        print(f"📄 页面内容已保存到 debug_oddslist_{match_id}_new.html")
        
        # 解析页面，查找赔率链接
        soup = BeautifulSoup(response.text, 'html.parser')
        
        odds_links = []
        
        # 方法1：查找所有包含OddsHistory的链接
        all_links = soup.find_all('a', href=True)
        print(f"🔗 页面中总共有 {len(all_links)} 个链接")
        
        for link in all_links:
            href = link.get('href', '')
            text = link.get_text().strip()
            
            # 查找包含OddsHistory的链接
            if 'OddsHistory.aspx' in href:
                print(f"  🎯 找到OddsHistory链接: {href}")
                print(f"     链接文本: {text}")
                
                # 提取参数
                if f'sid={match_id}' in href:
                    cid_match = re.search(r'cid=(\d+)', href)
                    id_match = re.search(r'id=(\d+)', href)
                    
                    if cid_match:
                        company_id = cid_match.group(1)
                        record_id = id_match.group(1) if id_match else "unknown"
                        
                        full_url = href if href.startswith('http') else f"https://1x2.titan007.com{href}"
                        
                        odds_links.append({
                            'company_id': company_id,
                            'company_name': text,
                            'record_id': record_id,
                            'url': full_url
                        })
        
        # 方法2：查找JavaScript中的数据
        scripts = soup.find_all('script')
        for script in scripts:
            if script.string:
                content = script.string
                # 查找可能包含公司信息的JavaScript
                if 'company' in content.lower() or 'odds' in content.lower():
                    print(f"📜 找到可能相关的JavaScript代码片段:")
                    print(content[:200] + "..." if len(content) > 200 else content)
        
        print(f"📊 总共找到 {len(odds_links)} 个有效的赔率链接")
        return odds_links
        
    except Exception as e:
        print(f"❌ 第一步失败: {e}")
        return []

def test_step2_get_odds_data(odds_url, company_name):
    """第二步：从赔率历史页面获取数据"""
    print(f"\n🔗 第二步 - 访问赔率历史页面: {company_name}")
    print(f"URL: {odds_url}")
    
    try:
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
            'Accept-Language': 'zh-CN,zh;q=0.8,en-US;q=0.5,en;q=0.3',
            'Connection': 'keep-alive',
            'Referer': 'https://1x2.titan007.com/'
        }
        
        response = requests.get(odds_url, headers=headers, timeout=15)
        
        print(f"📊 状态码: {response.status_code}")
        print(f"📏 内容长度: {len(response.text)}")
        
        if response.status_code != 200:
            print("❌ 赔率历史页面访问失败")
            return False
        
        # 保存页面内容用于调试
        safe_name = re.sub(r'[^\w\-_.]', '_', company_name)
        with open(f'debug_odds_history_{safe_name}.html', 'w', encoding='utf-8') as f:
            f.write(response.text)
        print(f"📄 页面内容已保存到 debug_odds_history_{safe_name}.html")
        
        # 简单检查是否包含赔率数据
        content = response.text.lower()
        keywords = ['odds', '赔率', 'kelly', '凯利', 'table', '表格']
        found_keywords = [kw for kw in keywords if kw in content]
        
        print(f"🔍 找到关键词: {found_keywords}")
        
        # 显示前500个字符
        print(f"📄 内容预览:")
        print("-" * 50)
        print(response.text[:500])
        print("-" * 50)
        
        return len(found_keywords) > 0
        
    except Exception as e:
        print(f"❌ 第二步失败: {e}")
        return False

def test_new_method(match_id):
    """测试完整的新方法"""
    print(f"🎯 测试新的两步抓取方案 - 比赛ID: {match_id}")
    print("=" * 60)
    
    # 第一步：获取赔率链接
    odds_links = test_step1_get_odds_links(match_id)
    
    if not odds_links:
        print("❌ 第一步失败，无法获取赔率链接")
        return False
    
    print(f"\n✅ 第一步成功，找到 {len(odds_links)} 个赔率链接:")
    for i, link in enumerate(odds_links[:5]):  # 只显示前5个
        print(f"  {i+1}. 公司ID: {link['company_id']}, 名称: {link['company_name']}")
        print(f"     URL: {link['url']}")
    
    if len(odds_links) > 5:
        print(f"  ... 还有 {len(odds_links) - 5} 个链接")
    
    # 第二步：测试前3个链接
    success_count = 0
    for i, link in enumerate(odds_links[:3]):
        print(f"\n{'='*40}")
        print(f"测试链接 {i+1}/3")
        print(f"{'='*40}")
        
        success = test_step2_get_odds_data(link['url'], link['company_name'])
        if success:
            success_count += 1
        
        time.sleep(1)  # 避免请求过快
    
    print(f"\n📊 测试结果: {success_count}/3 个链接成功")
    return success_count > 0

def main():
    """主函数"""
    print("🎯 测试新的两步抓取方案")
    print("=" * 60)
    
    # 测试不同年代的比赛
    test_matches = [
        "2804677",  # 您提到的比赛
        "2598314",  # 较新的比赛
        "2500000",  # 更老的比赛
    ]
    
    for match_id in test_matches:
        print(f"\n{'='*80}")
        print(f"测试比赛: {match_id}")
        print(f"{'='*80}")
        
        success = test_new_method(match_id)
        
        if success:
            print(f"✅ 比赛 {match_id} 测试成功")
        else:
            print(f"❌ 比赛 {match_id} 测试失败")
        
        print("\n" + "="*80)

if __name__ == "__main__":
    main()

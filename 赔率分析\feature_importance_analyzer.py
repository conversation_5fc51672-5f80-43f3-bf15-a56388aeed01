#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
特征重要性分析
分析模型中各特征的重要性，识别关键预测因子
"""

import pandas as pd
import numpy as np
import json
import os
from datetime import datetime
import matplotlib.pyplot as plt
import seaborn as sns

from sklearn.ensemble import RandomForestClassifier, GradientBoostingClassifier
from sklearn.linear_model import LogisticRegression
from sklearn.preprocessing import StandardScaler, LabelEncoder
from sklearn.feature_selection import SelectKBest, f_classif, mutual_info_classif, RFE
from sklearn.inspection import permutation_importance
from sklearn.model_selection import train_test_split

import warnings
warnings.filterwarnings('ignore')

class FeatureImportanceAnalyzer:
    def __init__(self, processed_data_file=None):
        self.processed_data_file = processed_data_file
        self.data = None
        self.X = None
        self.y = None
        self.feature_names = None
        self.target_encoder = LabelEncoder()
        self.scaler = StandardScaler()
        
        self.importance_results = {}
        
    def load_processed_data(self, filename=None):
        """加载处理后的数据"""
        if filename:
            self.processed_data_file = filename
        
        if not self.processed_data_file:
            # 查找最新的处理数据文件
            files = [f for f in os.listdir('.') if f.startswith('processed_features_') and f.endswith('.csv')]
            if files:
                self.processed_data_file = sorted(files)[-1]
                print(f"自动选择最新文件: {self.processed_data_file}")
            else:
                print("未找到处理数据文件")
                return False
        
        self.data = pd.read_csv(self.processed_data_file)
        print(f"加载处理数据: {self.processed_data_file}")
        print(f"数据形状: {self.data.shape}")
        
        return True
    
    def prepare_data(self):
        """准备数据"""
        print("\n=== 准备数据 ===")
        
        # 排除非特征列
        exclude_cols = ['match_id', 'company', 'match_result', 'home_team', 'away_team', 'target']
        feature_cols = [col for col in self.data.columns if col not in exclude_cols]
        
        self.X = self.data[feature_cols]
        self.y = self.data['match_result']
        self.feature_names = feature_cols
        
        # 编码目标变量
        self.y_encoded = self.target_encoder.fit_transform(self.y)
        
        # 标准化特征
        self.X_scaled = self.scaler.fit_transform(self.X)
        
        print(f"特征数量: {len(feature_cols)}")
        print(f"样本数量: {len(self.X)}")
        
        return True
    
    def tree_based_importance(self):
        """基于树模型的特征重要性"""
        print("\n=== 基于树模型的特征重要性 ===")
        
        tree_importance = {}
        
        # 随机森林重要性
        print("  计算随机森林特征重要性...")
        rf = RandomForestClassifier(n_estimators=100, random_state=42, class_weight='balanced')
        rf.fit(self.X, self.y_encoded)
        
        rf_importance = pd.DataFrame({
            'feature': self.feature_names,
            'importance': rf.feature_importances_
        }).sort_values('importance', ascending=False)
        
        tree_importance['random_forest'] = rf_importance
        
        # 梯度提升重要性
        print("  计算梯度提升特征重要性...")
        gb = GradientBoostingClassifier(n_estimators=100, random_state=42)
        gb.fit(self.X, self.y_encoded)
        
        gb_importance = pd.DataFrame({
            'feature': self.feature_names,
            'importance': gb.feature_importances_
        }).sort_values('importance', ascending=False)
        
        tree_importance['gradient_boosting'] = gb_importance
        
        # 输出前10个重要特征
        print(f"\n  随机森林前10个重要特征:")
        for i, (_, row) in enumerate(rf_importance.head(10).iterrows(), 1):
            print(f"    {i:2d}. {row['feature']}: {row['importance']:.4f}")
        
        print(f"\n  梯度提升前10个重要特征:")
        for i, (_, row) in enumerate(gb_importance.head(10).iterrows(), 1):
            print(f"    {i:2d}. {row['feature']}: {row['importance']:.4f}")
        
        return tree_importance
    
    def statistical_importance(self):
        """基于统计方法的特征重要性"""
        print("\n=== 基于统计方法的特征重要性 ===")
        
        statistical_importance = {}
        
        # F统计量
        print("  计算F统计量...")
        f_selector = SelectKBest(score_func=f_classif, k='all')
        f_selector.fit(self.X, self.y_encoded)
        
        f_scores = pd.DataFrame({
            'feature': self.feature_names,
            'f_score': f_selector.scores_,
            'p_value': f_selector.pvalues_
        }).sort_values('f_score', ascending=False)
        
        statistical_importance['f_statistic'] = f_scores
        
        # 互信息
        print("  计算互信息...")
        mi_selector = SelectKBest(score_func=mutual_info_classif, k='all')
        mi_selector.fit(self.X, self.y_encoded)
        
        mi_scores = pd.DataFrame({
            'feature': self.feature_names,
            'mi_score': mi_selector.scores_
        }).sort_values('mi_score', ascending=False)
        
        statistical_importance['mutual_information'] = mi_scores
        
        # 输出结果
        print(f"\n  F统计量前10个重要特征:")
        for i, (_, row) in enumerate(f_scores.head(10).iterrows(), 1):
            print(f"    {i:2d}. {row['feature']}: {row['f_score']:.2f} (p={row['p_value']:.3f})")
        
        print(f"\n  互信息前10个重要特征:")
        for i, (_, row) in enumerate(mi_scores.head(10).iterrows(), 1):
            print(f"    {i:2d}. {row['feature']}: {row['mi_score']:.4f}")
        
        return statistical_importance
    
    def linear_model_importance(self):
        """基于线性模型的特征重要性"""
        print("\n=== 基于线性模型的特征重要性 ===")
        
        # 逻辑回归系数
        lr = LogisticRegression(random_state=42, max_iter=1000, class_weight='balanced')
        lr.fit(self.X_scaled, self.y_encoded)
        
        # 对于多分类，取系数的平均绝对值
        if lr.coef_.shape[0] > 1:
            coef_importance = np.mean(np.abs(lr.coef_), axis=0)
        else:
            coef_importance = np.abs(lr.coef_[0])
        
        lr_importance = pd.DataFrame({
            'feature': self.feature_names,
            'coef_abs': coef_importance
        }).sort_values('coef_abs', ascending=False)
        
        print(f"  逻辑回归系数前10个重要特征:")
        for i, (_, row) in enumerate(lr_importance.head(10).iterrows(), 1):
            print(f"    {i:2d}. {row['feature']}: {row['coef_abs']:.4f}")
        
        return lr_importance
    
    def permutation_importance_analysis(self):
        """排列重要性分析"""
        print("\n=== 排列重要性分析 ===")
        
        # 分割数据
        X_train, X_test, y_train, y_test = train_test_split(
            self.X, self.y_encoded, test_size=0.3, random_state=42, stratify=self.y_encoded
        )
        
        permutation_results = {}
        
        # 随机森林排列重要性
        print("  计算随机森林排列重要性...")
        rf = RandomForestClassifier(n_estimators=50, random_state=42, class_weight='balanced')
        rf.fit(X_train, y_train)
        
        rf_perm_importance = permutation_importance(
            rf, X_test, y_test, n_repeats=10, random_state=42, scoring='accuracy'
        )
        
        rf_perm_df = pd.DataFrame({
            'feature': self.feature_names,
            'importance_mean': rf_perm_importance.importances_mean,
            'importance_std': rf_perm_importance.importances_std
        }).sort_values('importance_mean', ascending=False)
        
        permutation_results['random_forest'] = rf_perm_df
        
        print(f"  随机森林排列重要性前10个特征:")
        for i, (_, row) in enumerate(rf_perm_df.head(10).iterrows(), 1):
            print(f"    {i:2d}. {row['feature']}: {row['importance_mean']:.4f} (±{row['importance_std']:.4f})")
        
        return permutation_results
    
    def recursive_feature_elimination(self):
        """递归特征消除"""
        print("\n=== 递归特征消除 ===")
        
        # 使用随机森林进行RFE
        rf = RandomForestClassifier(n_estimators=50, random_state=42, class_weight='balanced')
        
        # 选择前20个特征
        n_features_to_select = min(20, len(self.feature_names))
        rfe = RFE(estimator=rf, n_features_to_select=n_features_to_select, step=1)
        rfe.fit(self.X, self.y_encoded)
        
        rfe_results = pd.DataFrame({
            'feature': self.feature_names,
            'selected': rfe.support_,
            'ranking': rfe.ranking_
        }).sort_values('ranking')
        
        selected_features = rfe_results[rfe_results['selected']]['feature'].tolist()
        
        print(f"  RFE选择的前{n_features_to_select}个特征:")
        for i, feature in enumerate(selected_features, 1):
            print(f"    {i:2d}. {feature}")
        
        return rfe_results
    
    def comprehensive_importance_ranking(self):
        """综合重要性排名"""
        print("\n=== 综合重要性排名 ===")
        
        # 收集所有重要性分数
        all_rankings = pd.DataFrame({'feature': self.feature_names})
        
        # 树模型重要性
        tree_importance = self.tree_based_importance()
        rf_ranks = tree_importance['random_forest'].reset_index(drop=True)
        rf_ranks['rf_rank'] = range(1, len(rf_ranks) + 1)
        all_rankings = all_rankings.merge(rf_ranks[['feature', 'rf_rank']], on='feature', how='left')
        
        gb_ranks = tree_importance['gradient_boosting'].reset_index(drop=True)
        gb_ranks['gb_rank'] = range(1, len(gb_ranks) + 1)
        all_rankings = all_rankings.merge(gb_ranks[['feature', 'gb_rank']], on='feature', how='left')
        
        # 统计重要性
        statistical_importance = self.statistical_importance()
        f_ranks = statistical_importance['f_statistic'].reset_index(drop=True)
        f_ranks['f_rank'] = range(1, len(f_ranks) + 1)
        all_rankings = all_rankings.merge(f_ranks[['feature', 'f_rank']], on='feature', how='left')
        
        mi_ranks = statistical_importance['mutual_information'].reset_index(drop=True)
        mi_ranks['mi_rank'] = range(1, len(mi_ranks) + 1)
        all_rankings = all_rankings.merge(mi_ranks[['feature', 'mi_rank']], on='feature', how='left')
        
        # 线性模型重要性
        lr_importance = self.linear_model_importance()
        lr_ranks = lr_importance.reset_index(drop=True)
        lr_ranks['lr_rank'] = range(1, len(lr_ranks) + 1)
        all_rankings = all_rankings.merge(lr_ranks[['feature', 'lr_rank']], on='feature', how='left')
        
        # 计算平均排名
        rank_columns = ['rf_rank', 'gb_rank', 'f_rank', 'mi_rank', 'lr_rank']
        all_rankings['avg_rank'] = all_rankings[rank_columns].mean(axis=1)
        all_rankings['rank_std'] = all_rankings[rank_columns].std(axis=1)
        
        # 按平均排名排序
        final_ranking = all_rankings.sort_values('avg_rank')
        
        print(f"  综合排名前20个重要特征:")
        for i, (_, row) in enumerate(final_ranking.head(20).iterrows(), 1):
            print(f"    {i:2d}. {row['feature']}: 平均排名{row['avg_rank']:.1f} (±{row['rank_std']:.1f})")
        
        return final_ranking
    
    def analyze_feature_categories(self):
        """分析特征类别的重要性"""
        print("\n=== 特征类别重要性分析 ===")
        
        # 获取综合排名
        final_ranking = self.comprehensive_importance_ranking()
        
        # 定义特征类别
        feature_categories = {
            'opening': ['opening_'],
            'closing': ['closing_'],
            'trend': ['trend_', 'slope_'],
            'volatility': ['volatility_', 'std_', 'cv_'],
            'pattern': ['direction_', 'monotonic_', 'acceleration_'],
            'company': ['company_'],
            'consistency': ['consistency_'],
            'anomaly': ['anomaly_', 'is_anomaly'],
            'interaction': ['interaction_'],
            'sync': ['sync_'],
            'leading': ['leading_']
        }
        
        category_importance = {}
        
        for category, keywords in feature_categories.items():
            category_features = []
            for feature in final_ranking['feature']:
                if any(keyword in feature for keyword in keywords):
                    category_features.append(feature)
            
            if category_features:
                category_ranks = final_ranking[final_ranking['feature'].isin(category_features)]['avg_rank']
                category_importance[category] = {
                    'feature_count': len(category_features),
                    'avg_rank': category_ranks.mean(),
                    'best_rank': category_ranks.min(),
                    'features': category_features[:5]  # 前5个特征
                }
        
        # 按平均排名排序
        sorted_categories = sorted(category_importance.items(), key=lambda x: x[1]['avg_rank'])
        
        print(f"  特征类别重要性排名:")
        for i, (category, stats) in enumerate(sorted_categories, 1):
            print(f"    {i:2d}. {category}: 平均排名{stats['avg_rank']:.1f}, "
                  f"最佳排名{stats['best_rank']:.0f}, {stats['feature_count']}个特征")
        
        return category_importance
    
    def save_importance_analysis(self, filename_prefix="feature_importance"):
        """保存重要性分析结果"""
        print("\n=== 保存重要性分析结果 ===")
        
        # 收集所有结果
        results = {
            'tree_importance': self.tree_based_importance(),
            'statistical_importance': self.statistical_importance(),
            'linear_importance': self.linear_model_importance(),
            'comprehensive_ranking': self.comprehensive_importance_ranking(),
            'category_importance': self.analyze_feature_categories()
        }
        
        # 转换为可序列化的格式
        serializable_results = self._make_serializable(results)
        
        output_file = f"{filename_prefix}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(serializable_results, f, ensure_ascii=False, indent=2)
        
        print(f"重要性分析结果已保存到: {output_file}")
        
        # 保存综合排名为CSV
        csv_file = output_file.replace('.json', '_ranking.csv')
        results['comprehensive_ranking'].to_csv(csv_file, index=False)
        print(f"综合排名已保存到: {csv_file}")
        
        return output_file
    
    def _make_serializable(self, obj):
        """将对象转换为可序列化的格式"""
        if isinstance(obj, dict):
            return {k: self._make_serializable(v) for k, v in obj.items()}
        elif isinstance(obj, (list, tuple)):
            return [self._make_serializable(item) for item in obj]
        elif isinstance(obj, pd.DataFrame):
            return obj.to_dict('records')
        elif isinstance(obj, (np.integer, np.int64)):
            return int(obj)
        elif isinstance(obj, (np.floating, np.float64)):
            return float(obj)
        elif isinstance(obj, (np.bool_, bool)):
            return bool(obj)
        elif isinstance(obj, np.ndarray):
            return obj.tolist()
        elif pd.isna(obj) or obj is None:
            return None
        else:
            return str(obj)

def main():
    analyzer = FeatureImportanceAnalyzer()
    
    # 加载处理后的数据
    if not analyzer.load_processed_data():
        return
    
    # 准备数据
    analyzer.prepare_data()
    
    # 保存重要性分析结果
    analyzer.save_importance_analysis()

if __name__ == "__main__":
    main()

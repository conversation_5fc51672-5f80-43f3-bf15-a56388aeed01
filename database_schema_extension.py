#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据库架构扩展模块
用于管理和扩展数据库架构，支持新功能的数据存储需求

功能特点:
1. 向后兼容的架构升级
2. 灵活的数据结构设计
3. 自动化的表结构管理
4. 数据迁移和备份支持

架构设计原则:
- 不破坏现有数据结构
- 使用JSON存储灵活数据
- 关键字段单独存储便于查询
- 支持未来功能扩展

使用方法:
    manager = DatabaseSchemaManager()
    manager.upgrade_schema()
    manager.verify_schema()
"""

import sqlite3
import json
import logging
from typing import Dict, List, Optional, Any
from datetime import datetime
import os

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class DatabaseSchemaManager:
    """数据库架构管理器"""
    
    def __init__(self, db_path: str = 'odds_data.db'):
        """
        初始化架构管理器
        
        Args:
            db_path: 数据库文件路径
        """
        self.db_path = db_path
        self.schema_version = "2.0.0"  # 扩展版本号
        
    def get_schema_design(self) -> Dict[str, str]:
        """获取完整的架构设计"""
        return {
            # 1. 扩展现有的matches表
            "matches_extended": """
            -- 保持原有字段不变，只添加新字段
            ALTER TABLE matches ADD COLUMN home_formation TEXT;
            ALTER TABLE matches ADD COLUMN away_formation TEXT;
            ALTER TABLE matches ADD COLUMN match_events_json TEXT;
            ALTER TABLE matches ADD COLUMN extended_data_json TEXT;
            ALTER TABLE matches ADD COLUMN schema_version TEXT DEFAULT '2.0.0';
            """,
            
            # 2. 技术统计表
            "technical_stats": """
            CREATE TABLE IF NOT EXISTS technical_stats (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                match_id TEXT NOT NULL,
                stat_type TEXT NOT NULL,  -- 'current', 'historical', 'halftime' 等
                stats_json TEXT NOT NULL, -- JSON格式存储所有统计数据
                stat_count INTEGER DEFAULT 0, -- 统计项目数量
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (match_id) REFERENCES matches(match_id),
                UNIQUE(match_id, stat_type)
            );
            
            CREATE INDEX IF NOT EXISTS idx_technical_stats_match_id ON technical_stats(match_id);
            CREATE INDEX IF NOT EXISTS idx_technical_stats_type ON technical_stats(stat_type);
            """,
            
            # 3. 阵容信息表
            "lineups": """
            CREATE TABLE IF NOT EXISTS lineups (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                match_id TEXT NOT NULL,
                team_type TEXT NOT NULL,  -- 'home' 或 'away'
                lineup_json TEXT NOT NULL, -- JSON格式存储完整阵容数据
                formation TEXT,           -- 阵型信息（便于查询）
                player_count INTEGER DEFAULT 0, -- 球员数量
                substitute_count INTEGER DEFAULT 0, -- 替补数量
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (match_id) REFERENCES matches(match_id),
                UNIQUE(match_id, team_type)
            );
            
            CREATE INDEX IF NOT EXISTS idx_lineups_match_id ON lineups(match_id);
            CREATE INDEX IF NOT EXISTS idx_lineups_formation ON lineups(formation);
            """,
            
            # 4. 球员统计表
            "player_stats": """
            CREATE TABLE IF NOT EXISTS player_stats (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                match_id TEXT NOT NULL,
                player_id INTEGER NOT NULL,
                player_name TEXT NOT NULL,
                team_type TEXT NOT NULL,  -- 'home' 或 'away'
                player_number TEXT,
                position TEXT,
                stats_json TEXT NOT NULL, -- JSON格式存储所有技术统计
                events_json TEXT,         -- JSON格式存储球员相关事件
                rating REAL,              -- 球员评分（便于查询）
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (match_id) REFERENCES matches(match_id),
                UNIQUE(match_id, player_id)
            );
            
            CREATE INDEX IF NOT EXISTS idx_player_stats_match_id ON player_stats(match_id);
            CREATE INDEX IF NOT EXISTS idx_player_stats_player_id ON player_stats(player_id);
            CREATE INDEX IF NOT EXISTS idx_player_stats_position ON player_stats(position);
            CREATE INDEX IF NOT EXISTS idx_player_stats_rating ON player_stats(rating);
            """,
            
            # 5. 进失球概率表
            "goal_probability": """
            CREATE TABLE IF NOT EXISTS goal_probability (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                match_id TEXT NOT NULL,
                probability_type TEXT NOT NULL, -- 'Count_30', 'Count_50' 等
                probability_json TEXT NOT NULL, -- JSON格式存储概率数据
                time_segments INTEGER DEFAULT 0, -- 时间段数量
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (match_id) REFERENCES matches(match_id),
                UNIQUE(match_id, probability_type)
            );
            
            CREATE INDEX IF NOT EXISTS idx_goal_probability_match_id ON goal_probability(match_id);
            CREATE INDEX IF NOT EXISTS idx_goal_probability_type ON goal_probability(probability_type);
            """,
            
            # 6. 架构版本表
            "schema_versions": """
            CREATE TABLE IF NOT EXISTS schema_versions (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                version TEXT NOT NULL UNIQUE,
                description TEXT,
                applied_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                applied_by TEXT DEFAULT 'system'
            );
            
            INSERT OR IGNORE INTO schema_versions (version, description) 
            VALUES ('2.0.0', '扩展架构：支持技术统计、阵容、球员统计、进失球概率');
            """,
            
            # 7. 数据统计视图
            "data_summary_view": """
            CREATE VIEW IF NOT EXISTS match_data_summary AS
            SELECT 
                m.match_id,
                m.home_team,
                m.away_team,
                m.match_time,
                m.home_formation,
                m.away_formation,
                ts_current.stat_count as current_stats_count,
                ts_historical.stat_count as historical_stats_count,
                l_home.player_count as home_player_count,
                l_away.player_count as away_player_count,
                ps_count.player_stats_count,
                gp_count.probability_count
            FROM matches m
            LEFT JOIN technical_stats ts_current ON m.match_id = ts_current.match_id AND ts_current.stat_type = 'current'
            LEFT JOIN technical_stats ts_historical ON m.match_id = ts_historical.match_id AND ts_historical.stat_type = 'historical'
            LEFT JOIN lineups l_home ON m.match_id = l_home.match_id AND l_home.team_type = 'home'
            LEFT JOIN lineups l_away ON m.match_id = l_away.match_id AND l_away.team_type = 'away'
            LEFT JOIN (SELECT match_id, COUNT(*) as player_stats_count FROM player_stats GROUP BY match_id) ps_count 
                ON m.match_id = ps_count.match_id
            LEFT JOIN (SELECT match_id, COUNT(*) as probability_count FROM goal_probability GROUP BY match_id) gp_count 
                ON m.match_id = gp_count.match_id;
            """
        }
    
    def upgrade_schema(self) -> Dict[str, Any]:
        """
        升级数据库架构
        
        Returns:
            升级结果字典
        """
        try:
            logger.info(f"开始升级数据库架构到版本 {self.schema_version}")
            
            # 备份数据库
            backup_result = self.backup_database()
            if not backup_result['success']:
                return {'success': False, 'error': f"备份失败: {backup_result['error']}"}
            
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            schema_design = self.get_schema_design()
            results = {}
            
            # 执行架构升级
            for table_name, sql_commands in schema_design.items():
                try:
                    # 分割多个SQL命令
                    commands = [cmd.strip() for cmd in sql_commands.split(';') if cmd.strip()]
                    
                    for command in commands:
                        cursor.execute(command)
                    
                    results[table_name] = "成功"
                    logger.info(f"表 {table_name} 升级成功")
                    
                except Exception as e:
                    results[table_name] = f"失败: {e}"
                    logger.warning(f"表 {table_name} 升级失败: {e}")
            
            conn.commit()
            conn.close()
            
            # 验证架构
            verification_result = self.verify_schema()
            
            logger.info("数据库架构升级完成")
            return {
                'success': True,
                'backup_file': backup_result.get('backup_file'),
                'upgrade_results': results,
                'verification': verification_result
            }
            
        except Exception as e:
            logger.error(f"架构升级失败: {e}")
            return {'success': False, 'error': str(e)}
    
    def verify_schema(self) -> Dict[str, Any]:
        """
        验证数据库架构
        
        Returns:
            验证结果字典
        """
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            verification_results = {}
            
            # 检查表是否存在
            expected_tables = [
                'matches', 'odds', 'technical_stats', 'lineups', 
                'player_stats', 'goal_probability', 'schema_versions'
            ]
            
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
            existing_tables = [row[0] for row in cursor.fetchall()]
            
            for table in expected_tables:
                verification_results[f'table_{table}'] = table in existing_tables
            
            # 检查matches表的扩展字段
            cursor.execute("PRAGMA table_info(matches)")
            matches_columns = [row[1] for row in cursor.fetchall()]
            
            expected_extended_fields = [
                'home_formation', 'away_formation', 
                'match_events_json', 'extended_data_json'
            ]
            
            for field in expected_extended_fields:
                verification_results[f'matches_{field}'] = field in matches_columns
            
            # 检查索引
            cursor.execute("SELECT name FROM sqlite_master WHERE type='index'")
            existing_indexes = [row[0] for row in cursor.fetchall()]
            
            expected_indexes = [
                'idx_technical_stats_match_id', 'idx_lineups_match_id',
                'idx_player_stats_match_id', 'idx_goal_probability_match_id'
            ]
            
            for index in expected_indexes:
                verification_results[f'index_{index}'] = index in existing_indexes
            
            # 检查视图
            cursor.execute("SELECT name FROM sqlite_master WHERE type='view'")
            existing_views = [row[0] for row in cursor.fetchall()]
            verification_results['view_match_data_summary'] = 'match_data_summary' in existing_views
            
            conn.close()
            
            # 计算验证通过率
            total_checks = len(verification_results)
            passed_checks = sum(1 for result in verification_results.values() if result)
            pass_rate = (passed_checks / total_checks) * 100 if total_checks > 0 else 0
            
            return {
                'pass_rate': pass_rate,
                'total_checks': total_checks,
                'passed_checks': passed_checks,
                'details': verification_results
            }
            
        except Exception as e:
            logger.error(f"架构验证失败: {e}")
            return {'error': str(e)}
    
    def backup_database(self) -> Dict[str, Any]:
        """
        备份数据库
        
        Returns:
            备份结果字典
        """
        try:
            if not os.path.exists(self.db_path):
                return {'success': False, 'error': '数据库文件不存在'}
            
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_filename = f"{self.db_path}.backup_{timestamp}"
            
            # 复制数据库文件
            import shutil
            shutil.copy2(self.db_path, backup_filename)
            
            logger.info(f"数据库已备份到: {backup_filename}")
            return {'success': True, 'backup_file': backup_filename}
            
        except Exception as e:
            logger.error(f"数据库备份失败: {e}")
            return {'success': False, 'error': str(e)}
    
    def get_schema_info(self) -> Dict[str, Any]:
        """
        获取当前架构信息
        
        Returns:
            架构信息字典
        """
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            info = {}
            
            # 获取所有表
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
            tables = [row[0] for row in cursor.fetchall()]
            info['tables'] = tables
            
            # 获取每个表的行数
            table_counts = {}
            for table in tables:
                try:
                    cursor.execute(f"SELECT COUNT(*) FROM {table}")
                    count = cursor.fetchone()[0]
                    table_counts[table] = count
                except:
                    table_counts[table] = 'N/A'
            
            info['table_counts'] = table_counts
            
            # 获取架构版本
            try:
                cursor.execute("SELECT version, applied_at FROM schema_versions ORDER BY applied_at DESC LIMIT 1")
                version_info = cursor.fetchone()
                if version_info:
                    info['current_version'] = version_info[0]
                    info['version_applied_at'] = version_info[1]
                else:
                    info['current_version'] = '1.0.0'
            except:
                info['current_version'] = '1.0.0'
            
            # 获取数据库文件大小
            if os.path.exists(self.db_path):
                file_size = os.path.getsize(self.db_path)
                info['database_size_mb'] = round(file_size / (1024 * 1024), 2)
            
            conn.close()
            return info
            
        except Exception as e:
            logger.error(f"获取架构信息失败: {e}")
            return {'error': str(e)}
    
    def print_schema_info(self):
        """打印架构信息"""
        info = self.get_schema_info()
        
        if 'error' in info:
            print(f"❌ 获取架构信息失败: {info['error']}")
            return
        
        print("📊 数据库架构信息")
        print("=" * 50)
        print(f"当前版本: {info.get('current_version', 'N/A')}")
        print(f"数据库大小: {info.get('database_size_mb', 'N/A')} MB")
        print(f"表数量: {len(info.get('tables', []))}")
        
        print("\n📋 表统计:")
        for table, count in info.get('table_counts', {}).items():
            print(f"  {table}: {count} 行")

def test_database_schema_extension():
    """测试数据库架构扩展"""
    print("🧪 测试数据库架构扩展")
    print("=" * 50)
    
    manager = DatabaseSchemaManager()
    
    # 显示当前架构信息
    print("1. 当前架构信息:")
    manager.print_schema_info()
    
    # 升级架构
    print("\n2. 升级架构:")
    result = manager.upgrade_schema()
    
    if result['success']:
        print("✅ 架构升级成功")
        print(f"备份文件: {result.get('backup_file', 'N/A')}")
        
        verification = result.get('verification', {})
        if verification:
            pass_rate = verification.get('pass_rate', 0)
            print(f"验证通过率: {pass_rate:.1f}%")
    else:
        print(f"❌ 架构升级失败: {result['error']}")
    
    # 显示升级后的架构信息
    print("\n3. 升级后架构信息:")
    manager.print_schema_info()

if __name__ == '__main__':
    test_database_schema_extension()

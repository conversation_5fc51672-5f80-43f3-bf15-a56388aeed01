#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
配置文件
统一管理博彩公司列表和其他配置项
"""

# 博彩公司列表 - 统一配置
BETTING_COMPANIES = {
    # 主要国际公司
    '281': 'bet365',
    '115': '威廉希尔',
    '82': '立博',
    '81': '伟德',
    '90': '易胜博',
    '2': 'betfair',
    '177': 'pinnacle',
    '255': 'bwin',
    '70': 'Betfair',
    '104': 'Interwetten',
    
    # 亚洲公司
    '80': '澳门',
    '432': '香港马会',
    '499': '金宝博',
    '474': '利记',
    '517': '明升',
    '1129': '竞彩官方',
    '91': '188bet',
    '92': 'Dafabet',
    
    # 欧洲公司
    '173': 'betathome',
    '88': 'coral',
    '4': 'Nordicbet',
    '545': 'crown',
    '12': 'Interwetten', 
    '14': 'S<PERSON>',
    '23': 'Pinnacle',
    '31': 'Crown',
    '37': 'SNAI',
    '44': 'Expekt',
    '45': 'Nordicbet',
    '47': 'Betsson',
    '49': 'Unibet',
    '51': 'Bwin',
    '85': 'Betfred',
    '86': 'Coral',
    '87': 'Paddy Power',
    '89': 'Betvictor',
    '93': 'Marathon',
    '95': 'Betway',
    '16': '10bet',
    '18': '12bet',
}

# 网站配置
WEBSITE_CONFIG = {
    'base_url': 'https://m.titan007.com',
    'headers': {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
        'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
        'Accept-Encoding': 'gzip, deflate',
        'Connection': 'keep-alive',
        'Referer': 'https://m.titan007.com/',
    },
    'timeout': 15,
    'max_retries': 3,
    'default_delay': 2.0
}

# 数据库配置
DATABASE_CONFIG = {
    'default_db_name': 'odds_data.db',
    'backup_interval': 24 * 60 * 60,  # 24小时备份一次
    'max_backup_files': 7,  # 保留最近7个备份
    'max_size_mb': 500,  # 数据库最大大小(MB)
    'auto_cleanup_days': 30,  # 自动清理天数
    'warning_size_mb': 100,  # 警告大小(MB)
    'archive_days': 90,  # 归档天数
    'enable_auto_cleanup': False,  # 默认关闭自动清理
    'enable_auto_backup': True,  # 启用自动备份
    'compression_enabled': False,  # 启用压缩(未来功能)

    # 安全模式配置
    'safe_mode': True,  # 启用安全模式（归档而不是删除）
    'archive_before_cleanup': True,  # 清理前先归档
    'never_delete_mode': True,  # 永不删除模式
    'confirm_before_delete': True,  # 删除前需要确认
    'auto_archive_enabled': True,  # 启用自动归档
    'archive_format': 'db',  # 归档格式: 'db', 'csv', 'json'

    # 数据库模式配置
    'mode': 'single',  # 数据库模式: 'single' 或 'league'
    'league_base_dir': 'league_databases',  # 联赛数据库基础目录
    'auto_switch_threshold_mb': 200,  # 自动切换到联赛模式的阈值(MB)
    'enable_auto_switch': False,  # 启用自动切换模式
}

# UI配置
UI_CONFIG = {
    'window_title': '足球赔率数据抓取器 v2.1',
    'window_size': '1000x700',
    'default_match_id': '2741454',
    'default_max_companies': 5,
    'default_delay': 2.0,
    'refresh_interval': 100  # 毫秒
}

# 数据验证配置
VALIDATION_CONFIG = {
    'odds_range': (1.0, 50.0),
    'return_rate_range': (85.0, 100.0),
    'kelly_range': (0.0, 5.0)
}

# 导出配置
EXPORT_CONFIG = {
    'csv_encoding': 'utf-8-sig',
    'json_indent': 2,
    'date_format': '%Y-%m-%d %H:%M:%S'
}

def get_company_name(company_id: str) -> str:
    """根据公司ID获取公司名称"""
    return BETTING_COMPANIES.get(company_id, f"Company_{company_id}")

def get_all_company_ids() -> list:
    """获取所有公司ID列表"""
    return list(BETTING_COMPANIES.keys())

def get_all_companies() -> dict:
    """获取所有公司字典"""
    return BETTING_COMPANIES.copy()

def get_priority_companies(count: int = 17) -> dict:
    """获取优先级较高的公司列表（基于您常用的目标公司）"""
    # 基于bai.py、bai_realtime.py等脚本中使用的17家目标公司
    priority_ids = [
        # 主要国际公司（前10家）
        '281',  # bet365
        '115',  # 威廉希尔
        '82',   # 立博
        '81',   # 伟德
        '90',   # 易胜博
        '80',   # 澳门
        '432',  # 香港马会
        '2',    # betfair
        '177',  # pinnacle
        '255',  # bwin

        # 扩展的目标公司（第11-17家）
        '104',  # Interwetten
        '499',  # 金宝博
        '474',  # 利记
        '517',  # 明升
        '1129', # 竞彩官方
        '173',  # betathome
        '88'    # coral
    ]

    result = {}
    for company_id in priority_ids[:count]:
        if company_id in BETTING_COMPANIES:
            result[company_id] = BETTING_COMPANIES[company_id]

    return result

# 版本信息
VERSION_INFO = {
    'version': '2.1',
    'release_date': '2025-05-25',
    'author': 'Football Odds Scraper Team',
    'description': '足球赔率数据抓取器 - 支持UI界面和数据库存储'
}

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试修复后的功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from enhanced_odds_scraper import EnhancedOddsScraper
import logging

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_fixes():
    """测试修复后的功能"""
    print("🚀 测试修复后的功能")
    print("=" * 60)
    
    scraper = EnhancedOddsScraper()
    match_id = "2213559"
    
    print(f"\n🎯 测试比赛: {match_id}")
    print("阿斯顿维拉 vs 埃弗顿 (应该是2022-08-13)")
    print("-" * 40)
    
    # 测试1：比赛信息提取（时间年份修复）
    print("\n📋 测试1：比赛信息提取")
    print("-" * 40)
    
    match_info = scraper.extract_match_info(match_id)
    
    if match_info:
        print("✅ 比赛信息提取成功")
        print(f"  比赛时间: {match_info.get('match_time', 'N/A')}")
        print(f"  比赛日期: {match_info.get('match_date', 'N/A')}")
        print(f"  推测年份: {match_info.get('inferred_year', 'N/A')}")
        print(f"  原始日期: {match_info.get('raw_date', 'N/A')}")
        print(f"  原始时间: {match_info.get('raw_time', 'N/A')}")
        print(f"  主队: {match_info.get('home_team', 'N/A')}")
        print(f"  客队: {match_info.get('away_team', 'N/A')}")
        
        # 检查年份是否正确
        if match_info.get('match_time', '').startswith('2022'):
            print("  ✅ 年份正确：2022年")
        elif match_info.get('match_time', '').startswith('2025'):
            print("  ❌ 年份错误：仍然是2025年")
        else:
            print(f"  ⚠️ 年份未知：{match_info.get('match_time', '')[:4]}")
    else:
        print("❌ 比赛信息提取失败")
    
    # 测试2：完整数据抓取（赔率抓取修复）
    print(f"\n📋 测试2：完整数据抓取")
    print("-" * 40)
    
    complete_data = scraper.scrape_complete_match_data(match_id, max_companies=5)
    
    if complete_data:
        print("✅ 完整数据抓取成功")
        
        # 检查比赛信息
        match_info = complete_data.get('match_info', {})
        if match_info:
            print(f"  📊 比赛信息: ✅")
            print(f"    时间: {match_info.get('match_time', 'N/A')}")
            print(f"    主队: {match_info.get('home_team', 'N/A')}")
            print(f"    客队: {match_info.get('away_team', 'N/A')}")
        else:
            print(f"  📊 比赛信息: ❌ 无数据")
        
        # 检查赔率数据
        odds_data = complete_data.get('odds_data', [])
        if odds_data:
            print(f"  💰 赔率数据: ✅ {len(odds_data)} 条记录")
            
            # 统计公司
            companies = set(record.get('company_name', 'Unknown') for record in odds_data)
            print(f"    涉及公司: {len(companies)} 家")
            for company in list(companies)[:5]:
                company_records = [r for r in odds_data if r.get('company_name') == company]
                print(f"      {company}: {len(company_records)} 条记录")
            
            # 检查时间数据
            time_samples = []
            for record in odds_data[:5]:
                change_time = record.get('change_time', '')
                if change_time and change_time not in time_samples:
                    time_samples.append(change_time)
            
            if time_samples:
                print(f"    时间样本: {time_samples}")
                # 检查是否包含08月份的数据
                august_records = [r for r in odds_data if '08-' in r.get('change_time', '')]
                if august_records:
                    print(f"    ✅ 包含 {len(august_records)} 条08月份记录")
                else:
                    print(f"    ❌ 未找到08月份记录")
            else:
                print(f"    ⚠️ 无时间数据")
                
        else:
            print(f"  💰 赔率数据: ❌ 无数据")
        
        # 检查摘要信息
        summary = complete_data.get('summary', {})
        if summary:
            print(f"  📈 摘要信息:")
            print(f"    总记录数: {summary.get('total_odds_records', 0)}")
            print(f"    成功公司: {summary.get('successful_companies', 0)}")
            print(f"    抓取方法: {summary.get('scraping_method', 'Unknown')}")
        
    else:
        print("❌ 完整数据抓取失败")
    
    # 测试3：年份推测功能
    print(f"\n📋 测试3：年份推测功能")
    print("-" * 40)
    
    test_cases = [
        ("08-13", "2213559", "应该是2022年"),
        ("12-25", "3000000", "应该是2024年"),
        ("01-15", "2500000", "应该是2023年"),
    ]
    
    for date_str, test_match_id, expected in test_cases:
        inferred_year = scraper._infer_match_year(date_str, test_match_id)
        print(f"  日期: {date_str}, 比赛ID: {test_match_id}")
        print(f"    推测年份: {inferred_year} ({expected})")
    
    print("\n" + "=" * 60)
    print("📋 测试总结")
    print("=" * 60)
    
    # 总结测试结果
    if match_info and match_info.get('match_time', '').startswith('2022'):
        print("时间年份修复: ✅ 成功")
    else:
        print("时间年份修复: ❌ 失败")
    
    if complete_data and complete_data.get('odds_data'):
        print("赔率抓取修复: ✅ 成功")
    else:
        print("赔率抓取修复: ❌ 失败")
    
    print(f"\n🎉 修复测试完成！")

if __name__ == "__main__":
    test_fixes()

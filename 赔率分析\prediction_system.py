#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
模型集成与预测系统
构建模型集成系统，实现自动化预测和概率输出
"""

import pandas as pd
import numpy as np
import json
import os
from datetime import datetime
import pickle

from sklearn.ensemble import RandomForestClassifier, GradientBoostingClassifier, VotingClassifier
from sklearn.linear_model import LogisticRegression
from sklearn.preprocessing import StandardScaler, LabelEncoder
from sklearn.model_selection import train_test_split
from sklearn.metrics import accuracy_score, classification_report, confusion_matrix
from sklearn.calibration import CalibratedClassifierCV

import warnings
warnings.filterwarnings('ignore')

class PredictionSystem:
    def __init__(self, processed_data_file=None):
        self.processed_data_file = processed_data_file
        self.data = None
        self.X = None
        self.y = None
        self.feature_names = None
        
        # 模型组件
        self.target_encoder = LabelEncoder()
        self.scaler = StandardScaler()
        self.ensemble_model = None
        self.calibrated_model = None
        
        # 预测结果
        self.prediction_results = {}
        
    def load_processed_data(self, filename=None):
        """加载处理后的数据"""
        if filename:
            self.processed_data_file = filename
        
        if not self.processed_data_file:
            # 查找最新的处理数据文件
            files = [f for f in os.listdir('.') if f.startswith('processed_features_') and f.endswith('.csv')]
            if files:
                self.processed_data_file = sorted(files)[-1]
                print(f"自动选择最新文件: {self.processed_data_file}")
            else:
                print("未找到处理数据文件")
                return False
        
        self.data = pd.read_csv(self.processed_data_file)
        print(f"加载处理数据: {self.processed_data_file}")
        print(f"数据形状: {self.data.shape}")
        
        return True
    
    def prepare_data(self):
        """准备数据"""
        print("\n=== 准备数据 ===")
        
        # 排除非特征列
        exclude_cols = ['match_id', 'company', 'match_result', 'home_team', 'away_team', 'target']
        feature_cols = [col for col in self.data.columns if col not in exclude_cols]
        
        self.X = self.data[feature_cols]
        self.y = self.data['match_result']
        self.feature_names = feature_cols
        
        # 编码目标变量
        self.y_encoded = self.target_encoder.fit_transform(self.y)
        
        # 标准化特征
        self.X_scaled = self.scaler.fit_transform(self.X)
        
        print(f"特征数量: {len(feature_cols)}")
        print(f"样本数量: {len(self.X)}")
        print(f"类别分布: {dict(self.y.value_counts())}")
        
        return True
    
    def create_ensemble_model(self):
        """创建集成模型"""
        print("\n=== 创建集成模型 ===")
        
        # 定义基础模型
        base_models = [
            ('logistic', LogisticRegression(
                random_state=42, 
                max_iter=1000, 
                class_weight='balanced'
            )),
            ('random_forest', RandomForestClassifier(
                random_state=42, 
                n_estimators=100, 
                class_weight='balanced'
            )),
            ('gradient_boosting', GradientBoostingClassifier(
                random_state=42, 
                n_estimators=100
            ))
        ]
        
        # 创建投票分类器
        self.ensemble_model = VotingClassifier(
            estimators=base_models,
            voting='soft'  # 使用概率投票
        )
        
        print(f"集成模型包含 {len(base_models)} 个基础模型:")
        for name, model in base_models:
            print(f"  - {name}: {type(model).__name__}")
        
        return True
    
    def train_ensemble_model(self):
        """训练集成模型"""
        print("\n=== 训练集成模型 ===")
        
        # 分割数据
        X_train, X_test, y_train, y_test = train_test_split(
            self.X_scaled, self.y_encoded, 
            test_size=0.3, random_state=42, 
            stratify=self.y_encoded
        )
        
        print(f"训练集大小: {len(X_train)}")
        print(f"测试集大小: {len(X_test)}")
        
        # 训练集成模型
        self.ensemble_model.fit(X_train, y_train)
        
        # 评估模型
        train_score = self.ensemble_model.score(X_train, y_train)
        test_score = self.ensemble_model.score(X_test, y_test)
        
        print(f"训练集准确率: {train_score:.3f}")
        print(f"测试集准确率: {test_score:.3f}")
        
        # 预测
        y_pred = self.ensemble_model.predict(X_test)
        y_pred_proba = self.ensemble_model.predict_proba(X_test)
        
        # 详细评估
        print(f"\n分类报告:")
        print(classification_report(
            y_test, y_pred, 
            target_names=self.target_encoder.classes_
        ))
        
        # 保存训练结果
        self.prediction_results['training'] = {
            'train_accuracy': train_score,
            'test_accuracy': test_score,
            'predictions': y_pred,
            'probabilities': y_pred_proba,
            'true_labels': y_test,
            'X_test': X_test
        }
        
        return True
    
    def calibrate_probabilities(self):
        """校准概率输出"""
        print("\n=== 校准概率输出 ===")
        
        # 使用交叉验证校准概率
        self.calibrated_model = CalibratedClassifierCV(
            self.ensemble_model, 
            method='isotonic',  # 等渗回归
            cv=3
        )
        
        # 重新训练校准模型
        self.calibrated_model.fit(self.X_scaled, self.y_encoded)
        
        # 评估校准效果
        training_results = self.prediction_results['training']
        X_test = training_results['X_test']
        y_test = training_results['true_labels']
        
        # 原始概率
        original_proba = self.ensemble_model.predict_proba(X_test)
        
        # 校准后概率
        calibrated_proba = self.calibrated_model.predict_proba(X_test)
        
        print(f"概率校准完成")
        print(f"原始概率范围: [{original_proba.min():.3f}, {original_proba.max():.3f}]")
        print(f"校准后概率范围: [{calibrated_proba.min():.3f}, {calibrated_proba.max():.3f}]")
        
        # 保存校准结果
        self.prediction_results['calibration'] = {
            'original_probabilities': original_proba,
            'calibrated_probabilities': calibrated_proba
        }
        
        return True
    
    def predict_match_outcome(self, match_features):
        """预测单场比赛结果"""
        if self.calibrated_model is None:
            print("模型未训练，请先训练模型")
            return None
        
        # 确保特征格式正确
        if isinstance(match_features, dict):
            # 如果是字典，转换为DataFrame
            feature_df = pd.DataFrame([match_features])
            feature_array = feature_df[self.feature_names].values
        elif isinstance(match_features, (list, np.ndarray)):
            # 如果是数组，直接使用
            feature_array = np.array(match_features).reshape(1, -1)
        else:
            print("不支持的特征格式")
            return None
        
        # 标准化特征
        feature_scaled = self.scaler.transform(feature_array)
        
        # 预测
        prediction = self.calibrated_model.predict(feature_scaled)[0]
        probabilities = self.calibrated_model.predict_proba(feature_scaled)[0]
        
        # 转换预测结果
        predicted_outcome = self.target_encoder.inverse_transform([prediction])[0]
        
        # 构建结果
        result = {
            'predicted_outcome': predicted_outcome,
            'confidence': probabilities.max(),
            'probabilities': {
                class_name: prob 
                for class_name, prob in zip(self.target_encoder.classes_, probabilities)
            }
        }
        
        return result
    
    def batch_predict(self, features_data):
        """批量预测"""
        print(f"\n=== 批量预测 {len(features_data)} 个样本 ===")
        
        if self.calibrated_model is None:
            print("模型未训练，请先训练模型")
            return None
        
        # 准备特征数据
        if isinstance(features_data, pd.DataFrame):
            feature_array = features_data[self.feature_names].values
        else:
            feature_array = np.array(features_data)
        
        # 标准化
        feature_scaled = self.scaler.transform(feature_array)
        
        # 预测
        predictions = self.calibrated_model.predict(feature_scaled)
        probabilities = self.calibrated_model.predict_proba(feature_scaled)
        
        # 转换结果
        predicted_outcomes = self.target_encoder.inverse_transform(predictions)
        
        # 构建结果列表
        results = []
        for i in range(len(predictions)):
            result = {
                'predicted_outcome': predicted_outcomes[i],
                'confidence': probabilities[i].max(),
                'probabilities': {
                    class_name: prob 
                    for class_name, prob in zip(self.target_encoder.classes_, probabilities[i])
                }
            }
            results.append(result)
        
        print(f"批量预测完成")
        
        # 统计预测分布
        outcome_counts = pd.Series(predicted_outcomes).value_counts()
        print(f"预测结果分布:")
        for outcome, count in outcome_counts.items():
            print(f"  {outcome}: {count} ({count/len(predictions)*100:.1f}%)")
        
        return results
    
    def evaluate_prediction_quality(self):
        """评估预测质量"""
        print("\n=== 评估预测质量 ===")
        
        if 'training' not in self.prediction_results:
            print("没有训练结果可评估")
            return None
        
        training_results = self.prediction_results['training']
        y_true = training_results['true_labels']
        y_pred = training_results['predictions']
        y_proba = training_results['probabilities']
        
        # 计算各类别的预测质量
        quality_metrics = {}
        
        for i, class_name in enumerate(self.target_encoder.classes_):
            class_mask = (y_true == i)
            if class_mask.sum() > 0:
                class_proba = y_proba[class_mask, i]
                class_pred = y_pred[class_mask]
                class_accuracy = (class_pred == i).mean()
                
                quality_metrics[class_name] = {
                    'sample_count': class_mask.sum(),
                    'accuracy': class_accuracy,
                    'avg_confidence': class_proba.mean(),
                    'confidence_std': class_proba.std()
                }
        
        print(f"各类别预测质量:")
        for class_name, metrics in quality_metrics.items():
            print(f"  {class_name}:")
            print(f"    样本数: {metrics['sample_count']}")
            print(f"    准确率: {metrics['accuracy']:.3f}")
            print(f"    平均置信度: {metrics['avg_confidence']:.3f}")
            print(f"    置信度标准差: {metrics['confidence_std']:.3f}")
        
        return quality_metrics
    
    def save_prediction_system(self, filename_prefix="prediction_system"):
        """保存预测系统"""
        print("\n=== 保存预测系统 ===")
        
        # 保存模型
        model_file = f"{filename_prefix}_model_{datetime.now().strftime('%Y%m%d_%H%M%S')}.pkl"
        
        model_components = {
            'ensemble_model': self.ensemble_model,
            'calibrated_model': self.calibrated_model,
            'scaler': self.scaler,
            'target_encoder': self.target_encoder,
            'feature_names': self.feature_names
        }
        
        with open(model_file, 'wb') as f:
            pickle.dump(model_components, f)
        
        print(f"模型已保存到: {model_file}")
        
        # 保存系统信息
        info_file = f"{filename_prefix}_info_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        
        system_info = {
            'model_type': 'ensemble_voting_classifier',
            'base_models': ['logistic_regression', 'random_forest', 'gradient_boosting'],
            'feature_count': len(self.feature_names),
            'target_classes': list(self.target_encoder.classes_),
            'training_accuracy': self.prediction_results.get('training', {}).get('train_accuracy', 0),
            'test_accuracy': self.prediction_results.get('training', {}).get('test_accuracy', 0),
            'calibration_method': 'isotonic_regression',
            'created_at': datetime.now().isoformat()
        }
        
        with open(info_file, 'w', encoding='utf-8') as f:
            json.dump(system_info, f, ensure_ascii=False, indent=2)
        
        print(f"系统信息已保存到: {info_file}")
        
        return model_file, info_file
    
    def load_prediction_system(self, model_file):
        """加载预测系统"""
        print(f"\n=== 加载预测系统 ===")
        
        try:
            with open(model_file, 'rb') as f:
                model_components = pickle.load(f)
            
            self.ensemble_model = model_components['ensemble_model']
            self.calibrated_model = model_components['calibrated_model']
            self.scaler = model_components['scaler']
            self.target_encoder = model_components['target_encoder']
            self.feature_names = model_components['feature_names']
            
            print(f"预测系统加载成功")
            print(f"特征数量: {len(self.feature_names)}")
            print(f"目标类别: {list(self.target_encoder.classes_)}")
            
            return True
            
        except Exception as e:
            print(f"加载预测系统失败: {e}")
            return False
    
    def generate_prediction_report(self):
        """生成预测报告"""
        print("\n=== 生成预测报告 ===")
        
        # 评估预测质量
        quality_metrics = self.evaluate_prediction_quality()
        
        # 生成报告
        report = {
            'system_info': {
                'model_type': 'ensemble_voting_classifier',
                'feature_count': len(self.feature_names),
                'target_classes': list(self.target_encoder.classes_)
            },
            'performance_metrics': self.prediction_results.get('training', {}),
            'quality_metrics': quality_metrics,
            'recommendations': self._generate_recommendations(quality_metrics)
        }
        
        return report
    
    def _generate_recommendations(self, quality_metrics):
        """生成使用建议"""
        recommendations = []
        
        if quality_metrics:
            # 检查整体准确率
            avg_accuracy = np.mean([m['accuracy'] for m in quality_metrics.values()])
            if avg_accuracy > 0.8:
                recommendations.append("模型整体性能良好，可用于实际预测")
            elif avg_accuracy > 0.6:
                recommendations.append("模型性能中等，建议谨慎使用并结合其他信息")
            else:
                recommendations.append("模型性能较低，建议进一步优化后使用")
            
            # 检查类别平衡性
            sample_counts = [m['sample_count'] for m in quality_metrics.values()]
            if max(sample_counts) / min(sample_counts) > 3:
                recommendations.append("数据类别不平衡，建议收集更多少数类别样本")
            
            # 检查置信度
            avg_confidence = np.mean([m['avg_confidence'] for m in quality_metrics.values()])
            if avg_confidence < 0.6:
                recommendations.append("模型置信度较低，建议增加特征或改进模型")
        
        return recommendations

def main():
    system = PredictionSystem()
    
    # 加载处理后的数据
    if not system.load_processed_data():
        return
    
    # 准备数据
    system.prepare_data()
    
    # 创建集成模型
    system.create_ensemble_model()
    
    # 训练模型
    system.train_ensemble_model()
    
    # 校准概率
    system.calibrate_probabilities()
    
    # 生成预测报告
    report = system.generate_prediction_report()
    
    # 保存预测系统
    model_file, info_file = system.save_prediction_system()
    
    print(f"\n=== 预测系统构建完成 ===")
    print(f"模型文件: {model_file}")
    print(f"信息文件: {info_file}")
    
    # 输出使用建议
    if report['recommendations']:
        print(f"\n使用建议:")
        for rec in report['recommendations']:
            print(f"  - {rec}")

if __name__ == "__main__":
    main()

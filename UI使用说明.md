# 足球赔率数据抓取器 - UI版本使用说明

## 程序简介

这是一个带图形界面的足球赔率数据抓取器，可以从win007网站抓取比赛信息和赔率数据，并保存到本地SQLite数据库中。

## 功能特点

- 🎯 **图形界面操作**：简单易用的GUI界面
- 📊 **完整数据抓取**：同时获取比赛信息和赔率数据
- 💾 **数据库存储**：自动保存到本地SQLite数据库
- 📈 **数据管理**：查看、删除、导出数据
- 🔄 **实时状态**：显示抓取进度和状态
- 📋 **数据统计**：数据库统计信息

## 启动程序

### 方法1：使用启动脚本（推荐）
双击运行 `start_ui.bat` 文件，程序会自动检查依赖并启动。

### 方法2：命令行启动
```bash
# 安装依赖（首次运行）
pip install -r requirements.txt

# 启动程序
python odds_scraper_ui.py
```

## 界面说明

### 主界面布局

程序界面分为以下几个部分：

1. **数据抓取区域**（顶部）
   - 比赛ID输入框
   - 最大公司数设置
   - 延迟时间设置
   - 开始抓取按钮
   - 进度条和状态显示

2. **数据显示区域**（中间，分为3个标签页）
   - **比赛列表**：显示所有已抓取的比赛
   - **比赛详情**：显示选中比赛的详细信息和赔率数据
   - **数据库统计**：显示数据库统计信息

3. **状态栏**（底部）
   - 当前状态信息
   - 数据库统计摘要

## 使用步骤

### 1. 抓取新比赛数据

1. 在"比赛ID"输入框中输入要抓取的比赛ID（如：2741454）
2. 设置"最大公司数"（建议5-10家，避免抓取时间过长）
3. 设置"延迟时间"（建议2-3秒，避免被网站封禁）
4. 点击"开始抓取"按钮
5. 等待抓取完成，程序会显示进度和结果

### 2. 查看比赛数据

1. 在"比赛列表"标签页中查看所有已抓取的比赛
2. 双击任意比赛行，自动切换到"比赛详情"页面
3. 在"比赛详情"页面查看：
   - 比赛基本信息（时间、队伍、比分等）
   - 详细赔率数据（各公司的历史赔率变化）

### 3. 管理数据

- **刷新**：点击"刷新"按钮更新数据显示
- **删除**：选中比赛后点击"删除选中"删除数据
- **导出**：选中比赛后点击"导出CSV"导出数据到文件

### 4. 查看统计信息

在"数据库统计"标签页中查看：
- 比赛数量
- 赔率记录数
- 博彩公司数
- 数据库文件大小

## 数据存储

### 数据库文件
- 文件名：`odds_data.db`
- 位置：程序运行目录
- 格式：SQLite数据库

### 数据表结构

**matches表（比赛信息）**
- match_id：比赛ID
- league：联赛名称
- home_team/away_team：主客队名称
- match_time：比赛时间
- home_score/away_score：比分
- match_state：比赛状态
- weather/temperature：天气信息

**odds表（赔率数据）**
- match_id：比赛ID
- company_name：博彩公司名称
- date/time：赔率时间
- home_odds/draw_odds/away_odds：三项赔率
- return_rate：返还率
- kelly_home/kelly_draw/kelly_away：凯利指数

## 获取比赛ID

比赛ID可以从win007网站的URL中获取：

1. 访问 https://m.titan007.com/
2. 找到想要的比赛
3. 点击进入比赛页面
4. 从URL中提取ID，例如：
   - URL: `https://m.titan007.com/compensate/2741454.htm`
   - 比赛ID: `2741454`

## 常见问题

### Q: 程序启动失败
**A:** 检查以下几点：
- 确保已安装Python 3.7+
- 运行 `pip install -r requirements.txt` 安装依赖
- 检查网络连接

### Q: 抓取失败或数据为空
**A:** 可能的原因：
- 比赛ID不正确
- 网络连接问题
- 被网站临时封禁（增加延迟时间）
- 比赛没有赔率数据

### Q: 程序运行缓慢
**A:** 优化建议：
- 减少最大公司数量
- 增加延迟时间
- 检查网络连接速度

### Q: 数据库文件损坏
**A:** 解决方法：
- 删除 `odds_data.db` 文件
- 重新启动程序（会自动创建新数据库）

## 注意事项

1. **合理使用**：请合理控制抓取频率，避免对网站造成过大负担
2. **网络环境**：确保网络连接稳定
3. **数据备份**：定期备份 `odds_data.db` 文件
4. **法律合规**：仅用于学习和研究目的

## 技术支持

如遇到问题，请检查：
1. 程序运行目录下的日志信息
2. 网络连接状态
3. Python和依赖包版本

## 更新日志

### v2.0
- 新增图形界面
- 集成数据库存储
- 支持数据管理功能
- 添加实时状态显示

### v1.0
- 基础命令行版本
- 支持赔率数据抓取
- CSV/JSON格式输出

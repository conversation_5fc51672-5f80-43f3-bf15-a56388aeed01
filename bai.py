#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
独立比赛筛选分析工具
实现博彩态度2和凯利分析2的筛选功能
每次都重新抓取赔率数据，无数据库依赖
"""

import logging
from datetime import datetime
from typing import List, Dict, Optional
from enhanced_odds_scraper import EnhancedOddsScraper

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class MatchAnalyzer:
    """独立比赛分析器"""

    def __init__(self):
        """初始化分析器"""
        # 使用现有的赔率抓取器
        self.scraper = EnhancedOddsScraper()

        self.target_companies = [
            'bet365', 'betathome', 'betfair', 'bwin', 'coral',
            'Interwetten', 'pinnacle', '伟德', '利记', '威廉希尔',
            '明升', '易胜博', '澳门', '立博', '金宝博', '香港马会', '竞彩官方'
        ]

        print("🎯 独立比赛筛选分析工具已初始化")
        print("📡 每次分析都将重新抓取最新赔率数据")
        print(f"🏢 支持的博彩公司: {len(self.target_companies)} 家")
    
    def scrape_match_odds(self, match_id: str) -> Optional[Dict]:
        """重新抓取比赛的赔率数据"""
        print(f"\n📡 正在抓取比赛 {match_id} 的赔率数据...")

        try:
            # 使用现有的抓取器获取完整数据
            complete_data = self.scraper.scrape_complete_match_data(
                match_id=match_id,
                max_companies=16,  # 抓取所有16家公司
                delay=1.0  # 1秒延迟
            )

            if not complete_data or not complete_data.get('odds_data'):
                print("❌ 未能获取到赔率数据")
                return None

            # 转换数据格式
            companies_data = {}
            odds_records = complete_data['odds_data']

            print(f"📊 原始数据包含 {len(odds_records)} 条记录")

            # 按公司分组，取最新的赔率
            company_latest_odds = {}
            for record in odds_records:
                company_name = record['company_name']

                # 检查是否是目标公司
                if company_name not in self.target_companies:
                    continue

                # 构建时间键用于排序
                time_key = f"{record['date']} {record['time']}"

                if company_name not in company_latest_odds:
                    company_latest_odds[company_name] = record
                else:
                    # 比较时间，保留最新的
                    current_time = f"{company_latest_odds[company_name]['date']} {company_latest_odds[company_name]['time']}"
                    if time_key > current_time:
                        company_latest_odds[company_name] = record

            # 转换为标准格式
            for company_name, record in company_latest_odds.items():
                companies_data[company_name] = {
                    'home_odds': record['home_odds'],
                    'draw_odds': record['draw_odds'],
                    'away_odds': record['away_odds'],
                    'scrape_time': f"{record['date']} {record['time']}"
                }

            print(f"✅ 成功处理 {len(companies_data)} 家公司的最新赔率")
            for company, odds in companies_data.items():
                print(f"  📊 {company}: {odds['home_odds']:.2f} {odds['draw_odds']:.2f} {odds['away_odds']:.2f}")

            return {
                'match_id': match_id,
                'companies': companies_data,
                'scrape_time': datetime.now().isoformat()
            }

        except Exception as e:
            print(f"❌ 抓取赔率数据失败: {e}")
            return None



    def analyze_betting_attitude2(self, match_id: str, odds_type: str, threshold: int, target_company: str):
        """分析博彩态度2（基于实时抓取数据）"""
        print(f"\n🔍 开始分析比赛 {match_id} 的博彩态度2")
        print(f"📋 目标公司: {target_company}, 投注类型: {odds_type}, 阈值: {threshold}")

        try:
            # 重新抓取赔率数据
            odds_data = self.scrape_match_odds(match_id)
            if not odds_data or not odds_data['companies']:
                return False, f"无法获取比赛 {match_id} 的赔率数据"

            companies = odds_data['companies']
            print(f"📊 成功获取 {len(companies)} 家公司的赔率数据")

            # 检查目标公司是否存在
            if target_company not in companies:
                available_companies = list(companies.keys())
                return False, f"目标公司 {target_company} 不在可用公司列表中。可用公司: {available_companies}"

            # 获取目标公司的赔率
            target_data = companies[target_company]
            if odds_type == 'home':
                target_odds = target_data['home_odds']
            elif odds_type == 'draw':
                target_odds = target_data['draw_odds']
            elif odds_type == 'away':
                target_odds = target_data['away_odds']
            else:
                return False, f"无效的投注类型: {odds_type}"

            print(f"🎯 目标公司 {target_company} 的 {odds_type} 赔率: {target_odds:.2f}")

            # 统计有多少其他公司的赔率高于目标公司
            higher_count = 0
            other_companies_data = []

            for company_name, company_data in companies.items():
                if company_name == target_company:
                    continue

                if odds_type == 'home':
                    company_odds = company_data['home_odds']
                elif odds_type == 'draw':
                    company_odds = company_data['draw_odds']
                else:  # away
                    company_odds = company_data['away_odds']

                other_companies_data.append({
                    'company': company_name,
                    'odds': company_odds,
                    'higher': company_odds > target_odds
                })

                if company_odds > target_odds:
                    higher_count += 1
                    print(f"  📈 {company_name}: {company_odds:.2f} (高于目标)")
                else:
                    print(f"  📉 {company_name}: {company_odds:.2f} (低于或等于目标)")

            # 判断是否符合条件：高于目标公司赔率的公司数量小于阈值
            is_qualified = higher_count < threshold

            result_msg = f"目标公司 {target_company} 赔率: {target_odds:.2f}, 高于此赔率的公司数量: {higher_count}/{len(other_companies_data)}, 阈值: {threshold}, 结果: {'✅符合条件' if is_qualified else '❌不符合条件'}"

            print(f"📊 分析结果: {result_msg}")
            return is_qualified, result_msg

        except Exception as e:
            error_msg = f"分析失败: {e}"
            print(f"❌ {error_msg}")
            return False, error_msg

def main():
    """主函数"""
    print("🎯 独立比赛筛选分析工具")
    print("=" * 60)

    analyzer = MatchAnalyzer()

    while True:
        print(f"\n请输入比赛ID（输入 'quit' 退出）:")
        match_id = input().strip()

        if match_id.lower() == 'quit':
            break

        if not match_id:
            print("❌ 请输入有效的比赛ID")
            continue

        print(f"\n🧪 开始分析比赛 {match_id}...")

        # 测试筛选条件1：竞猜官方，阈值2，主胜
        print(f"\n{'='*50}")
        print("🔍 筛选条件1：博彩态度2（竞猜官方，阈值2，主胜）")
        print(f"{'='*50}")

        result1, msg1 = analyzer.analyze_betting_attitude2(match_id, 'home', 2, '竞猜官方')
        print(f"📊 条件1结果: {msg1}")

        # 测试筛选条件2：香港马会，阈值2，主胜
        print(f"\n{'='*50}")
        print("🔍 筛选条件2：博彩态度2（香港马会，阈值2，主胜）")
        print(f"{'='*50}")

        result2, msg2 = analyzer.analyze_betting_attitude2(match_id, 'home', 2, '香港马会')
        print(f"📊 条件2结果: {msg2}")

        # 综合结果
        print(f"\n{'='*50}")
        print("📋 综合分析结果")
        print(f"{'='*50}")
        print(f"条件1（竞猜官方）: {'✅通过' if result1 else '❌未通过'}")
        print(f"条件2（香港马会）: {'✅通过' if result2 else '❌未通过'}")

        if result1 and result2:
            print("🎉 比赛通过所有筛选条件！")
        elif result1 or result2:
            print("⚠️  比赛部分通过筛选条件")
        else:
            print("❌ 比赛未通过任何筛选条件")

if __name__ == "__main__":
    main()
        """获取其他公司在指定时间点的赔率"""
        try:
            # 构造目标时间点 - 动态获取年份
            target_datetime = None
            for year in [2024, 2025]:
                try:
                    target_datetime_str = f"{year}-{target_date} {target_time}:00"
                    target_datetime = datetime.strptime(target_datetime_str, "%Y-%m-%d %H:%M:%S")
                    break
                except ValueError:
                    continue

            if target_datetime is None:
                logger.error(f"无法解析时间: {target_date} {target_time}")
                return []

            # 获取所有其他公司的赔率数据
            cursor.execute(f'''
                SELECT company_name, date, time, {odds_type}
                FROM odds
                WHERE match_id = ? AND company_name != ? AND {odds_type} IS NOT NULL
                ORDER BY company_name, date ASC, time ASC
            ''', (match_id, target_company))

            all_other_odds = cursor.fetchall()

            # 按公司分组
            company_odds = {}
            for record in all_other_odds:
                company_name = record[0]
                if company_name not in company_odds:
                    company_odds[company_name] = []

                # 构造该记录的时间 - 动态获取年份
                record_datetime = None
                for year in [2024, 2025]:
                    try:
                        record_datetime_str = f"{year}-{record[1]} {record[2]}:00"
                        record_datetime = datetime.strptime(record_datetime_str, "%Y-%m-%d %H:%M:%S")
                        break
                    except ValueError:
                        continue

                if record_datetime is not None:
                    company_odds[company_name].append({
                        'datetime': record_datetime,
                        'odds': float(record[3])
                    })

            # 对每个公司找到在目标时间点的赔率
            result_odds = []

            for company_name, odds_list in company_odds.items():
                # 按时间排序
                odds_list.sort(key=lambda x: x['datetime'])

                # 找到该公司在目标时间点的赔率
                target_odds_value = None

                # 首先检查是否在目标时间点有数据
                for odds_record in odds_list:
                    if odds_record['datetime'] == target_datetime:
                        target_odds_value = odds_record['odds']
                        break

                # 如果目标时间点没有数据，找最近的之前时间点
                if target_odds_value is None:
                    latest_before_target = None
                    for odds_record in odds_list:
                        if odds_record['datetime'] < target_datetime:
                            latest_before_target = odds_record
                        else:
                            break  # 已经超过目标时间点

                    if latest_before_target:
                        target_odds_value = latest_before_target['odds']

                # 如果该公司在目标时间点或之前有数据，添加其赔率
                if target_odds_value is not None:
                    result_odds.append({
                        'company_name': company_name,
                        odds_type: target_odds_value
                    })

            return result_odds

        except Exception as e:
            logger.error(f"获取其他公司赔率失败: {e}")
            return []
    
    def analyze_match_betting_attitude2(self, match_id, odds_type, threshold, target_company):
        """分析单场比赛的博彩态度2"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                conn.row_factory = sqlite3.Row
                cursor = conn.cursor()

                # 获取目标公司的开盘时间和赔率
                cursor.execute(f'''
                    SELECT date, time, {odds_type}
                    FROM odds
                    WHERE match_id = ? AND company_name = ? AND {odds_type} IS NOT NULL
                    ORDER BY date ASC, time ASC
                    LIMIT 1
                ''', (match_id, target_company))

                target_odds_record = cursor.fetchone()
                if not target_odds_record:
                    return False

                target_date = target_odds_record['date']
                target_time = target_odds_record['time']
                target_odds = float(target_odds_record[odds_type])

                # 获取该时间点所有其他公司的赔率
                other_companies_odds = self.get_other_companies_odds_at_time(
                    cursor, match_id, target_company, target_date, target_time, odds_type
                )

                if len(other_companies_odds) == 0:
                    return False

                # 统计其他公司中赔率 > 目标公司赔率的数量
                higher_count = 0
                for record in other_companies_odds:
                    other_odds = float(record[odds_type])
                    if other_odds > target_odds:
                        higher_count += 1

                # 检查是否满足阈值条件：数量小于阈值
                return higher_count < threshold

        except Exception as e:
            logger.error(f"分析比赛 {match_id} 失败: {e}")
            return False

    def apply_betting_attitude2_filter(self, odds_type, threshold, selected_company):
        """应用博彩态度2筛选"""
        try:
            qualified_matches = []

            # 获取所有有赔率数据的比赛
            with sqlite3.connect(self.db_path) as conn:
                conn.row_factory = sqlite3.Row
                cursor = conn.cursor()

                # 获取所有比赛ID
                cursor.execute(f'SELECT DISTINCT match_id FROM odds WHERE {odds_type} IS NOT NULL')
                match_ids = [row[0] for row in cursor.fetchall()]

                logger.info(f"开始博彩态度2筛选，共 {len(match_ids)} 场比赛")

                # 对每场比赛进行博彩态度2分析
                for match_id in match_ids:
                    if self.analyze_match_betting_attitude2(match_id, odds_type, threshold, selected_company):
                        qualified_matches.append(match_id)

                logger.info(f"博彩态度2筛选完成，符合条件的比赛: {len(qualified_matches)} 场")

            return qualified_matches

        except Exception as e:
            logger.error(f"博彩态度2筛选失败: {e}")
            return []

    def scrape_match_odds(self, match_id: str) -> bool:
        """抓取比赛赔率数据"""
        print(f"\n🔍 开始抓取比赛 {match_id} 的赔率数据...")
        
        try:
            # 检查比赛是否已存在
            if self.check_match_exists(match_id):
                print(f"✅ 比赛 {match_id} 的数据已存在")
                existing_companies = self.get_match_companies(match_id)
                print(f"📊 已有博彩公司: {', '.join(existing_companies)}")
                
                # 检查是否包含目标公司
                missing_companies = [c for c in self.target_companies if c not in existing_companies]
                if missing_companies:
                    print(f"⚠️  缺少的公司: {', '.join(missing_companies)}")
                else:
                    print("✅ 所有目标公司数据完整")
                
                return True
            else:
                print(f"⚠️  比赛 {match_id} 数据不存在，需要手动抓取")
                print("💡 请使用主程序的单场抓取功能获取数据")
                return False

        except Exception as e:
            print(f"❌ 抓取失败: {e}")
            return False

    def calculate_kelly_criterion(self, odds, probability):
        """计算凯利公式值"""
        try:
            if odds <= 1 or probability <= 0 or probability >= 1:
                return 0

            # 凯利公式: f = (bp - q) / b
            # 其中 b = odds - 1, p = probability, q = 1 - probability
            b = odds - 1
            p = probability
            q = 1 - p

            kelly = (b * p - q) / b
            return max(0, kelly)  # 凯利值不能为负

        except Exception:
            return 0

    def analyze_match_kelly2(self, match_id, kelly_type, stats_count, kelly_threshold, selected_company, meaningless_threshold):
        """分析单场比赛的凯利分析2"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                conn.row_factory = sqlite3.Row
                cursor = conn.cursor()

                # 获取目标公司的最新赔率
                cursor.execute(f'''
                    SELECT {kelly_type}
                    FROM odds
                    WHERE match_id = ? AND company_name = ? AND {kelly_type} IS NOT NULL
                    ORDER BY date DESC, time DESC
                    LIMIT 1
                ''', (match_id, selected_company))

                target_record = cursor.fetchone()
                if not target_record:
                    return False

                target_odds = float(target_record[kelly_type])

                # 获取其他公司的最新赔率
                cursor.execute(f'''
                    SELECT company_name, {kelly_type}
                    FROM (
                        SELECT company_name, {kelly_type},
                               ROW_NUMBER() OVER (PARTITION BY company_name ORDER BY date DESC, time DESC) as rn
                        FROM odds
                        WHERE match_id = ? AND company_name != ? AND {kelly_type} IS NOT NULL
                    ) ranked
                    WHERE rn = 1
                ''', (match_id, selected_company))

                other_odds = cursor.fetchall()

                if len(other_odds) < stats_count:
                    return False

                # 过滤掉无意义的赔率（过高的赔率）
                meaningful_odds = []
                for record in other_odds:
                    odds_value = float(record[kelly_type])
                    if odds_value <= meaningless_threshold:
                        meaningful_odds.append(odds_value)

                if len(meaningful_odds) < stats_count:
                    return False

                # 取前stats_count家公司的赔率
                meaningful_odds = sorted(meaningful_odds)[:stats_count]

                # 计算平均赔率
                avg_odds = sum(meaningful_odds) / len(meaningful_odds)

                # 计算隐含概率
                implied_probability = 1.0 / avg_odds

                # 计算凯利值
                kelly_value = self.calculate_kelly_criterion(target_odds, implied_probability)

                # 检查是否满足凯利门槛
                return kelly_value >= kelly_threshold

        except Exception as e:
            logger.error(f"凯利分析2失败: {e}")
            return False

    def apply_kelly_analysis2_filter(self, kelly_type, stats_count, kelly_threshold, selected_company, meaningless_threshold):
        """应用凯利分析2筛选"""
        try:
            qualified_matches = []

            # 获取所有有赔率数据的比赛
            with sqlite3.connect(self.db_path) as conn:
                conn.row_factory = sqlite3.Row
                cursor = conn.cursor()

                # 获取所有比赛ID
                cursor.execute(f'SELECT DISTINCT match_id FROM odds WHERE {kelly_type} IS NOT NULL')
                match_ids = [row[0] for row in cursor.fetchall()]

                logger.info(f"开始凯利分析2筛选，共 {len(match_ids)} 场比赛")

                # 对每场比赛进行凯利分析2
                for match_id in match_ids:
                    if self.analyze_match_kelly2(match_id, kelly_type, stats_count, kelly_threshold, selected_company, meaningless_threshold):
                        qualified_matches.append(match_id)

                logger.info(f"凯利分析2筛选完成，符合条件的比赛: {len(qualified_matches)} 场")

            return qualified_matches

        except Exception as e:
            logger.error(f"凯利分析2筛选失败: {e}")
            return []
                
        except Exception as e:
            print(f"❌ 抓取失败: {e}")
            return False
    
    def apply_filter_condition_1(self, match_id: str) -> bool:
        """应用筛选条件1：博彩态度2"""
        print("\n🔍 应用筛选条件1: 博彩态度2")
        print("  - 阈值: 2")
        print("  - 赔率类型: 主胜")
        print("  - 博彩公司: 竞猜官方")

        try:
            # 使用独立的博彩态度2筛选方法
            result = self.analyze_match_betting_attitude2(
                match_id=match_id,
                odds_type='home_odds',
                threshold=2,
                target_company='竞猜官方'
            )

            print(f"📊 筛选结果: {'✅ 通过' if result else '❌ 不通过'}")
            return result

        except Exception as e:
            print(f"❌ 筛选条件1执行失败: {e}")
            return False
    
    def apply_filter_condition_2(self, match_id: str) -> bool:
        """应用筛选条件2：博彩态度2 + 凯利分析2"""
        print("\n🔍 应用筛选条件2: 博彩态度2 + 凯利分析2")
        print("  博彩态度2:")
        print("    - 阈值: 2")
        print("    - 赔率类型: 主胜")
        print("    - 博彩公司: 香港马会")
        print("  凯利分析2:")
        print("    - 凯利类型: 凯利主")
        print("    - 统计数量: 10")
        print("    - 凯利门槛: 1.0")
        print("    - 博彩公司: betfair")
        print("    - 无意义公司门槛: 2")

        try:
            # 博彩态度2筛选
            betting_attitude2_pass = self.analyze_match_betting_attitude2(
                match_id=match_id,
                odds_type='home_odds',
                threshold=2,
                target_company='香港马会'
            )

            print(f"📊 博彩态度2结果: {'✅ 通过' if betting_attitude2_pass else '❌ 不通过'}")

            if not betting_attitude2_pass:
                print("❌ 博彩态度2不通过，整体筛选失败")
                return False

            # 凯利分析2筛选
            kelly_analysis2_pass = self.analyze_match_kelly2(
                match_id=match_id,
                kelly_type='home_odds',  # 凯利主对应home_odds
                stats_count=10,
                kelly_threshold=1.0,
                selected_company='betfair',
                meaningless_threshold=2
            )

            print(f"📊 凯利分析2结果: {'✅ 通过' if kelly_analysis2_pass else '❌ 不通过'}")

            # 两个条件都要通过
            result = betting_attitude2_pass and kelly_analysis2_pass
            print(f"📊 综合结果: {'✅ 通过' if result else '❌ 不通过'}")
            return result

        except Exception as e:
            print(f"❌ 筛选条件2执行失败: {e}")
            return False
    
    def apply_filter_condition_3(self, match_id: str) -> Dict[str, bool]:
        """应用筛选条件3：分别使用条件1和条件2"""
        print("\n🔍 应用筛选条件3: 分别测试条件1和条件2")
        
        result1 = self.apply_filter_condition_1(match_id)
        result2 = self.apply_filter_condition_2(match_id)
        
        results = {
            'condition_1': result1,
            'condition_2': result2
        }
        
        print(f"\n📊 条件3综合结果:")
        print(f"  条件1 (博彩态度2): {'✅ 通过' if result1 else '❌ 不通过'}")
        print(f"  条件2 (博彩态度2+凯利分析2): {'✅ 通过' if result2 else '❌ 不通过'}")
        
        return results
    
    def analyze_match(self, match_id: str, observer_company: int, filter_condition: int):
        """分析比赛"""
        print("=" * 60)
        print("🎯 比赛筛选分析工具")
        print("=" * 60)
        
        # 显示输入参数
        observer_names = {1: "竞彩官方", 2: "香港马会"}
        observer_name = observer_names.get(observer_company, "未知")
        
        print(f"📋 分析参数:")
        print(f"  比赛ID: {match_id}")
        print(f"  观察公司: {observer_company} ({observer_name})")
        print(f"  筛选条件: {filter_condition}")
        
        # 步骤1：抓取赔率数据
        print(f"\n{'='*40}")
        print("📊 步骤1: 检查/抓取赔率数据")
        print(f"{'='*40}")
        
        if not self.scrape_match_odds(match_id):
            print("❌ 无法获取比赛数据，分析终止")
            return
        
        # 步骤2：应用筛选条件
        print(f"\n{'='*40}")
        print("🔍 步骤2: 应用筛选条件")
        print(f"{'='*40}")
        
        if filter_condition == 1:
            result = self.apply_filter_condition_1(match_id)
            print(f"\n🎯 最终结果: 比赛 {match_id} {'✅ 符合' if result else '❌ 不符合'}筛选条件1")
            
        elif filter_condition == 2:
            result = self.apply_filter_condition_2(match_id)
            print(f"\n🎯 最终结果: 比赛 {match_id} {'✅ 符合' if result else '❌ 不符合'}筛选条件2")
            
        elif filter_condition == 3:
            results = self.apply_filter_condition_3(match_id)
            print(f"\n🎯 最终结果: 比赛 {match_id}")
            print(f"  - 筛选条件1: {'✅ 符合' if results['condition_1'] else '❌ 不符合'}")
            print(f"  - 筛选条件2: {'✅ 符合' if results['condition_2'] else '❌ 不符合'}")
            
        else:
            print(f"❌ 无效的筛选条件: {filter_condition}")
    


def main():
    """主函数"""
    print("🎯 比赛筛选分析工具")
    print("=" * 60)
    
    try:
        # 获取用户输入
        match_id = input("请输入比赛ID: ").strip()
        if not match_id:
            print("❌ 比赛ID不能为空")
            return
        
        print("\n观察公司选择:")
        print("1 - 竞彩官方")
        print("2 - 香港马会")
        observer_company = int(input("请选择观察公司 (1或2): ").strip())
        
        if observer_company not in [1, 2]:
            print("❌ 无效的观察公司选择")
            return
        
        print("\n筛选条件选择:")
        print("1 - 博彩态度2 (阈值2, 主胜, 竞猜官方)")
        print("2 - 博彩态度2 + 凯利分析2 (香港马会 + betfair)")
        print("3 - 分别使用条件1和条件2")
        filter_condition = int(input("请选择筛选条件 (1、2或3): ").strip())
        
        if filter_condition not in [1, 2, 3]:
            print("❌ 无效的筛选条件选择")
            return
        
        # 创建分析器并执行分析
        analyzer = MatchAnalyzer()
        analyzer.analyze_match(match_id, observer_company, filter_condition)
        
    except KeyboardInterrupt:
        print("\n\n👋 用户取消操作")
    except ValueError:
        print("❌ 输入格式错误，请输入有效的数字")
    except Exception as e:
        print(f"❌ 程序执行失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()

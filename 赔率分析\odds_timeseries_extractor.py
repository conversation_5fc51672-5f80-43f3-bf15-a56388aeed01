#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
赔率时间序列提取器
从数据库中提取每场比赛的赔率变化时间序列，按公司和时间维度组织数据
"""

import sqlite3
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import json
import os
from collections import defaultdict
import re

class OddsTimeseriesExtractor:
    def __init__(self, main_db_path="../odds_data.db", league_db_dir="../league_databases"):
        self.main_db_path = main_db_path
        self.league_db_dir = league_db_dir
        
    def connect_readonly(self, db_path):
        """只读方式连接数据库"""
        return sqlite3.connect(f"file:{db_path}?mode=ro", uri=True)
    
    def parse_datetime(self, date_str, time_str, match_year=2025):
        """解析日期时间字符串，转换为datetime对象"""
        try:
            # 处理日期格式 "MM-DD"
            if date_str and time_str:
                month, day = date_str.split('-')
                hour, minute = time_str.split(':')
                return datetime(match_year, int(month), int(day), int(hour), int(minute))
        except:
            pass
        return None
    
    def get_match_info(self, db_path, match_id):
        """获取比赛基本信息"""
        try:
            conn = self.connect_readonly(db_path)
            cursor = conn.cursor()
            
            # 尝试从matches表获取比赛信息
            cursor.execute("""
                SELECT match_time, match_date, home_team, away_team, 
                       home_score, away_score, match_state
                FROM matches 
                WHERE match_id = ?
            """, (match_id,))
            
            match_info = cursor.fetchone()
            conn.close()
            
            if match_info:
                return {
                    'match_time': match_info[0],
                    'match_date': match_info[1],
                    'home_team': match_info[2],
                    'away_team': match_info[3],
                    'home_score': match_info[4],
                    'away_score': match_info[5],
                    'match_state': match_info[6]
                }
        except:
            pass
        return None
    
    def extract_match_odds_timeseries(self, db_path, match_id, limit_companies=None):
        """提取单场比赛的赔率时间序列"""
        try:
            conn = self.connect_readonly(db_path)
            
            # 构建查询条件
            query = """
                SELECT company_name, date, time, 
                       home_odds, draw_odds, away_odds, 
                       return_rate, kelly_home, kelly_draw, kelly_away,
                       extraction_time
                FROM odds 
                WHERE match_id = ?
                ORDER BY date, time, company_name
            """
            
            df = pd.read_sql_query(query, conn, params=(match_id,))
            conn.close()
            
            if df.empty:
                return None
            
            # 如果指定了公司限制
            if limit_companies:
                df = df[df['company_name'].isin(limit_companies)]
                if df.empty:
                    return None
            
            # 解析时间
            df['datetime'] = df.apply(
                lambda row: self.parse_datetime(row['date'], row['time']), 
                axis=1
            )
            
            # 过滤掉无法解析时间的记录
            df = df.dropna(subset=['datetime'])
            
            if df.empty:
                return None
            
            # 按公司分组组织数据
            companies_data = {}
            for company in df['company_name'].unique():
                company_df = df[df['company_name'] == company].copy()
                company_df = company_df.sort_values('datetime')
                
                companies_data[company] = {
                    'timestamps': company_df['datetime'].tolist(),
                    'home_odds': company_df['home_odds'].tolist(),
                    'draw_odds': company_df['draw_odds'].tolist(),
                    'away_odds': company_df['away_odds'].tolist(),
                    'return_rate': company_df['return_rate'].tolist(),
                    'kelly_home': company_df['kelly_home'].tolist(),
                    'kelly_draw': company_df['kelly_draw'].tolist(),
                    'kelly_away': company_df['kelly_away'].tolist(),
                    'record_count': len(company_df)
                }
            
            # 获取比赛信息
            match_info = self.get_match_info(db_path, match_id)
            
            return {
                'match_id': match_id,
                'match_info': match_info,
                'companies_data': companies_data,
                'total_records': len(df),
                'companies_count': len(companies_data),
                'time_span': {
                    'start': df['datetime'].min(),
                    'end': df['datetime'].max(),
                    'duration_hours': (df['datetime'].max() - df['datetime'].min()).total_seconds() / 3600
                }
            }
            
        except Exception as e:
            print(f"提取比赛 {match_id} 的赔率时间序列时出错: {e}")
            return None
    
    def extract_top_matches_timeseries(self, db_path, db_name="main", top_n=10):
        """提取赔率变化最多的前N场比赛的时间序列"""
        print(f"\n=== 提取 {db_name} 数据库中前{top_n}场比赛的赔率时间序列 ===")
        
        try:
            conn = self.connect_readonly(db_path)
            cursor = conn.cursor()
            
            # 获取赔率变化最多的比赛
            cursor.execute("""
                SELECT match_id, COUNT(*) as change_count 
                FROM odds 
                GROUP BY match_id 
                ORDER BY change_count DESC 
                LIMIT ?
            """, (top_n,))
            
            top_matches = cursor.fetchall()
            conn.close()
            
            results = {}
            
            for i, (match_id, change_count) in enumerate(top_matches, 1):
                print(f"  处理第{i}场比赛: {match_id} ({change_count}次变化)")
                
                timeseries = self.extract_match_odds_timeseries(db_path, match_id)
                if timeseries:
                    results[match_id] = timeseries
                    print(f"    成功提取: {timeseries['companies_count']}家公司, "
                          f"时间跨度{timeseries['time_span']['duration_hours']:.1f}小时")
                else:
                    print(f"    提取失败")
            
            return results
            
        except Exception as e:
            print(f"提取 {db_name} 数据库时出错: {e}")
            return {}
    
    def analyze_timeseries_patterns(self, timeseries_data):
        """分析时间序列模式"""
        print(f"\n=== 分析时间序列模式 ===")
        
        patterns = {
            'companies_activity': defaultdict(int),
            'time_spans': [],
            'change_frequencies': [],
            'odds_ranges': {
                'home': {'min': float('inf'), 'max': 0},
                'draw': {'min': float('inf'), 'max': 0},
                'away': {'min': float('inf'), 'max': 0}
            }
        }
        
        for match_id, data in timeseries_data.items():
            # 统计公司活跃度
            for company in data['companies_data'].keys():
                patterns['companies_activity'][company] += 1
            
            # 统计时间跨度
            patterns['time_spans'].append(data['time_span']['duration_hours'])
            
            # 统计变化频率
            patterns['change_frequencies'].append(data['total_records'])
            
            # 统计赔率范围
            for company, company_data in data['companies_data'].items():
                for odds_type in ['home', 'draw', 'away']:
                    odds_list = company_data[f'{odds_type}_odds']
                    if odds_list:
                        valid_odds = [x for x in odds_list if x is not None]
                        if valid_odds:
                            patterns['odds_ranges'][odds_type]['min'] = min(
                                patterns['odds_ranges'][odds_type]['min'], 
                                min(valid_odds)
                            )
                            patterns['odds_ranges'][odds_type]['max'] = max(
                                patterns['odds_ranges'][odds_type]['max'], 
                                max(valid_odds)
                            )
        
        # 输出分析结果
        print(f"  分析了 {len(timeseries_data)} 场比赛")
        print(f"  平均时间跨度: {np.mean(patterns['time_spans']):.1f} 小时")
        print(f"  平均变化次数: {np.mean(patterns['change_frequencies']):.0f} 次")
        
        print(f"  最活跃的公司:")
        sorted_companies = sorted(patterns['companies_activity'].items(), 
                                key=lambda x: x[1], reverse=True)
        for company, count in sorted_companies[:5]:
            print(f"    {company}: {count}场比赛")
        
        print(f"  赔率范围:")
        for odds_type, range_data in patterns['odds_ranges'].items():
            if range_data['min'] != float('inf'):
                print(f"    {odds_type}: {range_data['min']:.2f} - {range_data['max']:.2f}")
        
        return patterns
    
    def save_timeseries_data(self, timeseries_data, filename_prefix="odds_timeseries"):
        """保存时间序列数据"""
        # 转换为可序列化的格式
        serializable_data = {}
        for match_id, data in timeseries_data.items():
            serializable_data[match_id] = self._make_serializable(data)
        
        output_file = f"{filename_prefix}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(serializable_data, f, ensure_ascii=False, indent=2)
        
        print(f"\n时间序列数据已保存到: {output_file}")
        return output_file
    
    def _make_serializable(self, obj):
        """将对象转换为可序列化的格式"""
        if isinstance(obj, dict):
            return {k: self._make_serializable(v) for k, v in obj.items()}
        elif isinstance(obj, (list, tuple)):
            return [self._make_serializable(item) for item in obj]
        elif isinstance(obj, datetime):
            return obj.isoformat()
        elif obj is None:
            return None
        else:
            return str(obj)
    
    def run_extraction(self, top_matches_per_db=5):
        """运行完整的时间序列提取"""
        print("开始赔率时间序列提取...")
        
        all_results = {}
        
        # 处理主数据库
        if os.path.exists(self.main_db_path):
            main_results = self.extract_top_matches_timeseries(
                self.main_db_path, "主数据库", top_matches_per_db
            )
            if main_results:
                all_results['main'] = main_results
                self.analyze_timeseries_patterns(main_results)
        
        # 处理联赛分库
        if os.path.exists(self.league_db_dir):
            for file in os.listdir(self.league_db_dir):
                if file.endswith('.db'):
                    league_name = file[:-3]
                    db_path = os.path.join(self.league_db_dir, file)
                    
                    league_results = self.extract_top_matches_timeseries(
                        db_path, f"联赛-{league_name}", min(top_matches_per_db, 2)
                    )
                    if league_results:
                        all_results[f'league_{league_name}'] = league_results
        
        # 保存结果
        if all_results:
            self.save_timeseries_data(all_results)
        
        return all_results

def main():
    extractor = OddsTimeseriesExtractor()
    results = extractor.run_extraction(top_matches_per_db=1)  # 先提取少量数据测试
    
    print(f"\n=== 提取完成 ===")
    total_matches = sum(len(db_results) for db_results in results.values())
    print(f"总共提取了 {total_matches} 场比赛的时间序列数据")

if __name__ == "__main__":
    main()

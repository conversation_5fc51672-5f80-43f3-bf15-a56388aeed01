# 足球赔率数据抓取分析系统

一个完整的足球比赛数据抓取和分析系统，支持从win007网站抓取比赛信息、赔率数据，并提供图形界面和时间线分析功能。

## 🎯 功能特点

- 🖥️ **图形界面**：提供完整的GUI界面，操作简单直观
- 🏈 **比赛信息抓取**：自动提取比赛时间、队伍、比分、联赛信息等
- 📊 **赔率数据抓取**：支持多家博彩公司的历史赔率数据
- 🔄 **多轮次抓取**：自动发现并抓取联赛所有轮次的比赛数据
- 🔍 **智能筛选**：多条件比赛筛选，快速查找目标数据 ⭐
- 💰 **投资回测**：多策略投注回测分析，评估盈利能力 ⭐
- 📈 **时间线分析**：生成博彩公司开盘时间线图表
- 💾 **数据库存储**：SQLite数据库管理，支持数据导入导出
- 📱 **用户友好**：实时进度显示、详细日志、错误处理

## 📁 项目结构

```
win007_odds_analysis/
├── 🎯 主程序
│   ├── odds_scraper_ui.py          # 图形界面主程序 ⭐
│   ├── playground.py               # 联赛比赛抓取工具 ⭐
│   └── start_ui.bat               # Windows启动脚本
│
├── 🔧 核心模块
│   ├── enhanced_odds_scraper.py    # 增强版数据抓取器
│   ├── match_info_extractor.py     # 比赛信息提取器
│   ├── database.py                 # 数据库管理模块
│   ├── config.py                   # 配置文件
│   ├── timeline_chart.py           # 时间线图表模块
│   └── dynamic_timeline_chart.py   # 动态时间线图表
│
├── 📋 测试文件
│   ├── test_database.py            # 数据库测试
│   └── test_multi_round.py         # 多轮次功能测试
│
├── 📚 文档
│   ├── README.md                   # 主要使用说明
│   ├── UI使用说明.md               # 界面操作指南
│   ├── 项目总结.md                 # 技术实现总结
│   └── 项目完成报告.md             # 项目完成报告
│
└── 📦 配置
    ├── requirements.txt            # 依赖包列表
    └── odds_data.db               # 主数据库文件
```

## 🚀 快速开始

### 安装依赖

```bash
pip install -r requirements.txt
```

### 启动方式

#### 方法1：图形界面（推荐）⭐

```bash
# Windows用户
start_ui.bat

# 或者直接运行
python odds_scraper_ui.py
```

**图形界面功能：**
- 输入比赛ID，一键抓取完整数据
- 实时显示抓取进度和状态
- 比赛列表管理和查看
- 智能比赛筛选和查找 ⭐
- 多策略投资回测分析 ⭐
- 比赛详情和赔率数据展示
- 生成时间线图表分析
- 数据库统计信息
- 数据导出功能

#### 方法2：联赛比赛抓取工具

```bash
# 运行多轮次抓取工具
python playground.py
```

**支持的抓取模式：**
1. 单轮次抓取 - 仅抓取指定URL的轮次
2. 多轮次抓取 - 自动发现并抓取所有轮次 ⭐
3. 指定轮次范围抓取
4. 调试页面结构
5. 多轮次功能演示

#### 方法3：命令行工具

```bash
# 抓取完整数据（比赛信息+赔率）
python enhanced_odds_scraper.py 2741454

# 限制抓取5家公司，间隔2秒
python enhanced_odds_scraper.py 2741454 --max-companies 5 --delay 2.0

# 只提取比赛信息
python match_info_extractor.py 2741454
```

## 📊 数据格式

### 比赛信息字段
- `match_id`: 比赛ID
- `league`: 联赛名称
- `round`: 轮次信息
- `home_team`/`away_team`: 主客队名称
- `match_time`: 比赛开始时间
- `home_score`/`away_score`: 比分
- `match_state`: 比赛状态
- `weather`/`temperature`: 天气信息

### 赔率数据字段
- `company_name`: 博彩公司名称
- `company_id`: 博彩公司ID
- `date`: 日期 (MM-DD格式)
- `time`: 时间 (HH:MM格式)
- `home_odds`: 主胜赔率
- `draw_odds`: 平局赔率
- `away_odds`: 客胜赔率
- `return_rate`: 返还率
- `kelly_home`: 主胜凯利指数
- `kelly_draw`: 平局凯利指数
- `kelly_away`: 客胜凯利指数

## 🔧 主要功能

### 1. 单场比赛数据抓取
- 输入比赛ID（如：2741454）
- 自动抓取比赛信息和所有博彩公司赔率数据
- 生成时间线图表显示各公司开盘时间

### 2. 联赛多轮次抓取
- 自动发现联赛可用轮次
- 批量抓取整个赛季的比赛数据
- 按轮次分组显示和统计

### 3. 时间线分析
- 静态时间线：显示各公司开盘时间点
- 动态时间线：交互式图表，支持缩放和拖拽
- 开盘时间相对于比赛开始时间的分布

### 4. 智能筛选 ⭐
- 联赛筛选：按联赛名称筛选比赛
- 赛季筛选：按赛季年份筛选比赛
- 时间范围筛选：按比赛时间范围筛选
- 比赛状态筛选：按比赛状态筛选
- 队伍筛选：按队伍名称模糊搜索
- 比分筛选：按比分情况筛选
- 凯利分析筛选：基于凯利指数和返还率的高级统计分析 ⭐
- 凯利分析2筛选：基于凯利指数和博彩公司分布的精细化分析 ⭐
- 多条件组合：支持同时启用多个筛选条件

### 5. 投资回测 ⭐
- 主胜策略：按主胜赔率范围投注
- 平局策略：按平局赔率范围投注
- 客胜策略：按客胜赔率范围投注
- 凯利指数策略：基于凯利指数进行投注决策
- 低赔率策略：投注低风险的低赔率选项
- 高赔率策略：投注高风险高回报的高赔率选项
- 资金管理：支持固定金额和固定比例模式
- 结果分析：详细的回测统计和报告导出

### 6. 数据管理
- SQLite数据库存储
- 数据导入导出（CSV/JSON格式）
- 比赛列表管理
- 数据库统计信息

## 🎯 使用示例

### 示例1：抓取单场比赛
1. 启动图形界面：`python odds_scraper_ui.py`
2. 输入比赛ID：`2741454`
3. 点击"开始抓取"
4. 查看结果和时间线图表

### 示例2：抓取联赛所有轮次
1. 运行：`python playground.py`
2. 选择模式2（多轮次抓取）
3. 自动发现并抓取所有可用轮次
4. 查看按轮次分组的结果

### 示例3：命令行快速抓取
```bash
# 抓取比赛2741454的完整数据
python enhanced_odds_scraper.py 2741454 --max-companies 10 --delay 2.0
```

## 📈 技术特点

- **智能解析**：使用BeautifulSoup精确解析HTML结构
- **反爬虫策略**：合理的请求头、延迟控制、错误重试
- **数据验证**：确保抓取数据的准确性和完整性
- **多线程处理**：UI界面支持异步操作，不阻塞界面
- **错误处理**：完善的异常处理和日志记录

## 🛠️ 依赖包

- requests: HTTP请求
- beautifulsoup4: HTML解析
- pandas: 数据处理
- matplotlib: 图表绘制
- tkinter: 图形界面（Python内置）

## 📞 技术支持

如有问题，请参考：
- `UI使用说明.md` - 界面操作指南
- `比赛筛选功能使用指南.md` - 筛选功能详细说明 ⭐
- `凯利分析筛选功能使用指南.md` - 凯利分析功能详细说明 ⭐
- `凯利分析2筛选功能使用指南.md` - 凯利分析2功能详细说明 ⭐
- `投资回测功能使用指南.md` - 回测功能详细说明 ⭐
- `项目总结.md` - 技术实现细节
- `项目完成报告.md` - 项目完成报告

---

**版本**: v4.0
**状态**: 已完成并测试通过 ✅
**最新更新**: 2025年6月28日 - 新增投资回测功能 ⭐
**上次更新**: 2025年6月28日 - 新增智能比赛筛选功能
**创建时间**: 2025年6月3日

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
联赛选择对话框
用于选择要抓取的联赛
"""

import tkinter as tk
from tkinter import ttk
from typing import Dict, List, Optional

class LeagueSelectionDialog:
    def __init__(self, parent, matches_by_league: Dict[str, List[Dict]]):
        self.parent = parent
        self.matches_by_league = matches_by_league
        self.selected_leagues = []
        self.result = None
        
        # 创建对话框窗口
        self.dialog = tk.Toplevel(parent)
        self.dialog.title("选择要抓取的联赛")
        self.dialog.geometry("600x500")
        self.dialog.resizable(True, True)
        
        # 设置为模态对话框
        self.dialog.transient(parent)
        self.dialog.grab_set()
        
        # 居中显示
        self.center_window()
        
        # 创建界面
        self.create_widgets()
        
        # 绑定关闭事件
        self.dialog.protocol("WM_DELETE_WINDOW", self.on_cancel)

    def center_window(self):
        """将对话框居中显示"""
        self.dialog.update_idletasks()
        
        # 获取对话框尺寸
        dialog_width = self.dialog.winfo_width()
        dialog_height = self.dialog.winfo_height()
        
        # 获取父窗口位置和尺寸
        parent_x = self.parent.winfo_rootx()
        parent_y = self.parent.winfo_rooty()
        parent_width = self.parent.winfo_width()
        parent_height = self.parent.winfo_height()
        
        # 计算居中位置
        x = parent_x + (parent_width - dialog_width) // 2
        y = parent_y + (parent_height - dialog_height) // 2
        
        self.dialog.geometry(f"{dialog_width}x{dialog_height}+{x}+{y}")

    def create_widgets(self):
        """创建界面控件"""
        
        # 标题和说明
        title_frame = ttk.Frame(self.dialog)
        title_frame.pack(fill=tk.X, padx=10, pady=(10, 5))
        
        title_label = ttk.Label(title_frame, text="选择要抓取的联赛", font=('Arial', 12, 'bold'))
        title_label.pack(anchor=tk.W)
        
        total_matches = sum(len(matches) for matches in self.matches_by_league.values())
        info_label = ttk.Label(title_frame, 
                              text=f"共发现 {len(self.matches_by_league)} 个联赛，{total_matches} 场比赛")
        info_label.pack(anchor=tk.W, pady=(5, 0))
        
        # 操作按钮区域
        button_frame = ttk.Frame(self.dialog)
        button_frame.pack(fill=tk.X, padx=10, pady=(5, 10))
        
        ttk.Button(button_frame, text="全选", command=self.select_all).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(button_frame, text="全不选", command=self.select_none).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(button_frame, text="反选", command=self.invert_selection).pack(side=tk.LEFT)
        
        # 联赛列表区域
        list_frame = ttk.LabelFrame(self.dialog, text="联赛列表", padding="5")
        list_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=(0, 10))
        
        # 创建滚动区域
        canvas = tk.Canvas(list_frame)
        scrollbar = ttk.Scrollbar(list_frame, orient="vertical", command=canvas.yview)
        scrollable_frame = ttk.Frame(canvas)
        
        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )
        
        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)
        
        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")
        
        # 创建联赛复选框
        self.league_vars = {}
        
        # 按比赛数量排序联赛
        sorted_leagues = sorted(self.matches_by_league.items(), 
                               key=lambda x: len(x[1]), reverse=True)
        
        for i, (league, matches) in enumerate(sorted_leagues):
            var = tk.BooleanVar()
            self.league_vars[league] = var
            
            # 创建复选框框架
            checkbox_frame = ttk.Frame(scrollable_frame)
            checkbox_frame.pack(fill=tk.X, pady=2)
            
            # 复选框
            checkbox = ttk.Checkbutton(
                checkbox_frame, 
                text=f"{league} ({len(matches)} 场)",
                variable=var
            )
            checkbox.pack(side=tk.LEFT)
            
            # 显示前几场比赛作为预览
            if matches:
                preview_matches = matches[:3]  # 显示前3场
                preview_text = ", ".join([
                    f"{match['home_team']} vs {match['away_team']}"
                    for match in preview_matches
                ])
                if len(matches) > 3:
                    preview_text += f" ..."
                
                preview_label = ttk.Label(
                    checkbox_frame, 
                    text=f"  ({preview_text})",
                    foreground="gray"
                )
                preview_label.pack(side=tk.LEFT)
        
        # 底部按钮区域
        bottom_frame = ttk.Frame(self.dialog)
        bottom_frame.pack(fill=tk.X, padx=10, pady=(0, 10))
        
        # 选择统计
        self.selection_label = ttk.Label(bottom_frame, text="已选择: 0 个联赛，0 场比赛")
        self.selection_label.pack(anchor=tk.W, pady=(0, 10))
        
        # 确定和取消按钮
        button_container = ttk.Frame(bottom_frame)
        button_container.pack(anchor=tk.E)
        
        ttk.Button(button_container, text="确定", command=self.on_confirm).pack(side=tk.RIGHT, padx=(5, 0))
        ttk.Button(button_container, text="取消", command=self.on_cancel).pack(side=tk.RIGHT)
        
        # 绑定变量变化事件
        for var in self.league_vars.values():
            var.trace('w', self.update_selection_info)
        
        # 初始更新选择信息
        self.update_selection_info()
        
        # 绑定鼠标滚轮事件
        def _on_mousewheel(event):
            canvas.yview_scroll(int(-1*(event.delta/120)), "units")
        
        canvas.bind("<MouseWheel>", _on_mousewheel)

    def select_all(self):
        """全选所有联赛"""
        for var in self.league_vars.values():
            var.set(True)

    def select_none(self):
        """取消选择所有联赛"""
        for var in self.league_vars.values():
            var.set(False)

    def invert_selection(self):
        """反选"""
        for var in self.league_vars.values():
            var.set(not var.get())

    def update_selection_info(self, *args):
        """更新选择统计信息"""
        selected_leagues = []
        total_matches = 0
        
        for league, var in self.league_vars.items():
            if var.get():
                selected_leagues.append(league)
                total_matches += len(self.matches_by_league[league])
        
        self.selection_label.config(
            text=f"已选择: {len(selected_leagues)} 个联赛，{total_matches} 场比赛"
        )

    def on_confirm(self):
        """确认选择"""
        selected_leagues = []
        selected_matches = []
        
        for league, var in self.league_vars.items():
            if var.get():
                selected_leagues.append(league)
                selected_matches.extend(self.matches_by_league[league])
        
        if not selected_leagues:
            tk.messagebox.showwarning("警告", "请至少选择一个联赛")
            return
        
        self.result = {
            'leagues': selected_leagues,
            'matches': selected_matches,
            'total_matches': len(selected_matches)
        }
        
        self.dialog.destroy()

    def on_cancel(self):
        """取消选择"""
        self.result = None
        self.dialog.destroy()

    def show(self) -> Optional[Dict]:
        """显示对话框并返回结果"""
        # 等待对话框关闭
        self.dialog.wait_window()
        return self.result


def show_league_selection_dialog(parent, matches_by_league: Dict[str, List[Dict]]) -> Optional[Dict]:
    """
    显示联赛选择对话框
    
    Args:
        parent: 父窗口
        matches_by_league: 按联赛分组的比赛信息
        
    Returns:
        Dict 或 None: 选择结果
        {
            'leagues': ['英超', '西甲', ...],
            'matches': [match1, match2, ...],
            'total_matches': 25
        }
    """
    dialog = LeagueSelectionDialog(parent, matches_by_league)
    return dialog.show()


if __name__ == "__main__":
    # 测试代码
    root = tk.Tk()
    root.withdraw()  # 隐藏主窗口
    
    # 模拟数据
    test_matches = {
        "英超": [
            {"match_id": "1", "home_team": "曼城", "away_team": "利物浦", "match_time": "2025-07-22 20:00:00"},
            {"match_id": "2", "home_team": "切尔西", "away_team": "阿森纳", "match_time": "2025-07-22 22:30:00"},
        ],
        "西甲": [
            {"match_id": "3", "home_team": "巴萨", "away_team": "皇马", "match_time": "2025-07-22 21:00:00"},
        ],
        "德甲": [
            {"match_id": "4", "home_team": "拜仁", "away_team": "多特", "match_time": "2025-07-22 19:30:00"},
            {"match_id": "5", "home_team": "莱比锡", "away_team": "勒沃库森", "match_time": "2025-07-22 21:30:00"},
            {"match_id": "6", "home_team": "法兰克福", "away_team": "门兴", "match_time": "2025-07-22 23:00:00"},
        ]
    }
    
    result = show_league_selection_dialog(root, test_matches)
    
    if result:
        print("选择结果:")
        print(f"联赛: {result['leagues']}")
        print(f"比赛数: {result['total_matches']}")
        print("比赛列表:")
        for match in result['matches'][:5]:  # 显示前5场
            print(f"  {match['match_id']}: {match['home_team']} vs {match['away_team']}")
    else:
        print("用户取消了选择")
    
    root.destroy()

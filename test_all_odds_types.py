#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试博彩态度筛选的三种赔率类型：主胜、平局、客胜
"""

import sqlite3
from datetime import datetime

def get_other_companies_odds_at_time(cursor, match_id, target_company, target_date, target_time, odds_type):
    """获取其他公司在指定时间点的赔率"""
    try:
        # 构造目标时间点
        target_datetime_str = f"2025-{target_date} {target_time}:00"
        target_datetime = datetime.strptime(target_datetime_str, "%Y-%m-%d %H:%M:%S")
        
        # 获取所有其他公司的赔率数据
        cursor.execute(f'''
            SELECT company_name, date, time, {odds_type}
            FROM odds
            WHERE match_id = ? AND company_name != ? AND {odds_type} IS NOT NULL
            ORDER BY company_name, date ASC, time ASC
        ''', (match_id, target_company))
        
        all_other_odds = cursor.fetchall()
        
        # 按公司分组
        company_odds = {}
        for record in all_other_odds:
            company_name = record['company_name']
            if company_name not in company_odds:
                company_odds[company_name] = []
            
            try:
                # 构造该记录的时间
                record_datetime_str = f"2025-{record['date']} {record['time']}:00"
                record_datetime = datetime.strptime(record_datetime_str, "%Y-%m-%d %H:%M:%S")
                
                company_odds[company_name].append({
                    'datetime': record_datetime,
                    'odds': float(record[odds_type])
                })
            except ValueError:
                continue
        
        # 对每个公司找到在目标时间点的赔率
        result_odds = []
        
        for company_name, odds_list in company_odds.items():
            # 按时间排序
            odds_list.sort(key=lambda x: x['datetime'])
            
            # 找到该公司在目标时间点的赔率
            target_odds_value = None
            
            # 首先检查是否在目标时间点有数据
            for odds_record in odds_list:
                if odds_record['datetime'] == target_datetime:
                    target_odds_value = odds_record['odds']
                    break
            
            # 如果目标时间点没有数据，找最近的之前时间点
            if target_odds_value is None:
                latest_before_target = None
                for odds_record in odds_list:
                    if odds_record['datetime'] < target_datetime:
                        latest_before_target = odds_record
                    else:
                        break  # 已经超过目标时间点
                
                if latest_before_target:
                    target_odds_value = latest_before_target['odds']
            
            # 如果该公司在目标时间点或之前有数据，添加其赔率
            if target_odds_value is not None:
                result_odds.append({
                    'company_name': company_name,
                    odds_type: target_odds_value
                })
        
        return result_odds
        
    except Exception as e:
        print(f"获取其他公司赔率失败: {e}")
        return []

def test_odds_type(match_id, target_company, odds_type, odds_type_name, threshold):
    """测试特定赔率类型的博彩态度筛选"""
    print(f"\n🔍 测试 {odds_type_name} 赔率类型")
    print("=" * 50)
    
    try:
        with sqlite3.connect("odds_data.db") as conn:
            conn.row_factory = sqlite3.Row
            cursor = conn.cursor()
            
            # 获取目标公司的开盘数据
            cursor.execute(f'''
                SELECT date, time, {odds_type}
                FROM odds
                WHERE match_id = ? AND company_name = ? AND {odds_type} IS NOT NULL
                ORDER BY date ASC, time ASC
                LIMIT 1
            ''', (match_id, target_company))
            
            target_record = cursor.fetchone()
            if not target_record:
                print(f"❌ 没有找到 {target_company} 的 {odds_type_name} 数据")
                return False
            
            target_date = target_record['date']
            target_time = target_record['time']
            target_odds = float(target_record[odds_type])
            
            print(f"📊 {target_company} 开盘数据:")
            print(f"  开盘时间: {target_date} {target_time}")
            print(f"  {odds_type_name}赔率: {target_odds}")
            
            # 获取其他公司在该时间点的赔率
            other_companies_odds = get_other_companies_odds_at_time(
                cursor, match_id, target_company, target_date, target_time, odds_type
            )
            
            if len(other_companies_odds) == 0:
                print(f"❌ 没有其他公司在该时间点或之前开盘")
                return False
            
            print(f"\n🏢 其他公司 ({len(other_companies_odds)} 家):")
            other_odds_values = []
            for i, record in enumerate(other_companies_odds):
                odds_value = float(record[odds_type])
                other_odds_values.append(odds_value)
                print(f"  {i+1:2d}. {record['company_name']:12s}: {odds_value}")
            
            # 计算结果
            avg_other_odds = sum(other_odds_values) / len(other_odds_values)
            ratio = target_odds / avg_other_odds
            
            print(f"\n🧮 计算结果:")
            print(f"  {target_company} {odds_type_name}赔率: {target_odds}")
            print(f"  其他公司平均: {avg_other_odds:.6f}")
            print(f"  比率: {target_odds} ÷ {avg_other_odds:.6f} = {ratio:.6f}")
            print(f"  阈值: {threshold}")
            print(f"  条件: {ratio:.6f} >= {threshold} = {ratio >= threshold}")
            
            if ratio >= threshold:
                print(f"  ✅ 满足博彩态度条件")
                return True
            else:
                print(f"  ❌ 不满足博彩态度条件")
                return False
                
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def main():
    """主函数：测试所有三种赔率类型"""
    print("🎯 测试博彩态度筛选 - 所有赔率类型")
    print("=" * 60)
    
    # 测试参数
    match_id = "2511950"
    target_company = "澳门"
    threshold = 1.05
    
    print(f"比赛ID: {match_id}")
    print(f"目标公司: {target_company}")
    print(f"阈值: {threshold}")
    
    # 测试三种赔率类型
    odds_types = [
        ("home_odds", "主胜"),
        ("draw_odds", "平局"),
        ("away_odds", "客胜")
    ]
    
    results = {}
    for odds_type, odds_type_name in odds_types:
        results[odds_type_name] = test_odds_type(
            match_id, target_company, odds_type, odds_type_name, threshold
        )
    
    # 总结结果
    print(f"\n📋 测试结果总结:")
    print("=" * 30)
    for odds_type_name, result in results.items():
        status = "✅ 通过" if result else "❌ 不通过"
        print(f"  {odds_type_name:6s}: {status}")

if __name__ == "__main__":
    main()

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试博彩态度2筛选功能
"""

import sqlite3
from datetime import datetime

def get_other_companies_odds_at_time(cursor, match_id, target_company, target_date, target_time, odds_type):
    """获取其他公司在指定时间点的赔率"""
    try:
        # 构造目标时间点
        target_datetime_str = f"2025-{target_date} {target_time}:00"
        target_datetime = datetime.strptime(target_datetime_str, "%Y-%m-%d %H:%M:%S")
        
        # 获取所有其他公司的赔率数据
        cursor.execute(f'''
            SELECT company_name, date, time, {odds_type}
            FROM odds
            WHERE match_id = ? AND company_name != ? AND {odds_type} IS NOT NULL
            ORDER BY company_name, date ASC, time ASC
        ''', (match_id, target_company))
        
        all_other_odds = cursor.fetchall()
        
        # 按公司分组
        company_odds = {}
        for record in all_other_odds:
            company_name = record['company_name']
            if company_name not in company_odds:
                company_odds[company_name] = []
            
            try:
                # 构造该记录的时间
                record_datetime_str = f"2025-{record['date']} {record['time']}:00"
                record_datetime = datetime.strptime(record_datetime_str, "%Y-%m-%d %H:%M:%S")
                
                company_odds[company_name].append({
                    'datetime': record_datetime,
                    'odds': float(record[odds_type])
                })
            except ValueError:
                continue
        
        # 对每个公司找到在目标时间点的赔率
        result_odds = []
        
        for company_name, odds_list in company_odds.items():
            # 按时间排序
            odds_list.sort(key=lambda x: x['datetime'])
            
            # 找到该公司在目标时间点的赔率
            target_odds_value = None
            
            # 首先检查是否在目标时间点有数据
            for odds_record in odds_list:
                if odds_record['datetime'] == target_datetime:
                    target_odds_value = odds_record['odds']
                    break
            
            # 如果目标时间点没有数据，找最近的之前时间点
            if target_odds_value is None:
                latest_before_target = None
                for odds_record in odds_list:
                    if odds_record['datetime'] < target_datetime:
                        latest_before_target = odds_record
                    else:
                        break  # 已经超过目标时间点
                
                if latest_before_target:
                    target_odds_value = latest_before_target['odds']
            
            # 如果该公司在目标时间点或之前有数据，添加其赔率
            if target_odds_value is not None:
                result_odds.append({
                    'company_name': company_name,
                    odds_type: target_odds_value
                })
        
        return result_odds
        
    except Exception as e:
        print(f"获取其他公司赔率失败: {e}")
        return []

def analyze_match_betting_attitude2(match_id, odds_type, threshold, target_company):
    """分析单场比赛的博彩态度2"""
    try:
        with sqlite3.connect("odds_data.db") as conn:
            conn.row_factory = sqlite3.Row
            cursor = conn.cursor()

            # 获取目标公司的开盘时间和赔率
            cursor.execute(f'''
                SELECT date, time, {odds_type}
                FROM odds
                WHERE match_id = ? AND company_name = ? AND {odds_type} IS NOT NULL
                ORDER BY date ASC, time ASC
                LIMIT 1
            ''', (match_id, target_company))

            target_odds_record = cursor.fetchone()
            if not target_odds_record:
                return False

            target_date = target_odds_record['date']
            target_time = target_odds_record['time']
            target_odds = float(target_odds_record[odds_type])

            # 获取该时间点所有其他公司的赔率
            other_companies_odds = get_other_companies_odds_at_time(
                cursor, match_id, target_company, target_date, target_time, odds_type
            )

            if len(other_companies_odds) == 0:
                return False

            # 统计其他公司中赔率 >= 目标公司赔率的数量
            higher_or_equal_count = 0
            for record in other_companies_odds:
                other_odds = float(record[odds_type])
                if other_odds >= target_odds:
                    higher_or_equal_count += 1

            # 检查是否满足阈值条件
            return higher_or_equal_count < threshold

    except Exception as e:
        print(f"分析比赛 {match_id} 失败: {e}")
        return False

def apply_betting_attitude2_filter(odds_type, threshold, selected_company):
    """应用博彩态度2筛选"""
    try:
        qualified_matches = []

        # 获取所有有赔率数据的比赛
        with sqlite3.connect("odds_data.db") as conn:
            conn.row_factory = sqlite3.Row
            cursor = conn.cursor()

            # 获取所有比赛ID
            cursor.execute(f'SELECT DISTINCT match_id FROM odds WHERE {odds_type} IS NOT NULL')
            match_ids = [row[0] for row in cursor.fetchall()]

            print(f"开始博彩态度2筛选，共 {len(match_ids)} 场比赛")

            # 对每场比赛进行博彩态度2分析
            for i, match_id in enumerate(match_ids):
                if i % 100 == 0:
                    print(f"处理进度: {i}/{len(match_ids)}")
                
                if analyze_match_betting_attitude2(match_id, odds_type, threshold, selected_company):
                    qualified_matches.append(match_id)

            print(f"博彩态度2筛选完成，符合条件的比赛: {len(qualified_matches)} 场")

        return qualified_matches

    except Exception as e:
        print(f"博彩态度2筛选失败: {e}")
        return []

def main():
    """测试不同阈值的筛选结果"""
    print("🔍 调试博彩态度2筛选功能")
    print("=" * 60)
    
    # 测试参数
    target_company = "澳门"
    odds_type = "home_odds"
    
    print(f"目标公司: {target_company}")
    print(f"赔率类型: 主胜")
    
    # 测试不同阈值
    thresholds = [0, 5, 10, 15]
    
    for threshold in thresholds:
        print(f"\n{'='*40}")
        print(f"🎚️ 测试阈值: {threshold}")
        
        qualified_matches = apply_betting_attitude2_filter(odds_type, threshold, target_company)
        
        print(f"📊 结果: {len(qualified_matches)} 场比赛符合条件")
        
        if len(qualified_matches) > 0:
            print(f"前5场比赛ID: {qualified_matches[:5]}")

if __name__ == "__main__":
    main()

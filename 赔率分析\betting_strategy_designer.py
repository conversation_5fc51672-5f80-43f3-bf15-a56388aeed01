#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
投注策略设计
基于预测模型设计多种投注策略：置信度策略、价值投注策略、套利策略等
"""

import pandas as pd
import numpy as np
import json
import os
from datetime import datetime
import pickle
from abc import ABC, abstractmethod

import warnings
warnings.filterwarnings('ignore')

class BettingStrategy(ABC):
    """投注策略基类"""
    
    def __init__(self, name, description):
        self.name = name
        self.description = description
        self.parameters = {}
    
    @abstractmethod
    def should_bet(self, prediction_result, odds_data, match_info):
        """判断是否应该投注"""
        pass
    
    @abstractmethod
    def calculate_stake(self, prediction_result, odds_data, match_info, bankroll):
        """计算投注金额"""
        pass
    
    def get_bet_decision(self, prediction_result, odds_data, match_info, bankroll):
        """获取投注决策"""
        if self.should_bet(prediction_result, odds_data, match_info):
            stake = self.calculate_stake(prediction_result, odds_data, match_info, bankroll)
            return {
                'should_bet': True,
                'outcome': prediction_result['predicted_outcome'],
                'stake': stake,
                'confidence': prediction_result['confidence'],
                'strategy': self.name
            }
        else:
            return {
                'should_bet': False,
                'strategy': self.name
            }

class ConfidenceStrategy(BettingStrategy):
    """置信度策略：基于模型置信度进行投注"""
    
    def __init__(self, min_confidence=0.7, max_stake_ratio=0.05):
        super().__init__(
            "Confidence Strategy",
            "基于模型预测置信度进行投注，置信度越高投注越多"
        )
        self.parameters = {
            'min_confidence': min_confidence,
            'max_stake_ratio': max_stake_ratio
        }
    
    def should_bet(self, prediction_result, odds_data, match_info):
        """置信度超过阈值时投注"""
        return prediction_result['confidence'] >= self.parameters['min_confidence']
    
    def calculate_stake(self, prediction_result, odds_data, match_info, bankroll):
        """根据置信度计算投注金额"""
        confidence = prediction_result['confidence']
        min_conf = self.parameters['min_confidence']
        max_ratio = self.parameters['max_stake_ratio']
        
        # 线性映射：置信度越高，投注比例越大
        stake_ratio = max_ratio * (confidence - min_conf) / (1 - min_conf)
        return bankroll * stake_ratio

class ValueBettingStrategy(BettingStrategy):
    """价值投注策略：寻找赔率高估的机会"""
    
    def __init__(self, min_value=0.05, max_stake_ratio=0.03):
        super().__init__(
            "Value Betting Strategy", 
            "寻找市场赔率高估真实概率的价值投注机会"
        )
        self.parameters = {
            'min_value': min_value,
            'max_stake_ratio': max_stake_ratio
        }
    
    def should_bet(self, prediction_result, odds_data, match_info):
        """计算期望价值，正值时投注"""
        predicted_outcome = prediction_result['predicted_outcome']
        model_prob = prediction_result['probabilities'][predicted_outcome]
        
        # 获取对应的赔率
        if predicted_outcome == 'home_win':
            market_odds = odds_data.get('closing_home', odds_data.get('opening_home', 2.0))
        elif predicted_outcome == 'draw':
            market_odds = odds_data.get('closing_draw', odds_data.get('opening_draw', 3.0))
        else:  # away_win
            market_odds = odds_data.get('closing_away', odds_data.get('opening_away', 3.0))
        
        # 计算期望价值 = 模型概率 * 赔率 - 1
        expected_value = model_prob * market_odds - 1
        
        return expected_value >= self.parameters['min_value']
    
    def calculate_stake(self, prediction_result, odds_data, match_info, bankroll):
        """使用凯利公式计算最优投注金额"""
        predicted_outcome = prediction_result['predicted_outcome']
        model_prob = prediction_result['probabilities'][predicted_outcome]
        
        # 获取赔率
        if predicted_outcome == 'home_win':
            market_odds = odds_data.get('closing_home', odds_data.get('opening_home', 2.0))
        elif predicted_outcome == 'draw':
            market_odds = odds_data.get('closing_draw', odds_data.get('opening_draw', 3.0))
        else:
            market_odds = odds_data.get('closing_away', odds_data.get('opening_away', 3.0))
        
        # 凯利公式：f = (bp - q) / b
        # b = 赔率-1, p = 胜率, q = 败率
        b = market_odds - 1
        p = model_prob
        q = 1 - p
        
        kelly_fraction = (b * p - q) / b
        
        # 限制最大投注比例
        max_ratio = self.parameters['max_stake_ratio']
        stake_ratio = min(kelly_fraction, max_ratio)
        stake_ratio = max(stake_ratio, 0)  # 确保非负
        
        return bankroll * stake_ratio

class ArbitrageStrategy(BettingStrategy):
    """套利策略：寻找不同公司间的套利机会"""
    
    def __init__(self, min_arbitrage=0.02):
        super().__init__(
            "Arbitrage Strategy",
            "寻找不同博彩公司间的套利机会"
        )
        self.parameters = {
            'min_arbitrage': min_arbitrage
        }
    
    def should_bet(self, prediction_result, odds_data, match_info):
        """检查是否存在套利机会"""
        # 这里简化处理，实际需要多个公司的赔率数据
        # 暂时基于单一赔率数据判断
        return False  # 需要多公司数据才能实现真正的套利
    
    def calculate_stake(self, prediction_result, odds_data, match_info, bankroll):
        """计算套利投注金额"""
        return 0  # 暂不实现

class ConservativeStrategy(BettingStrategy):
    """保守策略：只在高置信度且低风险时投注"""
    
    def __init__(self, min_confidence=0.85, max_stake_ratio=0.02):
        super().__init__(
            "Conservative Strategy",
            "保守投注策略，只在极高置信度时小额投注"
        )
        self.parameters = {
            'min_confidence': min_confidence,
            'max_stake_ratio': max_stake_ratio
        }
    
    def should_bet(self, prediction_result, odds_data, match_info):
        """只在极高置信度时投注"""
        return prediction_result['confidence'] >= self.parameters['min_confidence']
    
    def calculate_stake(self, prediction_result, odds_data, match_info, bankroll):
        """固定小比例投注"""
        return bankroll * self.parameters['max_stake_ratio']

class AggressiveStrategy(BettingStrategy):
    """激进策略：较低置信度阈值，较高投注比例"""
    
    def __init__(self, min_confidence=0.6, max_stake_ratio=0.08):
        super().__init__(
            "Aggressive Strategy",
            "激进投注策略，较低阈值但较高投注比例"
        )
        self.parameters = {
            'min_confidence': min_confidence,
            'max_stake_ratio': max_stake_ratio
        }
    
    def should_bet(self, prediction_result, odds_data, match_info):
        """较低置信度阈值"""
        return prediction_result['confidence'] >= self.parameters['min_confidence']
    
    def calculate_stake(self, prediction_result, odds_data, match_info, bankroll):
        """根据置信度动态调整投注金额"""
        confidence = prediction_result['confidence']
        min_conf = self.parameters['min_confidence']
        max_ratio = self.parameters['max_stake_ratio']
        
        # 非线性映射：置信度越高，投注比例增长越快
        normalized_conf = (confidence - min_conf) / (1 - min_conf)
        stake_ratio = max_ratio * (normalized_conf ** 2)
        
        return bankroll * stake_ratio

class BettingStrategyDesigner:
    """投注策略设计器"""
    
    def __init__(self):
        self.strategies = {}
        self.initialize_strategies()
    
    def initialize_strategies(self):
        """初始化所有策略"""
        print("=== 初始化投注策略 ===")
        
        self.strategies = {
            'confidence': ConfidenceStrategy(min_confidence=0.7, max_stake_ratio=0.05),
            'value_betting': ValueBettingStrategy(min_value=0.05, max_stake_ratio=0.03),
            'conservative': ConservativeStrategy(min_confidence=0.85, max_stake_ratio=0.02),
            'aggressive': AggressiveStrategy(min_confidence=0.6, max_stake_ratio=0.08),
            'arbitrage': ArbitrageStrategy(min_arbitrage=0.02)
        }
        
        print(f"初始化了 {len(self.strategies)} 个投注策略:")
        for name, strategy in self.strategies.items():
            print(f"  - {name}: {strategy.description}")
    
    def get_strategy(self, strategy_name):
        """获取指定策略"""
        return self.strategies.get(strategy_name)
    
    def evaluate_all_strategies(self, prediction_result, odds_data, match_info, bankroll=1000):
        """评估所有策略的投注决策"""
        decisions = {}
        
        for name, strategy in self.strategies.items():
            try:
                decision = strategy.get_bet_decision(
                    prediction_result, odds_data, match_info, bankroll
                )
                decisions[name] = decision
            except Exception as e:
                print(f"策略 {name} 评估失败: {e}")
                decisions[name] = {'should_bet': False, 'strategy': name, 'error': str(e)}
        
        return decisions
    
    def create_custom_strategy(self, strategy_config):
        """创建自定义策略"""
        strategy_type = strategy_config.get('type', 'confidence')
        parameters = strategy_config.get('parameters', {})
        
        if strategy_type == 'confidence':
            return ConfidenceStrategy(**parameters)
        elif strategy_type == 'value_betting':
            return ValueBettingStrategy(**parameters)
        elif strategy_type == 'conservative':
            return ConservativeStrategy(**parameters)
        elif strategy_type == 'aggressive':
            return AggressiveStrategy(**parameters)
        else:
            raise ValueError(f"不支持的策略类型: {strategy_type}")
    
    def optimize_strategy_parameters(self, strategy_name, parameter_ranges, historical_data):
        """优化策略参数（网格搜索）"""
        print(f"\n=== 优化策略参数: {strategy_name} ===")
        
        best_params = None
        best_score = -float('inf')
        
        # 简化的网格搜索
        param_combinations = self._generate_parameter_combinations(parameter_ranges)
        
        for params in param_combinations[:10]:  # 限制搜索次数
            try:
                # 创建策略实例
                if strategy_name == 'confidence':
                    strategy = ConfidenceStrategy(**params)
                elif strategy_name == 'value_betting':
                    strategy = ValueBettingStrategy(**params)
                else:
                    continue
                
                # 评估策略（简化评估）
                score = self._evaluate_strategy_on_data(strategy, historical_data)
                
                if score > best_score:
                    best_score = score
                    best_params = params
                    
            except Exception as e:
                print(f"参数组合 {params} 评估失败: {e}")
                continue
        
        print(f"最佳参数: {best_params}")
        print(f"最佳得分: {best_score:.3f}")
        
        return best_params, best_score
    
    def _generate_parameter_combinations(self, parameter_ranges):
        """生成参数组合"""
        combinations = []
        
        # 简化实现：只考虑两个参数的组合
        if 'min_confidence' in parameter_ranges and 'max_stake_ratio' in parameter_ranges:
            for conf in parameter_ranges['min_confidence']:
                for ratio in parameter_ranges['max_stake_ratio']:
                    combinations.append({
                        'min_confidence': conf,
                        'max_stake_ratio': ratio
                    })
        
        return combinations
    
    def _evaluate_strategy_on_data(self, strategy, historical_data):
        """在历史数据上评估策略"""
        total_return = 0
        bet_count = 0
        
        for _, row in historical_data.iterrows():
            # 模拟预测结果
            prediction_result = {
                'predicted_outcome': row.get('predicted_outcome', 'home_win'),
                'confidence': row.get('confidence', 0.8),
                'probabilities': {
                    'home_win': 0.5,
                    'draw': 0.3,
                    'away_win': 0.2
                }
            }
            
            odds_data = {
                'closing_home': row.get('closing_home', 2.0),
                'closing_draw': row.get('closing_draw', 3.0),
                'closing_away': row.get('closing_away', 4.0)
            }
            
            match_info = {'match_id': row.get('match_id', 'test')}
            
            decision = strategy.get_bet_decision(prediction_result, odds_data, match_info, 1000)
            
            if decision['should_bet']:
                bet_count += 1
                # 简化收益计算
                if row.get('actual_outcome') == decision['outcome']:
                    total_return += decision['stake'] * 0.5  # 假设平均赔率2.0
                else:
                    total_return -= decision['stake']
        
        return total_return / max(bet_count, 1)  # 平均每次投注收益
    
    def generate_strategy_report(self, decisions, match_info):
        """生成策略决策报告"""
        report = {
            'match_info': match_info,
            'strategy_decisions': decisions,
            'summary': {
                'total_strategies': len(decisions),
                'betting_strategies': sum(1 for d in decisions.values() if d.get('should_bet', False)),
                'total_stake': sum(d.get('stake', 0) for d in decisions.values()),
                'avg_confidence': np.mean([d.get('confidence', 0) for d in decisions.values() if d.get('should_bet', False)]) if any(d.get('should_bet', False) for d in decisions.values()) else 0
            }
        }
        
        return report
    
    def save_strategies(self, filename_prefix="betting_strategies"):
        """保存策略配置"""
        strategy_configs = {}
        
        for name, strategy in self.strategies.items():
            strategy_configs[name] = {
                'name': strategy.name,
                'description': strategy.description,
                'parameters': strategy.parameters,
                'type': type(strategy).__name__
            }
        
        output_file = f"{filename_prefix}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(strategy_configs, f, ensure_ascii=False, indent=2)
        
        print(f"\n策略配置已保存到: {output_file}")
        return output_file

def main():
    designer = BettingStrategyDesigner()
    
    # 模拟预测结果
    prediction_result = {
        'predicted_outcome': 'home_win',
        'confidence': 0.85,
        'probabilities': {
            'home_win': 0.6,
            'draw': 0.25,
            'away_win': 0.15
        }
    }
    
    # 模拟赔率数据
    odds_data = {
        'opening_home': 1.8,
        'closing_home': 1.9,
        'opening_draw': 3.2,
        'closing_draw': 3.1,
        'opening_away': 4.5,
        'closing_away': 4.2
    }
    
    match_info = {'match_id': 'test_match_001'}
    
    # 评估所有策略
    print("\n=== 策略决策评估 ===")
    decisions = designer.evaluate_all_strategies(
        prediction_result, odds_data, match_info, bankroll=1000
    )
    
    for strategy_name, decision in decisions.items():
        print(f"\n{strategy_name}:")
        if decision['should_bet']:
            print(f"  投注: {decision['outcome']}")
            print(f"  金额: {decision['stake']:.2f}")
            print(f"  置信度: {decision.get('confidence', 0):.3f}")
        else:
            print(f"  不投注")
    
    # 生成报告
    report = designer.generate_strategy_report(decisions, match_info)
    print(f"\n=== 策略总结 ===")
    print(f"总策略数: {report['summary']['total_strategies']}")
    print(f"建议投注策略数: {report['summary']['betting_strategies']}")
    print(f"总投注金额: {report['summary']['total_stake']:.2f}")
    print(f"平均置信度: {report['summary']['avg_confidence']:.3f}")
    
    # 保存策略
    designer.save_strategies()

if __name__ == "__main__":
    main()

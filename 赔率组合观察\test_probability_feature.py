#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试新增的概率计算功能
"""

import sys
import os

# 添加父目录到Python路径
parent_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, parent_dir)

from prob_cal import multinomial_tail_prob

def test_probability_calculation():
    """测试概率计算逻辑"""
    print("测试概率计算功能")
    print("=" * 40)
    
    # 模拟您提到的例子：组合2的赔率是 1.95, 3.4, 4.0
    # 匹配次数是6，其中6胜0平0负
    
    home_odds = 1.95
    draw_odds = 3.4
    away_odds = 4.0
    
    # 计算隐含概率
    home_prob_raw = 1.0 / home_odds
    draw_prob_raw = 1.0 / draw_odds
    away_prob_raw = 1.0 / away_odds
    
    # 计算返还率
    return_rate = home_prob_raw + draw_prob_raw + away_prob_raw
    
    # 计算真实概率（归一化）
    p = [home_prob_raw / return_rate, draw_prob_raw / return_rate, away_prob_raw / return_rate]
    
    print(f"赔率: 主胜{home_odds}, 平局{draw_odds}, 客胜{away_odds}")
    print(f"返还率: {return_rate:.4f}")
    print(f"隐含概率: {p[0]:.4f}, {p[1]:.4f}, {p[2]:.4f}")
    print(f"概率和: {sum(p):.4f}")
    
    # 观察到的结果：6胜0平0负
    observed = [6, 0, 0]
    
    print(f"\n观察结果: {observed}")
    
    # 计算概率
    try:
        prob, tail_prob = multinomial_tail_prob(p, observed)
        print(f"观察概率: {prob:.6f}")
        print(f"尾部概率: {tail_prob:.6f}")
        
        return True
        
    except Exception as e:
        print(f"❌ 计算失败: {e}")
        return False

def test_edge_cases():
    """测试边界情况"""
    print("\n测试边界情况")
    print("=" * 30)
    
    # 测试匹配次数为0的情况
    print("1. 测试匹配次数为0:")
    observed_zero = [0, 0, 0]
    print(f"   观察结果: {observed_zero}")
    print(f"   应该返回: prob=1.0, tail_prob=1.0")
    
    # 测试其他情况
    print("\n2. 测试正常情况:")
    p = [0.5, 0.3, 0.2]
    observed = [3, 2, 1]
    
    try:
        prob, tail_prob = multinomial_tail_prob(p, observed)
        print(f"   概率: {p}")
        print(f"   观察结果: {observed}")
        print(f"   观察概率: {prob:.6f}")
        print(f"   尾部概率: {tail_prob:.6f}")
        
        return True
        
    except Exception as e:
        print(f"❌ 计算失败: {e}")
        return False

def test_ui_integration():
    """测试UI集成逻辑"""
    print("\n测试UI集成逻辑")
    print("=" * 30)
    
    # 模拟组合数据
    combination = [
        {'home_odds': 1.9, 'draw_odds': 3.4, 'away_odds': 4.1, 'date': '04-28', 'time': '10:23'},
        {'home_odds': 1.95, 'draw_odds': 3.4, 'away_odds': 4.0, 'date': '04-28', 'time': '17:39'}
    ]
    
    # 模拟统计数据
    stats = {'win': 6, 'draw': 0, 'loss': 0}
    match_count = 6
    
    print(f"组合数据: {len(combination)} 个赔率")
    print(f"最后赔率: 主{combination[-1]['home_odds']}, 平{combination[-1]['draw_odds']}, 客{combination[-1]['away_odds']}")
    print(f"统计数据: {stats}")
    print(f"匹配次数: {match_count}")
    
    try:
        # 模拟UI中的计算逻辑
        if match_count == 0:
            prob, tail_prob = 1.0, 1.0
        else:
            last_odds = combination[-1]
            home_odds = last_odds['home_odds']
            draw_odds = last_odds['draw_odds']
            away_odds = last_odds['away_odds']
            
            # 计算隐含概率
            home_prob_raw = 1.0 / home_odds
            draw_prob_raw = 1.0 / draw_odds
            away_prob_raw = 1.0 / away_odds
            
            # 计算返还率
            return_rate = home_prob_raw + draw_prob_raw + away_prob_raw
            
            # 计算真实概率
            p = [home_prob_raw / return_rate, draw_prob_raw / return_rate, away_prob_raw / return_rate]
            
            # 构建观察结果
            observed = [stats['win'], stats['draw'], stats['loss']]
            
            # 计算概率
            prob, tail_prob = multinomial_tail_prob(p, observed)
        
        print(f"\n✅ 计算成功:")
        print(f"   概率: {prob:.6f}")
        print(f"   尾部概率: {tail_prob:.6f}")
        
        return True
        
    except Exception as e:
        print(f"❌ UI集成测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("赔率组合观察工具 - 概率计算功能测试")
    print("=" * 50)
    
    # 基本概率计算测试
    success1 = test_probability_calculation()
    
    # 边界情况测试
    success2 = test_edge_cases()
    
    # UI集成测试
    success3 = test_ui_integration()
    
    print(f"\n" + "=" * 50)
    if success1 and success2 and success3:
        print("🎉 所有测试通过！")
        print("💡 概率计算功能已准备就绪")
        print("💡 可以在UI中使用新功能了")
    else:
        print("❌ 部分测试失败")
        print("💡 请检查代码实现")

if __name__ == '__main__':
    main()

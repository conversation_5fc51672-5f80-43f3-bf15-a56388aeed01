#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增强比赛数据系统
整合比赛详细数据抓取、灵活数据保存和数据库架构扩展功能

主要功能:
1. 完整的比赛详细数据抓取
2. 灵活的数据保存和管理
3. 自动化的数据库架构升级
4. 数据完整性验证和监控

使用方法:
    system = EnhancedMatchDataSystem()
    system.initialize_system()
    result = system.process_match_data(match_id)

集成的模块:
- match_detail_scraper: 比赛详细数据抓取器
- flexible_data_saver: 灵活数据保存器
- database_schema_extension: 数据库架构扩展
"""

import logging
from typing import Dict, List, Optional, Any
from datetime import datetime
import time

# 导入我们创建的模块
from match_detail_scraper import MatchDetailScraper
from flexible_data_saver import FlexibleMatchDataSaver
from database_schema_extension import DatabaseSchemaManager

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class EnhancedMatchDataSystem:
    """增强比赛数据系统"""
    
    def __init__(self, db_path: str = 'odds_data.db'):
        """
        初始化增强比赛数据系统
        
        Args:
            db_path: 数据库文件路径
        """
        self.db_path = db_path
        self.scraper = MatchDetailScraper()
        self.data_saver = FlexibleMatchDataSaver(db_path)
        self.schema_manager = DatabaseSchemaManager(db_path)
        self.initialized = False
    
    def initialize_system(self) -> Dict[str, Any]:
        """
        初始化系统
        
        Returns:
            初始化结果字典
        """
        try:
            logger.info("开始初始化增强比赛数据系统")
            
            # 1. 升级数据库架构
            schema_result = self.schema_manager.upgrade_schema()
            if not schema_result['success']:
                return {'success': False, 'error': f"架构升级失败: {schema_result['error']}"}
            
            # 2. 验证系统组件
            verification_result = self.verify_system_components()
            
            self.initialized = True
            logger.info("增强比赛数据系统初始化完成")
            
            return {
                'success': True,
                'schema_upgrade': schema_result,
                'component_verification': verification_result,
                'initialized_at': datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"系统初始化失败: {e}")
            return {'success': False, 'error': str(e)}
    
    def verify_system_components(self) -> Dict[str, bool]:
        """验证系统组件"""
        try:
            verification = {}
            
            # 验证数据抓取器
            verification['scraper'] = hasattr(self.scraper, 'scrape_match_details')
            
            # 验证数据保存器
            verification['data_saver'] = hasattr(self.data_saver, 'save_extended_match_data')
            
            # 验证架构管理器
            verification['schema_manager'] = hasattr(self.schema_manager, 'upgrade_schema')
            
            # 验证数据库连接
            try:
                schema_info = self.schema_manager.get_schema_info()
                verification['database_connection'] = 'error' not in schema_info
            except:
                verification['database_connection'] = False
            
            return verification
            
        except Exception as e:
            logger.warning(f"组件验证失败: {e}")
            return {}
    
    def process_match_data(self, match_id: str, save_data: bool = True) -> Dict[str, Any]:
        """
        处理比赛数据（抓取+保存）
        
        Args:
            match_id: 比赛ID
            save_data: 是否保存数据到数据库
            
        Returns:
            处理结果字典
        """
        if not self.initialized:
            return {'success': False, 'error': '系统未初始化，请先调用 initialize_system()'}
        
        try:
            logger.info(f"开始处理比赛 {match_id} 的数据")
            start_time = time.time()
            
            # 1. 抓取比赛详细数据
            scrape_result = self.scraper.scrape_match_details(match_id)
            
            if 'error' in scrape_result:
                return {
                    'success': False,
                    'error': f"数据抓取失败: {scrape_result['error']}",
                    'match_id': match_id
                }
            
            # 2. 保存数据（如果需要）
            save_result = None
            if save_data:
                save_result = self.data_saver.save_extended_match_data(match_id, scrape_result)
                
                if not save_result['success']:
                    logger.warning(f"数据保存失败: {save_result['error']}")
            
            # 3. 生成数据摘要
            data_summary = self._generate_data_summary(scrape_result)
            
            processing_time = time.time() - start_time
            
            result = {
                'success': True,
                'match_id': match_id,
                'scrape_result': scrape_result,
                'save_result': save_result,
                'data_summary': data_summary,
                'processing_time': round(processing_time, 2),
                'processed_at': datetime.now().isoformat()
            }
            
            logger.info(f"比赛 {match_id} 数据处理完成，耗时 {processing_time:.2f} 秒")
            return result
            
        except Exception as e:
            logger.error(f"处理比赛数据失败: {e}")
            return {'success': False, 'error': str(e), 'match_id': match_id}
    
    def _generate_data_summary(self, scrape_result: Dict[str, Any]) -> Dict[str, Any]:
        """生成数据摘要"""
        summary = {
            'data_types_found': [],
            'data_counts': {},
            'data_quality': {}
        }
        
        # 检查各类数据
        data_checks = [
            ('technical_stats', '技术统计'),
            ('lineup', '阵容信息'),
            ('goal_probability', '进失球概率'),
            ('halftime_fulltime_stats', '半场/全场统计'),
            ('player_stats', '球员统计'),
            ('match_events', '比赛事件')
        ]
        
        for key, name in data_checks:
            if key in scrape_result and scrape_result[key]:
                summary['data_types_found'].append(name)
                
                # 计算数据量
                data = scrape_result[key]
                if key == 'technical_stats':
                    summary['data_counts'][name] = len(data) if isinstance(data, dict) else 0
                elif key == 'lineup':
                    home_count = len(data.get('home_players', []))
                    away_count = len(data.get('away_players', []))
                    summary['data_counts'][name] = f"主队{home_count}人, 客队{away_count}人"
                elif key == 'player_stats':
                    home_players = len(data.get('home_team', {}).get('player_tech_info', []))
                    away_players = len(data.get('away_team', {}).get('player_tech_info', []))
                    summary['data_counts'][name] = f"主队{home_players}人, 客队{away_players}人"
                else:
                    summary['data_counts'][name] = "已获取"
                
                # 评估数据质量
                summary['data_quality'][name] = self._assess_data_quality(key, data)
        
        summary['total_data_types'] = len(summary['data_types_found'])
        summary['completeness_score'] = (len(summary['data_types_found']) / len(data_checks)) * 100
        
        return summary
    
    def _assess_data_quality(self, data_type: str, data: Any) -> str:
        """评估数据质量"""
        if not data:
            return "无数据"
        
        try:
            if data_type == 'technical_stats':
                if isinstance(data, dict) and len(data) >= 10:
                    return "优秀"
                elif isinstance(data, dict) and len(data) >= 5:
                    return "良好"
                else:
                    return "一般"
            
            elif data_type == 'lineup':
                home_players = len(data.get('home_players', []))
                away_players = len(data.get('away_players', []))
                if home_players >= 11 and away_players >= 11:
                    return "完整"
                elif home_players > 0 and away_players > 0:
                    return "部分"
                else:
                    return "缺失"
            
            elif data_type == 'player_stats':
                home_count = len(data.get('home_team', {}).get('player_tech_info', []))
                away_count = len(data.get('away_team', {}).get('player_tech_info', []))
                total_players = home_count + away_count
                if total_players >= 20:
                    return "详细"
                elif total_players >= 10:
                    return "基本"
                else:
                    return "简单"
            
            else:
                return "正常"
                
        except:
            return "异常"
    
    def batch_process_matches(self, match_ids: List[str], 
                            save_data: bool = True,
                            delay_seconds: float = 1.0) -> Dict[str, Any]:
        """
        批量处理比赛数据
        
        Args:
            match_ids: 比赛ID列表
            save_data: 是否保存数据
            delay_seconds: 请求间隔（秒）
            
        Returns:
            批量处理结果
        """
        if not self.initialized:
            return {'success': False, 'error': '系统未初始化'}
        
        try:
            logger.info(f"开始批量处理 {len(match_ids)} 场比赛的数据")
            start_time = time.time()
            
            results = []
            successful_count = 0
            failed_count = 0
            
            for i, match_id in enumerate(match_ids, 1):
                logger.info(f"处理第 {i}/{len(match_ids)} 场比赛: {match_id}")
                
                result = self.process_match_data(match_id, save_data)
                results.append(result)
                
                if result['success']:
                    successful_count += 1
                else:
                    failed_count += 1
                
                # 添加延迟避免请求过快
                if i < len(match_ids):
                    time.sleep(delay_seconds)
            
            total_time = time.time() - start_time
            
            batch_result = {
                'success': True,
                'total_matches': len(match_ids),
                'successful_count': successful_count,
                'failed_count': failed_count,
                'success_rate': (successful_count / len(match_ids)) * 100,
                'total_time': round(total_time, 2),
                'average_time_per_match': round(total_time / len(match_ids), 2),
                'results': results,
                'processed_at': datetime.now().isoformat()
            }
            
            logger.info(f"批量处理完成: 成功 {successful_count}/{len(match_ids)} 场，耗时 {total_time:.2f} 秒")
            return batch_result
            
        except Exception as e:
            logger.error(f"批量处理失败: {e}")
            return {'success': False, 'error': str(e)}
    
    def get_system_status(self) -> Dict[str, Any]:
        """获取系统状态"""
        try:
            status = {
                'initialized': self.initialized,
                'components': self.verify_system_components(),
                'schema_info': self.schema_manager.get_schema_info(),
                'timestamp': datetime.now().isoformat()
            }
            
            # 计算组件健康度
            components = status['components']
            if components:
                healthy_components = sum(1 for v in components.values() if v)
                total_components = len(components)
                status['health_score'] = (healthy_components / total_components) * 100
            else:
                status['health_score'] = 0
            
            return status
            
        except Exception as e:
            return {'error': str(e)}
    
    def print_system_status(self):
        """打印系统状态"""
        status = self.get_system_status()
        
        if 'error' in status:
            print(f"❌ 获取系统状态失败: {status['error']}")
            return
        
        print("🚀 增强比赛数据系统状态")
        print("=" * 50)
        print(f"系统状态: {'✅ 已初始化' if status['initialized'] else '❌ 未初始化'}")
        print(f"健康度: {status.get('health_score', 0):.1f}%")
        
        print("\n🔧 组件状态:")
        for component, healthy in status.get('components', {}).items():
            status_icon = "✅" if healthy else "❌"
            print(f"  {status_icon} {component}")
        
        schema_info = status.get('schema_info', {})
        if schema_info and 'error' not in schema_info:
            print(f"\n📊 数据库信息:")
            print(f"  版本: {schema_info.get('current_version', 'N/A')}")
            print(f"  大小: {schema_info.get('database_size_mb', 'N/A')} MB")
            print(f"  表数量: {len(schema_info.get('tables', []))}")

def test_enhanced_match_data_system():
    """测试增强比赛数据系统"""
    print("🧪 测试增强比赛数据系统")
    print("=" * 50)
    
    # 创建系统实例
    system = EnhancedMatchDataSystem()
    
    # 1. 初始化系统
    print("1. 初始化系统:")
    init_result = system.initialize_system()
    
    if init_result['success']:
        print("✅ 系统初始化成功")
    else:
        print(f"❌ 系统初始化失败: {init_result['error']}")
        return
    
    # 2. 显示系统状态
    print("\n2. 系统状态:")
    system.print_system_status()
    
    # 3. 测试单个比赛数据处理
    print("\n3. 测试单个比赛数据处理:")
    test_match_id = "2511566"
    result = system.process_match_data(test_match_id, save_data=True)
    
    if result['success']:
        print(f"✅ 比赛 {test_match_id} 处理成功")
        summary = result.get('data_summary', {})
        print(f"   数据完整度: {summary.get('completeness_score', 0):.1f}%")
        print(f"   处理时间: {result.get('processing_time', 0)} 秒")
    else:
        print(f"❌ 比赛处理失败: {result['error']}")

if __name__ == '__main__':
    test_enhanced_match_data_system()

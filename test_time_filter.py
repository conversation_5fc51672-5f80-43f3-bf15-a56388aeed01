#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试时间范围筛选功能
验证时间筛选逻辑是否正确处理 YYYY-MM-DD HH:MM 格式
"""

import sqlite3
import sys
import os
from datetime import datetime

def test_time_data_format():
    """测试数据库中的时间数据格式"""
    print("=== 测试数据库中的时间数据格式 ===")
    try:
        with sqlite3.connect("odds_data.db") as conn:
            conn.row_factory = sqlite3.Row
            cursor = conn.cursor()
            
            # 查看时间字段的样本数据
            cursor.execute('''
                SELECT match_id, match_time, match_date, accurate_datetime, 
                       accurate_date, accurate_time, weekday
                FROM matches 
                WHERE accurate_datetime IS NOT NULL
                ORDER BY accurate_datetime DESC
                LIMIT 10
            ''')
            
            rows = cursor.fetchall()
            print(f"✅ 找到 {len(rows)} 条有准确时间的记录:")
            
            for row in rows:
                match_dict = dict(row)
                print(f"  比赛ID: {match_dict['match_id']}")
                print(f"    accurate_datetime: {match_dict['accurate_datetime']}")
                print(f"    accurate_date: {match_dict['accurate_date']}")
                print(f"    accurate_time: {match_dict['accurate_time']}")
                print(f"    match_time: {match_dict['match_time']}")
                print(f"    weekday: {match_dict['weekday']}")
                print()
            
            return True
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def test_time_range_query():
    """测试时间范围查询"""
    print("=== 测试时间范围查询 ===")
    try:
        with sqlite3.connect("odds_data.db") as conn:
            conn.row_factory = sqlite3.Row
            cursor = conn.cursor()
            
            # 测试1: 查询2025年5月18日的比赛
            print("测试1: 查询2025年5月18日的比赛")
            start_time = "2025-05-18 00:00:00"
            end_time = "2025-05-18 23:59:59"
            
            query = '''
                SELECT match_id, accurate_datetime, home_team, away_team
                FROM matches 
                WHERE (accurate_datetime >= ? AND accurate_datetime <= ?)
                   OR (accurate_datetime IS NULL AND match_time >= ? AND match_time <= ?)
                   OR (accurate_datetime IS NULL AND match_time IS NULL AND match_date >= ? AND match_date <= ?)
                ORDER BY accurate_datetime DESC, match_time DESC
                LIMIT 10
            '''
            
            cursor.execute(query, [start_time, end_time, start_time, end_time, 
                                 start_time[:10], end_time[:10]])
            rows = cursor.fetchall()
            
            print(f"✅ 找到 {len(rows)} 场2025-05-18的比赛:")
            for row in rows:
                match_dict = dict(row)
                teams = f"{match_dict['home_team']} vs {match_dict['away_team']}"
                print(f"  {match_dict['match_id']}: {teams} ({match_dict['accurate_datetime']})")
            
            # 测试2: 查询2025年5月17日14:00之后的比赛
            print("\n测试2: 查询2025年5月17日14:00之后的比赛")
            start_time = "2025-05-17 14:00:00"
            
            query = '''
                SELECT match_id, accurate_datetime, home_team, away_team
                FROM matches 
                WHERE accurate_datetime >= ?
                   OR (accurate_datetime IS NULL AND match_time >= ?)
                   OR (accurate_datetime IS NULL AND match_time IS NULL AND match_date >= ?)
                ORDER BY accurate_datetime DESC, match_time DESC
                LIMIT 10
            '''
            
            cursor.execute(query, [start_time, start_time, start_time[:10]])
            rows = cursor.fetchall()
            
            print(f"✅ 找到 {len(rows)} 场2025-05-17 14:00之后的比赛:")
            for row in rows:
                match_dict = dict(row)
                teams = f"{match_dict['home_team']} vs {match_dict['away_team']}"
                print(f"  {match_dict['match_id']}: {teams} ({match_dict['accurate_datetime']})")
            
            return True
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def test_time_normalization():
    """测试时间标准化函数"""
    print("=== 测试时间标准化函数 ===")
    
    def normalize_datetime_for_filter(time_input, is_start=True):
        """标准化时间输入为数据库查询格式"""
        try:
            time_input = time_input.strip()
            if not time_input:
                return None
            
            # 处理不同的输入格式
            if len(time_input) == 10 and time_input.count('-') == 2:
                # 格式: YYYY-MM-DD
                if is_start:
                    return f"{time_input} 00:00:00"
                else:
                    return f"{time_input} 23:59:59"
            
            elif len(time_input) == 16 and time_input.count('-') == 2 and time_input.count(':') == 1:
                # 格式: YYYY-MM-DD HH:MM
                return f"{time_input}:00"
            
            elif len(time_input) == 19 and time_input.count('-') == 2 and time_input.count(':') == 2:
                # 格式: YYYY-MM-DD HH:MM:SS
                return time_input
            
            else:
                # 尝试解析其他格式
                formats = [
                    '%Y-%m-%d',
                    '%Y-%m-%d %H:%M',
                    '%Y-%m-%d %H:%M:%S',
                    '%Y/%m/%d',
                    '%Y/%m/%d %H:%M',
                    '%Y/%m/%d %H:%M:%S'
                ]
                
                for fmt in formats:
                    try:
                        dt = datetime.strptime(time_input, fmt)
                        if fmt == '%Y-%m-%d' or fmt == '%Y/%m/%d':
                            # 只有日期，添加时间
                            if is_start:
                                return dt.strftime('%Y-%m-%d 00:00:00')
                            else:
                                return dt.strftime('%Y-%m-%d 23:59:59')
                        else:
                            return dt.strftime('%Y-%m-%d %H:%M:%S')
                    except ValueError:
                        continue
                
                return None
                
        except Exception as e:
            return None
    
    # 测试用例
    test_cases = [
        ("2025-05-18", True, "2025-05-18 00:00:00"),
        ("2025-05-18", False, "2025-05-18 23:59:59"),
        ("2025-05-18 07:30", True, "2025-05-18 07:30:00"),
        ("2025-05-18 07:30", False, "2025-05-18 07:30:00"),
        ("2025-05-18 07:30:45", True, "2025-05-18 07:30:45"),
        ("2025/05/18", True, "2025-05-18 00:00:00"),
        ("2025/05/18 14:00", True, "2025-05-18 14:00:00"),
    ]
    
    all_passed = True
    for input_time, is_start, expected in test_cases:
        result = normalize_datetime_for_filter(input_time, is_start)
        if result == expected:
            print(f"✅ '{input_time}' (start={is_start}) -> '{result}'")
        else:
            print(f"❌ '{input_time}' (start={is_start}) -> '{result}', 期望: '{expected}'")
            all_passed = False
    
    return all_passed

def main():
    """主测试函数"""
    print("🧪 时间范围筛选功能测试")
    print("=" * 50)
    
    # 检查数据库文件是否存在
    if not os.path.exists("odds_data.db"):
        print("❌ 数据库文件 odds_data.db 不存在")
        return False
    
    tests = [
        test_time_data_format,
        test_time_range_query,
        test_time_normalization
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"❌ 测试异常: {e}")
    
    print("\n" + "=" * 50)
    print(f"📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！时间范围筛选功能应该可以正常工作。")
        return True
    else:
        print("⚠️  部分测试失败，请检查相关功能。")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)

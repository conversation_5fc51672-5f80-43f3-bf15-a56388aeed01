#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sqlite3

def check_matches():
    conn = sqlite3.connect('odds_data.db')
    cursor = conn.cursor()
    
    # 从截图中看到的比赛ID
    match_ids = ["2701670", "2741774", "2511950", "2511950"]  # 重复的是因为截图中有重复
    
    print("检查截图中的比赛:")
    for match_id in set(match_ids):  # 去重
        cursor.execute('SELECT match_id, home_team, away_team, match_time FROM matches WHERE match_id = ?', (match_id,))
        result = cursor.fetchone()
        if result:
            print(f'{result[0]}: {result[1]} vs {result[2]} - {result[3]}')
        else:
            print(f'{match_id}: 未找到')
    
    # 也检查一下所有比赛
    print("\n数据库中的所有比赛:")
    cursor.execute('SELECT match_id, home_team, away_team, match_time FROM matches ORDER BY match_time DESC LIMIT 10')
    results = cursor.fetchall()
    for r in results:
        print(f'{r[0]}: {r[1]} vs {r[2]} - {r[3]}')
    
    conn.close()

if __name__ == "__main__":
    check_matches()

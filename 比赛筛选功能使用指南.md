# 比赛筛选功能使用指南

## 🎯 功能概述

新增的**比赛筛选**功能允许您根据多种条件快速筛选和查找比赛数据，筛选结果直接在"比赛列表"中显示，大大提高了数据查找和分析的效率。

## 📍 功能位置

在主界面中，您会看到一个新的标签页：**"比赛筛选"**，位于"比赛列表"标签页之后。

## 🔧 界面布局

### 比赛筛选标签页
- 包含多种筛选条件，每个条件都有独立的启用开关
- 支持同时启用多个筛选条件
- 提供筛选操作按钮和状态显示

### 筛选结果显示
- 筛选结果直接在"比赛列表"标签页中显示
- 点击"应用筛选"后自动切换到"比赛列表"标签页
- 筛选状态在"比赛筛选"标签页底部显示

## 📋 筛选条件详解

### 1. 联赛筛选
- **功能**：按联赛名称筛选比赛
- **操作**：
  1. 勾选"启用联赛筛选"
  2. 在联赛列表中选择一个或多个联赛（支持多选）
  3. 点击"应用筛选"

### 2. 赛季筛选
- **功能**：按赛季筛选比赛
- **操作**：
  1. 勾选"启用赛季筛选"
  2. 在赛季列表中选择一个或多个赛季（支持多选）
  3. 点击"应用筛选"

### 3. 时间范围筛选
- **功能**：按比赛时间范围筛选
- **支持格式**：
  - `YYYY-MM-DD`（如：2025-05-18）
  - `YYYY-MM-DD HH:MM`（如：2025-05-18 07:30）
  - `YYYY-MM-DD HH:MM:SS`（如：2025-05-18 07:30:00）
- **操作**：
  1. 勾选"启用时间范围筛选"
  2. 设置开始时间（可选）
  3. 设置结束时间（可选）
  4. 点击"应用筛选"
- **示例**：
  - 查找2025年5月18日的比赛：开始时间 `2025-05-18`，结束时间 `2025-05-18`
  - 查找2025年5月18日07:30之后的比赛：开始时间 `2025-05-18 07:30`
  - 查找2025年5月的比赛：开始时间 `2025-05-01`，结束时间 `2025-05-31`

### 4. 比赛状态筛选
- **功能**：按比赛状态筛选（如完场、进行中等）
- **操作**：
  1. 勾选"启用比赛状态筛选"
  2. 在状态列表中选择一个或多个状态（支持多选）
  3. 点击"应用筛选"

### 5. 队伍筛选
- **功能**：按队伍名称筛选比赛（支持模糊匹配）
- **操作**：
  1. 勾选"启用队伍筛选"
  2. 在输入框中输入队伍名称（支持部分匹配）
  3. 点击"应用筛选"

### 6. 比分筛选
- **功能**：按比分情况筛选比赛
- **选项**：
  - 所有比赛：显示所有比赛
  - 仅有比分的比赛：只显示已有比分的比赛
  - 仅无比分的比赛：只显示未有比分的比赛
- **操作**：
  1. 勾选"启用比分筛选"
  2. 选择相应的比分类型
  3. 点击"应用筛选"

### 7. 凯利分析筛选 ⭐
- **功能**：基于凯利指数和返还率的高级统计分析
- **参数**：
  - 凯利类型：凯利主、凯利平、凯利客
  - 统计数量：分析的博彩公司数量（默认10家）
  - 凯利门槛：平均凯利指数最低要求（默认1.01）
  - 返还率门槛：平均返还率最高限制（默认97%）
- **逻辑**：选取凯利指数排名前N家的数据，计算平均值，筛选同时满足凯利和返还率条件的比赛
- **操作**：
  1. 勾选"启用凯利分析筛选"
  2. 选择凯利类型（主/平/客）
  3. 设置统计数量和门槛参数
  4. 点击"应用筛选"

### 8. 凯利分析2筛选 ⭐
- **功能**：基于凯利指数和博彩公司分布的精细化分析
- **参数**：
  - 凯利类型：凯利主、凯利平、凯利客
  - 统计数量：分析的博彩公司数量（默认10家）
  - 凯利门槛：平均凯利指数最低要求（默认1.05）
  - 博彩公司：可选择监控的博彩公司列表
  - 无意义公司门槛：监控公司允许出现的最大次数（默认2次）
- **逻辑**：在凯利分析基础上，监控特定博彩公司在高排名中的出现频率，避开可能被操控的比赛
- **操作**：
  1. 勾选"启用凯利分析2筛选"
  2. 选择凯利类型（主/平/客）
  3. 设置统计数量和凯利门槛
  4. 选择需要监控的博彩公司
  5. 设置无意义公司门槛
  6. 点击"应用筛选"

## 🎮 操作按钮

### 应用筛选
- **功能**：根据当前启用的筛选条件执行筛选
- **说明**：只有启用的筛选条件才会生效
- **结果**：筛选结果在"比赛列表"标签页中显示，并自动切换到该标签页

### 清除筛选
- **功能**：清除当前的筛选结果，恢复显示所有比赛
- **说明**：筛选条件的设置不会被清除
- **结果**：自动切换到"比赛列表"标签页显示所有比赛

### 重置条件
- **功能**：重置所有筛选条件到初始状态
- **说明**：
  - 取消所有筛选条件的启用状态
  - 清空所有筛选值
  - 清除筛选结果并显示所有比赛

## 📊 筛选结果

### 结果显示
筛选结果直接在"比赛列表"标签页中显示，包含以下列：
- **比赛ID**：比赛的唯一标识
- **联赛**：比赛所属联赛
- **赛季**：比赛所属赛季
- **轮次**：比赛轮次信息
- **对阵**：主队 vs 客队
- **比赛时间**：比赛开始时间
- **比分**：比赛结果比分
- **状态**：比赛当前状态
- **博彩公司**：该比赛的博彩公司数量

### 筛选状态显示
在"比赛筛选"标签页底部显示当前筛选状态：
- 未筛选时：`当前显示: 所有比赛 (X 场)`
- 已筛选时：`已筛选 (X 场): 联赛: XX | 赛季: XX | ...`

### 查看详情
- **双击**"比赛列表"中的任意比赛行
- 自动跳转到"比赛详情"页面
- 显示该比赛的完整信息和赔率数据

## 💡 使用技巧

### 1. 组合筛选
- 可以同时启用多个筛选条件
- 多个条件之间是"AND"关系（同时满足）
- 例如：选择"英超" + "2024-25赛季" + "完场状态"

### 2. 模糊搜索
- 队伍筛选支持模糊匹配
- 输入"曼联"可以匹配"曼彻斯特联"
- 输入"巴萨"可以匹配"巴塞罗那"

### 3. 时间范围技巧
- **灵活设置**：开始时间和结束时间都是可选的
- **只设置开始时间**：筛选该时间之后的比赛
- **只设置结束时间**：筛选该时间之前的比赛
- **同时设置**：筛选时间范围内的比赛
- **精确到分钟**：支持 `YYYY-MM-DD HH:MM` 格式进行精确筛选
- **智能处理**：
  - 只输入日期时，开始时间自动设为 00:00:00，结束时间自动设为 23:59:59
  - 支持多种日期格式：`2025-05-18` 或 `2025/05/18`

### 4. 多选技巧
- 在列表框中按住Ctrl键可以多选
- 按住Shift键可以选择连续范围
- 点击空白处可以取消选择

## 🔄 与其他功能的集成

### 数据库切换
- 筛选功能会自动适应当前选择的数据库
- 切换数据库后，筛选条件会自动更新
- 联赛和赛季列表会根据当前数据库内容动态填充

### 数据刷新
- 当数据库中添加新比赛后，可以重新打开筛选页面
- 或者重新启动程序来刷新筛选条件列表
- 筛选状态会在数据刷新时保持

### 与比赛列表的无缝集成
- 筛选结果直接替换比赛列表的内容
- 保持原有的排序和显示功能
- 双击查看详情功能完全兼容

## ⚠️ 注意事项

1. **性能考虑**：
   - 大量数据时筛选可能需要几秒钟
   - 建议合理使用筛选条件，避免过于宽泛的查询

2. **数据完整性**：
   - 筛选结果基于数据库中的实际数据
   - 如果某些字段为空，可能影响筛选结果

3. **时间格式**：
   - 时间范围筛选请使用YYYY-MM-DD格式
   - 例如：2024-01-01

4. **兼容性**：
   - 新功能完全兼容现有功能
   - 不会影响原有的数据抓取和管理功能

## 🚀 未来扩展

该筛选功能设计为可扩展的架构，未来可以轻松添加更多筛选条件，如：
- 博彩公司数量筛选
- 赔率范围筛选
- 比赛重要性筛选
- 自定义标签筛选

---

**版本**: v1.0  
**更新时间**: 2025年6月28日  
**兼容性**: 与现有所有功能完全兼容

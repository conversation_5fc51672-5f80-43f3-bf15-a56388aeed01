#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试联赛映射问题
"""

import requests
import re
import logging
from bs4 import BeautifulSoup

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def debug_league_mapping():
    """调试联赛映射"""
    
    print("🔍 调试联赛映射问题")
    print("=" * 50)
    
    try:
        # 获取页面
        session = requests.Session()
        session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        })
        
        url = "https://m.titan007.com/Schedule.htm?date=2025-07-22"
        response = session.get(url, timeout=30)
        response.encoding = 'utf-8'
        
        soup = BeautifulSoup(response.text, 'html.parser')
        
        # 查找所有script标签
        scripts = soup.find_all('script')
        
        for i, script in enumerate(scripts):
            script_text = script.get_text()
            if not script_text:
                continue
            
            print(f"\n📜 Script {i+1}:")
            print("-" * 30)
            
            # 查找比赛数据
            match_pattern = r'(\d{7,})\^(\d+)\^(\d+)\^(\d{14})\^\^([^^\r\n]+)\^([^^\r\n]+)\^'
            matches = re.findall(match_pattern, script_text)
            
            if matches:
                print(f"🎯 找到 {len(matches)} 场比赛")
                print("📊 前5场比赛数据:")
                for j, match in enumerate(matches[:5]):
                    match_id, league_id, field3, time_str, home_team, away_team = match
                    print(f"  {j+1}. 比赛ID: {match_id}, 联赛ID: {league_id}, 字段3: {field3}")
                    print(f"     时间: {time_str}, 主队: {home_team}, 客队: {away_team}")
                
                # 分析联赛ID分布
                league_ids = [match[1] for match in matches]
                unique_leagues = set(league_ids)
                print(f"\n📈 联赛ID统计:")
                print(f"  总联赛数: {len(unique_leagues)}")
                print(f"  联赛ID示例: {list(unique_leagues)[:10]}")
            
            # 查找联赛名称数据
            league_patterns = [
                r'([^!^]+)\^(\d+)\^',  # 原始模式
                r'([^!]+)\^(\d+)\^',   # 修改模式1
                r'([^$]+)\$(\d+)\$',   # 美元符号分隔
                r'([^#]+)#(\d+)#',     # 井号分隔
                r'([^|]+)\|(\d+)\|',   # 竖线分隔
            ]
            
            for pattern_name, pattern in zip(['原始', '修改1', '美元', '井号', '竖线'], league_patterns):
                league_matches = re.findall(pattern, script_text)
                if league_matches:
                    print(f"\n🏆 {pattern_name}模式找到 {len(league_matches)} 个联赛:")
                    for j, (name, id_) in enumerate(league_matches[:10]):
                        print(f"  {j+1}. ID: {id_}, 名称: {name}")
            
            # 查找可能的联赛数据字符串
            if '欧冠杯' in script_text or '英超' in script_text or '西甲' in script_text:
                print(f"\n📝 发现联赛名称，查找相关数据...")
                
                # 查找包含联赛名称的行
                lines = script_text.split('\n')
                for line_num, line in enumerate(lines):
                    if any(league in line for league in ['欧冠杯', '英超', '西甲', '德甲', '意甲', '法甲']):
                        print(f"  行 {line_num}: {line.strip()[:200]}...")
                        break
            
            # 如果找到比赛数据，就不需要继续查找其他script
            if matches:
                break
        
        print(f"\n🎯 分析完成")
        
    except Exception as e:
        print(f"❌ 调试失败: {e}")
        import traceback
        traceback.print_exc()

def analyze_data_structure():
    """分析数据结构"""
    
    print(f"\n🔬 分析数据结构")
    print("-" * 30)
    
    # 模拟从调试中看到的数据
    sample_data = """
    2833447^2705^0^20250723093000^^黑龙江U15^沈阳大球中心U15^0^0^0^0^0^0^0^0^^^^^0^0^^False^^^
    2833441^2705^0^20250723093000^^大连三十九中U15^大连英博U14^0^0^0^0^0^0^0^0^^^^^0^0^^False^^^
    """
    
    print("📊 数据字段分析:")
    print("字段1: 比赛ID (7-8位数字)")
    print("字段2: 联赛ID (3-4位数字)")
    print("字段3: 未知字段 (通常为0)")
    print("字段4: 时间戳 (14位: YYYYMMDDHHMMSS)")
    print("字段5: 空字段")
    print("字段6: 主队名称")
    print("字段7: 客队名称")
    print("字段8+: 其他数据...")
    
    print(f"\n💡 问题分析:")
    print("1. 当前代码可能将比赛ID误认为联赛ID")
    print("2. 联赛名称映射可能不正确")
    print("3. 需要正确解析联赛ID和联赛名称的对应关系")

if __name__ == "__main__":
    debug_league_mapping()
    analyze_data_structure()

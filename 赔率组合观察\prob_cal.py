from itertools import product
from scipy.stats import multinomial

def multinomial_tail_prob(p, observed, tol=1e-12):
    """
    计算多项分布中，所有组合中点概率小于等于当前观测组合的概率的总和。
    
    参数:
        p: list[float]，如 [0.5, 0.3, 0.2]，每个类别的预测概率，和为1
        observed: list[int]，如 [5, 0, 0]，实际观察到的频数
        tol: float，浮点数误差容忍值，默认1e-12
    
    返回:
        observed_prob: 当前观测组合的概率
        tail_prob: 所有点概率 ≤ 当前组合的概率的组合的总概率
    """
    n = sum(observed)
    k = len(p)
    observed_prob = multinomial.pmf(observed, n=n, p=p)
    tail_prob = 0.0

    # 枚举所有和为 n 的 k 元非负整数组合
    def generate_combinations(n, k):
        if k == 1:
            yield [n]
        else:
            for i in range(n + 1):
                for tail in generate_combinations(n - i, k - 1):
                    yield [i] + tail

    for combo in generate_combinations(n, k):
        prob = multinomial.pmf(combo, n=n, p=p)
        if prob <= observed_prob + tol:
            tail_prob += prob

    return observed_prob, tail_prob



"""
# 测试代码
p = [0.5, 0.3, 0.2]
observed = [8, 0, 0]
prob, tail = multinomial_tail_prob(p, observed)
print(f"观察值组合 {observed} 的概率为: {prob:.5f}")
print(f"小于等于该点概率的组合概率总和为: {tail:.5f}")
"""


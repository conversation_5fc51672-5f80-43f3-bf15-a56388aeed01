#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
并发抓取模式演示
可视化展示3个线程如何协同工作
"""

import time
import threading
import concurrent.futures
from datetime import datetime

class ConcurrentScrapingDemo:
    """并发抓取演示类"""
    
    def __init__(self):
        self.proxy_pool = [
            {"ip": "local", "port": "local", "name": "本地IP"},
            {"ip": "***************", "port": "40014", "name": "山东烟台电信"},
            {"ip": "*************", "port": "40037", "name": "江苏盐城电信"},
            {"ip": "**************", "port": "40022", "name": "安徽池州电信"},
            {"ip": "**************", "port": "40007", "name": "浙江丽水电信"}
        ]
        
        self.matches = [
            {"id": "2741001", "teams": "曼城 vs 阿森纳"},
            {"id": "2741002", "teams": "利物浦 vs 切尔西"},
            {"id": "2741003", "teams": "曼联 vs 热刺"},
            {"id": "2741004", "teams": "纽卡 vs 布莱顿"},
            {"id": "2741005", "teams": "西汉姆 vs 水晶宫"},
            {"id": "2741006", "teams": "狼队 vs 埃弗顿"},
            {"id": "2741007", "teams": "阿斯顿维拉 vs 富勒姆"},
            {"id": "2741008", "teams": "伯恩茅斯 vs 谢菲联"},
            {"id": "2741009", "teams": "诺丁汉森林 vs 卢顿"}
        ]
    
    def simulate_single_thread(self):
        """模拟单线程抓取"""
        print("🔄 单线程模式演示")
        print("=" * 60)
        
        start_time = time.time()
        
        for i, match in enumerate(self.matches):
            print(f"⏳ [{datetime.now().strftime('%H:%M:%S')}] 正在抓取比赛 {i+1}: {match['teams']}")
            
            # 模拟抓取时间（实际可能是2-5秒）
            time.sleep(1)  # 缩短演示时间
            
            print(f"✅ [{datetime.now().strftime('%H:%M:%S')}] 完成比赛 {i+1}: {match['teams']}")
        
        total_time = time.time() - start_time
        print(f"\n📊 单线程总耗时: {total_time:.1f}秒")
        print(f"📈 平均每场比赛: {total_time/len(self.matches):.1f}秒")
        
        return total_time
    
    def simulate_concurrent_threads(self, max_workers=3):
        """模拟并发线程抓取"""
        print(f"\n🚀 {max_workers}线程并发模式演示")
        print("=" * 60)
        
        start_time = time.time()
        completed_matches = []
        
        def scrape_single_match(match, thread_id, proxy):
            """模拟抓取单个比赛"""
            match_id = match['id']
            teams = match['teams']
            
            print(f"🧵 [线程{thread_id}] [{datetime.now().strftime('%H:%M:%S')}] 开始抓取: {teams}")
            print(f"🌐 [线程{thread_id}] 使用代理: {proxy['name']} ({proxy['ip']}:{proxy['port']})")
            
            # 模拟抓取时间
            time.sleep(1)  # 缩短演示时间
            
            print(f"✅ [线程{thread_id}] [{datetime.now().strftime('%H:%M:%S')}] 完成抓取: {teams}")
            
            return {
                'match_id': match_id,
                'teams': teams,
                'thread_id': thread_id,
                'proxy': proxy['name'],
                'success': True
            }
        
        # 使用线程池执行并发抓取
        with concurrent.futures.ThreadPoolExecutor(max_workers=max_workers) as executor:
            # 提交所有任务
            future_to_match = {}
            
            for i, match in enumerate(self.matches):
                # 为每个任务分配代理（轮询分配）
                proxy = self.proxy_pool[i % len(self.proxy_pool)]
                thread_id = (i % max_workers) + 1
                
                future = executor.submit(scrape_single_match, match, thread_id, proxy)
                future_to_match[future] = match
            
            # 收集结果
            for future in concurrent.futures.as_completed(future_to_match):
                match = future_to_match[future]
                try:
                    result = future.result()
                    completed_matches.append(result)
                except Exception as e:
                    print(f"❌ 抓取失败: {match['teams']} - {e}")
        
        total_time = time.time() - start_time
        
        print(f"\n📊 {max_workers}线程并发总耗时: {total_time:.1f}秒")
        print(f"📈 平均每场比赛: {total_time/len(self.matches):.1f}秒")
        print(f"🚀 效率提升: {((len(self.matches) * 1.0) / total_time):.1f}倍")
        
        return total_time, completed_matches
    
    def analyze_thread_distribution(self, completed_matches):
        """分析线程分配情况"""
        print(f"\n📋 线程工作分配分析")
        print("-" * 40)
        
        thread_stats = {}
        for result in completed_matches:
            thread_id = result['thread_id']
            if thread_id not in thread_stats:
                thread_stats[thread_id] = []
            thread_stats[thread_id].append(result)
        
        for thread_id, matches in thread_stats.items():
            print(f"\n🧵 线程{thread_id} 处理了 {len(matches)} 场比赛:")
            for match in matches:
                print(f"   - {match['teams']} (代理: {match['proxy']})")
    
    def compare_performance(self):
        """性能对比"""
        print("🎯 并发抓取性能对比演示")
        print("=" * 80)
        
        # 单线程测试
        single_time = self.simulate_single_thread()
        
        # 3线程并发测试
        concurrent_time, completed_matches = self.simulate_concurrent_threads(3)
        
        # 分析线程分配
        self.analyze_thread_distribution(completed_matches)
        
        # 性能对比
        print(f"\n🏆 性能对比总结")
        print("=" * 40)
        print(f"📊 单线程模式: {single_time:.1f}秒")
        print(f"🚀 3线程并发: {concurrent_time:.1f}秒")
        print(f"⚡ 速度提升: {single_time/concurrent_time:.1f}倍")
        print(f"💾 时间节省: {single_time - concurrent_time:.1f}秒 ({((single_time - concurrent_time)/single_time*100):.1f}%)")
        
        return single_time, concurrent_time

def demonstrate_proxy_rotation():
    """演示代理轮换机制"""
    print("\n🔄 代理轮换机制演示")
    print("=" * 60)
    
    proxies = [
        {"ip": "local", "port": "local", "name": "本地IP", "failures": 0},
        {"ip": "***************", "port": "40014", "name": "山东烟台", "failures": 0},
        {"ip": "*************", "port": "40037", "name": "江苏盐城", "failures": 0},
        {"ip": "**************", "port": "40022", "name": "安徽池州", "failures": 5},  # 部分失败
        {"ip": "**************", "port": "40007", "name": "浙江丽水", "failures": 12}  # 已不可用
    ]
    
    print("📋 代理池状态:")
    for i, proxy in enumerate(proxies):
        status = "🟢 可用" if proxy['failures'] < 10 else "🔴 不可用"
        print(f"  {i+1}. {proxy['name']} ({proxy['ip']}:{proxy['port']}) - 失败次数: {proxy['failures']} - {status}")
    
    # 模拟代理选择
    available_proxies = [p for p in proxies if p['failures'] < 10]
    print(f"\n✅ 可用代理数量: {len(available_proxies)}/{len(proxies)}")
    
    print("\n🎲 模拟代理分配:")
    for i in range(6):
        import random
        selected_proxy = random.choice(available_proxies)
        print(f"  任务{i+1} → {selected_proxy['name']} ({selected_proxy['ip']}:{selected_proxy['port']})")

def explain_thread_benefits():
    """解释线程数量的选择"""
    print("\n💡 线程数量选择说明")
    print("=" * 60)
    
    scenarios = [
        {"threads": 1, "description": "单线程", "pros": ["稳定", "简单"], "cons": ["速度慢", "效率低"]},
        {"threads": 3, "description": "3线程（推荐）", "pros": ["平衡性好", "适中的资源占用", "明显提速"], "cons": ["略微复杂"]},
        {"threads": 5, "description": "5线程", "pros": ["速度更快"], "cons": ["资源占用高", "可能被限制"]},
        {"threads": 10, "description": "10线程", "pros": ["理论最快"], "cons": ["容易被封IP", "资源消耗大", "不稳定"]}
    ]
    
    for scenario in scenarios:
        print(f"\n🧵 {scenario['description']}:")
        print(f"   ✅ 优点: {', '.join(scenario['pros'])}")
        print(f"   ❌ 缺点: {', '.join(scenario['cons'])}")
    
    print(f"\n🎯 推荐配置:")
    print(f"   • 小规模抓取（<50场比赛）: 3线程")
    print(f"   • 中等规模抓取（50-200场）: 3-5线程") 
    print(f"   • 大规模抓取（>200场）: 5线程 + 更多代理IP")

def main():
    """主演示函数"""
    demo = ConcurrentScrapingDemo()
    
    # 性能对比演示
    demo.compare_performance()
    
    # 代理轮换演示
    demonstrate_proxy_rotation()
    
    # 线程选择说明
    explain_thread_benefits()
    
    print(f"\n🎉 演示完成！")
    print(f"💡 总结: 3线程并发模式通过同时处理多个比赛，显著提高抓取效率")

if __name__ == "__main__":
    main()

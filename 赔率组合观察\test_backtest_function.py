#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试回测功能
验证新增的回测功能是否正确工作
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from PyQt5.QtWidgets import QApplication
from odds_combination_ui import OddsCombinationMainWindow

def main():
    """启动UI进行回测功能测试"""
    print("🎯 启动赔率组合观察UI - 回测功能测试")
    print("=" * 80)
    print("📝 测试说明:")
    print("1. 选择一个数据库")
    print("2. 输入起始ID和结束ID（如：2598140 到 2598150）")
    print("3. 选择一个博彩公司（如：bet365）")
    print("4. 设置组合数为3")
    print("5. 点击'ID区间数据分析'并等待完成")
    print("6. 设置回测参数：")
    print("   - 投资阈值：1.05（默认）")
    print("   - 场次阈值：5（默认）")
    print("7. 点击'回测'按钮")
    print("")
    print("✅ 预期结果:")
    print("- 回测按钮初始状态为禁用")
    print("- ID区间分析完成后，回测按钮启用")
    print("- 点击回测后显示投资统计结果")
    print("- 包含总投资次数、胜率、投资回报率等")
    print("")
    print("🔍 回测逻辑:")
    print("- 条件：总主胜 >= 场次阈值")
    print("- 条件：(总主胜/总匹配次数) × 主胜赔率 >= 投资阈值")
    print("- 收益：命中时按赔率计算，未命中为0")
    print("- 统计：总投资、总回报、净利润、投资回报率")
    print("")
    print("📊 新增控件:")
    print("- 投资阈值输入框（小数）")
    print("- 场次阈值输入框（整数）")
    print("- 回测按钮（橙色）")
    print("")
    print("⚠️ 注意事项:")
    print("- 必须先完成ID区间分析才能进行回测")
    print("- 回测会重新获取比赛结果（解决'错误'显示问题）")
    print("- 同一场比赛可能有多个满足条件的投资")
    print("- 投资阈值和场次阈值可以调整")
    print("")
    print("🚀 正在启动UI...")
    
    app = QApplication(sys.argv)
    
    # 创建主窗口
    window = OddsCombinationMainWindow()
    window.show()
    
    # 运行应用
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()

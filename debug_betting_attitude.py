#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试博彩态度筛选问题
"""

import sqlite3
import sys
import os

def debug_betting_attitude_match():
    """调试特定比赛的博彩态度筛选"""
    print("🔍 调试博彩态度筛选问题")
    print("=" * 60)
    
    # 从截图看到的比赛ID和条件
    match_id = "2511950"  # 蒙彼利埃 vs 马赛
    target_company = "澳门"
    odds_type = "home_odds"  # 主胜
    threshold = 1.03
    
    print(f"调试比赛: {match_id}")
    print(f"目标公司: {target_company}")
    print(f"赔率类型: 主胜")
    print(f"阈值: {threshold}")
    print()
    
    try:
        with sqlite3.connect("odds_data.db") as conn:
            conn.row_factory = sqlite3.Row
            cursor = conn.cursor()
            
            # 获取比赛信息
            cursor.execute('SELECT home_team, away_team, match_time FROM matches WHERE match_id = ?', (match_id,))
            match_info = cursor.fetchone()
            if match_info:
                print(f"比赛信息: {match_info['home_team']} vs {match_info['away_team']} - {match_info['match_time']}")
            else:
                print("❌ 未找到比赛信息")
                return False
            
            # 获取澳门的开盘数据
            cursor.execute(f'''
                SELECT date, time, {odds_type}
                FROM odds
                WHERE match_id = ? AND company_name = ? AND {odds_type} IS NOT NULL
                ORDER BY date ASC, time ASC
                LIMIT 1
            ''', (match_id, target_company))
            
            target_record = cursor.fetchone()
            if not target_record:
                print(f"❌ 没有找到 {target_company} 的数据")
                return False
            
            target_date = target_record['date']
            target_time = target_record['time']
            target_odds = float(target_record[odds_type])
            
            print(f"\n{target_company} 开盘信息:")
            print(f"  时间: {target_date} {target_time}")
            print(f"  主胜赔率: {target_odds}")
            
            # 获取该时间点所有其他公司的赔率
            cursor.execute(f'''
                SELECT company_name, {odds_type}
                FROM odds
                WHERE match_id = ? AND company_name != ? AND {odds_type} IS NOT NULL
                AND date = ? AND time = ?
                ORDER BY company_name
            ''', (match_id, target_company, target_date, target_time))
            
            other_companies = cursor.fetchall()
            
            print(f"\n在 {target_company} 开盘时间点 {target_date} {target_time} 的其他公司:")
            print(f"  数量: {len(other_companies)}")
            
            if len(other_companies) == 0:
                print("  ❌ 没有其他公司在同一时间点开盘")
                
                # 查看所有公司的开盘时间
                print(f"\n所有公司的开盘时间:")
                cursor.execute(f'''
                    SELECT company_name, date, time, {odds_type}
                    FROM odds
                    WHERE match_id = ? AND {odds_type} IS NOT NULL
                    ORDER BY date ASC, time ASC, company_name
                ''', (match_id,))
                
                all_companies = cursor.fetchall()
                for record in all_companies:
                    print(f"    {record['company_name']:12s}: {record['date']} {record['time']} - {record[odds_type]}")
                
                return False
            
            # 显示其他公司详情
            other_odds_values = []
            print(f"  详情:")
            for i, record in enumerate(other_companies):
                odds_value = float(record[odds_type])
                other_odds_values.append(odds_value)
                print(f"    {i+1:2d}. {record['company_name']:12s}: {odds_value}")
            
            # 计算平均值和比率
            avg_other_odds = sum(other_odds_values) / len(other_odds_values)
            ratio = target_odds / avg_other_odds
            
            print(f"\n计算结果:")
            print(f"  {target_company} 赔率: {target_odds}")
            print(f"  其他公司平均: {avg_other_odds:.6f}")
            print(f"  比率: {target_odds} ÷ {avg_other_odds:.6f} = {ratio:.6f}")
            print(f"  阈值: {threshold}")
            print(f"  条件: {ratio:.6f} >= {threshold} = {ratio >= threshold}")
            
            if ratio >= threshold:
                print(f"  ✅ 应该通过筛选")
                return True
            else:
                print(f"  ❌ 不应该通过筛选")
                return False
                
    except Exception as e:
        print(f"❌ 调试失败: {e}")
        return False

def check_all_time_points():
    """检查所有时间点的数据"""
    print("\n" + "=" * 60)
    print("🕐 检查所有时间点的数据")
    
    match_id = "2511950"
    odds_type = "home_odds"
    
    try:
        with sqlite3.connect("odds_data.db") as conn:
            conn.row_factory = sqlite3.Row
            cursor = conn.cursor()
            
            # 获取所有时间点的数据
            cursor.execute(f'''
                SELECT DISTINCT date, time
                FROM odds
                WHERE match_id = ? AND {odds_type} IS NOT NULL
                ORDER BY date ASC, time ASC
            ''', (match_id,))
            
            time_points = cursor.fetchall()
            
            print(f"总共 {len(time_points)} 个时间点:")
            
            for i, tp in enumerate(time_points):
                date, time = tp['date'], tp['time']
                print(f"\n  时间点 {i+1}: {date} {time}")
                
                # 获取该时间点的所有公司数据
                cursor.execute(f'''
                    SELECT company_name, {odds_type}
                    FROM odds
                    WHERE match_id = ? AND {odds_type} IS NOT NULL
                    AND date = ? AND time = ?
                    ORDER BY company_name
                ''', (match_id, date, time))
                
                companies = cursor.fetchall()
                
                print(f"    公司数量: {len(companies)}")
                
                # 检查澳门是否在这个时间点
                macao_record = None
                other_records = []
                
                for record in companies:
                    if record['company_name'] == '澳门':
                        macao_record = record
                    else:
                        other_records.append(record)
                
                if macao_record and len(other_records) > 0:
                    macao_odds = float(macao_record[odds_type])
                    other_odds = [float(r[odds_type]) for r in other_records]
                    avg_other = sum(other_odds) / len(other_odds)
                    ratio = macao_odds / avg_other
                    
                    print(f"    🎯 澳门: {macao_odds}, 其他平均: {avg_other:.6f}, 比率: {ratio:.6f}")
                    
                    if ratio >= 1.03:
                        print(f"    ✅ 满足条件 (>= 1.03)")
                    else:
                        print(f"    ❌ 不满足条件 (< 1.03)")
                
                # 显示前几家公司
                for j, record in enumerate(companies[:5]):
                    print(f"      {record['company_name']:12s}: {record[odds_type]}")
                if len(companies) > 5:
                    print(f"      ... 还有 {len(companies) - 5} 家公司")
                    
    except Exception as e:
        print(f"❌ 检查失败: {e}")

def main():
    """主函数"""
    if not os.path.exists("odds_data.db"):
        print("❌ 数据库文件不存在")
        return False
    
    result = debug_betting_attitude_match()
    check_all_time_points()
    
    print("\n" + "=" * 60)
    print("📊 调试完成")
    
    return result

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)

function formatData(strnum, digit) {

    if (strnum == "") return "";

    var strnum = Math.abs(strnum).toString();

    if (strnum.indexOf(".") == -1) strnum += ".";

    var nil = ((Math.pow(10, digit + 1)).toString()).substring(1, digit);

    strnum += nil;

    var numf = parseFloat(strnum) + parseFloat("0.0" + nil + "5");

    var snum = numf.toString();

    return snum.substring(0, snum.indexOf(".") + digit + 1);

}

function miniopen(a) {

    var w = window.screen.width;

    var h = window.screen.height;

    var winWidth = 400;

    var winHeight = 600;

    var winTop = (h - winHeight) / 2;

    var winLeft = (w - winWidth) / 2;

    window.open(a, "_blank", "top=" + winTop + ",left=" + winLeft + ",height=" + winHeight + ",width=" + winWidth + ",status=yes,toolbar=auto,menubar=no,location=no");

    return false;

}

function showDate(t1, type) {

    var t2 = t1.split(",");

    var t = new Date(t2[0], eval(t2[1]), t2[2], t2[3], t2[4], t2[5]);

    var date = new Date();

    t = new Date(Date.UTC(t.getFullYear(), t.getMonth(), t.getDate(), t.getHours(), t.getMinutes(), t.getSeconds()));

    var nums = t.getTime() - date.getTime()

    var minutes = Math.floor(nums / (60 * 1000))

    var y = t.getFullYear();

    var M = t.getMonth() + 1;

    var d = t.getDate();

    var h = t.getHours();

    var m = t.getMinutes();

    if (M < 10) M = "0" + M;

    if (d < 10) d = "0" + d;

    if (h < 10) h = "0" + h;

    if (m < 10) m = "0" + m;

    if (type == 1)

        return (y + '年' + M + "月" + d + "日" + h + ":" + m);

    else {

        if (minutes < 0 && minutes >= -30)

            return ("<font style='color:red;'>" + M + "-" + d + " " + h + ":" + m + "</font>");

        else

            return (M + "-" + d + " " + h + ":" + m);

    }

}

Array.prototype.del = function (n) {

    return this.slice(0, n).concat(this.slice(n + 1, this.length));

}

function Hashtable() {

    this._hash = new Object();

    this.add = function (key, value) {

        if (typeof (key) != "undefined") {

            this._hash[key] = typeof (value) == "undefined" ? null : value;

            return true;

        }

        else

            return false;

    }

    this.remove = function (key) { delete this._hash[key]; }

    this.keys = function () {

        var keys = new Array();

        for (var key in this._hash) {

            keys.push(key);

        }

        return keys;

    }

    this.count = function () { var i = 0; for (var k in this._hash) { i++; } return i; }

    this.items = function (key) { return this._hash[key]; }

    this.contains = function (key) {

        return typeof (this._hash[key]) != "undefined";

    }

    this.clear = function () { for (var k in this._hash) { delete this._hash[k]; } }

}

function OddsHistory(theURL) {

    //    window.open(theURL, "", "width=470,height=280,top=40,left=100,resizable=yes,scrollbars=yes");

    window.open(theURL);

}

var mName = "", hName = "", gName = "";

if (lang == 1) {

    mName = typeof (matchname_cn) == "undefined" ? "" : matchname_cn;

    hName = typeof (hometeam_cn) == "undefined" ? "" : hometeam_cn;

    gName = typeof (guestteam_cn) == "undefined" ? "" : guestteam_cn;

} else {

    mName = typeof (matchname_f) == "undefined" ? typeof (matchname_cn) == "undefined" ? "" : matchname_cn : matchname_f;

    hName = typeof (hometeam_f) == "undefined" ? typeof (hometeam_cn) == "undefined" ? "" : hometeam_cn : hometeam_f;

    gName = typeof (guestteam_f) == "undefined" ? typeof (guestteam_cn) == "undefined" ? "" : guestteam_cn : guestteam_f;

}

var hsDetail = new Hashtable();

var min = new Array(23);

var max = new Array(23);

var avg = new Array(23);

var odernum = Array(true, true, true, true, true, true, true, true,true); //排序初始化数组

var showType = 3; //1:所有赔率 2:初盘 3:即时

var dataNum = 0;

var numcount;

var allCount = 0;

var imgName = "";

var companyType = 0;

var showCountNum = 0;

var haveJcLetgoal = false;

var tempgame = new Array();

if (typeof (game) != "undefined") {

    if (typeof (jcEuropeOddsData) != "undefined" && jcEuropeOddsData != "") {

        var tempData = jcEuropeOddsData.split('|');

        var tempIndex = -1;

        if (tempData.length > 24 && tempData[24]!="") {//判断是否放指定ID后面，没有的话放在首位

            for (var i = 0; i < game.length; i++) {

                var tempStr = game[i].split("|");

                if (tempStr[0] == tempData[24]) {

                    tempIndex = i + 1;

                    break;

                }

            }

        }

        //else {

        //    for (var i = 0; i < game.length; i++) {

        //        var tempStr = game[i].split("|");

        //        if (tempStr[22] != "1" && i > 0) {

        //            tempIndex = i - 1;

        //            break;

        //        }

        //    }

        //}

        tempIndex = tempIndex == -1 ? 0 : tempIndex;

        game.splice(tempIndex, 0, jcEuropeOddsData);

        haveJcLetgoal = true;

    }

    tempgame = game;

    allCount = game.length;

}

var isFloatCookie = getCookie("oddFloatDiv");

var haveKellyFirstGoal = typeof (jsVersion) != 'undefined';//是否有凯利初盘数据

var isFirstLoad = true;

var firstTotalAvg = [0, 0, 0, 0, 0, 0, 0, 0, 0, 0];

var realTotalAvg = [0, 0, 0, 0, 0, 0, 0, 0, 0, 0];

function CreateTable() {

    

    winResult = new qtResult();

    drawResult = new qtResult();

    loseResult = new qtResult();

    for (var i = 0; i < 23; i++) {

        min[i] = 100;

        max[i] = 0;

        avg[i] = 0;

    }

    var totalHtml = new Array();

    var html = new Array();

    var strSclass = "";

    var theURL = "";

    var strname = "";

    dataNum = 0;

    showCountNum = 0;

    totalHtml.push('<table width="1080" border="0" cellspacing="0" cellpadding="1" class="tcenter" id="oddsList_tab" style="line-height:' + (showType == 1 ? "20" : "30") + 'px;">');

    for (var i = 0; i < game.length; i++) {

        if (i % 2 != 0)

            strSclass = "tbg2";

        else

            strSclass = "";

        stringq = game[i].split("|");

        showCountNum++;

        imgName = (stringq[22] == "1" ? "/images/z.png" : stringq[23] == "1" ? "/images/e.png" : "");

        strname = (stringq[22] == "1" ? "主流公司" : stringq[23] == "1" ? "交易所" : "");

        var tdWidthList = "66,66,66,auto,auto,auto,auto".split(',');

        if (stringq[0] == "5000") {//竞彩让球单独处理

            //0公司ID,固定ID为5000|1赔率ID||3第一条胜赔率|4第一条平赔率|5第一条负赔率|||||10最新一条胜赔率|11最新一条平赔率|12最新一条负赔率|||||||19盘口|20更新时间|21公司名称|22是否主流|23是否交易所

            html.push('<tr' + (strSclass != "" ? " class=" + strSclass : "") + ' id=oddstr_' + stringq[0] + '>');

            html.push('<td width="35" ' + (showType == 1 ? "rowspan=\"2\"" : "") + ' class="lb rb"><input type=checkbox name=Show value=' + i + ' ></td>');

            html.push('<td width="240" ' + (showType == 1 ? "rowspan=\"2\"" : "") + ' class="rb com-big-f" style="text-align:left;padding-left:6px;">' + stringq[21] + (imgName != "" ? '<a title="' + strname + '"> <img style="vertical-align:middle;" src="' + imgName + '" width="12" height="12"/>' : '') + '</a>');

            html.push('<span style="float:right;padding-right:6px"><b class="' + (parseFloat(stringq[19]) > 0 ? "o_red" : "o_green") + '">' + (parseFloat(stringq[19]) > 0 ? "+" : "") + stringq[19] + '</b></span></td>');

            for (var j = 3; j < 10; j++) {

                if (showType == 3 && stringq[j + 7] != "") {

                    var odds = Number(stringq[j + 7]);

                    var old = Number(stringq[j]);

                    if (odds > old && j < 6)

                        html.push("<td width=" + tdWidthList[j - 3] + ((j == 5 || j == 9) ? " class=\"rb\"" : "") + " style='cursor:pointer;' onmouseover=\"showtips(event," + i + ",this)\" onmouseout=\"hidetips(this);\"><span class=\"o_red\">" + formatData(odds, 2) + "</span></td>");

                    else if (odds < old && j < 6)

                        html.push("<td width=" + tdWidthList[j - 3] + ((j == 5 || j == 9) ? " class=\"rb\"" : "") + " style='cursor:pointer;' onmouseover=\"showtips(event," + i + ",this)\" onmouseout=\"hidetips(this);\"><span class=\"o_green\">" + formatData(odds, 2) + "</span></td>");

                    else

                        html.push("<td width=" + tdWidthList[j - 3] + ((j == 5 || j == 9) ? " class=\"rb\"" : "") + (j < 6 ? " style='cursor:pointer;'" + " onmouseover=\"showtips(event," + i + ",this)\" onmouseout=\"hidetips(this);\"" : "") + ">" + formatData(odds, 2) + "</td>");

                }

                else

                    html.push("<td width=" + tdWidthList[j - 3] + ((j == 5 || j == 9) ? " class=\"rb\"" : "") + (j < 6 ? " style='cursor:pointer;'" : "") + ">" + formatData(stringq[j], 2) + "</td>");

            }

            html.push('<td width="50" ' + (showType == 1 ? "rowspan=\"2\"" : "") + '></td>');

            html.push('<td width="50" ' + (showType == 1 ? "rowspan=\"2\"" : "") + '></td>');

            html.push('<td width="50" ' + (showType == 1 ? "rowspan=\"2\"" : "") + ' class="rb"></td>');

            var time = (showType == 2 ? stringq[stringq.length - 1].substring(5) : showDate(stringq[20], 2));//初盘时取初盘时间

            html.push('<td width="90" ' + (showType == 1 ? "rowspan=\"2\"" : "") + ' class="rb time">' + time + '</td>');

            html.push("<td width=\"100\"" + (showType == 1 ? "rowspan=\"2\"" : "") + " class=\"rb\"></td>");

            html.push('</tr>');

            if (showType == 1) {

                html.push('<tr' + (strSclass != "" ? " class=" + strSclass : "") + '>');

                if (stringq[10] != "") {

                    for (var j = 10; j < 13; j++) {

                        var stra = Number(stringq[j]);

                        var strb = Number(stringq[j - 7]);

                        if (stra > strb)

                            html.push("<td" + (j == 12 ? " class=\"rb\"" : "") + " style='cursor:pointer;' onmouseover=\"showtips(event," + i + ",this)\" onmouseout=\"hidetips(this);\"><span class=\"o_red\">" + formatData(stringq[j], 2) + "</span></td>");

                        else if (stra < strb)

                            html.push("<td" + (j == 12 ? " class=\"rb\"" : "") + " style='cursor:pointer;' onmouseover=\"showtips(event," + i + ",this)\" onmouseout=\"hidetips(this);\"><span class=\"o_green\">" + formatData(stringq[j], 2) + "</span></td>");

                        else

                            html.push("<td" + (j == 12 ? " class=\"rb\"" : "") + " style='cursor:pointer;' onmouseover=\"showtips(event," + i + ",this)\" onmouseout=\"hidetips(this);\">" + formatData(stringq[j], 2) + "</td>");

                    }

                    html.push('<td></td>');

                    html.push('<td></td>');

                    html.push('<td></td>');

                    html.push('<td class="rb"></td>');

                }

                else {

                    html.push('<td>&nbsp;</td><td>&nbsp;</td><td class="rb">&nbsp;</td>');

                    html.push('<td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td class="rb">&nbsp;</td>');

                }

                html.push('</tr>');

            }

            continue;

        }



        theURL = "/OddsHistory.aspx?id=" + stringq[1] + "&sid=" + ScheduleID + "&cid=" + stringq[0] + "&l=" + (lang - 1);



        //第一行初盘数据

        html.push('<tr' + (strSclass != "" ? " class=" + strSclass : "") + ' id=oddstr_' + stringq[0] + '>');

        html.push('<td width="35" ' + (showType == 1 ? "rowspan=\"2\"" : "") + ' class="lb rb"><input type=checkbox name=Show value=' + i + ' ></td>');

        html.push('<td width="240" ' + (showType == 1 ? "rowspan=\"2\"" : "") + ' class="rb com-big-f" style="text-align:left;padding-left:6px;">' + stringq[21] + (imgName != "" ? '<a title="' + strname + '"> <img style="vertical-align:middle;" src="' + imgName + '" width="12" height="12"/>' : '') + '</a></td>');

        

        for (var j = 3; j < 10; j++) {

            var strNum = showType < 3 ? stringq[j] : stringq[j + 7];

            if (showType == 3 && stringq[j + 7] != "") {

                var odds = Number(stringq[j + 7]);

                var old = Number(stringq[j]);

                if (odds > old && j < 6)

                    html.push("<td width=" + tdWidthList[j - 3] + ((j == 5 || j == 9) ? " class=\"rb\"" : "") + " onclick=\"OddsHistory('" + theURL + "')\"  style='cursor:pointer;' onmouseover=\"showtips(event," + i + ",this)\" onmouseout=\"hidetips(this);\"><span class=\"o_red\">" + formatData(odds, 2) + "</span></td>");

                else if (odds < old && j < 6)

                    html.push("<td width=" + tdWidthList[j - 3] + ((j == 5 || j == 9) ? " class=\"rb\"" : "") + " onclick=\"OddsHistory('" + theURL + "')\"  style='cursor:pointer;' onmouseover=\"showtips(event," + i + ",this)\" onmouseout=\"hidetips(this);\"><span class=\"o_green\">" + formatData(odds, 2) + "</span></td>");

                else

                    html.push("<td width=" + tdWidthList[j - 3] + ((j == 5 || j == 9) ? " class=\"rb\"" : "") + (j < 6 ? " onclick=\"OddsHistory('" + theURL + "')\"  style='cursor:pointer;'" + " onmouseover=\"showtips(event," + i + ",this)\" onmouseout=\"hidetips(this);\"" : "") + ">" + formatData(odds, 2) + "</td>");

            }

            else

                html.push("<td width=" + tdWidthList[j - 3] + ((j == 5 || j == 9) ? " class=\"rb\"" : "") + (j < 6 ? " onclick=\"OddsHistory('" + theURL + "')\"  style='cursor:pointer;'" : "") + ">" + formatData(stringq[j], 2) + "</td>");

        }

        if (!haveKellyFirstGoal) {//旧版没有凯利初盘数据，需要兼容

            html.push('<td width="50" ' + (showType == 1 ? "rowspan=\"2\"" : "") + '>' + (parseFloat(stringq[17]) >= 1 ? "<b style=\"color:red;\">" : "") + formatData(stringq[17], 2) + (parseFloat(stringq[17]) >= 1 ? "</b>" : "") + '</td>');

            html.push('<td width="50" ' + (showType == 1 ? "rowspan=\"2\"" : "") + '>' + (parseFloat(stringq[18]) >= 1 ? "<b style=\"color:red;\">" : "") + formatData(stringq[18], 2) + (parseFloat(stringq[18]) >= 1 ? "</b>" : "") + '</td>');

            html.push('<td width="50" ' + (showType == 1 ? "rowspan=\"2\"" : "") + ' class="rb">' + (parseFloat(stringq[19]) >= 1 ? "<b style=\"color:red;\">" : "") + formatData(stringq[19], 2) + (parseFloat(stringq[19]) >= 1 ? "</b>" : "") + '</td>');

        }

        else {

            if (showType == 3) {//显示即时盘数据

                html.push('<td width="50">' + (parseFloat(stringq[17]) >= 1 ? "<b style=\"color:red;\">" : "") + formatData(stringq[17], 2) + (parseFloat(stringq[17]) >= 1 ? "</b>" : "") + '</td>');

                html.push('<td width="50" >' + (parseFloat(stringq[18]) >= 1 ? "<b style=\"color:red;\">" : "") + formatData(stringq[18], 2) + (parseFloat(stringq[18]) >= 1 ? "</b>" : "") + '</td>');

                html.push('<td width="50" class="rb">' + (parseFloat(stringq[19]) >= 1 ? "<b style=\"color:red;\">" : "") + formatData(stringq[19], 2) + (parseFloat(stringq[19]) >= 1 ? "</b>" : "") + '</td>');

            }

            else {//初盘数据

                html.push('<td width="50">' + (parseFloat(stringq[24]) >= 1 ? "<b style=\"color:red;\">" : "") + formatData(stringq[24], 2) + (parseFloat(stringq[24]) >= 1 ? "</b>" : "") + '</td>');

                html.push('<td width="50" >' + (parseFloat(stringq[25]) >= 1 ? "<b style=\"color:red;\">" : "") + formatData(stringq[25], 2) + (parseFloat(stringq[25]) >= 1 ? "</b>" : "") + '</td>');

                html.push('<td width="50" class="rb">' + (parseFloat(stringq[26]) >= 1 ? "<b style=\"color:red;\">" : "") + formatData(stringq[26], 2) + (parseFloat(stringq[26]) >= 1 ? "</b>" : "") + '</td>');

            }

        }

        var time = (showType == 2 ? stringq[stringq.length - 1].substring(5) : showDate(stringq[20], 2));//初盘时取初盘时间

        html.push('<td width="90" ' + (showType == 1 ? "rowspan=\"2\"" : "") + ' class="rb time">' + time + '</td>');

        html.push("<td width=\"100\"" + (showType == 1 ? "rowspan=\"2\"" : "") + " class=\"rb\"><a href='http://vip.titan007.com/count/goalCount.aspx?t=5&sid=" + ScheduleID + "&cid=" + stringq[0] + "' target='_blank' title='" + (lang == 1 ? "盘口统计" : "盤口統計") + "'>统</a> <a href='/" + (lang == 1 ? "list.aspx" : "list_big.aspx") + "?id=" + ScheduleID + "&id2=" + stringq[0] + "&Company=" + stringq[2] + "&rid=" + hometeamID + "' target=_blank>主</A> <A href='../" + (lang == 1 ? "list.aspx" : "list_big.aspx") + "?id=" + ScheduleID + "&id2=" + stringq[0] + "&Company=" + stringq[2] + "&rid=" + guestteamID + "' target=_blank>客</A> <A href='../" + (lang == 1 ? "list2.aspx" : "list2_big.aspx") + "?id=" + ScheduleID + "&id2=" + stringq[0] + "&Company=" + stringq[2] + "&Win=" + stringq[3] + "&Draw=" + stringq[4] + "&Lost=" + stringq[5] + "' target=_blank>同</A></td>");

        html.push('</tr>');

        //第二行，即时数据

        if (showType == 1) {

            html.push('<tr' + (strSclass != "" ? " class=" + strSclass : "") + '>');

            if (stringq[10] != "") {

                for (var j = 10; j < 13; j++) {

                    var stra = Number(stringq[j]);

                    var strb = Number(stringq[j - 7]);

                    if (stra > strb)

                        html.push("<td" + (j == 12 ? " class=\"rb\"" : "") + " onclick=\"OddsHistory('" + theURL + "')\"  style='cursor:pointer;' onmouseover=\"showtips(event," + i + ",this)\" onmouseout=\"hidetips(this);\"><span class=\"o_red\">" + formatData(stringq[j], 2) + "</span></td>");

                    else if (stra < strb)

                        html.push("<td" + (j == 12 ? " class=\"rb\"" : "") + " onclick=\"OddsHistory('" + theURL + "')\"  style='cursor:pointer;' onmouseover=\"showtips(event," + i + ",this)\" onmouseout=\"hidetips(this);\"><span class=\"o_green\">" + formatData(stringq[j], 2) + "</span></td>");

                    else

                        html.push("<td" + (j == 12 ? " class=\"rb\"" : "") + " onclick=\"OddsHistory('" + theURL + "')\"  style='cursor:pointer;' onmouseover=\"showtips(event," + i + ",this)\" onmouseout=\"hidetips(this);\">" + formatData(stringq[j], 2) + "</td>");

                }

                html.push('<td>' + formatData(stringq[13], 2) + '</td>');

                html.push('<td>' + formatData(stringq[14], 2) + '</td>');

                html.push('<td>' + formatData(stringq[15], 2) + '</td>');

                html.push('<td class="rb">' + formatData(stringq[16], 2) + '</td>');

            }

            else {

                html.push('<td>&nbsp;</td><td>&nbsp;</td><td class="rb">&nbsp;</td>');

                html.push('<td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td class="rb">&nbsp;</td>');

            }

			 if (haveKellyFirstGoal){

            //凯利指数即时盘

            html.push('<td width="50">' + (parseFloat(stringq[17]) >= 1 ? "<b style=\"color:red;\">" : "") + formatData(stringq[17], 2) + (parseFloat(stringq[17]) >= 1 ? "</b>" : "") + '</td>');

            html.push('<td width="50" >' + (parseFloat(stringq[18]) >= 1 ? "<b style=\"color:red;\">" : "") + formatData(stringq[18], 2) + (parseFloat(stringq[18]) >= 1 ? "</b>" : "") + '</td>');

            html.push('<td width="50" class="rb">' + (parseFloat(stringq[19]) >= 1 ? "<b style=\"color:red;\">" : "") + formatData(stringq[19], 2) + (parseFloat(stringq[19]) >= 1 ? "</b>" : "") + '</td>');

			 }

            html.push('</tr>');

        }

        for (var j = 3; j <= 19; j++) {

            if (j == 10) {

                winResult.upCount += parseFloat(stringq[10]) > parseFloat(stringq[j - 7]) ? 1 : 0;

                winResult.drawCount += parseFloat(stringq[10]) == parseFloat(stringq[j - 7]) ? 1 : 0;

                winResult.downCount += parseFloat(stringq[10]) < parseFloat(stringq[j - 7]) ? 1 : 0;

            } else if (j == 11) {

                drawResult.upCount += parseFloat(stringq[j]) > parseFloat(stringq[j - 7]) ? 1 : 0;

                drawResult.drawCount += parseFloat(stringq[j]) == parseFloat(stringq[j - 7]) ? 1 : 0;

                drawResult.downCount += parseFloat(stringq[j]) < parseFloat(stringq[j - 7]) ? 1 : 0;

            } else if (j == 12) {

                loseResult.upCount += parseFloat(stringq[j]) > parseFloat(stringq[j - 7]) ? 1 : 0;

                loseResult.drawCount += parseFloat(stringq[j]) == parseFloat(stringq[j - 7]) ? 1 : 0;

                loseResult.downCount += parseFloat(stringq[j]) < parseFloat(stringq[j - 7]) ? 1 : 0;

            }



            if (stringq[j] == "") stringq[j] = stringq[j - 7];

            var stra = Number(stringq[j]);

            if (stra < min[j]) min[j] = stra;

            if (stra > max[j]) max[j] = stra;

            avg[j] += stra;

            

        }

        if (haveKellyFirstGoal) {

            for (j = 20; j < 23; j++) {//获取凯利指数初盘最大小传值和总数

                var stra = Number(stringq[j + 4]);

                if (stra < min[j]) min[j] = stra;

                if (stra > max[j]) max[j] = stra;

                avg[j] += stra;

            }

        }

        dataNum++;

    }

    if (isFirstLoad) {

        for (var i = 0; i < 10; i++) {//求所有公司的平均值

            firstTotalAvg[i] = avg[i + 3] / dataNum;

            realTotalAvg[i] = avg[i + 10] / dataNum;

            if (i > 2) {

                if (i > 6) {

                    firstTotalAvg[i] = GetAvgKelly(firstTotalAvg[i - 4], formatData(firstTotalAvg[i - 7], 3));

                    realTotalAvg[i] = GetAvgKelly(realTotalAvg[i - 4], formatData(realTotalAvg[i - 7], 3));

                }

                else {

                    firstTotalAvg[i] = GetAvgRate(formatData(avg[3] / dataNum, 2), formatData(avg[4] / dataNum, 2), formatData(avg[5] / dataNum, 2), i - 3);

                    realTotalAvg[i] = GetAvgRate(formatData(avg[10] / dataNum, 2), formatData(avg[11] / dataNum, 2), formatData(avg[12] / dataNum, 2), i - 3);

                }

            }

        }

        isFirstLoad = false;

    }

    var avgHtml = allCount != game.length ? getTotalAvgTr() : "";



    html.push("</table>");

    totalHtml.push(avgHtml);

    totalHtml.push(html.join(""));

    JQ("#dataList").html(totalHtml.join(""));

    setFilter();

    InitPie();

}

function getTotalAvgTr() {

    var tdWidthList = "66,66,66,auto,auto,auto,auto".split(',');

    var html = new Array();

    html.push('<tr class="bg2"  id="oddstr_10001">');

    html.push('<td width="35" ' + (showType == 1 ? "rowspan=\"2\"" : "") + ' class="lb rb"></td>');//<input type=checkbox name=Show value="10001">

    html.push('<td width="240" ' + (showType == 1 ? "rowspan=\"2\"" : "") + ' class="rb com-big-f" style="text-align:left;padding-left:6px;">' + (haveJcLetgoal ? (allCount - 1) : allCount) + '家平均(本站)</td>');

        for (var i = 0; i < 7; i++) {

            if (showType == 3) {

                var odds = Number(realTotalAvg[i]);

                var old = Number(firstTotalAvg[i]);

                if (odds > old && i < 3)

                    html.push("<td width=" + tdWidthList[i] + ((i == 2 || i == 6) ? " class=\"rb\"" : "") + "<span class=\"o_red\">" + formatData(odds, 2) + "</span></td>");

                else if (odds < old && i < 3)

                    html.push("<td width=" + tdWidthList[i] + ((i == 2 || i == 6) ? " class=\"rb\"" : "") + " <span class=\"o_green\">" + formatData(odds, 2) + "</span></td>");

                else

                    html.push("<td width=" + tdWidthList[i] + ((i == 2 || i == 6) ? " class=\"rb\"" : "")  + ">" + formatData(odds, 2) + "</td>");

            }

            else

                html.push("<td width=" + tdWidthList[i] + ((i == 2 || i == 6) ? " class=\"rb\"" : "") + ">" + formatData(firstTotalAvg[i], 2) + "</td>");

        }



    //凯利指数

    var tempHome = showType == 3 ? realTotalAvg[7] : firstTotalAvg[7];

    var tempDraw = showType == 3 ? realTotalAvg[8] : firstTotalAvg[8];

    var tempGuest = showType == 3 ? realTotalAvg[9] : firstTotalAvg[9];

    html.push('<td width="50">' + (parseFloat(tempHome) >= 1 ? "<b style=\"color:red;\">" : "") + tempHome + (parseFloat(tempHome) >= 1 ? "</b>" : "") + '</td>');

    html.push('<td width="50" >' + (parseFloat(tempDraw) >= 1 ? "<b style=\"color:red;\">" : "") + tempDraw + (parseFloat(tempDraw) >= 1 ? "</b>" : "") + '</td>');

    html.push('<td width="50" class="rb">' + (parseFloat(tempGuest) >= 1 ? "<b style=\"color:red;\">" : "") + tempGuest + (parseFloat(tempGuest) >= 1 ? "</b>" : "") + '</td>');

    html.push('<td width="90" ' + (showType == 1 ? "rowspan=\"2\"" : "") + ' class="rb time"></td>');

    html.push("<td width=\"100\"" + (showType == 1 ? "rowspan=\"2\"" : "") + " class=\"rb\"></td>");

    html.push('</tr>');



    if (showType == 1) {

        html.push('<tr>');

        for (var i = 0; i < 7; i++) {

            var odds = Number(realTotalAvg[i]);

            var old = Number(firstTotalAvg[i]);

            if (odds > old && i < 3)

                html.push("<td" + ((i == 2 || i == 6) ? " class=\"rb\"" : "") + "><span class=\"o_red\">" + formatData(odds, 2) + "</span></td>");

            else if (odds < old && i < 3)

                html.push("<td" + ((i == 2 || i == 6) ? " class=\"rb\"" : "") + "><span class=\"o_green\">" + formatData(odds, 2) + "</span></td>");

            else

                html.push("<td" + ((i == 2 || i == 6) ? " class=\"rb\"" : "") + ">" + formatData(odds, 2) + "</td>");

        }

        //凯利指数即时平均值

        tempHome = realTotalAvg[7];

        tempDraw = realTotalAvg[8];

        tempGuest = realTotalAvg[9];

        html.push('<td width="50">' + (parseFloat(tempHome) >= 1 ? "<b style=\"color:red;\">" : "") + tempHome + (parseFloat(tempHome) >= 1 ? "</b>" : "") + '</td>');

        html.push('<td width="50">' + (parseFloat(tempDraw) >= 1 ? "<b style=\"color:red;\">" : "") + tempDraw + (parseFloat(tempDraw) >= 1 ? "</b>" : "") + '</td>');

        html.push('<td width="50" class="rb">' + (parseFloat(tempGuest) >= 1 ? "<b style=\"color:red;\">" : "") + tempGuest + (parseFloat(tempGuest) >= 1 ? "</b>" : "") + '</td>');

        //html.push('<td class="rb"></td>');





        html.push('</tr>');

    }



    return html.join("");

}

function companyFilter(t) {

    var kindName = new Array("所有", "主流", "交易所", "非交易所");

    setCookie('setting.solution', '');

    companyType = t;

    var string = "";

    var gametemp = new Array();

    game = tempgame;

    for (var j = 0; j < game.length; j++) {

        Comparisonstr1 = game[j].split("|");

        if (t == 0 || (t == 1 && Number(Comparisonstr1[22]) == 1) || (t == 2 && Number(Comparisonstr1[23]) == 1) || (t == 3 && Number(Comparisonstr1[23]) == 0)) {

            string = string + "@" + game[j];

        }

    }

    gametemp = string.split("@");

    gametemp = gametemp.del(0);

    game = gametemp;

    gametemp = "";

    document.getElementById("chkall").checked = false;

    w();

    odds.init();

    JQ("#a_companySelect").html(kindName[t]);

}

function dataFiletr() {

    var filterObj = JQ("#tab_MinMax");

    var trObj1 = filterObj.find("tr").eq(0);

    var trObj2 = filterObj.find("tr").eq(1);

    var minF = new Array(10);

    var maxF = new Array(10);

    var string = "";

    minF = getFilterData(trObj1.find("td"));

    maxF = getFilterData(trObj2.find("td"));

    var gametemp = new Array();

    game = tempgame;

    for (var j = 0; j < game.length; j++) {

        Comparisonstr1 = game[j].split("|");

        if (isSaveData(Comparisonstr1, minF, maxF)) {

            string = string + "@" + game[j];

        }

    }

    gametemp = string.split("@");

    gametemp = gametemp.del(0);

    game = gametemp;

    gametemp = "";

    w();

}

function isSaveData(arrData, min, max) {

    var isSave = true;

    for (var i = 0; i < min.length; i++) {

        if (max[i] == "" && min[i] == "") continue;

        var chekData = showType == 2 ? arrData[i + 3] : arrData[i + 10];

        chekData = chekData == "" ? arrData[i + 3] : chekData;

        if (min[i] != "" && max[i] != "" && (parseFloat(chekData) < parseFloat(min[i]) || parseFloat(chekData) > parseFloat(max[i])))

            isSave = false;

        else if (min[i] != "" && max[i] == "" && parseFloat(chekData) < parseFloat(min[i]))

            isSave = false;

        else if (min[i] == "" && max[i] != "" && parseFloat(chekData) > parseFloat(max[i]))

            isSave = false;

    }

    return isSave;

}

function getFilterData(tdObj) {

    var arr = new Array(10);

    for (var i = 0; i < tdObj.length; i++) {

        var input = tdObj.eq(i).find("input");

        if (tdObj.length > 12 && i < 12 && i >= 2)

            arr[i - 2] = input.eq(0).attr("value")

        else if (tdObj.length < 12 && i >= 1)

            arr[i - 1] = input.eq(0).attr("value")

    }

    return arr;

}

function clearFilter() {

    var filterObj = JQ("#tab_MinMax");

    var tdObj1 = filterObj.find("tr").eq(0).find("td");

    var tdObj2 = filterObj.find("tr").eq(1).find("td");

    for (var i = 0; i < tdObj1.length; i++) {

        if (i >= 2 && i < 12) {

            var input = tdObj1.eq(i).find("input");

            input.eq(0).attr("value", "");

        }

    }

    for (var i = 0; i < tdObj2.length; i++) {

        if (i >= 1) {

            var input = tdObj2.eq(i).find("input");

            input.eq(0).attr("value", "");

        }

    }

}

function setCount() {

    numcount = dataNum;

    var highFTr = JQ("#highFObj");

    var highRTr = JQ("#highRObj");

    var lowFTr = JQ("#lowFObj");

    var lowRTr = JQ("#lowRObj");

    var avgFTr = JQ("#avgFObj");

    var avgRTr = JQ("#avgRObj");



    var firstAvg = [0, 0, 0, 0];//平均主胜率，平均和率，平均客胜率，平均返回率

    var realAvg = [0, 0, 0, 0];

    for (var i = 3; i < 23; i++) {

        if (i < 10) {

            highFTr.find("td").eq(i - 1).html(numcount > 0 ? formatData(max[i], 2) : "&nbsp;");

            lowFTr.find("td").eq(i - 2).html(numcount > 0 ? formatData(min[i], 2) : "&nbsp;");

            var avgData = formatData(avg[i] / numcount, 2);

            if (i > 5 && i < 10) {

                //实盘平均值用公式计算

                avgData = GetAvgRate(formatData(avg[3] / numcount, 2), formatData(avg[4] / numcount, 2), formatData(avg[5] / numcount, 2), i-6);

                firstAvg[i - 6] = avgData;

            }

            avgFTr.find("td").eq(i - 2).html(numcount > 0 ? avgData : "&nbsp;");

        }

        else if (i < 17) {

            var strMax = (max[i] > max[i - 7] ? "<span class=\"o_red\">" + formatData(max[i], 2) + "</span>" : max[i] < max[i - 7] ? "<span class=\"o_green\">" + formatData(max[i], 2) + "</span>" : max[i]);

            var strMin = (min[i] > min[i - 7] ? "<span class=\"o_red\">" + formatData(min[i], 2) + "</span>" : min[i] < min[i - 7] ? "<span class=\"o_green\">" + formatData(min[i], 2) + "</span>" : min[i]);

            highRTr.find("td").eq(i - 9).html(numcount > 0 ? i < 13 ? strMax : formatData(max[i], 2) : "&nbsp;");

            lowRTr.find("td").eq(i - 9).html(numcount > 0 ? i < 13 ? strMin : formatData(min[i], 2) : "&nbsp;");

            var avgR = formatData(avg[i] / numcount, 2);

            var avgF = formatData(avg[i - 7] / numcount, 2);

            if (i > 12 && i < 17) {

                //即时平均值用公式计算，用各公司平均值再除公司数计算会不够精确

                avgR = GetAvgRate(formatData(avg[10] / numcount, 2), formatData(avg[11] / numcount, 2), formatData(avg[12] / numcount, 2), i-13);

                avgF = firstAvg[i - 13];

                realAvg[i - 13] = avgR;

            }

            var strAvg = (avgR > avgF ? "<span class=\"o_red\">" + avgR + "</span>" : avgR < avgF ? "<span class=\"o_green\">" + avgR + "</span>" : avgR);

            avgRTr.find("td").eq(i - 9).html(numcount > 0 ? i < 13 ? strAvg : avgR : "&nbsp;");

        }

        else {

            if (i < 20) {

                highRTr.find("td").eq(i - 9).html(numcount > 0 ? formatData(max[i], 2) : "&nbsp;");

                lowRTr.find("td").eq(i - 9).html(numcount > 0 ? formatData(min[i], 2) : "&nbsp;");

                var avgKelly = GetAvgKelly(realAvg[i - 17], formatData(avg[i - 7] / numcount, 3));

                avgRTr.find("td").eq(i - 9).html(numcount > 0 ? avgKelly : "&nbsp;");

            }

            else {

                if (haveKellyFirstGoal) {

                    highFTr.find("td").eq(i - 11).html(numcount > 0 ? formatData(max[i], 2) : "&nbsp;");

                    lowFTr.find("td").eq(i - 12).html(numcount > 0 ? formatData(min[i], 2) : "&nbsp;");

                    var avgKelly = GetAvgKelly(firstAvg[i - 20], formatData(avg[i-17] / numcount, 3));

                    avgFTr.find("td").eq(i - 12).html(numcount > 0 ? avgKelly : "&nbsp;");

                }

            }

            //avgFTr.find("td").eq(i - 9).html(numcount > 0 ? formatData(avg[i] / numcount, 2) : "&nbsp;");

        }

    }

}

function GetAvgRate(homewin, standoff, guestwin, type) {

    var val = 0;

    switch (type) {

        case 0:

            val = (1 / (1 + homewin / standoff + homewin / guestwin)) * 100.0; //主胜率

            break;

        case 1:

            val = (1 / (1 + standoff / homewin + standoff / guestwin)) * 100.0;//和率

            break;

        case 2:

            val = (1 / (1 + guestwin / homewin + guestwin / standoff)) * 100.0;//客赢率

            break;

        case 3:

            val = (1 / (1 + homewin / standoff + homewin / guestwin)) * 100.0 * homewin;//返还率

            break;

    }

    val = formatData(val, 2);

    return val;

}

function GetAvgKelly(avgOdds,odds) {

    return formatData(avgOdds * odds / 100.0, 3)

}

function setFilter() {

    numcount = dataNum;

    var filterObj = JQ("#tab_MinMax");

    var tdObj1 = filterObj.find("tr").eq(0).find("td");

    var tdObj2 = filterObj.find("tr").eq(1).find("td");

    for (var i = 0; i < tdObj1.length; i++) {

        if (i >= 2 && i < 12) {

            var input = tdObj1.eq(i).find("input");

            if (i < 9)

                input.eq(0).attr("value", numcount > 0 ? formatData(min[i + 1], 2) : "&nbsp;");

            else

                input.eq(0).attr("value", numcount > 0 ? formatData(min[i + 8], 2) : "&nbsp;");

        }

    }

    for (var i = 0; i < tdObj2.length; i++) {

        if (i >= 1) {

            var input = tdObj2.eq(i).find("input");

            if (i < 8)

                input.eq(0).attr("value", numcount > 0 ? formatData(max[i + 2], 2) : "&nbsp;");

            else

                input.eq(0).attr("value", numcount > 0 ? formatData(max[i + 9], 2) : "&nbsp;");

        }

    }

}

function changeShowType(t) {

    showType = t;

    setCookie('1x2showType', showType);

    document.getElementById("chkall").checked = false;

    CreateTable();

    if (lastSetNum != lastSetNum2)

        oderlist(lastSetNum, lastSetNum2, false);

}

function CheckAll() {

    JQ("#dataList").find("input[type='checkbox']").each(function () { this.checked = !this.checked; });

}

function delCheck(isSave) {

    companyType = 0;

    if (getCookie("setting.solution") != "") {

        setCookie('setting.solution', '');

        //        odds.init();

    }

    var string = "", numlist;

    var gametemp = new Array();

    var i = 0, selectCount = 0;

    JQ("#dataList").find("input[type='checkbox']").each(function () {

        if (this.checked == isSave) {

            string = string + "@" + game[i];

            selectCount += isSave ? 1 : 0;

        }

        else

            selectCount += !isSave ? 1 : 0;

        i++;

    });

    if (selectCount == 0) {

        alert(lang == 1 ? "请先选择公司" : "請先選擇公司");

        return;

    }

    gametemp = string.split("@");

    gametemp = gametemp.del(0);

    tempgame = game;

    game = gametemp;

    gametemp = "";

    w();

}

function w() {

    CreateTable();

    setCount();

    checkTitleFooterFloat();

    JQ("#divNumCount").html("共[<b>" + showCountNum + "</b>/" + allCount + "]间");

}

function showSelect(t) {

    if (t == 1) {

        var obj = JQ("#div_companySelect");

        var pos = JQ('#a_companySelect').position();

        obj.css({ left: pos.left, top: pos.top + 23 });

        obj.show();

    }

    else

        JQ("#div_companySelect").hide();

}

function showSolution(t, obj) {

    var obj = JQ("#div_solutions");

    if (t == 1) {

        var pos = JQ('#a_solutions').position();

        obj.css({ left: pos.left, top: pos.top + 23 });

        obj.show();

    }

    else

        obj.hide();

}

function showFileter() {

    var obj = JQ("#tab_MinMax");

    if (obj.is(':hidden'))

        obj.show();

    else

        obj.hide();

}

function oderlist(getnum, getnum2, ischange) {

    try {

        //var time1 = new Date();

        var numList = "0,1,2,4,5,6,7,8";

        var count = game.length;

        var bet = 0;

        var isSmallToBig = ischange ? odernum[getnum] : !odernum[getnum];

        if (isSmallToBig) {//从小到大

            for (var i = 0; i < count; i++) {

                for (var j = 0; j < count - i - 1; j++) {



                    Comparisonstr1 = game[j].split("|");

                    Comparisonstr2 = game[j + 1].split("|");

                    var index1 = Comparisonstr1.length - 1;

                    var index2 = Comparisonstr2.length - 1;

                    if (getnum == 8) {//变化时间

                        var oldTime, newTime;

                        if (showType == 2) {

                            var temp = Comparisonstr1[index1];

                            oldTime = new Date(temp);

                            temp = Comparisonstr2[index2];

                            newTime = new Date(temp);

                        } else {

                            var t2 = Comparisonstr1[getnum2].split(",");

                            oldTime = new Date(t2[0], eval(t2[1]), t2[2], t2[3], t2[4], t2[5]);

                            var t4 = Comparisonstr2[getnum2].split(",");

                            newTime = new Date(t4[0], eval(t4[1]), t4[2], t4[3], t4[4], t4[5]);

                        }

                        if (oldTime > newTime) {

                            tmp = game[j];

                            game[j] = game[j + 1];

                            game[j + 1] = tmp;

                        }

                    }

                    else {

                        var t1 = Number(Comparisonstr1[getnum2]);

                        var t2 = Number(Comparisonstr2[getnum2]);

                        if (showType == 3 && getnum2 < 10) {

                            if (Comparisonstr1[getnum2 + 7] != "")

                                t1 = Number(Comparisonstr1[getnum2 + 7]);

                            if (Comparisonstr2[getnum2 + 7] != "")

                                t2 = Number(Comparisonstr2[getnum2 + 7]);

                        }

                        if (t1 > t2) {

                            tmp = game[j];

                            game[j] = game[j + 1];

                            game[j + 1] = tmp;

                        }

                    }

                }

                bet = j;

            }

            if (ischange)

                odernum[getnum] = false;

        }

        else {

            for (var i = 0; i < count; i++) {//从大到小

                for (var j = 0; j < count - i - 1; j++) {

                    Comparisonstr1 = game[j].split("|");

                    Comparisonstr2 = game[j + 1].split("|");

                    var index1 = Comparisonstr1.length - 1;

                    var index2 = Comparisonstr2.length - 1;

                    if (getnum == 8) {

                        var oldTime, newTime;

                        if (showType == 2) {

                            var temp = Comparisonstr1[index1];

                            oldTime = new Date(temp);

                            temp = Comparisonstr2[index2];

                            newTime = new Date(temp);

                        } else {

                            var t2 = Comparisonstr1[getnum2].split(",");

                            oldTime = new Date(t2[0], eval(t2[1]), t2[2], t2[3], t2[4], t2[5]);

                            var t4 = Comparisonstr2[getnum2].split(",");

                            newTime = new Date(t4[0], eval(t4[1]), t4[2], t4[3], t4[4], t4[5]);

                        }

                        if (oldTime < newTime) {

                            tmp = game[j];

                            game[j] = game[j + 1];

                            game[j + 1] = tmp;

                        }

                    }

                    else {

                        var t1 = Number(Comparisonstr1[getnum2]);

                        var t2 = Number(Comparisonstr2[getnum2]);

                        if (showType == 3 && getnum2 < 10) {

                            if (Comparisonstr1[getnum2 + 7] != "")

                                t1 = Number(Comparisonstr1[getnum2 + 7]);

                            if (Comparisonstr2[getnum2 + 7] != "")

                                t2 = Number(Comparisonstr2[getnum2 + 7]);

                        }

                        if (t1 < t2) {

                            tmp = game[j];

                            game[j] = game[j + 1];

                            game[j + 1] = tmp;

                        }

                    }

                }

                bet = j;

            }

            if (ischange)

                odernum[getnum] = true;

        }

        //var time3 = new Date();

        //console.log("t1:" + (time3.getTime() - time1.getTime()));

        w();

        //var time4 = new Date();

        //console.log("t2:" + (time4.getTime() - time3.getTime()));

        for (var i = 0; i < odernum.length; i++) {

            if (numList.indexOf(i.toString()) != -1) {

                JQ("#order" + i).removeClass("sd down_on");

                JQ("#order" + i).removeClass("sd up_on");

                JQ("#order" + i).addClass("sd");

            }

        }

        if (!odernum[getnum])

            JQ("#order" + getnum).addClass("sd up_on");

        else

            JQ("#order" + getnum).addClass("sd down_on");

        lastSetNum = getnum;

        lastSetNum2 = getnum2;

        //var time2 = new Date();

        //console.log("t3:"+(time2.getTime() - time4.getTime()));

    }

    catch (e) {

        console.log(e);

    }

}

function dataInit() {

    if (getCookie("1x2showType") != "") {

        showType = parseInt(getCookie("1x2showType"));

        JQ("#sel_showType").val(showType);

    }

    if (typeof (gameDetail) != "undefined") {

        for (var i = 0; i < gameDetail.length; i++) {

            var data = gameDetail[i].split('^');

            var oddsID = parseInt(data[0]);

            if (!hsDetail.contains(oddsID)) {

                hsDetail.add(oddsID, data[1]);

            }

        }

    }

    if (typeof (jcEuropeOddsDetail) != "undefined" && jcEuropeOddsDetail != "") {

        var data = jcEuropeOddsDetail.split('^');

        var oddsID = parseInt(data[0]);

        if (!hsDetail.contains(oddsID)) {

            hsDetail.add(oddsID, data[1]);

        }

    }

    if (JQ('#divFooterFload').length) {

        checkTitleFooterFloat();

        if (JQ("#inputFloat").length) {

            if (isFloatCookie == "0") {

                JQ("#inputFloat")[0].checked = false;

            } else {

                JQ("#inputFloat")[0].checked = "checked";

            }

        }

    }

    if (game.length > 0) {

        for (var i = 0; i < game.length; i++) {

            var str = game[i];

            var d = str.split('|');

            var time = getFirstOddsTime(d[1], d[0] == "5000");

            game[i] = str + "|" + time;

        }

    }

    isFloatCookie = isFloatCookie == "" ? "1" : isFloatCookie;

    solution.Show();

    sortByCookie();

    JQ("#uu2").dragsort({ dragSelector: 'li', dragBetween: false });



    var goDivObj = JQ("#goDiv");

    var thisBodyWidth = document.getElementById("divHeadFloat").scrollWidth;

    function getGoDivOff() {

        var thisWidth = document.documentElement.clientWidth;

        if ((thisWidth - thisBodyWidth) / 2 > 18) {

            var goDivRight = (thisWidth - thisBodyWidth) / 2 - 18;

        } else {

            var goDivRight = 0;

        }

        goDivObj.css("right", goDivRight + "px");

    }

    getGoDivOff();

    window.onresize = getGoDivOff;

}

var tipsobj;

function showtips(e, i, obj) {

    if (!tipsobj) {

        tipsobj = JQ("<div></div>");

        JQ("body").append(tipsobj);

    }

    else {

        tipsobj.show();

    }

    tipsobj[0].className = "twin";

    var coordinates = { "x": 0, "y": 0 };

    if (e.pageX || e.pageY) {

        coordinates.x = e.pageX;

        coordinates.y = e.pageY;

    }

    else {

        coordinates.x = e.clientX + document.body.scrollLeft - document.body.clientLeft;

        coordinates.y = e.clientY + document.documentElement.scrollTop;

    }

    coordinates.x += 20;

    var data = game[i].split('|');

    var html = getchangedata(data[1], hsDetail.items(parseInt(data[1])), data[21]);

    if (html != "") {

        tipsobj.html(html);

        tipsobj.css({ "position": "absolute", "width": "244px", "z-index": "197", "left": coordinates.x + "px", "top": coordinates.y + "px" });

    }

    if (window.event) { e.cancelBubble = true; }

    return false;

}

getchangedata = function (name, data, company) {

    if (typeof (data) == "undefined") return "";

    var spchange = '';

    var temhtmlold = "";

    var arrData = data.split(';');

    spchange = '<table width="100%" border="0" cellspacing="0" cellpadding="0" class="tgs2">';

    spchange += '<tr>';

    spchange += '<td colspan="4" class="odds-td-title"><b>' + company + ' 指数变化</b></td>';

    spchange += '</tr>';

    //    spchange += '<tr>';

    //    spchange += '<td bgcolor="#FEF4E2">主胜</td>';

    //    spchange += '<td bgcolor="#FEF4E2">和</td>';

    //    spchange += '<td bgcolor="#FEF4E2">客胜</td>';

    //    spchange += '<td bgcolor="#FEF4E2">变化时间</td>';

    //    spchange += '</tr>';

    var checkforindex = 0;

    for (var i = 0; i < arrData.length - 1; i++) {

        var temhtml = "";

        checkforindex++;

        var temdata = arrData[i];

        var arrTemData = temdata.split('|');

        if (i == arrData.length - 2) {

            temhtmlold = "<tr>";

            temhtmlold += "<td style='width:19%;'>" + formatData(arrTemData[0], 2) + "</td>";

            temhtmlold += "<td style='width:19%;'>" + formatData(arrTemData[1], 2) + "</td>";

            temhtmlold += "<td style='width:19%;'>" + formatData(arrTemData[2], 2) + "</td>";

            temhtmlold += "<td style='text-align:left;'>" + arrTemData[3] + "(初盘)</td>";

            temhtmlold += "</tr>";

        }

        else {

            if (checkforindex > 10) continue;

            var arrTemDataNext = arrData[i + 1].split('|');

            temhtml = "<tr>";

            temhtml += "<td style='width:19%;'>" + getChanelSclass(arrTemData[0], arrTemDataNext[0]) + "</td>";

            temhtml += "<td style='width:19%;'>" + getChanelSclass(arrTemData[1], arrTemDataNext[1]) + "</td>";

            temhtml += "<td style='width:19%;'>" + getChanelSclass(arrTemData[2], arrTemDataNext[2]) + "</td>";

            temhtml += "<td style='text-align:left;;'>" + arrTemData[3] + "</td>";

            temhtml += "</tr>";

        }

        spchange += temhtml;

    }

    if (checkforindex > 10) { spchange += "<tr><td colspan='4'>点击指数查看更多变化</td></tr>" }

    spchange += temhtmlold; spchange += "</table>";

    return spchange;

}

function getChanelSclass(odds1, odds2) {

    var strAvg = (odds1 > odds2 ? "<span class=\"o_red\">" + formatData(odds1, 2) + "</span>" : odds1 < odds2 ? "<span class=\"o_green\">" + formatData(odds1, 2) + "</span>" : formatData(odds1, 2));

    return strAvg;

}

function hidetips(obj) {

    if (window.event) {

        e = window.event;

        e.cancelBubble = true;

    }

    tipsobj.hide();

    return false;

}

function checkTitleFooterFloat() {

    var dataTitleObj = JQ('#divHeadFloat');

    var dataTitleTop = dataTitleObj.offset().top;

    var dataTitleHeight = dataTitleObj.height();

    var datafooterObj = JQ('#divFooterFload');

    var dataFooterHeight = datafooterObj.height();

    var dataMainObj = JQ("#dataList");

    var dataMainTop = dataMainObj.offset().top;

    var dataMainHeight = dataMainObj.height();

    var scrollTop = Number(JQ(document).scrollTop());

    function getDataTitleLocate(scrollTop) {

        if (isFloatCookie == "0") return false;

        dataMainHeight = dataMainObj.height();

        if (scrollTop > dataTitleTop && scrollTop < dataMainTop + dataMainHeight - dataTitleHeight * 2) {

            var titleLeft = Number(JQ(document).scrollLeft());

            if (titleLeft > 0) {

                if (JQ.browser.msie) { titleLeft = 0; }

                dataTitleObj.css("left", -1 * titleLeft + 2 + "px");

            }

            else {

                if (JQ.browser.msie) {

                    dataTitleObj.css("left", '0px');

                } else {

                    dataTitleObj.css("left", 'auto');

                }

            }

            if (dataTitleObj.hasClass('oddtitDiv')) return false;

            dataTitleObj.addClass('oddtitDiv');

        } else {

            if (!dataTitleObj.hasClass('oddtitDiv')) return false;

            dataTitleObj.removeClass('oddtitDiv');

        }

    }

    function getDataFooterLocate(scrollTop) {

        if (isFloatCookie == "0") return false;

        dataMainHeight = dataMainObj.height();

        var nowClientHeigth = document.documentElement.clientHeight;

        var betTitleWidth = 0;

        if (scrollTop + nowClientHeigth - 50 < dataMainTop + dataMainHeight) {

            var footerLeft = Number(JQ(document).scrollLeft());

            if (footerLeft > 0) {

                if (JQ.browser.msie) {

                    footerLeft = 0;

                }

                datafooterObj.css("left", -1 * footerLeft + 2 + "px");

            } else {

                if (JQ.browser.msie) {

                    datafooterObj.css("left", "0px");

                } else {

                    datafooterObj.css("left", 'auto');

                }

            }

            if (datafooterObj.hasClass('oddfooterDiv')) return false;

            datafooterObj.addClass('oddfooterDiv');

        } else {

            if (!datafooterObj.hasClass('oddfooterDiv')) return false;

            datafooterObj.removeClass('oddfooterDiv');

        }

    }

    JQ(window).scroll(function () {

        var scrollTop = Number(JQ(document).scrollTop());

        getDataTitleLocate(scrollTop);

        getDataFooterLocate(scrollTop);

    });

    getDataTitleLocate(scrollTop);

    getDataFooterLocate(scrollTop);

    JQ('#goDown').click(function () {

        var dis = JQ('#helptxt').offset().top + 20;

        window.scrollTo('0', dis);

    });

    JQ("#inputFloat").click(function () {

        if (this.checked) {

            isFloatCookie = "1";

            var scrollTop = Number(JQ(document).scrollTop());

            getDataTitleLocate(scrollTop);

            getDataFooterLocate(scrollTop);

        } else {

            isFloatCookie = "0";

            var dataTitleObj = JQ('#divHeadFloat');

            dataTitleObj.removeClass('oddtitDiv');

            var datafooterObj = JQ('#divFooterFload');

            datafooterObj.removeClass('oddfooterDiv');

        }

        setCookie("oddFloatDiv", isFloatCookie);

    })

}

function exChange() {

    var string = ",";

    var i = 0;

    JQ("#dataList").find("input[type='checkbox']").each(function () {

        if (this.checked == true) {

            var stringq = game[i].split("|");

            string += stringq[0] + ",";

        }

        i++;

    });

    if (string.length < 2) {

        alert("请先选择公司再进行转换");

        return false;

    }

    var url = "/exchange.aspx?id=" + ScheduleID + "&cids=" + string;

    window.open(url);

}

function downEx() {

    if (typeof (game) != "undefined") {

        var cid = [];

        for (var i = 0; i < game.length; i++) {

            var strData = game[i].split("|");

            cid.push(strData[0]);

        }

        JQ("#ids").val(cid.join(","));

        JQ("#DownloadForm").submit();

        //        var url = "/ExportExcelNew.aspx?id=" + ScheduleID + "&ids=" + cid.join(",")

        //        window.open(url);

    }

}

try {

    var _hmt = _hmt || [];

    (function () {

        var hm = document.createElement("script");

        hm.src = "https://hm.baidu.com/hm.js?9b2fa6e9a7be61eafab413cb6c2952b8";

        var s = document.getElementsByTagName("script")[0];

        s.parentNode.insertBefore(hm, s);

    })();

}

catch (e) { }



function vipSubscribe(type) {

	window.open("//users.titan007.com/member/VipRole.aspx");

}

function vipBannerCheck() {

    vipSubscribe(1);

}

var tempYear = new Date().getFullYear();

function getFirstOddsTime(oddsId,isJc) {//通过明细数据获取初盘时间用来排序

    var time = "";

    if (isJc && typeof (jcEuropeOddsDetail) != "undefined" && jcEuropeOddsDetail != null) {

        var strArr = jcEuropeOddsDetail.split('^');

        if (strArr[0] == oddsId) {

            var oddsList = strArr[1].split(';');

            var obj = oddsList[oddsList.length - 2].split('|');

            time = (obj.length == 8 ? obj[7] : tempYear) + "-" + obj[3];

        }

    } else {

        for (var i = 0; i < gameDetail.length; i++) {

            var strArr = gameDetail[i].split('^');

            if (strArr[0] == oddsId) {

                var oddsList = strArr[1].split(';');

                var obj = oddsList[oddsList.length - 2].split('|');

                time = (obj.length == 8 ? obj[7] : tempYear) + "-" + obj[3];

                break;

            }

        }

    }

    return time;

}

var lastSetNum = 0, lastSetNum2 = 0;



function qtPoint(data) {

    var arrTr = data.split("^");

    this.name = arrTr[0];

    this.value = parseInt(arrTr[1]);

}

function qtResult() {

    this.upCount = 0;

    this.drawCount = 0;

    this.downCount = 0;

}

var winResult = new qtResult();

var drawResult = new qtResult();

var loseResult = new qtResult();

function initData(data) {

    var arrData = data.split(',');

    var pointList = new Array();

    for (var i = 0; i < arrData.length; i++) {

        var qt = new qtPoint(arrData[i]);

        pointList.push(qt);

    }

    return pointList;

}

function CreatePie(myChart, pointList,title) {

    var option = {

        tooltip: {

            trigger: 'item',

            formatter: function (params) {

                return params.marker+ params.name + " " + params.value + "家";

            },

            position: function (point) {

                return [point[0] + 20, point[1] + 20]; // 这里仅为示例，将 tooltip 向右下方移动了 10 像素

            },

            extraCssText: "width:80px;",

        },

        color: ['#f7464a', '#0a8fea', '#5baf32'],

        legend: {

            icon: "rect",

            data: pointList,

            formatter: function (name) {

                for (var i = 0; i < pointList.length; i++) {

                    if (pointList[i].name == name) {

                        return name + " " + pointList[i].value + "家";

                    }

                }

            },

            textStyle: {

                fontSize: "13",

            },           

            bottom: "10%",

            orient: 'vertical',

            itemWidth: 2,

            itemHeight:12

        },

        series: [

            {

                type: 'pie',

                bottom:"50%",

                radius: ['50%', '70%'], // 设置环形图的内外半径，实现环形效果

                avoidLabelOverlap: false,

                label: {

                    show: false,

                    position: 'center'

                },

                emphasis: {

                    label: {

                        show: false,

                        fontSize: '14',

                        //fontWeight: 'bold'

                    }

                },

                labelLine: {

                    show: false

                },

                data: pointList

            }

        ],

        title: {

            text: title,

            left: "center",

            top: "20%",

            //bottom: "0%",

            textStyle: {

                align: "center",

                fontSize: 12,

                color: "#000"

            }

        }

    };

    myChart.setOption(option);

}

function InitPie() {

    var dataList = new Array();

    dataList.push("上升^" + winResult.upCount + ",不变^" + winResult.drawCount + ",下降^" + winResult.downCount);

    dataList.push("上升^" + drawResult.upCount + ",不变^" + drawResult.drawCount + ",下降^" + drawResult.downCount);

    dataList.push("上升^" + loseResult.upCount + ",不变^" + loseResult.drawCount + ",下降^" + loseResult.downCount);

    for (var i = 0; i < dataList.length; i++) {

        var pointList = initData(dataList[i]);

        var dom = document.getElementById("pie" + (i + 1));

        dom.style.height = "174px";

        var myChart = echarts.init(dom);

        var title = "主胜";

        if (i == 1)

            title = "平局";

        else if (i == 2)

            title = "客胜";

        CreatePie(myChart, pointList,title);

    }



}
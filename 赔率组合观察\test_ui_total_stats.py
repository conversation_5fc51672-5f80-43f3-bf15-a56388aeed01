#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试UI总统计功能
启动UI程序进行手动测试
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from PyQt5.QtWidgets import QApplication
from odds_combination_ui import OddsCombinationAnalyzerUI

def main():
    """启动UI进行测试"""
    print("🎯 启动赔率组合观察UI - 总统计功能测试")
    print("=" * 60)
    print("📝 测试说明:")
    print("1. 选择一个数据库")
    print("2. 输入一个比赛ID（如：2598146）")
    print("3. 选择一个博彩公司（如：bet365）")
    print("4. 设置组合数为3")
    print("5. 点击'开始分析'")
    print("6. 观察结果表格的首行是否显示总统计")
    print("")
    print("✅ 预期结果:")
    print("- 首行显示'总计'和统计汇总")
    print("- 首行有特殊样式（加粗、浅蓝色背景）")
    print("- 点击首行不显示详细匹配信息")
    print("- 其他行功能正常")
    print("")
    print("🚀 正在启动UI...")
    
    app = QApplication(sys.argv)
    
    # 创建主窗口
    window = OddsCombinationAnalyzerUI()
    window.show()
    
    # 运行应用
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()

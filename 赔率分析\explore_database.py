#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据库结构探索脚本
用于分析现有数据库的表结构和数据特征
"""

import sqlite3
import os
import sys

def explore_database(db_path):
    """探索数据库结构"""
    if not os.path.exists(db_path):
        print(f"数据库文件不存在: {db_path}")
        return
    
    try:
        # 只读方式连接数据库
        conn = sqlite3.connect(f"file:{db_path}?mode=ro", uri=True)
        cursor = conn.cursor()
        
        print(f"=== 数据库文件: {db_path} ===\n")
        
        # 获取所有表名
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = cursor.fetchall()
        
        print(f"发现 {len(tables)} 个表:")
        for table in tables:
            print(f"  - {table[0]}")
        print()
        
        # 分析每个表的结构
        for table_name in [t[0] for t in tables]:
            print(f"=== 表: {table_name} ===")
            
            # 获取表结构
            cursor.execute(f"PRAGMA table_info({table_name})")
            columns = cursor.fetchall()
            
            print("字段结构:")
            for col in columns:
                print(f"  {col[1]} ({col[2]}) - {'NOT NULL' if col[3] else 'NULL'} - {'PK' if col[5] else ''}")
            
            # 获取记录数
            cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
            count = cursor.fetchone()[0]
            print(f"记录数: {count}")
            
            # 如果有数据，显示前几条记录的样本
            if count > 0:
                cursor.execute(f"SELECT * FROM {table_name} LIMIT 3")
                samples = cursor.fetchall()
                print("样本数据:")
                for i, sample in enumerate(samples, 1):
                    print(f"  样本{i}: {sample}")
            
            print("-" * 50)
        
        conn.close()
        
    except Exception as e:
        print(f"探索数据库时出错: {e}")

def main():
    # 主数据库
    main_db = "../odds_data.db"
    explore_database(main_db)
    
    # 联赛分库
    league_db_dir = "../league_databases"
    if os.path.exists(league_db_dir):
        print(f"\n=== 联赛分库目录: {league_db_dir} ===")
        for file in os.listdir(league_db_dir):
            if file.endswith('.db'):
                print(f"\n联赛数据库: {file}")
                explore_database(os.path.join(league_db_dir, file))

if __name__ == "__main__":
    main()

{"validation_results": {"logistic_regression": {"model_name": "logistic_regression", "fold_results": [{"fold": "2", "train_size": "25", "test_size": "12", "accuracy": "1.0", "precision": 1.0, "recall": 1.0, "f1_score": 1.0, "log_loss": null, "auc_score": null}, {"fold": "3", "train_size": "37", "test_size": "12", "accuracy": "0.5833333333333334", "precision": 0.5833333333333334, "recall": 0.5833333333333334, "f1_score": 0.5833333333333334, "log_loss": "0.17933427406532074", "auc_score": null}], "cv_stats": {"accuracy_mean": 0.7916666666666667, "accuracy_std": 0.20833333333333331, "precision_mean": 0.7916666666666667, "precision_std": 0.20833333333333331, "recall_mean": 0.7916666666666667, "recall_std": 0.20833333333333331, "f1_score_mean": 0.7916666666666667, "f1_score_std": 0.20833333333333331, "log_loss_mean": 0.17933427406532074, "log_loss_std": 0.0, "auc_score_mean": null, "auc_score_std": null}, "overall_accuracy": "0.7916666666666666", "overall_precision": 0.7916666666666666, "overall_recall": 0.7916666666666666, "overall_f1": 0.7916666666666666, "all_predictions": [2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 1, 1, 1, 1, 1, 2, 2, 2, 2, 2], "all_true_labels": [2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 0, 0, 0, 0, 0, 2, 2, 2, 2, 2], "all_probabilities": [[0.01404264430629043, 0.9859573556937096], [0.007740626809352169, 0.9922593731906478], [0.007673317041448691, 0.9923266829585513], [0.11998235382141031, 0.8800176461785897], [0.009454511746104122, 0.9905454882538959], [0.0022394813631041455, 0.9977605186368959], [0.041947581451471816, 0.9580524185485282], [0.014196651263818971, 0.985803348736181], [0.007367185807027887, 0.9926328141929721], [0.012955064137279093, 0.9870449358627209], [0.0073862285297806585, 0.9926137714702193], [0.024515215106792332, 0.9754847848932077], [0.0024316908713207797, 0.9975683091286792], [0.004248200642282263, 0.9957517993577177], [0.7264348755669, 0.2735651244331], [0.6572434126409964, 0.3427565873590036], [0.5457872231408705, 0.4542127768591295], [0.6953435652795137, 0.3046564347204863], [0.6764064829521883, 0.3235935170478117], [0.007753366284578567, 0.9922466337154214], [0.009828408815678369, 0.9901715911843216], [0.005341623287774877, 0.9946583767122251], [0.010308348176272908, 0.9896916518237271], [0.012715800346950412, 0.9872841996530496]]}, "random_forest": {"model_name": "random_forest", "fold_results": [{"fold": "2", "train_size": "25", "test_size": "12", "accuracy": "1.0", "precision": 1.0, "recall": 1.0, "f1_score": 1.0, "log_loss": null, "auc_score": null}, {"fold": "3", "train_size": "37", "test_size": "12", "accuracy": "0.5833333333333334", "precision": 0.5833333333333334, "recall": 0.5833333333333334, "f1_score": 0.5833333333333334, "log_loss": "0.20087897532448826", "auc_score": null}], "cv_stats": {"accuracy_mean": 0.7916666666666667, "accuracy_std": 0.20833333333333331, "precision_mean": 0.7916666666666667, "precision_std": 0.20833333333333331, "recall_mean": 0.7916666666666667, "recall_std": 0.20833333333333331, "f1_score_mean": 0.7916666666666667, "f1_score_std": 0.20833333333333331, "log_loss_mean": 0.20087897532448826, "log_loss_std": 0.0, "auc_score_mean": null, "auc_score_std": null}, "overall_accuracy": "0.7916666666666666", "overall_precision": 0.7916666666666666, "overall_recall": 0.7916666666666666, "overall_f1": 0.7916666666666666, "all_predictions": [2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 1, 1, 1, 1, 1, 2, 2, 2, 2, 2], "all_true_labels": [2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 0, 0, 0, 0, 0, 2, 2, 2, 2, 2], "all_probabilities": [[0.0, 1.0], [0.0, 1.0], [0.03, 0.97], [0.21, 0.79], [0.0, 1.0], [0.0, 1.0], [0.03, 0.97], [0.02, 0.98], [0.08, 0.92], [0.23, 0.77], [0.21, 0.79], [0.2, 0.8], [0.05, 0.95], [0.07, 0.93], [0.71, 0.29], [0.68, 0.32], [0.7, 0.3], [0.65, 0.35], [0.68, 0.32], [0.07, 0.93], [0.1, 0.9], [0.05, 0.95], [0.06, 0.94], [0.09, 0.91]]}, "gradient_boosting": {"model_name": "gradient_boosting", "fold_results": [{"fold": "2", "train_size": "25", "test_size": "12", "accuracy": "1.0", "precision": 1.0, "recall": 1.0, "f1_score": 1.0, "log_loss": null, "auc_score": null}, {"fold": "3", "train_size": "37", "test_size": "12", "accuracy": "0.5833333333333334", "precision": 0.5833333333333334, "recall": 0.5833333333333334, "f1_score": 0.5833333333333334, "log_loss": "0.003830517384072167", "auc_score": null}], "cv_stats": {"accuracy_mean": 0.7916666666666667, "accuracy_std": 0.20833333333333331, "precision_mean": 0.7916666666666667, "precision_std": 0.20833333333333331, "recall_mean": 0.7916666666666667, "recall_std": 0.20833333333333331, "f1_score_mean": 0.7916666666666667, "f1_score_std": 0.20833333333333331, "log_loss_mean": 0.003830517384072167, "log_loss_std": 0.0, "auc_score_mean": null, "auc_score_std": null}, "overall_accuracy": "0.7916666666666666", "overall_precision": 0.7916666666666666, "overall_recall": 0.7916666666666666, "overall_f1": 0.7916666666666666, "all_predictions": [2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 1, 1, 1, 1, 1, 2, 2, 2, 2, 2], "all_true_labels": [2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 0, 0, 0, 0, 0, 2, 2, 2, 2, 2], "all_probabilities": [[3.056075738783193e-05, 0.9999694392426122], [3.056075738783193e-05, 0.9999694392426122], [3.747488455674297e-05, 0.9999625251154433], [0.0005096596306902734, 0.9994903403693097], [3.056075738783193e-05, 0.9999694392426122], [3.056075738783193e-05, 0.9999694392426122], [7.036014235428656e-05, 0.9999296398576457], [3.056075738783193e-05, 0.9999694392426122], [0.0001282039423319281, 0.9998717960576681], [0.009538429690864603, 0.9904615703091354], [0.007822524467526848, 0.9921774755324732], [0.007822524467526848, 0.9921774755324732], [2.1339746426929374e-05, 0.9999786602535731], [3.183675573870115e-05, 0.9999681632442613], [0.9952551080534071, 0.004744891946592869], [0.9859812240570864, 0.014018775942913538], [0.9952551080534071, 0.004744891946592869], [0.992934422513077, 0.007065577486922994], [0.9859812240570864, 0.014018775942913538], [0.00014053915342115975, 0.9998594608465788], [0.00038130905226330114, 0.9996186909477367], [0.00017112294688992336, 0.9998288770531101], [0.00017112294688992336, 0.9998288770531101], [0.0002098429265583457, 0.9997901570734417]]}}, "stability_analysis": {"logistic_regression": {"accuracy_cv": 0.26315789473684204, "f1_cv": 0.26315789473684204, "accuracy_range": "0.41666666666666663", "f1_range": 0.41666666666666663, "consistent_performance": false}, "random_forest": {"accuracy_cv": 0.26315789473684204, "f1_cv": 0.26315789473684204, "accuracy_range": "0.41666666666666663", "f1_range": 0.41666666666666663, "consistent_performance": false}, "gradient_boosting": {"accuracy_cv": 0.26315789473684204, "f1_cv": 0.26315789473684204, "accuracy_range": "0.41666666666666663", "f1_range": 0.41666666666666663, "consistent_performance": false}}, "class_performance": {"logistic_regression": {"classification_report": {"away_win": {"precision": "0.0", "recall": "0.0", "f1-score": "0.0", "support": "5.0"}, "draw": {"precision": "0.0", "recall": "0.0", "f1-score": "0.0", "support": "0.0"}, "home_win": {"precision": "1.0", "recall": "1.0", "f1-score": "1.0", "support": "19.0"}, "accuracy": "0.7916666666666666", "macro avg": {"precision": "0.3333333333333333", "recall": "0.3333333333333333", "f1-score": "0.3333333333333333", "support": "24.0"}, "weighted avg": {"precision": "0.7916666666666666", "recall": "0.7916666666666666", "f1-score": "0.7916666666666666", "support": "24.0"}}, "confusion_matrix": [["0", "5", "0"], ["0", "0", "0"], ["0", "0", "19"]]}, "random_forest": {"classification_report": {"away_win": {"precision": "0.0", "recall": "0.0", "f1-score": "0.0", "support": "5.0"}, "draw": {"precision": "0.0", "recall": "0.0", "f1-score": "0.0", "support": "0.0"}, "home_win": {"precision": "1.0", "recall": "1.0", "f1-score": "1.0", "support": "19.0"}, "accuracy": "0.7916666666666666", "macro avg": {"precision": "0.3333333333333333", "recall": "0.3333333333333333", "f1-score": "0.3333333333333333", "support": "24.0"}, "weighted avg": {"precision": "0.7916666666666666", "recall": "0.7916666666666666", "f1-score": "0.7916666666666666", "support": "24.0"}}, "confusion_matrix": [["0", "5", "0"], ["0", "0", "0"], ["0", "0", "19"]]}, "gradient_boosting": {"classification_report": {"away_win": {"precision": "0.0", "recall": "0.0", "f1-score": "0.0", "support": "5.0"}, "draw": {"precision": "0.0", "recall": "0.0", "f1-score": "0.0", "support": "0.0"}, "home_win": {"precision": "1.0", "recall": "1.0", "f1-score": "1.0", "support": "19.0"}, "accuracy": "0.7916666666666666", "macro avg": {"precision": "0.3333333333333333", "recall": "0.3333333333333333", "f1-score": "0.3333333333333333", "support": "24.0"}, "weighted avg": {"precision": "0.7916666666666666", "recall": "0.7916666666666666", "f1-score": "0.7916666666666666", "support": "24.0"}}, "confusion_matrix": [["0", "5", "0"], ["0", "0", "0"], ["0", "0", "19"]]}}, "overfitting_analysis": {"logistic_regression": {"train_accuracy": "1.0", "cv_accuracy": 0.7916666666666667, "overfitting_gap": 0.20833333333333326, "overfitting_ratio": 0.20833333333333326, "is_overfitting": true}, "random_forest": {"train_accuracy": "1.0", "cv_accuracy": 0.7916666666666667, "overfitting_gap": 0.20833333333333326, "overfitting_ratio": 0.20833333333333326, "is_overfitting": true}, "gradient_boosting": {"train_accuracy": "1.0", "cv_accuracy": 0.7916666666666667, "overfitting_gap": 0.20833333333333326, "overfitting_ratio": 0.20833333333333326, "is_overfitting": true}}, "summary": {"best_model": "logistic_regression", "best_cv_accuracy": 0.7916666666666667, "most_stable_model": "logistic_regression", "recommendations": ["模型性能需要改进，建议增加更多特征或数据", "模型稳定性需要改进，建议使用更多数据或正则化"]}}
#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
比赛时间抓取器
从比赛分析页面抓取准确的比赛时间信息

功能：
1. 从 https://zq.titan007.com/analysis/{match_id}.htm 抓取比赛时间
2. 解析年月日、时间、星期信息
3. 格式化和验证时间数据

使用方法：
    from match_time_scraper import MatchTimeScraper
    
    scraper = MatchTimeScraper()
    time_info = scraper.scrape_match_time("2512125")
"""

import requests
import re
import logging
from typing import Dict, Optional
from bs4 import BeautifulSoup
from datetime import datetime
import time

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class MatchTimeScraper:
    def __init__(self):
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'zh-CN,zh;q=0.8,en-US;q=0.5,en;q=0.3',
            'Accept-Encoding': 'gzip, deflate',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1'
        })
    
    def scrape_match_time(self, match_id: str) -> Dict[str, Optional[str]]:
        """
        抓取比赛时间信息（优先使用准确时间提取）

        Args:
            match_id: 比赛ID

        Returns:
            包含时间信息的字典
        """
        try:
            # 首先尝试从分析页面获取准确时间
            accurate_time = self._extract_accurate_match_time(match_id)
            if accurate_time and accurate_time.get('success'):
                logger.info(f"从分析页面获取到准确时间: {accurate_time['match_time']}")
                return {
                    'match_id': match_id,
                    'full_datetime': accurate_time['match_time'],
                    'match_date': accurate_time['match_date'],
                    'match_time': accurate_time['match_time_only'],
                    'weekday': None,  # 可以从日期计算
                    'year': str(accurate_time['year']),
                    'month': str(accurate_time['month']),
                    'day': str(accurate_time['day']),
                    'hour': str(accurate_time['hour']),
                    'minute': str(accurate_time['minute']),
                    'source': 'analysis_page',
                    'raw_time_str': accurate_time.get('raw_time_str', '')
                }

            # 回退到原有方法
            url = f"https://zq.titan007.com/analysis/{match_id}.htm"
            logger.info(f"回退到原有方法抓取比赛 {match_id} 的时间信息: {url}")

            response = self.session.get(url, timeout=10)
            response.raise_for_status()
            response.encoding = 'utf-8'

            soup = BeautifulSoup(response.text, 'html.parser')

            # 解析时间信息
            time_info = self.extract_time_info(soup, match_id)
            time_info['source'] = 'zq_analysis_page'

            logger.info(f"成功抓取比赛 {match_id} 的时间信息: {time_info}")
            return time_info

        except Exception as e:
            logger.error(f"抓取比赛 {match_id} 时间信息失败: {e}")
            return {
                'match_id': match_id,
                'error': str(e),
                'full_datetime': None,
                'match_date': None,
                'match_time': None,
                'weekday': None,
                'year': None,
                'month': None,
                'day': None,
                'hour': None,
                'minute': None,
                'source': 'error'
            }
    
    def extract_time_info(self, soup: BeautifulSoup, match_id: str) -> Dict[str, Optional[str]]:
        """
        从页面中提取时间信息
        
        Args:
            soup: BeautifulSoup对象
            match_id: 比赛ID
            
        Returns:
            时间信息字典
        """
        try:
            # 查找包含时间信息的元素
            time_patterns = [
                # 目标格式: "2024-10-20 9:00 星期日"
                r'(\d{4}-\d{1,2}-\d{1,2})\s+(\d{1,2}:\d{2})\s+(星期[一二三四五六日])',
                # 其他可能的格式
                r'(\d{4}/\d{1,2}/\d{1,2})\s+(\d{1,2}:\d{2})\s+(星期[一二三四五六日])',
                r'(\d{4}-\d{1,2}-\d{1,2})\s+(\d{1,2}:\d{2})',
                r'(\d{4}/\d{1,2}/\d{1,2})\s+(\d{1,2}:\d{2})',
            ]
            
            # 获取页面文本
            page_text = soup.get_text()
            
            # 尝试匹配时间模式
            for pattern in time_patterns:
                matches = re.findall(pattern, page_text)
                if matches:
                    match = matches[0]
                    logger.info(f"找到时间信息: {match}")
                    
                    if len(match) >= 2:
                        date_str = match[0]
                        time_str = match[1]
                        weekday = match[2] if len(match) > 2 else None
                        
                        return self.parse_time_components(date_str, time_str, weekday, match_id)
            
            # 如果没有找到标准格式，尝试查找font元素
            font_elements = soup.find_all('font')
            for font in font_elements:
                font_text = font.get_text().strip()
                if any(keyword in font_text for keyword in ['星期', ':', '-', '/']):
                    logger.info(f"在font元素中找到可能的时间信息: {font_text}")
                    
                    # 尝试解析font中的时间
                    for pattern in time_patterns:
                        match = re.search(pattern, font_text)
                        if match:
                            groups = match.groups()
                            if len(groups) >= 2:
                                date_str = groups[0]
                                time_str = groups[1]
                                weekday = groups[2] if len(groups) > 2 else None
                                
                                return self.parse_time_components(date_str, time_str, weekday, match_id)
            
            # 查找其他可能包含时间的元素
            time_containers = soup.find_all(['div', 'span', 'td', 'p'], 
                                          string=re.compile(r'\d{4}[-/]\d{1,2}[-/]\d{1,2}'))
            
            for container in time_containers:
                container_text = container.get_text().strip()
                logger.info(f"在容器中找到可能的时间信息: {container_text}")
                
                for pattern in time_patterns:
                    match = re.search(pattern, container_text)
                    if match:
                        groups = match.groups()
                        if len(groups) >= 2:
                            date_str = groups[0]
                            time_str = groups[1]
                            weekday = groups[2] if len(groups) > 2 else None
                            
                            return self.parse_time_components(date_str, time_str, weekday, match_id)
            
            logger.warning(f"未找到比赛 {match_id} 的时间信息")
            return {
                'match_id': match_id,
                'error': '未找到时间信息',
                'full_datetime': None,
                'match_date': None,
                'match_time': None,
                'weekday': None,
                'year': None,
                'month': None,
                'day': None,
                'hour': None,
                'minute': None,
                'source_url': f"https://zq.titan007.com/analysis/{match_id}.htm"
            }
            
        except Exception as e:
            logger.error(f"解析时间信息失败: {e}")
            return {
                'match_id': match_id,
                'error': str(e),
                'full_datetime': None,
                'match_date': None,
                'match_time': None,
                'weekday': None,
                'year': None,
                'month': None,
                'day': None,
                'hour': None,
                'minute': None,
                'source_url': f"https://zq.titan007.com/analysis/{match_id}.htm"
            }
    
    def parse_time_components(self, date_str: str, time_str: str, weekday: str, match_id: str) -> Dict[str, Optional[str]]:
        """
        解析时间组件
        
        Args:
            date_str: 日期字符串 (如 "2024-10-20")
            time_str: 时间字符串 (如 "9:00")
            weekday: 星期 (如 "星期日")
            match_id: 比赛ID
            
        Returns:
            解析后的时间信息字典
        """
        try:
            # 标准化日期格式
            date_str = date_str.replace('/', '-')
            
            # 解析日期
            date_parts = date_str.split('-')
            if len(date_parts) != 3:
                raise ValueError(f"日期格式错误: {date_str}")
            
            year = date_parts[0]
            month = date_parts[1].zfill(2)  # 补零
            day = date_parts[2].zfill(2)    # 补零
            
            # 解析时间
            time_parts = time_str.split(':')
            if len(time_parts) != 2:
                raise ValueError(f"时间格式错误: {time_str}")
            
            hour = time_parts[0].zfill(2)   # 补零
            minute = time_parts[1].zfill(2) # 补零
            
            # 构建完整的日期时间
            full_date = f"{year}-{month}-{day}"
            full_time = f"{hour}:{minute}"
            full_datetime = f"{full_date} {full_time}:00"
            
            # 验证日期时间格式
            try:
                datetime.strptime(full_datetime, '%Y-%m-%d %H:%M:%S')
            except ValueError as e:
                logger.warning(f"日期时间格式验证失败: {full_datetime}, 错误: {e}")
            
            result = {
                'match_id': match_id,
                'error': None,
                'full_datetime': full_datetime,
                'match_date': full_date,
                'match_time': full_time,
                'weekday': weekday,
                'year': year,
                'month': month,
                'day': day,
                'hour': hour,
                'minute': minute,
                'source_url': f"https://zq.titan007.com/analysis/{match_id}.htm"
            }
            
            logger.info(f"时间解析成功: {result}")
            return result
            
        except Exception as e:
            logger.error(f"解析时间组件失败: {e}")
            return {
                'match_id': match_id,
                'error': str(e),
                'full_datetime': None,
                'match_date': None,
                'match_time': None,
                'weekday': weekday,
                'year': None,
                'month': None,
                'day': None,
                'hour': None,
                'minute': None,
                'source_url': f"https://zq.titan007.com/analysis/{match_id}.htm"
            }

    def _extract_accurate_match_time(self, match_id: str) -> Dict:
        """从分析页面提取准确的比赛时间"""
        if not match_id:
            return {'success': False, 'error': '比赛ID为空'}

        try:
            # 构建分析页面URL
            analysis_url = f"https://m.titan007.com/Analy/ShiJian/{match_id}.htm"

            # 获取页面内容
            response = self.session.get(analysis_url, timeout=15)
            response.raise_for_status()
            response.encoding = 'utf-8'

            content = response.text

            # 查找时间变量
            time_patterns = [
                r'var\s+headMatchTime\s*=\s*["\']([^"\']+)["\']',
                r'headMatchTime\s*=\s*["\']([^"\']+)["\']',
                r'matchTime\s*=\s*["\']([^"\']+)["\']',
                r'var\s+matchTime\s*=\s*["\']([^"\']+)["\']',
            ]

            for pattern in time_patterns:
                match = re.search(pattern, content, re.IGNORECASE)
                if match:
                    time_str = match.group(1)

                    # 解析时间
                    try:
                        if 'T' in time_str:
                            # ISO格式: 2025-07-27T19:00:00
                            dt = datetime.fromisoformat(time_str.replace('T', ' ').replace('Z', ''))
                        else:
                            # 其他格式
                            dt = datetime.strptime(time_str, "%Y-%m-%d %H:%M:%S")

                        return {
                            'success': True,
                            'match_time': dt.strftime('%Y-%m-%d %H:%M:%S'),
                            'match_date': dt.strftime('%Y-%m-%d'),
                            'match_time_only': dt.strftime('%H:%M:%S'),
                            'year': dt.year,
                            'month': dt.month,
                            'day': dt.day,
                            'hour': dt.hour,
                            'minute': dt.minute,
                            'source': 'analysis_page',
                            'raw_time_str': time_str
                        }
                    except Exception as e:
                        logger.warning(f"时间解析失败: {time_str}, 错误: {e}")
                        continue

            return {'success': False, 'error': '未找到时间信息'}

        except Exception as e:
            logger.warning(f"从分析页面提取时间失败: {e}")
            return {'success': False, 'error': str(e)}

def test_match_time_scraper():
    """测试比赛时间抓取器"""
    print("🧪 测试比赛时间抓取器...")
    
    scraper = MatchTimeScraper()
    
    # 测试用的比赛ID
    test_match_ids = ["2512125", "2741460", "2699999"]
    
    for match_id in test_match_ids:
        print(f"\n📅 测试比赛ID: {match_id}")
        print("-" * 50)
        
        time_info = scraper.scrape_match_time(match_id)
        
        if time_info.get('error'):
            print(f"❌ 抓取失败: {time_info['error']}")
        else:
            print(f"✅ 抓取成功:")
            print(f"   完整时间: {time_info['full_datetime']}")
            print(f"   日期: {time_info['match_date']}")
            print(f"   时间: {time_info['match_time']}")
            print(f"   星期: {time_info['weekday']}")
            print(f"   年: {time_info['year']}")
            print(f"   月: {time_info['month']}")
            print(f"   日: {time_info['day']}")
            print(f"   时: {time_info['hour']}")
            print(f"   分: {time_info['minute']}")
        
        # 添加延迟避免请求过快
        time.sleep(1)

if __name__ == "__main__":
    test_match_time_scraper()

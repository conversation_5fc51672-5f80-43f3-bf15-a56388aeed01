#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试比赛状态过滤功能
"""

import sys
import os
import sqlite3
from datetime import datetime, timedelta

def test_match_status_logic():
    """测试比赛状态判断逻辑"""
    
    print("🔍 测试比赛状态判断逻辑")
    print("=" * 50)
    
    # 模拟should_update_match方法
    def should_update_match(match_id, match_time, match_state, current_time):
        """判断比赛是否应该更新"""
        try:
            # 1. 检查比赛状态
            if match_state:
                match_state_lower = match_state.lower()
                
                # 明确的完场状态
                finished_states = ['完场', '结束', 'finished', 'ft', 'full time', '全场结束']
                if any(state in match_state_lower for state in finished_states):
                    print(f"  ✅ 比赛 {match_id} 状态为完场: {match_state}")
                    return True
                
                # 明确的未完场状态
                ongoing_states = ['进行中', '上半场', '下半场', '中场', 'live', 'ht', 'half time', 
                                '1h', '2h', '加时', 'et', 'extra time', '点球', 'pen', 'penalty']
                if any(state in match_state_lower for state in ongoing_states):
                    print(f"  ❌ 比赛 {match_id} 正在进行中，跳过: {match_state}")
                    return False
                
                # 未开始状态
                not_started_states = ['未开始', 'not started', 'scheduled', '待定', 'postponed', 'delayed']
                if any(state in match_state_lower for state in not_started_states):
                    print(f"  ❌ 比赛 {match_id} 未开始，跳过: {match_state}")
                    return False
            
            # 2. 如果状态不明确，根据时间判断
            if match_time:
                try:
                    # 尝试解析比赛时间
                    if isinstance(match_time, str):
                        # 常见的时间格式
                        time_formats = [
                            '%Y-%m-%d %H:%M:%S',
                            '%Y-%m-%d %H:%M',
                            '%Y/%m/%d %H:%M:%S',
                            '%Y/%m/%d %H:%M'
                        ]
                        
                        match_datetime = None
                        for fmt in time_formats:
                            try:
                                match_datetime = datetime.strptime(match_time, fmt)
                                break
                            except ValueError:
                                continue
                        
                        if match_datetime:
                            # 比赛时间 + 3小时（一般比赛时长）后才考虑更新
                            estimated_end_time = match_datetime + timedelta(hours=3)
                            
                            if current_time >= estimated_end_time:
                                print(f"  ✅ 比赛 {match_id} 预计已结束，允许更新")
                                return True
                            else:
                                time_diff = estimated_end_time - current_time
                                print(f"  ❌ 比赛 {match_id} 预计还需 {time_diff} 结束，跳过")
                                return False
                        else:
                            print(f"  ⚠️ 比赛 {match_id} 时间格式无法解析: {match_time}")
                    
                except Exception as e:
                    print(f"  ⚠️ 解析比赛 {match_id} 时间失败: {e}")
            
            # 3. 默认情况：如果无法确定状态和时间，保守地跳过
            print(f"  ❌ 比赛 {match_id} 状态不明确，保守跳过")
            return False
            
        except Exception as e:
            print(f"  ❌ 判断比赛 {match_id} 是否应该更新时发生错误: {e}")
            return False
    
    # 测试用例
    current_time = datetime.now()
    test_cases = [
        # (match_id, match_time, match_state, expected_result, description)
        ("test1", "2025-01-01 20:00:00", "完场", True, "明确完场状态"),
        ("test2", "2025-01-01 20:00:00", "结束", True, "结束状态"),
        ("test3", "2025-01-01 20:00:00", "FT", True, "英文完场状态"),
        ("test4", "2025-01-01 20:00:00", "进行中", False, "比赛进行中"),
        ("test5", "2025-01-01 20:00:00", "上半场", False, "上半场"),
        ("test6", "2025-01-01 20:00:00", "Live", False, "英文进行中"),
        ("test7", "2025-01-01 20:00:00", "未开始", False, "未开始"),
        ("test8", "2025-01-01 20:00:00", "Scheduled", False, "英文未开始"),
        ("test9", (current_time - timedelta(hours=5)).strftime('%Y-%m-%d %H:%M:%S'), None, True, "5小时前的比赛（无状态）"),
        ("test10", (current_time + timedelta(hours=1)).strftime('%Y-%m-%d %H:%M:%S'), None, False, "1小时后的比赛（无状态）"),
        ("test11", "2025-01-01 20:00:00", "", False, "空状态，保守跳过"),
    ]
    
    print("\n🧪 测试用例:")
    print("-" * 80)
    
    correct_count = 0
    for match_id, match_time, match_state, expected, description in test_cases:
        print(f"\n📋 {description}")
        print(f"   比赛ID: {match_id}")
        print(f"   比赛时间: {match_time}")
        print(f"   比赛状态: {match_state}")
        print(f"   预期结果: {'更新' if expected else '跳过'}")
        
        result = should_update_match(match_id, match_time, match_state, current_time)
        
        if result == expected:
            print(f"   ✅ 测试通过")
            correct_count += 1
        else:
            print(f"   ❌ 测试失败，实际结果: {'更新' if result else '跳过'}")
    
    print(f"\n📊 测试结果: {correct_count}/{len(test_cases)} 通过")
    
    return correct_count == len(test_cases)

def test_database_integration():
    """测试数据库集成"""
    
    print(f"\n💾 测试数据库集成:")
    print("-" * 30)
    
    try:
        # 查找数据库文件
        db_files = [f for f in os.listdir('.') if f.endswith('.db')]
        if not db_files:
            print("❌ 未找到数据库文件")
            return
        
        db_path = db_files[0]
        print(f"📂 使用数据库: {db_path}")
        
        with sqlite3.connect(db_path) as conn:
            cursor = conn.cursor()
            
            # 检查matches表的字段
            cursor.execute("PRAGMA table_info(matches);")
            columns = cursor.fetchall()
            column_names = [col[1] for col in columns]
            
            print(f"📊 matches表字段: {column_names}")
            
            # 检查是否有match_state字段
            if 'match_state' in column_names:
                print("✅ match_state 字段存在")
                
                # 查看不同的比赛状态
                cursor.execute("""
                    SELECT DISTINCT match_state, COUNT(*) as count
                    FROM matches 
                    WHERE match_state IS NOT NULL AND match_state != ''
                    GROUP BY match_state
                    ORDER BY count DESC
                    LIMIT 10
                """)
                
                states = cursor.fetchall()
                print(f"\n📋 数据库中的比赛状态（前10种）:")
                for state, count in states:
                    print(f"   {state}: {count} 场")
                
            else:
                print("❌ match_state 字段不存在")
            
            # 测试查询无比赛结果的比赛
            cursor.execute("""
                SELECT match_id, league, home_team, away_team, match_time, match_state
                FROM matches 
                WHERE (home_score IS NULL OR home_score = '' OR 
                       away_score IS NULL OR away_score = '')
                LIMIT 5
            """)
            
            matches = cursor.fetchall()
            print(f"\n🎯 无比赛结果的比赛示例（前5场）:")
            for match in matches:
                match_id, league, home_team, away_team, match_time, match_state = match
                print(f"   {match_id}: {home_team} vs {away_team}")
                print(f"     时间: {match_time}, 状态: {match_state}")
        
        print("✅ 数据库集成测试完成")
        
    except Exception as e:
        print(f"❌ 数据库集成测试失败: {e}")

if __name__ == "__main__":
    success = test_match_status_logic()
    test_database_integration()
    
    print(f"\n🎉 测试总结:")
    if success:
        print("✅ 比赛状态判断逻辑测试全部通过")
    else:
        print("❌ 部分测试失败，需要检查逻辑")
    
    print(f"\n📋 功能说明:")
    print("1. 明确完场状态的比赛：允许更新")
    print("2. 正在进行中的比赛：跳过更新")
    print("3. 未开始的比赛：跳过更新")
    print("4. 状态不明确的比赛：根据时间判断（比赛时间+3小时后才更新）")
    print("5. 无法判断的比赛：保守跳过")
    
    print(f"\n🚀 现在可以测试改进后的完赛后更新功能！")

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试删除功能问题
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from database import OddsDatabase
import logging

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def debug_delete_issue():
    """调试删除功能问题"""
    print("🔍 调试删除功能问题")
    print("=" * 60)
    
    # 连接数据库
    db = OddsDatabase()
    
    # 1. 检查数据库连接
    print("1️⃣ 检查数据库连接")
    try:
        stats = db.get_database_stats()
        if stats:
            print(f"   ✅ 数据库连接正常")
            print(f"   📊 比赛数量: {stats['match_count']}")
            print(f"   📊 赔率记录数: {stats['odds_count']}")
        else:
            print(f"   ❌ 无法获取数据库统计")
            return
    except Exception as e:
        print(f"   ❌ 数据库连接失败: {e}")
        return
    
    # 2. 获取一些比赛数据
    print(f"\n2️⃣ 获取比赛数据")
    try:
        matches = db.get_all_matches()
        if matches:
            print(f"   ✅ 获取到 {len(matches)} 场比赛")
            
            # 显示前5场比赛
            print(f"   📋 前5场比赛:")
            for i, match in enumerate(matches[:5], 1):
                match_id = match.get('match_id', 'N/A')
                home_team = match.get('home_team', 'N/A')
                away_team = match.get('away_team', 'N/A')
                print(f"      {i}. {match_id} - {home_team} vs {away_team}")
                
            # 选择第一场比赛进行测试
            test_match = matches[0]
            test_match_id = test_match.get('match_id')
            
            print(f"\n3️⃣ 测试删除功能 (比赛ID: {test_match_id})")
            
            # 3.1 检查删除前的数据
            print(f"   🔍 删除前检查:")
            
            # 检查比赛是否存在
            match_info = db.get_match_info(test_match_id)
            if match_info:
                print(f"      ✅ 比赛存在: {match_info.get('home_team')} vs {match_info.get('away_team')}")
            else:
                print(f"      ❌ 比赛不存在")
                return
                
            # 检查赔率数据
            odds_data = db.get_odds_data(test_match_id)
            if odds_data:
                print(f"      ✅ 赔率数据存在: {len(odds_data)} 条记录")
            else:
                print(f"      ⚠️ 无赔率数据")
            
            # 3.2 执行删除操作
            print(f"   🗑️ 执行删除操作:")
            
            # 先备份一下，以防万一
            print(f"      📋 删除前状态记录完成")
            
            # 执行删除
            delete_result = db.delete_match(test_match_id)
            print(f"      删除操作返回: {delete_result}")
            
            # 3.3 检查删除后的数据
            print(f"   🔍 删除后检查:")
            
            # 检查比赛是否还存在
            match_info_after = db.get_match_info(test_match_id)
            if match_info_after:
                print(f"      ❌ 比赛仍然存在！删除失败")
                print(f"         比赛信息: {match_info_after}")
            else:
                print(f"      ✅ 比赛已删除")
                
            # 检查赔率数据是否还存在
            odds_data_after = db.get_odds_data(test_match_id)
            if odds_data_after:
                print(f"      ❌ 赔率数据仍然存在！删除失败")
                print(f"         剩余记录数: {len(odds_data_after)}")
            else:
                print(f"      ✅ 赔率数据已删除")
            
            # 3.4 检查数据库统计变化
            print(f"   📊 数据库统计变化:")
            stats_after = db.get_database_stats()
            if stats_after:
                match_diff = stats['match_count'] - stats_after['match_count']
                odds_diff = stats['odds_count'] - stats_after['odds_count']
                print(f"      比赛数量变化: {stats['match_count']} -> {stats_after['match_count']} (差值: {match_diff})")
                print(f"      赔率记录变化: {stats['odds_count']} -> {stats_after['odds_count']} (差值: {odds_diff})")
                
                if match_diff == 1:
                    print(f"      ✅ 比赛数量正确减少")
                else:
                    print(f"      ❌ 比赛数量变化异常")
                    
                if odds_diff > 0:
                    print(f"      ✅ 赔率记录正确减少")
                else:
                    print(f"      ⚠️ 赔率记录无变化")
            
        else:
            print(f"   ❌ 无比赛数据")
            
    except Exception as e:
        print(f"   ❌ 获取比赛数据失败: {e}")
        import traceback
        traceback.print_exc()

def check_database_integrity():
    """检查数据库完整性"""
    print(f"\n4️⃣ 检查数据库完整性")
    
    db = OddsDatabase()
    
    try:
        import sqlite3
        with sqlite3.connect(db.db_path) as conn:
            cursor = conn.cursor()
            
            # 检查表结构
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
            tables = cursor.fetchall()
            print(f"   📋 数据库表: {[table[0] for table in tables]}")
            
            # 检查matches表结构
            cursor.execute("PRAGMA table_info(matches)")
            matches_columns = cursor.fetchall()
            print(f"   📋 matches表列: {[col[1] for col in matches_columns]}")
            
            # 检查odds表结构
            cursor.execute("PRAGMA table_info(odds)")
            odds_columns = cursor.fetchall()
            print(f"   📋 odds表列: {[col[1] for col in odds_columns]}")
            
            # 检查是否有外键约束
            cursor.execute("PRAGMA foreign_key_list(odds)")
            foreign_keys = cursor.fetchall()
            if foreign_keys:
                print(f"   🔗 外键约束: {foreign_keys}")
            else:
                print(f"   ⚠️ 无外键约束")
                
            # 检查索引
            cursor.execute("SELECT name FROM sqlite_master WHERE type='index'")
            indexes = cursor.fetchall()
            print(f"   📋 索引: {[idx[0] for idx in indexes if not idx[0].startswith('sqlite_')]}")
            
    except Exception as e:
        print(f"   ❌ 检查数据库完整性失败: {e}")

if __name__ == "__main__":
    debug_delete_issue()
    check_database_integrity()

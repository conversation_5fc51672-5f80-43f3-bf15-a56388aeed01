#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
赔率组合观察 - PyQt5 UI界面
"""

import sys
import os
import logging
from typing import List, Dict, Optional
from PyQt5.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout,
                            QHBoxLayout, QLabel, QLineEdit, QComboBox,
                            QSpinBox, QPushButton, QTableWidget, QTableWidgetItem,
                            QSplitter, QMessageBox, QProgressBar, QTextEdit,
                            QHeaderView, QAbstractItemView, QCheckBox, QListWidget,
                            QListWidgetItem)
from PyQt5.QtCore import Qt, QThread, pyqtSignal, QTimer
from PyQt5.QtGui import QFont, QColor

from odds_combination_database import OddsCombinationDatabase
from odds_combination_analyzer import OddsCombinationAnalyzer
from optimized_analyzer import OptimizedOddsCombinationAnalyzer

logger = logging.getLogger(__name__)

# 尝试导入概率计算函数，如果失败则使用备用方案
try:
    from prob_cal import multinomial_tail_prob
    PROB_CALC_AVAILABLE = True
except ImportError as e:
    logger.warning(f"无法导入概率计算模块: {e}")
    PROB_CALC_AVAILABLE = False

    def multinomial_tail_prob(p, observed):
        """备用概率计算函数"""
        return 1.0, 1.0

class AnalysisWorker(QThread):
    """分析工作线程"""
    progress_updated = pyqtSignal(str)  # 进度更新信号
    analysis_completed = pyqtSignal(dict)  # 分析完成信号
    analysis_failed = pyqtSignal(str)  # 分析失败信号
    
    def __init__(self, db_path: str, match_id: str, company_name: str, combination_size: int, use_optimized: bool = True, only_historical: bool = True):
        super().__init__()
        self.db_path = db_path
        self.match_id = match_id
        self.company_name = company_name
        self.combination_size = combination_size
        self.use_optimized = use_optimized
        self.only_historical = only_historical
    
    def run(self):
        """执行分析"""
        try:
            if self.use_optimized:
                # 使用优化的分析器
                self.progress_updated.emit("正在初始化优化分析器...")
                analyzer = OptimizedOddsCombinationAnalyzer(
                    db_path=self.db_path,
                    max_workers=4,  # 4个并发线程
                    batch_size=100  # 批量大小100
                )

                self.progress_updated.emit("正在执行优化分析...")
                result = analyzer.analyze_odds_combinations_optimized(
                    self.match_id, self.company_name, self.combination_size, self.only_historical
                )
            else:
                # 使用原始分析器
                self.progress_updated.emit("正在初始化标准分析器...")
                analyzer = OddsCombinationAnalyzer(self.db_path)

                self.progress_updated.emit("正在分析赔率组合...")
                result = analyzer.analyze_odds_combinations(
                    self.match_id, self.company_name, self.combination_size, self.only_historical
                )

            if result['success']:
                analysis_time = result.get('analysis_time', 0)
                self.progress_updated.emit(f"分析完成！耗时 {analysis_time:.2f} 秒")
                self.analysis_completed.emit(result)
            else:
                self.analysis_failed.emit(result['error'])

        except Exception as e:
            logger.error(f"分析过程中发生错误: {e}")
            self.analysis_failed.emit(f"分析过程中发生错误: {str(e)}")

class RangeAnalysisWorker(QThread):
    """ID区间分析工作线程"""
    progress_updated = pyqtSignal(str)
    match_completed = pyqtSignal(str, int, int)  # match_id, current, total
    analysis_completed = pyqtSignal(list)  # report_data
    analysis_failed = pyqtSignal(str)

    def __init__(self, db_path: str, start_id: int, end_id: int, company_name: str, combination_size: int, use_optimized: bool, only_historical: bool = True):
        super().__init__()
        self.db_path = db_path
        self.start_id = start_id
        self.end_id = end_id
        self.company_name = company_name
        self.combination_size = combination_size
        self.use_optimized = use_optimized
        self.only_historical = only_historical

    def run(self):
        """执行区间分析"""
        try:
            report_data = []
            total_matches = self.end_id - self.start_id + 1
            current_match = 0

            self.progress_updated.emit(f"开始分析 {total_matches} 场比赛...")

            for match_id in range(self.start_id, self.end_id + 1):
                current_match += 1
                match_id_str = str(match_id)

                try:
                    # 使用与单场分析完全相同的流程
                    if self.use_optimized:
                        from optimized_analyzer import OptimizedOddsCombinationAnalyzer
                        analyzer = OptimizedOddsCombinationAnalyzer()
                        result = analyzer.analyze_odds_combinations_optimized(
                            match_id_str, self.company_name, self.combination_size, self.only_historical
                        )
                    else:
                        analyzer = OddsCombinationAnalyzer(self.db_path)
                        result = analyzer.analyze_odds_combinations(
                            match_id_str, self.company_name, self.combination_size, self.only_historical
                        )

                    if result['success'] and result['results']:
                        # 找到尾部概率最小的组合
                        best_combination = self.find_best_combination(result['results'])

                        if best_combination:
                            # 获取比赛结果
                            match_result = self.get_match_result(match_id_str)

                            # 计算该比赛所有匹配次数不为零的组合的总统计
                            total_matches_all = 0
                            total_wins_all = 0
                            total_draws_all = 0
                            total_losses_all = 0

                            for combination_result in result['results']:
                                if combination_result['match_count'] > 0:  # 只统计匹配次数不为零的组合
                                    total_matches_all += combination_result['match_count']
                                    stats = combination_result['result_stats']
                                    total_wins_all += stats['win']
                                    total_draws_all += stats['draw']
                                    total_losses_all += stats['loss']

                            # 构建报告项
                            report_item = {
                                'match_id': match_id_str,
                                'match_result': match_result,
                                'combination_content': best_combination['combination_content'],
                                'match_count': best_combination['match_count'],
                                'win_count': best_combination['win_count'],
                                'draw_count': best_combination['draw_count'],
                                'loss_count': best_combination['loss_count'],
                                'prob': best_combination['prob'],
                                'tail_prob': best_combination['tail_prob'],
                                # 新增：该比赛所有组合的总统计
                                'total_matches_all': total_matches_all,
                                'total_wins_all': total_wins_all,
                                'total_draws_all': total_draws_all,
                                'total_losses_all': total_losses_all
                            }

                            report_data.append(report_item)

                    # 发送进度更新
                    self.match_completed.emit(match_id_str, current_match, total_matches)

                except Exception as e:
                    logger.warning(f"分析比赛 {match_id_str} 时发生错误: {e}")
                    continue

            # 发送完成信号
            self.analysis_completed.emit(report_data)

        except Exception as e:
            logger.error(f"区间分析过程中发生错误: {e}")
            self.analysis_failed.emit(f"区间分析过程中发生错误: {str(e)}")

    def find_best_combination(self, results: list) -> dict:
        """找到尾部概率最小的组合"""
        best_combination = None
        min_tail_prob = float('inf')

        for result in results:
            # 计算概率（使用与UI相同的逻辑）
            combination = result.get('combination') or result.get('target_combination')
            stats = result['result_stats']
            match_count = result['match_count']

            prob, tail_prob = self.calculate_probabilities(combination, stats, match_count)

            if tail_prob < min_tail_prob:
                min_tail_prob = tail_prob
                best_combination = {
                    'combination_content': self.format_combination_text(combination),
                    'match_count': match_count,
                    'win_count': stats['win'],
                    'draw_count': stats['draw'],
                    'loss_count': stats['loss'],
                    'prob': prob,
                    'tail_prob': tail_prob
                }

        return best_combination

    def calculate_probabilities(self, combination: List[Dict], stats: Dict, match_count: int) -> tuple:
        """计算概率和尾部概率（与UI中的逻辑相同）"""
        try:
            if not PROB_CALC_AVAILABLE or match_count == 0 or len(combination) < 2:
                return 1.0, 1.0

            last_odds = combination[-1]
            home_odds = last_odds['home_odds']
            draw_odds = last_odds['draw_odds']
            away_odds = last_odds['away_odds']

            if home_odds <= 0 or draw_odds <= 0 or away_odds <= 0:
                return 1.0, 1.0

            # 计算隐含概率
            home_prob_raw = 1.0 / home_odds
            draw_prob_raw = 1.0 / draw_odds
            away_prob_raw = 1.0 / away_odds

            # 计算返还率
            return_rate = home_prob_raw + draw_prob_raw + away_prob_raw

            # 计算真实概率
            p = [home_prob_raw / return_rate, draw_prob_raw / return_rate, away_prob_raw / return_rate]

            # 构建观察结果
            observed = [stats['win'], stats['draw'], stats['loss']]

            if sum(observed) != match_count:
                return 1.0, 1.0

            # 计算概率
            prob, tail_prob = multinomial_tail_prob(p, observed)

            if prob < 0 or tail_prob < 0 or prob > 1 or tail_prob > 1:
                return 1.0, 1.0

            return prob, tail_prob

        except Exception:
            return 1.0, 1.0

    def format_combination_text(self, combination: List[Dict]) -> str:
        """格式化组合文本显示（与UI中的逻辑相同）"""
        parts = []
        for i, odds in enumerate(combination):
            part = f"{i+1}:({odds['home_odds']},{odds['draw_odds']},{odds['away_odds']})"
            parts.append(part)
        return " | ".join(parts)

    def get_match_result(self, match_id: str) -> str:
        """获取比赛结果"""
        try:
            database = OddsCombinationDatabase(self.db_path)

            # 查询比赛结果
            query = """
                SELECT home_score, away_score
                FROM matches
                WHERE match_id = ?
            """

            result = database.execute_query(query, (match_id,))

            if result:
                home_score = result[0][0]
                away_score = result[0][1]

                if home_score is not None and away_score is not None:
                    if home_score > away_score:
                        return f"{home_score}:{away_score} (主胜)"
                    elif home_score < away_score:
                        return f"{home_score}:{away_score} (客胜)"
                    else:
                        return f"{home_score}:{away_score} (平局)"
                else:
                    return "未知"
            else:
                return "未找到"

        except Exception as e:
            logger.warning(f"获取比赛 {match_id} 结果时发生错误: {e}")
            return "错误"

class RecentMatchesAnalysisWorker(QThread):
    """最近比赛数据分析工作线程"""
    progress_updated = pyqtSignal(str)
    match_completed = pyqtSignal(str, int, int)  # match_id, current, total
    analysis_completed = pyqtSignal(list)  # report_data
    analysis_failed = pyqtSignal(str)

    def __init__(self, db_path: str, match_ids: list, company_name: str, combination_size: int, use_optimized: bool, only_historical: bool = True):
        super().__init__()
        self.db_path = db_path
        self.match_ids = match_ids
        self.company_name = company_name
        self.combination_size = combination_size
        self.use_optimized = use_optimized
        self.only_historical = only_historical

    def run(self):
        """执行最近比赛分析"""
        try:
            report_data = []
            total_matches = len(self.match_ids)
            current_match = 0

            self.progress_updated.emit(f"开始分析 {total_matches} 场最近比赛...")

            for match_id in self.match_ids:
                current_match += 1
                match_id_str = str(match_id)

                try:
                    # 使用与ID区间分析完全相同的流程
                    if self.use_optimized:
                        from optimized_analyzer import OptimizedOddsCombinationAnalyzer
                        analyzer = OptimizedOddsCombinationAnalyzer()
                        result = analyzer.analyze_odds_combinations_optimized(
                            match_id_str, self.company_name, self.combination_size, self.only_historical
                        )
                    else:
                        from odds_combination_analyzer import OddsCombinationAnalyzer
                        analyzer = OddsCombinationAnalyzer(self.db_path)
                        result = analyzer.analyze_odds_combinations(
                            match_id_str, self.company_name, self.combination_size, self.only_historical
                        )

                    if result['success'] and result['results']:
                        # 找到尾部概率最小的组合
                        best_combination = self.find_best_combination(result['results'])

                        if best_combination:
                            # 获取比赛结果
                            match_result = self.get_match_result(match_id_str)

                            # 计算该比赛所有匹配次数不为零的组合的总统计
                            total_matches_all = 0
                            total_wins_all = 0
                            total_draws_all = 0
                            total_losses_all = 0

                            for combination_result in result['results']:
                                if combination_result['match_count'] > 0:  # 只统计匹配次数不为零的组合
                                    total_matches_all += combination_result['match_count']
                                    stats = combination_result['result_stats']
                                    total_wins_all += stats['win']
                                    total_draws_all += stats['draw']
                                    total_losses_all += stats['loss']

                            # 构建报告项
                            report_item = {
                                'match_id': match_id_str,
                                'match_result': match_result,
                                'combination_content': best_combination['combination_content'],
                                'match_count': best_combination['match_count'],
                                'win_count': best_combination['win_count'],
                                'draw_count': best_combination['draw_count'],
                                'loss_count': best_combination['loss_count'],
                                'prob': best_combination['prob'],
                                'tail_prob': best_combination['tail_prob'],
                                # 新增：该比赛所有组合的总统计
                                'total_matches_all': total_matches_all,
                                'total_wins_all': total_wins_all,
                                'total_draws_all': total_draws_all,
                                'total_losses_all': total_losses_all
                            }

                            report_data.append(report_item)

                    # 发送进度更新
                    self.match_completed.emit(match_id_str, current_match, total_matches)

                except Exception as e:
                    logger.warning(f"分析比赛 {match_id_str} 时发生错误: {e}")
                    continue

            # 发送完成信号
            self.analysis_completed.emit(report_data)

        except Exception as e:
            logger.error(f"最近比赛分析过程中发生错误: {e}")
            self.analysis_failed.emit(f"最近比赛分析过程中发生错误: {str(e)}")

    def find_best_combination(self, results: list) -> dict:
        """找到尾部概率最小的组合（与RangeAnalysisWorker相同的逻辑）"""
        best_combination = None
        min_tail_prob = float('inf')

        for result in results:
            # 计算概率（使用与UI相同的逻辑）
            combination = result.get('combination') or result.get('target_combination')
            stats = result['result_stats']
            match_count = result['match_count']

            prob, tail_prob = self.calculate_probabilities(combination, stats, match_count)

            if tail_prob < min_tail_prob:
                min_tail_prob = tail_prob
                best_combination = {
                    'combination_content': self.format_combination_text(combination),
                    'match_count': match_count,
                    'win_count': stats['win'],
                    'draw_count': stats['draw'],
                    'loss_count': stats['loss'],
                    'prob': prob,
                    'tail_prob': tail_prob
                }

        return best_combination

    def calculate_probabilities(self, combination: list, stats: dict, match_count: int) -> tuple:
        """计算概率和尾部概率（与RangeAnalysisWorker相同的逻辑）"""
        try:
            if not PROB_CALC_AVAILABLE or match_count == 0 or len(combination) < 2:
                return 1.0, 1.0

            last_odds = combination[-1]
            home_odds = last_odds['home_odds']
            draw_odds = last_odds['draw_odds']
            away_odds = last_odds['away_odds']

            if home_odds <= 0 or draw_odds <= 0 or away_odds <= 0:
                return 1.0, 1.0

            # 计算隐含概率
            home_prob_raw = 1.0 / home_odds
            draw_prob_raw = 1.0 / draw_odds
            away_prob_raw = 1.0 / away_odds

            # 计算返还率
            return_rate = home_prob_raw + draw_prob_raw + away_prob_raw

            # 计算真实概率
            p = [home_prob_raw / return_rate, draw_prob_raw / return_rate, away_prob_raw / return_rate]

            # 构建观察结果
            observed = [stats['win'], stats['draw'], stats['loss']]

            if sum(observed) != match_count:
                return 1.0, 1.0

            # 计算概率
            prob, tail_prob = multinomial_tail_prob(p, observed)

            if prob < 0 or tail_prob < 0 or prob > 1 or tail_prob > 1:
                return 1.0, 1.0

            return prob, tail_prob

        except Exception:
            return 1.0, 1.0

    def format_combination_text(self, combination: list) -> str:
        """格式化组合文本显示（与RangeAnalysisWorker相同的逻辑）"""
        parts = []
        for i, odds in enumerate(combination):
            part = f"{i+1}:({odds['home_odds']},{odds['draw_odds']},{odds['away_odds']})"
            parts.append(part)
        return " | ".join(parts)

    def get_match_result(self, match_id: str) -> str:
        """获取比赛结果（与RangeAnalysisWorker相同的逻辑）"""
        try:
            from odds_combination_database import OddsCombinationDatabase
            database = OddsCombinationDatabase(self.db_path)

            # 查询比赛结果
            query = """
                SELECT home_score, away_score
                FROM matches
                WHERE match_id = ?
            """

            result = database.execute_query(query, (match_id,))

            if result:
                home_score = result[0][0]
                away_score = result[0][1]

                if home_score is not None and away_score is not None:
                    if home_score > away_score:
                        return f"{home_score}:{away_score} (主胜)"
                    elif home_score < away_score:
                        return f"{home_score}:{away_score} (客胜)"
                    else:
                        return f"{home_score}:{away_score} (平局)"
                else:
                    return "未知"
            else:
                return "未找到"

        except Exception as e:
            logger.warning(f"获取比赛 {match_id} 结果时发生错误: {e}")
            return "错误"

class OddsCombinationMainWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.database = None
        self.analysis_worker = None
        self.analysis_results = None
        self.range_analysis_data = None  # 存储ID区间分析的数据，用于回测
        
        self.init_ui()
        self.load_databases()
        
    def init_ui(self):
        """初始化UI界面"""
        self.setWindowTitle("赔率组合观察分析工具")
        self.setGeometry(100, 100, 1200, 800)
        
        # 创建中央窗口
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 主布局
        main_layout = QVBoxLayout(central_widget)
        
        # 创建控制面板
        control_panel = self.create_control_panel()
        main_layout.addWidget(control_panel)
        
        # 创建进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        main_layout.addWidget(self.progress_bar)
        
        # 创建状态显示
        self.status_text = QTextEdit()
        self.status_text.setMaximumHeight(100)
        self.status_text.setReadOnly(True)
        main_layout.addWidget(self.status_text)
        
        # 创建结果显示区域
        results_splitter = QSplitter(Qt.Vertical)

        # 组合统计表
        self.combination_table = self.create_combination_table()
        results_splitter.addWidget(self.combination_table)

        # 详细匹配表
        self.detail_table = self.create_detail_table()
        results_splitter.addWidget(self.detail_table)

        # 升降统计表
        self.odds_trend_table = self.create_odds_trend_table()
        results_splitter.addWidget(self.odds_trend_table)

        # 设置分割器比例
        results_splitter.setSizes([400, 200, 200])

        main_layout.addWidget(results_splitter)
        
    def create_control_panel(self) -> QWidget:
        """创建控制面板"""
        panel = QWidget()
        layout = QVBoxLayout(panel)
        
        # 第一行：数据库选择
        db_layout = QHBoxLayout()
        db_layout.addWidget(QLabel("数据库:"))
        self.db_combo = QComboBox()
        self.db_combo.currentTextChanged.connect(self.on_database_changed)
        db_layout.addWidget(self.db_combo)
        db_layout.addStretch()
        layout.addLayout(db_layout)
        
        # 第二行：比赛ID输入
        match_layout = QHBoxLayout()
        match_layout.addWidget(QLabel("比赛ID:"))
        self.match_id_input = QLineEdit()
        self.match_id_input.setPlaceholderText("请输入比赛ID，例如：2701757")
        match_layout.addWidget(self.match_id_input)
        layout.addLayout(match_layout)

        # 新增：最近比赛功能区域
        recent_matches_layout = QVBoxLayout()

        # 最近天数输入行
        recent_days_layout = QHBoxLayout()
        recent_days_layout.addWidget(QLabel("最近天数:"))
        self.recent_days_input = QSpinBox()
        self.recent_days_input.setMinimum(0)
        self.recent_days_input.setMaximum(365)
        self.recent_days_input.setValue(0)  # 默认值0
        self.recent_days_input.setToolTip("0表示今天，1表示昨天和今天，以此类推")
        recent_days_layout.addWidget(self.recent_days_input)

        # 显示最近比赛按钮
        self.show_recent_matches_button = QPushButton("显示最近比赛")
        self.show_recent_matches_button.clicked.connect(self.show_recent_matches)
        recent_days_layout.addWidget(self.show_recent_matches_button)

        recent_days_layout.addStretch()
        recent_matches_layout.addLayout(recent_days_layout)

        # 最近比赛表格
        recent_table_layout = QVBoxLayout()
        recent_table_layout.addWidget(QLabel("最近比赛:"))
        self.recent_matches_table = QTableWidget()
        self.recent_matches_table.setColumnCount(6)
        self.recent_matches_table.setHorizontalHeaderLabels([
            "比赛ID", "开赛时间", "联赛", "主队", "客队", "比赛结果"
        ])
        self.recent_matches_table.setMaximumHeight(150)  # 限制高度
        self.recent_matches_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.recent_matches_table.setAlternatingRowColors(True)
        self.recent_matches_table.horizontalHeader().setStretchLastSection(True)
        self.recent_matches_table.itemSelectionChanged.connect(self.on_recent_match_table_selected)
        recent_table_layout.addWidget(self.recent_matches_table)
        recent_matches_layout.addLayout(recent_table_layout)

        layout.addLayout(recent_matches_layout)
        
        # 第三行：博彩公司选择
        company_layout = QHBoxLayout()
        company_layout.addWidget(QLabel("博彩公司:"))
        self.company_combo = QComboBox()
        company_layout.addWidget(self.company_combo)
        company_layout.addStretch()
        layout.addLayout(company_layout)
        
        # 第四行：组合数选择
        combination_layout = QHBoxLayout()
        combination_layout.addWidget(QLabel("组合数:"))
        self.combination_spinbox = QSpinBox()
        self.combination_spinbox.setMinimum(2)
        self.combination_spinbox.setMaximum(10)
        self.combination_spinbox.setValue(3)
        combination_layout.addWidget(self.combination_spinbox)
        combination_layout.addStretch()
        layout.addLayout(combination_layout)

        # 第五行：分析器选择
        analyzer_layout = QHBoxLayout()
        analyzer_layout.addWidget(QLabel("分析器:"))
        self.analyzer_combo = QComboBox()
        self.analyzer_combo.addItem("优化分析器（推荐）", True)
        self.analyzer_combo.addItem("标准分析器", False)
        self.analyzer_combo.setCurrentIndex(0)
        analyzer_layout.addWidget(self.analyzer_combo)

        # 添加说明标签
        info_label = QLabel("优化分析器使用批量查询、缓存和并发处理，速度更快")
        info_label.setStyleSheet("color: #666; font-size: 11px;")
        analyzer_layout.addWidget(info_label)

        analyzer_layout.addStretch()
        layout.addLayout(analyzer_layout)

        # 第五点五行：时间筛选选择
        time_filter_layout = QHBoxLayout()
        time_filter_layout.addWidget(QLabel("数据筛选:"))
        self.time_filter_checkbox = QCheckBox("仅使用历史数据（推荐）")
        self.time_filter_checkbox.setChecked(True)  # 默认启用
        self.time_filter_checkbox.setToolTip("只使用开赛时间早于目标比赛的历史数据，避免未来信息泄露")
        time_filter_layout.addWidget(self.time_filter_checkbox)

        # 添加说明标签
        filter_info_label = QLabel("启用后只分析历史比赛，符合实际投注场景")
        filter_info_label.setStyleSheet("color: #666; font-size: 11px;")
        time_filter_layout.addWidget(filter_info_label)

        time_filter_layout.addStretch()
        layout.addLayout(time_filter_layout)
        
        # 第六行：ID区间分析控件
        range_layout = QHBoxLayout()
        range_layout.addWidget(QLabel("ID区间分析:"))

        # ID起始控件
        range_layout.addWidget(QLabel("起始ID:"))
        self.start_id_input = QLineEdit()
        self.start_id_input.setPlaceholderText("起始比赛ID")
        self.start_id_input.setMaximumWidth(120)
        range_layout.addWidget(self.start_id_input)

        # ID终止控件
        range_layout.addWidget(QLabel("终止ID:"))
        self.end_id_input = QLineEdit()
        self.end_id_input.setPlaceholderText("终止比赛ID")
        self.end_id_input.setMaximumWidth(120)
        range_layout.addWidget(self.end_id_input)

        range_layout.addStretch()
        layout.addLayout(range_layout)

        # 第七行：回测功能控件
        backtest_layout = QHBoxLayout()
        backtest_layout.addWidget(QLabel("回测功能:"))

        # 投资阈值输入
        backtest_layout.addWidget(QLabel("投资阈值:"))
        self.investment_threshold_input = QLineEdit()
        self.investment_threshold_input.setPlaceholderText("例如：1.05")
        self.investment_threshold_input.setText("1.05")  # 默认值
        self.investment_threshold_input.setMaximumWidth(80)
        backtest_layout.addWidget(self.investment_threshold_input)

        # 场次阈值输入
        backtest_layout.addWidget(QLabel("场次阈值:"))
        self.match_threshold_input = QLineEdit()
        self.match_threshold_input.setPlaceholderText("例如：5")
        self.match_threshold_input.setText("5")  # 默认值
        self.match_threshold_input.setMaximumWidth(60)
        backtest_layout.addWidget(self.match_threshold_input)

        # 回测按钮
        self.backtest_button = QPushButton("回测")
        self.backtest_button.clicked.connect(self.start_backtest)
        self.backtest_button.setStyleSheet("QPushButton { background-color: #FF9800; color: white; font-weight: bold; }")
        self.backtest_button.setEnabled(False)  # 初始状态禁用，需要先有ID区间分析数据
        backtest_layout.addWidget(self.backtest_button)

        backtest_layout.addStretch()
        layout.addLayout(backtest_layout)

        # 新增：观察清单功能控件
        watchlist_layout = QHBoxLayout()
        watchlist_layout.addWidget(QLabel("观察清单:"))

        # 尾部概率阈值输入
        watchlist_layout.addWidget(QLabel("尾部概率阈值:"))
        self.tail_prob_threshold_input = QLineEdit()
        self.tail_prob_threshold_input.setPlaceholderText("例如：0.05")
        self.tail_prob_threshold_input.setText("0.05")  # 默认值
        self.tail_prob_threshold_input.setMaximumWidth(80)
        self.tail_prob_threshold_input.setToolTip("筛选尾部概率小于等于此值的比赛")
        watchlist_layout.addWidget(self.tail_prob_threshold_input)

        # 加入观察清单按钮
        self.add_to_watchlist_button = QPushButton("加入观察清单")
        self.add_to_watchlist_button.clicked.connect(self.add_to_watchlist)
        self.add_to_watchlist_button.setStyleSheet("QPushButton { background-color: #E91E63; color: white; font-weight: bold; }")
        self.add_to_watchlist_button.setToolTip("根据尾部概率阈值筛选比赛并加入观察清单")
        self.add_to_watchlist_button.setEnabled(True)  # 临时启用用于测试
        watchlist_layout.addWidget(self.add_to_watchlist_button)

        watchlist_layout.addStretch()
        layout.addLayout(watchlist_layout)

        # 第八行：分析按钮
        button_layout = QHBoxLayout()
        self.analyze_button = QPushButton("开始分析")
        self.analyze_button.clicked.connect(self.start_analysis)
        button_layout.addWidget(self.analyze_button)

        # 新增：开始分析-去重按钮
        self.analyze_dedup_button = QPushButton("开始分析-去重")
        self.analyze_dedup_button.clicked.connect(self.start_analysis_with_dedup)
        self.analyze_dedup_button.setStyleSheet("QPushButton { background-color: #FF9800; color: white; font-weight: bold; }")
        self.analyze_dedup_button.setToolTip("执行标准分析后，对相同组合内容进行去重处理并统计重复次数")
        button_layout.addWidget(self.analyze_dedup_button)

        # ID区间数据分析按钮
        self.range_analyze_button = QPushButton("ID区间数据分析")
        self.range_analyze_button.clicked.connect(self.start_range_analysis)
        self.range_analyze_button.setStyleSheet("QPushButton { background-color: #4CAF50; color: white; font-weight: bold; }")
        button_layout.addWidget(self.range_analyze_button)

        # 新增：最近比赛数据分析按钮
        self.recent_matches_analyze_button = QPushButton("最近比赛数据分析")
        self.recent_matches_analyze_button.clicked.connect(self.start_recent_matches_analysis)
        self.recent_matches_analyze_button.setStyleSheet("QPushButton { background-color: #9C27B0; color: white; font-weight: bold; }")
        self.recent_matches_analyze_button.setToolTip("对最近比赛表格中的每场比赛执行数据分析")
        self.recent_matches_analyze_button.setEnabled(False)  # 初始状态禁用，需要先有最近比赛数据
        button_layout.addWidget(self.recent_matches_analyze_button)

        # 新增：升降统计按钮
        self.odds_trend_button = QPushButton("升降统计")
        self.odds_trend_button.clicked.connect(self.start_odds_trend_analysis)
        self.odds_trend_button.setStyleSheet("QPushButton { background-color: #FF5722; color: white; font-weight: bold; }")
        self.odds_trend_button.setToolTip("统计指定比赛和博彩公司的赔率升降情况")
        button_layout.addWidget(self.odds_trend_button)

        button_layout.addStretch()
        layout.addLayout(button_layout)
        
        return panel
    
    def create_combination_table(self) -> QTableWidget:
        """创建组合统计表"""
        table = QTableWidget()
        table.setColumnCount(8)
        table.setHorizontalHeaderLabels([
            "组合序号", "组合内容", "匹配次数", "主胜", "平局", "客胜", "概率", "尾部概率"
        ])
        
        # 设置表格属性
        table.setSelectionBehavior(QAbstractItemView.SelectRows)
        table.setAlternatingRowColors(True)
        table.horizontalHeader().setStretchLastSection(True)
        table.itemSelectionChanged.connect(self.on_combination_selected)
        
        return table
    
    def create_detail_table(self) -> QTableWidget:
        """创建详细匹配表"""
        table = QTableWidget()
        table.setColumnCount(4)
        table.setHorizontalHeaderLabels([
            "比赛ID", "比赛结果", "匹配位置", "比分"
        ])

        # 设置表格属性
        table.setAlternatingRowColors(True)
        table.horizontalHeader().setStretchLastSection(True)

        return table

    def create_odds_trend_table(self) -> QTableWidget:
        """创建升降统计表"""
        table = QTableWidget()
        table.setColumnCount(10)
        table.setHorizontalHeaderLabels([
            "比赛ID", "博彩公司", "结果类型", "下降次数", "上升次数",
            "初始赔率", "最终赔率", "赔率差值", "变化率(%)", "数据点数"
        ])

        # 设置表格属性
        table.setAlternatingRowColors(True)
        table.horizontalHeader().setStretchLastSection(True)
        table.setSelectionBehavior(QAbstractItemView.SelectRows)

        # 设置列宽
        table.setColumnWidth(0, 80)   # 比赛ID
        table.setColumnWidth(1, 100)  # 博彩公司
        table.setColumnWidth(2, 80)   # 结果类型
        table.setColumnWidth(3, 80)   # 下降次数
        table.setColumnWidth(4, 80)   # 上升次数
        table.setColumnWidth(5, 80)   # 初始赔率
        table.setColumnWidth(6, 80)   # 最终赔率
        table.setColumnWidth(7, 80)   # 赔率差值
        table.setColumnWidth(8, 80)   # 变化率
        table.setColumnWidth(9, 80)   # 数据点数

        return table
    
    def load_databases(self):
        """加载可用数据库"""
        try:
            # 创建临时数据库对象来获取数据库列表
            temp_db = OddsCombinationDatabase()
            databases = temp_db.get_available_databases()
            
            self.db_combo.clear()
            for db_path in databases:
                db_name = os.path.basename(db_path)
                self.db_combo.addItem(db_name, db_path)
            
            # 默认选择第一个数据库
            if databases:
                self.on_database_changed(self.db_combo.currentText())
                
        except Exception as e:
            logger.error(f"加载数据库列表失败: {e}")
            QMessageBox.critical(self, "错误", f"加载数据库列表失败: {str(e)}")
    
    def on_database_changed(self, db_name: str):
        """数据库选择改变"""
        try:
            db_path = self.db_combo.currentData()
            if db_path:
                self.database = OddsCombinationDatabase(db_path)
                self.load_companies()
                self.update_status(f"已切换到数据库: {db_name}")
        except Exception as e:
            logger.error(f"切换数据库失败: {e}")
            QMessageBox.critical(self, "错误", f"切换数据库失败: {str(e)}")
    
    def load_companies(self):
        """加载博彩公司列表"""
        if not self.database:
            return
            
        try:
            companies = self.database.get_available_companies()
            self.company_combo.clear()
            self.company_combo.addItems(companies)
            
            # 默认选择bet365（如果存在）
            bet365_index = self.company_combo.findText("bet365")
            if bet365_index >= 0:
                self.company_combo.setCurrentIndex(bet365_index)
                
        except Exception as e:
            logger.error(f"加载博彩公司列表失败: {e}")
            QMessageBox.warning(self, "警告", f"加载博彩公司列表失败: {str(e)}")
    
    def update_status(self, message: str):
        """更新状态显示"""
        from datetime import datetime
        timestamp = datetime.now().strftime("%H:%M:%S")
        self.status_text.append(f"[{timestamp}] {message}")
        self.status_text.verticalScrollBar().setValue(
            self.status_text.verticalScrollBar().maximum()
        )

    def start_analysis(self):
        """开始分析"""
        # 验证输入
        match_id = self.match_id_input.text().strip()
        if not match_id:
            QMessageBox.warning(self, "警告", "请输入比赛ID")
            return

        company_name = self.company_combo.currentText()
        if not company_name:
            QMessageBox.warning(self, "警告", "请选择博彩公司")
            return

        combination_size = self.combination_spinbox.value()
        db_path = self.db_combo.currentData()

        if not db_path:
            QMessageBox.warning(self, "警告", "请选择数据库")
            return

        # 禁用分析按钮
        self.analyze_button.setEnabled(False)
        self.progress_bar.setVisible(True)
        self.progress_bar.setRange(0, 0)  # 不确定进度

        # 清空之前的结果
        self.combination_table.setRowCount(0)
        self.detail_table.setRowCount(0)

        self.update_status(f"开始分析比赛 {match_id} 的赔率组合...")

        # 获取分析器选择
        use_optimized = self.analyzer_combo.currentData()

        # 获取时间筛选选择
        only_historical = self.time_filter_checkbox.isChecked()

        # 创建并启动工作线程
        self.analysis_worker = AnalysisWorker(db_path, match_id, company_name, combination_size, use_optimized, only_historical)
        self.analysis_worker.progress_updated.connect(self.update_status)
        self.analysis_worker.analysis_completed.connect(self.on_analysis_completed)
        self.analysis_worker.analysis_failed.connect(self.on_analysis_failed)
        self.analysis_worker.start()

    def start_range_analysis(self):
        """开始ID区间数据分析"""
        # 验证输入
        start_id = self.start_id_input.text().strip()
        end_id = self.end_id_input.text().strip()

        if not start_id or not end_id:
            QMessageBox.warning(self, "警告", "请输入起始ID和终止ID")
            return

        try:
            start_id_int = int(start_id)
            end_id_int = int(end_id)

            if start_id_int >= end_id_int:
                QMessageBox.warning(self, "警告", "起始ID必须小于终止ID")
                return

            if end_id_int - start_id_int > 100:
                reply = QMessageBox.question(self, "确认",
                    f"您要分析 {end_id_int - start_id_int + 1} 场比赛，这可能需要较长时间。是否继续？",
                    QMessageBox.Yes | QMessageBox.No)
                if reply != QMessageBox.Yes:
                    return

        except ValueError:
            QMessageBox.warning(self, "警告", "请输入有效的数字ID")
            return

        company_name = self.company_combo.currentText()
        if not company_name:
            QMessageBox.warning(self, "警告", "请选择博彩公司")
            return

        combination_size = self.combination_spinbox.value()
        db_path = self.db_combo.currentData()

        if not db_path:
            QMessageBox.warning(self, "警告", "请选择数据库")
            return

        # 禁用按钮
        self.analyze_button.setEnabled(False)
        self.range_analyze_button.setEnabled(False)
        self.progress_bar.setVisible(True)
        self.progress_bar.setRange(0, end_id_int - start_id_int + 1)
        self.progress_bar.setValue(0)

        # 清空之前的结果
        self.combination_table.setRowCount(0)
        self.detail_table.setRowCount(0)

        # 重置回测状态
        self.range_analysis_data = None
        self.backtest_button.setEnabled(False)

        self.update_status(f"开始ID区间分析: {start_id} 到 {end_id} ({end_id_int - start_id_int + 1} 场比赛)")

        # 获取分析器选择
        use_optimized = self.analyzer_combo.currentData()

        # 获取时间筛选选择
        only_historical = self.time_filter_checkbox.isChecked()

        # 创建并启动区间分析工作线程
        self.range_analysis_worker = RangeAnalysisWorker(
            db_path, start_id_int, end_id_int, company_name, combination_size, use_optimized, only_historical
        )
        self.range_analysis_worker.progress_updated.connect(self.update_status)
        self.range_analysis_worker.match_completed.connect(self.on_range_match_completed)
        self.range_analysis_worker.analysis_completed.connect(self.on_range_analysis_completed)
        self.range_analysis_worker.analysis_failed.connect(self.on_range_analysis_failed)
        self.range_analysis_worker.start()

    def on_analysis_completed(self, results: Dict):
        """分析完成处理"""
        self.analysis_results = results
        self.populate_combination_table(results)

        # 恢复UI状态
        self.analyze_button.setEnabled(True)
        self.progress_bar.setVisible(False)

        # 启用加入观察清单按钮（如果有结果）
        if results.get('results') and len(results['results']) > 0:
            self.add_to_watchlist_button.setEnabled(True)

        self.update_status(f"分析完成！共生成 {results['total_combinations']} 个组合")

    def on_range_match_completed(self, match_id: str, current: int, total: int):
        """区间分析中单场比赛完成"""
        self.progress_bar.setValue(current)
        self.update_status(f"已完成比赛 {match_id} ({current}/{total})")

    def on_range_analysis_completed(self, report_data: list):
        """区间分析完成处理"""
        # 保存ID区间分析数据用于回测
        self.range_analysis_data = report_data

        self.display_range_analysis_report(report_data)

        # 恢复UI状态
        self.analyze_button.setEnabled(True)

        # 启用回测按钮（如果有数据）
        if report_data:
            self.backtest_button.setEnabled(True)
        self.range_analyze_button.setEnabled(True)
        self.progress_bar.setVisible(False)

        self.update_status(f"ID区间分析完成！共分析 {len(report_data)} 场比赛")

    def get_match_result_for_backtest(self, match_id: str) -> str:
        """获取比赛结果用于回测（重新索引）"""
        try:
            print(f"DEBUG: 开始获取比赛 {match_id} 的结果")

            if not self.database:
                print(f"DEBUG: 数据库连接不存在")
                return "未知"

            # 使用数据库对象的方法获取比赛信息
            match_info = self.database.get_match_info(match_id)
            print(f"DEBUG: 获取到的比赛信息: {match_info}")

            if match_info:
                home_score = match_info.get('home_score')
                away_score = match_info.get('away_score')

                print(f"DEBUG: 主队得分: {home_score}, 客队得分: {away_score}")
                print(f"DEBUG: 得分类型: home_score={type(home_score)}, away_score={type(away_score)}")

                if home_score is not None and away_score is not None:
                    try:
                        # 将TEXT类型的得分转换为数值进行比较
                        home_score_int = int(home_score)
                        away_score_int = int(away_score)
                        print(f"DEBUG: 转换后的得分: {home_score_int} vs {away_score_int}")

                        if home_score_int > away_score_int:
                            print(f"DEBUG: 判断结果: 主胜")
                            return "主胜"
                        elif home_score_int < away_score_int:
                            print(f"DEBUG: 判断结果: 客胜")
                            return "客胜"
                        else:
                            print(f"DEBUG: 判断结果: 平局")
                            return "平局"
                    except (ValueError, TypeError) as e:
                        print(f"DEBUG: 得分转换失败: {e}")
                        return "未知"
                else:
                    print(f"DEBUG: 得分数据为空")
            else:
                print(f"DEBUG: 未找到比赛 {match_id} 的记录")

            print(f"DEBUG: 返回未知结果")
            return "未知"

        except Exception as e:
            print(f"DEBUG: 获取比赛结果时发生错误: {e}")
            logger.warning(f"获取比赛 {match_id} 结果时发生错误: {e}")
            return "未知"

    def start_backtest(self):
        """开始回测"""
        if not self.range_analysis_data:
            QMessageBox.warning(self, "警告", "请先进行ID区间数据分析！")
            return

        try:
            # 获取回测参数
            investment_threshold = float(self.investment_threshold_input.text())
            match_threshold = int(self.match_threshold_input.text())
        except ValueError:
            QMessageBox.warning(self, "参数错误", "请输入有效的投资阈值（小数）和场次阈值（整数）！")
            return

        # 执行回测
        self.perform_backtest(investment_threshold, match_threshold)

    def perform_backtest(self, investment_threshold: float, match_threshold: int):
        """执行回测分析"""
        if not self.range_analysis_data:
            return

        backtest_results = []
        total_investment = 0
        total_return = 0
        win_count = 0
        total_bets = 0

        self.update_status("开始回测分析...")

        for report_item in self.range_analysis_data:
            match_id = report_item['match_id']

            print(f"\n=== 处理比赛 {match_id} ===")
            print(f"报告项内容: {report_item}")

            # 重新获取比赛结果
            actual_result = self.get_match_result_for_backtest(match_id)
            print(f"获取到的实际结果: {actual_result}")

            # 获取总统计数据
            total_wins_all = report_item.get('total_wins_all', 0)
            total_matches_all = report_item.get('total_matches_all', 0)

            # 检查是否满足场次阈值
            if total_wins_all >= match_threshold and total_matches_all > 0:
                # 解析组合内容获取第二组赔率的主胜赔率
                combination_content = report_item.get('combination_content', '')
                home_odds = self.extract_home_odds_from_combination(combination_content)

                if home_odds > 0:
                    # 计算投资条件：总主胜/总匹配次数 * 主胜赔率 >= 投资阈值
                    win_rate = total_wins_all / total_matches_all
                    investment_value = win_rate * home_odds

                    if investment_value >= investment_threshold:
                        # 满足投资条件，记录投资
                        total_bets += 1
                        total_investment += 1  # 假设每次投资1单位

                        # 计算收益
                        if actual_result == "主胜":
                            return_value = home_odds
                            total_return += return_value
                            win_count += 1
                        else:
                            return_value = 0

                        # DEBUG: 输出详细信息
                        print(f"DEBUG: 比赛{match_id}")
                        print(f"  组合内容: {combination_content}")
                        print(f"  实际结果: {actual_result}")
                        print(f"  总主胜: {total_wins_all}, 总匹配: {total_matches_all}")
                        print(f"  胜率: {win_rate:.3f}")
                        print(f"  主胜赔率: {home_odds}")
                        print(f"  投资价值: {investment_value:.3f}")
                        print(f"  回报: {return_value}")
                        print(f"  净利润: {return_value - 1}")
                        print("-" * 40)

                        # 记录回测结果
                        backtest_results.append({
                            'match_id': match_id,
                            'actual_result': actual_result,
                            'total_wins': total_wins_all,
                            'total_matches': total_matches_all,
                            'win_rate': win_rate,
                            'home_odds': home_odds,
                            'investment_value': investment_value,
                            'bet_result': actual_result,
                            'return_value': return_value,
                            'profit': return_value - 1
                        })

        # 显示回测结果
        self.display_backtest_results(backtest_results, total_investment, total_return, win_count, total_bets)

    def extract_home_odds_from_combination(self, combination_content: str) -> float:
        """从组合内容中提取第二组赔率的主胜赔率"""
        try:
            print(f"DEBUG: 开始解析组合内容: '{combination_content}'")

            # 组合内容格式示例：1:(1.9,3.4,4.1)|2:(2.1,3.2,3.8)|...
            # 需要提取第二组（2:）的主胜赔率（第一个数字）

            parts = combination_content.split('|')
            print(f"DEBUG: 分割后的部分: {parts}")

            if len(parts) >= 2:
                second_part = parts[1]  # 第二组
                print(f"DEBUG: 第二组内容: '{second_part}'")

                # 提取括号内的内容
                if ':(' in second_part and ')' in second_part:
                    odds_str = second_part.split(':(')[1].split(')')[0]
                    print(f"DEBUG: 赔率字符串: '{odds_str}'")

                    odds_values = odds_str.split(',')
                    print(f"DEBUG: 赔率数组: {odds_values}")

                    if len(odds_values) >= 1:
                        home_odds = float(odds_values[0])  # 主胜赔率
                        print(f"DEBUG: 提取的主胜赔率: {home_odds}")
                        return home_odds
                else:
                    print(f"DEBUG: 第二组格式不正确，没有找到 ':(' 和 ')'")
            else:
                print(f"DEBUG: 组合内容分割后少于2部分")

            print(f"DEBUG: 无法提取赔率，返回0.0")
            return 0.0

        except Exception as e:
            print(f"DEBUG: 解析组合内容时发生错误: {e}")
            logger.warning(f"解析组合内容时发生错误: {e}")
            return 0.0

    def display_backtest_results(self, results: list, total_investment: float, total_return: float, win_count: int, total_bets: int):
        """显示回测结果"""
        if not results:
            QMessageBox.information(self, "回测结果", "没有找到满足投资条件的比赛。")
            return

        # 计算统计数据
        net_profit = total_return - total_investment
        win_rate = (win_count / total_bets * 100) if total_bets > 0 else 0
        roi = (net_profit / total_investment * 100) if total_investment > 0 else 0

        # 创建结果消息
        result_message = f"""回测结果统计：

总投资次数：{total_bets}
获胜次数：{win_count}
胜率：{win_rate:.2f}%

总投资金额：{total_investment:.2f}
总回报金额：{total_return:.2f}
净利润：{net_profit:.2f}
投资回报率：{roi:.2f}%

详细结果已在状态栏显示。"""

        QMessageBox.information(self, "回测完成", result_message)

        # 在状态栏显示详细结果
        detailed_results = []
        for result in results:
            detailed_results.append(
                f"比赛{result['match_id']}: 实际{result['actual_result']}, "
                f"胜率{result['win_rate']:.3f}, 赔率{result['home_odds']:.2f}, "
                f"投资值{result['investment_value']:.3f}, 收益{result['profit']:.2f}"
            )

        self.update_status(f"回测完成 | {'; '.join(detailed_results[:5])}{'...' if len(detailed_results) > 5 else ''}")

    def on_range_analysis_failed(self, error_message: str):
        """区间分析失败处理"""
        QMessageBox.critical(self, "区间分析失败", error_message)

        # 恢复UI状态
        self.analyze_button.setEnabled(True)
        self.range_analyze_button.setEnabled(True)
        self.progress_bar.setVisible(False)

        # 禁用回测按钮
        self.backtest_button.setEnabled(False)
        self.range_analysis_data = None

        self.update_status(f"区间分析失败: {error_message}")

    def display_range_analysis_report(self, report_data: list):
        """显示区间分析报告"""
        # 清空现有表格
        self.combination_table.setRowCount(len(report_data))

        # 修改表格列数以适应新增的4列总统计
        self.combination_table.setColumnCount(12)

        # 修改表格标题以适应报告格式（新增4列总统计）
        self.combination_table.setHorizontalHeaderLabels([
            "比赛ID", "比赛结果", "组合内容", "匹配次数", "主胜", "平局", "客胜", "尾部概率",
            "总匹配次数", "总主胜", "总平局", "总客胜"
        ])

        for row, report_item in enumerate(report_data):
            # 比赛ID
            self.combination_table.setItem(row, 0, QTableWidgetItem(str(report_item['match_id'])))

            # 比赛结果
            match_result = report_item.get('match_result', '未知')
            self.combination_table.setItem(row, 1, QTableWidgetItem(match_result))

            # 组合内容
            combination_text = report_item.get('combination_content', '')
            self.combination_table.setItem(row, 2, QTableWidgetItem(combination_text))

            # 匹配次数
            match_count = report_item.get('match_count', 0)
            self.combination_table.setItem(row, 3, QTableWidgetItem(str(match_count)))

            # 主胜
            win_count = report_item.get('win_count', 0)
            self.combination_table.setItem(row, 4, QTableWidgetItem(str(win_count)))

            # 平局
            draw_count = report_item.get('draw_count', 0)
            self.combination_table.setItem(row, 5, QTableWidgetItem(str(draw_count)))

            # 客胜
            loss_count = report_item.get('loss_count', 0)
            self.combination_table.setItem(row, 6, QTableWidgetItem(str(loss_count)))

            # 尾部概率
            tail_prob = report_item.get('tail_prob', 1.0)
            self.combination_table.setItem(row, 7, QTableWidgetItem(f"{tail_prob:.6f}"))

            # 新增：总统计4列
            # 总匹配次数
            total_matches_all = report_item.get('total_matches_all', 0)
            self.combination_table.setItem(row, 8, QTableWidgetItem(str(total_matches_all)))

            # 总主胜
            total_wins_all = report_item.get('total_wins_all', 0)
            self.combination_table.setItem(row, 9, QTableWidgetItem(str(total_wins_all)))

            # 总平局
            total_draws_all = report_item.get('total_draws_all', 0)
            self.combination_table.setItem(row, 10, QTableWidgetItem(str(total_draws_all)))

            # 总客胜
            total_losses_all = report_item.get('total_losses_all', 0)
            self.combination_table.setItem(row, 11, QTableWidgetItem(str(total_losses_all)))

            # 如果尾部概率很小，高亮显示（扩展到新的列）
            if tail_prob < 0.05:
                for col in range(12):  # 扩展到12列
                    item = self.combination_table.item(row, col)
                    if item:
                        if tail_prob < 0.01:
                            item.setBackground(QColor(255, 200, 200))  # 浅红色
                        else:
                            item.setBackground(QColor(255, 255, 200))  # 浅黄色

        # 调整列宽
        self.combination_table.resizeColumnsToContents()

        # 清空详细表格
        self.detail_table.setRowCount(0)

    def on_analysis_failed(self, error_message: str):
        """分析失败处理"""
        QMessageBox.critical(self, "分析失败", error_message)

        # 恢复UI状态
        self.analyze_button.setEnabled(True)
        self.progress_bar.setVisible(False)

        self.update_status(f"分析失败: {error_message}")

    def populate_combination_table(self, results: Dict):
        """填充组合统计表"""
        combination_results = results['results']

        # 确保表格列数正确（普通分析模式为8列）
        self.combination_table.setColumnCount(8)

        # 恢复普通分析模式的表格标题
        self.combination_table.setHorizontalHeaderLabels([
            "组合序号", "组合内容", "匹配次数", "主胜", "平局", "客胜", "概率", "尾部概率"
        ])

        # 计算总统计（只统计匹配次数不为零的行）
        total_matches = 0
        total_wins = 0
        total_draws = 0
        total_losses = 0

        for result in combination_results:
            if result['match_count'] > 0:  # 只统计匹配次数不为零的行
                total_matches += result['match_count']
                stats = result['result_stats']
                total_wins += stats['win']
                total_draws += stats['draw']
                total_losses += stats['loss']

        # 设置表格行数（原有行数 + 1行总统计）
        self.combination_table.setRowCount(len(combination_results) + 1)

        # 首行：插入总统计
        self.combination_table.setItem(0, 0, QTableWidgetItem("总计"))
        self.combination_table.setItem(0, 1, QTableWidgetItem("所有匹配组合的统计汇总"))
        self.combination_table.setItem(0, 2, QTableWidgetItem(str(total_matches)))
        self.combination_table.setItem(0, 3, QTableWidgetItem(str(total_wins)))
        self.combination_table.setItem(0, 4, QTableWidgetItem(str(total_draws)))
        self.combination_table.setItem(0, 5, QTableWidgetItem(str(total_losses)))
        self.combination_table.setItem(0, 6, QTableWidgetItem("-"))  # 概率列显示"-"
        self.combination_table.setItem(0, 7, QTableWidgetItem("-"))  # 尾部概率列显示"-"

        # 设置总统计行的样式（加粗、背景色）
        from PyQt5.QtCore import Qt
        from PyQt5.QtGui import QFont, QBrush, QColor

        bold_font = QFont()
        bold_font.setBold(True)
        light_blue = QBrush(QColor(230, 240, 255))  # 浅蓝色背景

        for col in range(8):
            item = self.combination_table.item(0, col)
            if item:
                item.setFont(bold_font)
                item.setBackground(light_blue)
                item.setTextAlignment(Qt.AlignCenter)

        # 填充其他行（从第2行开始，索引为1）
        for row, result in enumerate(combination_results, 1):
            # 组合序号
            self.combination_table.setItem(row, 0, QTableWidgetItem(str(result['combination_index'] + 1)))

            # 组合内容（显示赔率）- 兼容两种分析器
            combination = result.get('combination') or result.get('target_combination')
            combination_text = self.format_combination_text(combination)
            self.combination_table.setItem(row, 1, QTableWidgetItem(combination_text))

            # 匹配次数
            self.combination_table.setItem(row, 2, QTableWidgetItem(str(result['match_count'])))

            # 胜平负统计
            stats = result['result_stats']
            self.combination_table.setItem(row, 3, QTableWidgetItem(str(stats['win'])))
            self.combination_table.setItem(row, 4, QTableWidgetItem(str(stats['draw'])))
            self.combination_table.setItem(row, 5, QTableWidgetItem(str(stats['loss'])))

            # 计算概率和尾部概率
            prob, tail_prob = self.calculate_probabilities(combination, stats, result['match_count'])
            self.combination_table.setItem(row, 6, QTableWidgetItem(f"{prob:.6f}"))
            self.combination_table.setItem(row, 7, QTableWidgetItem(f"{tail_prob:.6f}"))

        # 调整列宽
        self.combination_table.resizeColumnsToContents()

    def format_combination_text(self, combination: List[Dict]) -> str:
        """格式化组合文本显示"""
        parts = []
        for i, odds in enumerate(combination):
            part = f"{i+1}:({odds['home_odds']},{odds['draw_odds']},{odds['away_odds']})"
            parts.append(part)
        return " | ".join(parts)

    def calculate_probabilities(self, combination: List[Dict], stats: Dict, match_count: int) -> tuple:
        """
        计算概率和尾部概率

        Args:
            combination: 组合数据，包含赔率信息
            stats: 胜平负统计
            match_count: 匹配次数

        Returns:
            (prob, tail_prob): 概率和尾部概率的元组
        """
        try:
            # 如果概率计算模块不可用，返回默认值
            if not PROB_CALC_AVAILABLE:
                return 1.0, 1.0

            # 如果匹配次数为0，直接返回1
            if match_count == 0:
                return 1.0, 1.0

            # 获取最后一个赔率（组合2的赔率）
            if len(combination) < 2:
                return 1.0, 1.0

            last_odds = combination[-1]  # 取最后一个赔率
            home_odds = last_odds['home_odds']
            draw_odds = last_odds['draw_odds']
            away_odds = last_odds['away_odds']

            # 验证赔率有效性
            if home_odds <= 0 or draw_odds <= 0 or away_odds <= 0:
                logger.warning(f"无效赔率: {home_odds}, {draw_odds}, {away_odds}")
                return 1.0, 1.0

            # 计算隐含概率（使用返还率除以赔率）
            # 先计算原始概率
            home_prob_raw = 1.0 / home_odds
            draw_prob_raw = 1.0 / draw_odds
            away_prob_raw = 1.0 / away_odds

            # 计算返还率
            return_rate = home_prob_raw + draw_prob_raw + away_prob_raw

            # 计算真实概率（归一化）
            p = [home_prob_raw / return_rate, draw_prob_raw / return_rate, away_prob_raw / return_rate]

            # 构建观察到的结果组合
            observed = [stats['win'], stats['draw'], stats['loss']]

            # 验证观察数据
            if sum(observed) != match_count:
                logger.warning(f"观察数据不一致: 总数{sum(observed)} != 匹配次数{match_count}")
                return 1.0, 1.0

            # 使用multinomial_tail_prob计算概率
            prob, tail_prob = multinomial_tail_prob(p, observed)

            # 验证结果
            if prob < 0 or tail_prob < 0 or prob > 1 or tail_prob > 1:
                logger.warning(f"概率结果异常: prob={prob}, tail_prob={tail_prob}")
                return 1.0, 1.0

            return prob, tail_prob

        except Exception as e:
            logger.error(f"计算概率时发生错误: {e}")
            import traceback
            logger.error(traceback.format_exc())
            return 1.0, 1.0

    def on_combination_selected(self):
        """组合选择改变"""
        if not self.analysis_results:
            return

        selected_rows = self.combination_table.selectionModel().selectedRows()
        if not selected_rows:
            self.detail_table.setRowCount(0)
            return

        row = selected_rows[0].row()

        # 跳过第一行（总统计行），第一行不显示详细信息
        if row == 0:
            self.detail_table.setRowCount(0)
            return

        # 调整行索引，因为第一行是总统计，实际数据从第二行开始
        actual_row = row - 1
        if actual_row < 0 or actual_row >= len(self.analysis_results['results']):
            return

        # 获取选中组合的匹配详情
        combination_result = self.analysis_results['results'][actual_row]
        matched_matches = combination_result['matched_matches']

        self.populate_detail_table(matched_matches)

    def populate_detail_table(self, matched_matches: List[Dict]):
        """填充详细匹配表"""
        self.detail_table.setRowCount(len(matched_matches))

        for row, match in enumerate(matched_matches):
            # 比赛ID
            self.detail_table.setItem(row, 0, QTableWidgetItem(match['match_id']))

            # 比赛结果类型
            result_type_text = self.get_result_type_text(match['result_type'])
            self.detail_table.setItem(row, 1, QTableWidgetItem(result_type_text))

            # 匹配位置
            self.detail_table.setItem(row, 2, QTableWidgetItem(str(match['match_position'])))

            # 比分
            match_result = match.get('match_result')
            if match_result:
                score_text = f"{match_result.get('home_score', '?')}:{match_result.get('away_score', '?')}"
            else:
                score_text = "未知"
            self.detail_table.setItem(row, 3, QTableWidgetItem(score_text))

        # 调整列宽
        self.detail_table.resizeColumnsToContents()

    def get_result_type_text(self, result_type: str) -> str:
        """获取结果类型文本"""
        type_map = {
            'win': '主胜',
            'draw': '平局',
            'loss': '客胜',
            'unknown': '未知'
        }
        return type_map.get(result_type, result_type)

    def show_recent_matches(self):
        """显示最近比赛"""
        if not self.database:
            QMessageBox.warning(self, "警告", "请先选择数据库")
            return

        days = self.recent_days_input.value()

        try:
            # 获取最近比赛
            recent_matches = self.database.get_recent_matches(days)

            # 清空表格
            self.recent_matches_table.setRowCount(0)

            if not recent_matches:
                self.update_status(f"未找到最近 {days} 天内的比赛")
                return

            # 设置表格行数
            self.recent_matches_table.setRowCount(len(recent_matches))

            # 填充表格
            for row, match in enumerate(recent_matches):
                match_id = match['match_id']
                home_team = match.get('home_team', '未知')
                away_team = match.get('away_team', '未知')
                league = match.get('league', '未知联赛')
                display_time = match.get('display_time', '未知时间')

                # 获取比赛结果
                match_result = self.get_match_result_text(match)

                # 填充各列
                self.recent_matches_table.setItem(row, 0, QTableWidgetItem(str(match_id)))
                self.recent_matches_table.setItem(row, 1, QTableWidgetItem(display_time))
                self.recent_matches_table.setItem(row, 2, QTableWidgetItem(league))
                self.recent_matches_table.setItem(row, 3, QTableWidgetItem(home_team))
                self.recent_matches_table.setItem(row, 4, QTableWidgetItem(away_team))
                self.recent_matches_table.setItem(row, 5, QTableWidgetItem(match_result))

            # 调整列宽
            self.recent_matches_table.resizeColumnsToContents()

            # 启用最近比赛数据分析按钮
            self.recent_matches_analyze_button.setEnabled(True)

            self.update_status(f"找到最近 {days} 天内的 {len(recent_matches)} 场比赛")

        except Exception as e:
            logger.error(f"查询最近比赛失败: {e}")
            QMessageBox.critical(self, "错误", f"查询最近比赛失败: {str(e)}")
            # 查询失败时禁用按钮
            self.recent_matches_analyze_button.setEnabled(False)

    def on_recent_match_table_selected(self):
        """选择最近比赛表格行时的处理"""
        selected_rows = self.recent_matches_table.selectionModel().selectedRows()
        if selected_rows:
            row = selected_rows[0].row()
            # 获取比赛ID（第0列）
            match_id_item = self.recent_matches_table.item(row, 0)
            if match_id_item:
                match_id = match_id_item.text()
                # 自动填入比赛ID输入框
                self.match_id_input.setText(match_id)
                self.update_status(f"已选择比赛 {match_id}")
            else:
                logger.warning("无法获取比赛ID")

    def get_match_result_text(self, match_info):
        """获取比赛结果文本"""
        try:
            home_score = match_info.get('home_score')
            away_score = match_info.get('away_score')
            match_state = match_info.get('match_state')

            # 如果比赛状态表明未开始或进行中，直接返回状态
            if match_state and match_state in ['未开始', '进行中', '推迟', '取消']:
                return match_state

            # 如果有比分信息
            if home_score is not None and away_score is not None:
                try:
                    home_score_int = int(home_score)
                    away_score_int = int(away_score)

                    if home_score_int > away_score_int:
                        result_text = "主胜"
                    elif home_score_int < away_score_int:
                        result_text = "客胜"
                    else:
                        result_text = "平局"

                    return f"{home_score}:{away_score} ({result_text})"

                except (ValueError, TypeError):
                    return f"{home_score}:{away_score}"

            # 如果没有比分信息，返回比赛状态或未知
            return match_state or "未知"

        except Exception as e:
            logger.warning(f"获取比赛结果失败: {e}")
            return "未知"

    def start_analysis_with_dedup(self):
        """开始分析-去重"""
        # 验证输入
        match_id = self.match_id_input.text().strip()
        if not match_id:
            QMessageBox.warning(self, "警告", "请输入比赛ID")
            return

        company_name = self.company_combo.currentText()
        if not company_name:
            QMessageBox.warning(self, "警告", "请选择博彩公司")
            return

        combination_size = self.combination_spinbox.value()
        db_path = self.db_combo.currentData()

        if not db_path:
            QMessageBox.warning(self, "警告", "请选择数据库")
            return

        # 禁用分析按钮
        self.analyze_button.setEnabled(False)
        self.analyze_dedup_button.setEnabled(False)
        self.progress_bar.setVisible(True)
        self.progress_bar.setRange(0, 0)  # 不确定进度

        # 清空之前的结果
        self.combination_table.setRowCount(0)
        self.detail_table.setRowCount(0)

        self.update_status(f"开始分析比赛 {match_id} 的赔率组合（去重模式）...")

        # 获取分析器选择
        use_optimized = self.analyzer_combo.currentData()

        # 获取时间筛选选择
        only_historical = self.time_filter_checkbox.isChecked()

        # 创建并启动工作线程
        self.analysis_worker = AnalysisWorker(db_path, match_id, company_name, combination_size, use_optimized, only_historical)
        self.analysis_worker.progress_updated.connect(self.update_status)
        self.analysis_worker.analysis_completed.connect(self.on_analysis_completed_with_dedup)
        self.analysis_worker.analysis_failed.connect(self.on_analysis_failed_with_dedup)
        self.analysis_worker.start()

    def on_analysis_completed_with_dedup(self, results: Dict):
        """分析完成处理（去重模式）"""
        self.analysis_results = results

        # 执行去重处理
        deduped_results = self.deduplicate_combinations(results)

        # 使用去重后的结果填充表格
        self.populate_combination_table_with_dedup(deduped_results)

        # 恢复UI状态
        self.analyze_button.setEnabled(True)
        self.analyze_dedup_button.setEnabled(True)
        self.progress_bar.setVisible(False)

        # 启用加入观察清单按钮（如果有结果）
        if results.get('results') and len(results['results']) > 0:
            self.add_to_watchlist_button.setEnabled(True)

        original_count = results['total_combinations']
        deduped_count = len(deduped_results['results'])
        self.update_status(f"去重分析完成！原始组合 {original_count} 个，去重后 {deduped_count} 个")

    def on_analysis_failed_with_dedup(self, error_message: str):
        """分析失败处理（去重模式）"""
        QMessageBox.critical(self, "分析失败", error_message)

        # 恢复UI状态
        self.analyze_button.setEnabled(True)
        self.analyze_dedup_button.setEnabled(True)
        self.progress_bar.setVisible(False)

        self.update_status(f"去重分析失败: {error_message}")

    def deduplicate_combinations(self, results: Dict) -> Dict:
        """对组合进行去重处理"""
        try:
            combination_results = results['results']

            # 用于存储去重后的结果
            deduped_combinations = {}
            deduped_results = []

            self.update_status("正在进行组合去重处理...")

            for result in combination_results:
                # 获取组合内容（兼容两种分析器）
                combination = result.get('combination') or result.get('target_combination')
                combination_text = self.format_combination_text(combination)

                # 如果这个组合内容已经存在，累加统计数据
                if combination_text in deduped_combinations:
                    existing = deduped_combinations[combination_text]

                    # 累加匹配次数和胜平负统计
                    existing['match_count'] += result['match_count']
                    existing['result_stats']['win'] += result['result_stats']['win']
                    existing['result_stats']['draw'] += result['result_stats']['draw']
                    existing['result_stats']['loss'] += result['result_stats']['loss']

                    # 合并匹配的比赛列表
                    existing['matched_matches'].extend(result['matched_matches'])

                    # 增加重复次数
                    existing['duplicate_count'] += 1

                else:
                    # 第一次出现的组合，直接添加
                    new_result = result.copy()
                    new_result['duplicate_count'] = 1  # 初始重复次数为1
                    deduped_combinations[combination_text] = new_result

            # 转换为列表格式
            for combination_text, result in deduped_combinations.items():
                deduped_results.append(result)

            # 按匹配次数降序排序
            deduped_results.sort(key=lambda x: x['match_count'], reverse=True)

            # 重新分配组合序号
            for i, result in enumerate(deduped_results):
                result['combination_index'] = i

            # 构建返回结果
            deduped_result = {
                'success': True,
                'results': deduped_results,
                'total_combinations': len(deduped_results),
                'analysis_time': results.get('analysis_time', 0)
            }

            return deduped_result

        except Exception as e:
            logger.error(f"去重处理失败: {e}")
            # 如果去重失败，返回原始结果
            return results

    def populate_combination_table_with_dedup(self, results: Dict):
        """填充组合统计表（去重模式）"""
        combination_results = results['results']

        # 设置表格列数（增加重复次数列）
        self.combination_table.setColumnCount(9)

        # 设置表格标题（增加重复次数列）
        self.combination_table.setHorizontalHeaderLabels([
            "组合序号", "组合内容", "匹配次数", "主胜", "平局", "客胜", "概率", "尾部概率", "重复次数"
        ])

        # 计算总统计（只统计匹配次数不为零的行）
        total_matches = 0
        total_wins = 0
        total_draws = 0
        total_losses = 0
        total_duplicates = 0

        for result in combination_results:
            if result['match_count'] > 0:  # 只统计匹配次数不为零的行
                total_matches += result['match_count']
                stats = result['result_stats']
                total_wins += stats['win']
                total_draws += stats['draw']
                total_losses += stats['loss']
                total_duplicates += result['duplicate_count']

        # 设置表格行数（原有行数 + 1行总统计）
        self.combination_table.setRowCount(len(combination_results) + 1)

        # 首行：插入总统计
        self.combination_table.setItem(0, 0, QTableWidgetItem("总计"))
        self.combination_table.setItem(0, 1, QTableWidgetItem("所有匹配组合的统计汇总"))
        self.combination_table.setItem(0, 2, QTableWidgetItem(str(total_matches)))
        self.combination_table.setItem(0, 3, QTableWidgetItem(str(total_wins)))
        self.combination_table.setItem(0, 4, QTableWidgetItem(str(total_draws)))
        self.combination_table.setItem(0, 5, QTableWidgetItem(str(total_losses)))
        self.combination_table.setItem(0, 6, QTableWidgetItem("-"))  # 概率列显示"-"
        self.combination_table.setItem(0, 7, QTableWidgetItem("-"))  # 尾部概率列显示"-"
        self.combination_table.setItem(0, 8, QTableWidgetItem(str(total_duplicates)))  # 总重复次数

        # 设置总统计行的样式（加粗、背景色）
        from PyQt5.QtCore import Qt
        from PyQt5.QtGui import QFont, QBrush, QColor

        bold_font = QFont()
        bold_font.setBold(True)
        light_blue = QBrush(QColor(230, 240, 255))  # 浅蓝色背景

        for col in range(9):  # 扩展到9列
            item = self.combination_table.item(0, col)
            if item:
                item.setFont(bold_font)
                item.setBackground(light_blue)
                item.setTextAlignment(Qt.AlignCenter)

        # 填充其他行（从第2行开始，索引为1）
        for row, result in enumerate(combination_results, 1):
            # 组合序号
            self.combination_table.setItem(row, 0, QTableWidgetItem(str(result['combination_index'] + 1)))

            # 组合内容（显示赔率）- 兼容两种分析器
            combination = result.get('combination') or result.get('target_combination')
            combination_text = self.format_combination_text(combination)
            self.combination_table.setItem(row, 1, QTableWidgetItem(combination_text))

            # 匹配次数
            self.combination_table.setItem(row, 2, QTableWidgetItem(str(result['match_count'])))

            # 胜平负统计
            stats = result['result_stats']
            self.combination_table.setItem(row, 3, QTableWidgetItem(str(stats['win'])))
            self.combination_table.setItem(row, 4, QTableWidgetItem(str(stats['draw'])))
            self.combination_table.setItem(row, 5, QTableWidgetItem(str(stats['loss'])))

            # 计算概率和尾部概率
            prob, tail_prob = self.calculate_probabilities(combination, stats, result['match_count'])
            self.combination_table.setItem(row, 6, QTableWidgetItem(f"{prob:.6f}"))
            self.combination_table.setItem(row, 7, QTableWidgetItem(f"{tail_prob:.6f}"))

            # 重复次数
            duplicate_count = result['duplicate_count']
            self.combination_table.setItem(row, 8, QTableWidgetItem(str(duplicate_count)))

            # 如果重复次数大于1，高亮显示重复次数列
            if duplicate_count > 1:
                duplicate_item = self.combination_table.item(row, 8)
                if duplicate_item:
                    duplicate_item.setBackground(QColor(255, 255, 200))  # 浅黄色背景

        # 调整列宽
        self.combination_table.resizeColumnsToContents()

    def start_recent_matches_analysis(self):
        """开始最近比赛数据分析"""
        # 检查是否有最近比赛数据
        if self.recent_matches_table.rowCount() == 0:
            QMessageBox.warning(self, "警告", "请先使用'显示最近比赛'功能获取比赛数据")
            return

        # 获取分析参数
        company_name = self.company_combo.currentText()
        if not company_name:
            QMessageBox.warning(self, "警告", "请选择博彩公司")
            return

        combination_size = self.combination_spinbox.value()
        db_path = self.db_combo.currentData()

        if not db_path:
            QMessageBox.warning(self, "警告", "请选择数据库")
            return

        # 从表格中提取所有比赛ID
        match_ids = []
        for row in range(self.recent_matches_table.rowCount()):
            match_id_item = self.recent_matches_table.item(row, 0)  # 第0列是比赛ID
            if match_id_item:
                match_ids.append(match_id_item.text())

        if not match_ids:
            QMessageBox.warning(self, "警告", "未找到有效的比赛ID")
            return

        # 确认分析
        reply = QMessageBox.question(self, "确认",
            f"您要分析 {len(match_ids)} 场最近比赛，这可能需要较长时间。是否继续？",
            QMessageBox.Yes | QMessageBox.No)
        if reply != QMessageBox.Yes:
            return

        # 禁用按钮
        self.analyze_button.setEnabled(False)
        self.analyze_dedup_button.setEnabled(False)
        self.range_analyze_button.setEnabled(False)
        self.recent_matches_analyze_button.setEnabled(False)
        self.progress_bar.setVisible(True)
        self.progress_bar.setRange(0, len(match_ids))
        self.progress_bar.setValue(0)

        # 清空之前的结果
        self.combination_table.setRowCount(0)
        self.detail_table.setRowCount(0)

        # 重置回测状态
        self.range_analysis_data = None
        self.backtest_button.setEnabled(False)

        self.update_status(f"开始最近比赛数据分析: {len(match_ids)} 场比赛")

        # 获取分析器选择
        use_optimized = self.analyzer_combo.currentData()

        # 获取时间筛选选择
        only_historical = self.time_filter_checkbox.isChecked()

        # 创建并启动最近比赛分析工作线程
        self.recent_matches_analysis_worker = RecentMatchesAnalysisWorker(
            db_path, match_ids, company_name, combination_size, use_optimized, only_historical
        )
        self.recent_matches_analysis_worker.progress_updated.connect(self.update_status)
        self.recent_matches_analysis_worker.match_completed.connect(self.on_recent_match_completed)
        self.recent_matches_analysis_worker.analysis_completed.connect(self.on_recent_matches_analysis_completed)
        self.recent_matches_analysis_worker.analysis_failed.connect(self.on_recent_matches_analysis_failed)
        self.recent_matches_analysis_worker.start()

    def on_recent_match_completed(self, match_id: str, current: int, total: int):
        """最近比赛分析中单场比赛完成"""
        self.progress_bar.setValue(current)
        self.update_status(f"已完成比赛 {match_id} ({current}/{total})")

    def on_recent_matches_analysis_completed(self, report_data: list):
        """最近比赛分析完成处理"""
        # 保存分析数据用于回测
        self.range_analysis_data = report_data

        self.display_range_analysis_report(report_data)

        # 恢复UI状态
        self.analyze_button.setEnabled(True)
        self.analyze_dedup_button.setEnabled(True)
        self.range_analyze_button.setEnabled(True)
        self.recent_matches_analyze_button.setEnabled(True)

        # 启用回测按钮（如果有数据）
        if report_data:
            self.backtest_button.setEnabled(True)
        self.progress_bar.setVisible(False)

        self.update_status(f"最近比赛数据分析完成！共分析 {len(report_data)} 场比赛")

    def on_recent_matches_analysis_failed(self, error_message: str):
        """最近比赛分析失败处理"""
        QMessageBox.critical(self, "最近比赛分析失败", error_message)

        # 恢复UI状态
        self.analyze_button.setEnabled(True)
        self.analyze_dedup_button.setEnabled(True)
        self.range_analyze_button.setEnabled(True)
        self.recent_matches_analyze_button.setEnabled(True)
        self.progress_bar.setVisible(False)

        # 禁用回测按钮
        self.backtest_button.setEnabled(False)
        self.range_analysis_data = None

        self.update_status(f"最近比赛分析失败: {error_message}")

    def add_to_watchlist(self):
        """加入观察清单 - 从表格获取数据"""
        try:
            print("🔍 加入观察清单功能被调用")

            # 1. 检查分析结果表格是否有数据
            if self.combination_table.rowCount() == 0:
                QMessageBox.warning(self, "警告", "请先进行分析以获取结果数据")
                return

            print(f"📊 分析结果表格有 {self.combination_table.rowCount()} 行数据")

            # 2. 获取并验证尾部概率阈值
            try:
                threshold_text = self.tail_prob_threshold_input.text().strip()
                if not threshold_text:
                    threshold = 0.05  # 默认值
                else:
                    threshold = float(threshold_text)
                    if threshold < 0 or threshold > 1:
                        QMessageBox.warning(self, "警告", "尾部概率阈值应该在0到1之间")
                        return
                print(f"📊 使用阈值: {threshold}")
            except ValueError:
                QMessageBox.warning(self, "警告", "请输入有效的尾部概率阈值（数字）")
                return

            # 3. 清空观察清单表格
            self.recent_matches_table.setRowCount(0)
            self.update_status(f"正在筛选尾部概率 <= {threshold} 的比赛...")

            # 4. 检查表格列数和结构，确定分析模式
            col_count = self.combination_table.columnCount()
            print(f"📊 表格列数: {col_count}")

            # 打印表格标题以确认结构
            headers = []
            for col in range(col_count):
                header_item = self.combination_table.horizontalHeaderItem(col)
                header_text = header_item.text() if header_item else f"列{col}"
                headers.append(header_text)
            print(f"📊 表格标题: {headers}")

            # 根据表格结构确定列索引和数据来源
            if col_count == 12:  # ID区间分析模式
                match_id_col = 0      # "比赛ID"列
                tail_prob_col = 7     # "尾部概率"列
                combination_col = 2   # "组合内容"列
                use_table_match_ids = True  # 使用表格中的比赛ID
                current_match_id = None  # ID区间分析不需要输入框的比赛ID
                print("📊 检测到ID区间分析模式")
            elif col_count == 8:  # 普通分析模式
                match_id_col = None
                tail_prob_col = 7     # "尾部概率"列
                combination_col = 1   # "组合内容"列
                use_table_match_ids = False  # 使用输入框的比赛ID
                # 获取输入框的比赛ID
                current_match_id = self.match_id_input.text().strip()
                if not current_match_id:
                    QMessageBox.warning(self, "警告", "请先输入比赛ID")
                    return
                print("📊 检测到普通分析模式")
            elif col_count == 9:  # 去重分析模式
                match_id_col = None
                tail_prob_col = 7     # "尾部概率"列
                combination_col = 1   # "组合内容"列
                use_table_match_ids = False  # 使用输入框的比赛ID
                # 获取输入框的比赛ID
                current_match_id = self.match_id_input.text().strip()
                if not current_match_id:
                    QMessageBox.warning(self, "警告", "请先输入比赛ID")
                    return
                print("📊 检测到去重分析模式")
            else:
                print(f"⚠️ 未知的表格列数: {col_count}")
                QMessageBox.warning(self, "警告", f"不支持的表格格式（{col_count}列）")
                return

            if current_match_id:
                print(f"🎯 当前分析的比赛ID: {current_match_id}")

            # 5. 从分析结果表格中筛选符合条件的组合
            qualifying_combinations = []

            for row in range(self.combination_table.rowCount()):
                try:
                    # 从表格获取尾部概率
                    tail_prob_item = self.combination_table.item(row, tail_prob_col)
                    if not tail_prob_item:
                        print(f"  行 {row+1}: 尾部概率列为空")
                        continue

                    tail_prob_text = tail_prob_item.text().strip()
                    if not tail_prob_text:
                        print(f"  行 {row+1}: 尾部概率文本为空")
                        continue

                    tail_prob = float(tail_prob_text)

                    print(f"  行 {row+1}: 尾部概率 = {tail_prob}, 阈值 = {threshold}")

                    # 检查是否符合阈值条件
                    if tail_prob <= threshold:
                        print(f"  ✅ 行 {row+1} 符合条件")

                        # 获取比赛ID
                        if use_table_match_ids and match_id_col is not None:
                            # 从表格获取比赛ID（ID区间分析模式）
                            match_id_item = self.combination_table.item(row, match_id_col)
                            match_id = match_id_item.text().strip() if match_id_item else None
                        else:
                            # 使用输入框的比赛ID（普通分析模式）
                            match_id = current_match_id

                        if not match_id:
                            print(f"  ⚠️ 行 {row+1}: 无法获取比赛ID")
                            continue

                        # 获取组合内容（用于显示）
                        combination_item = self.combination_table.item(row, combination_col)
                        combination_content = combination_item.text() if combination_item else f"组合{row+1}"

                        qualifying_combinations.append({
                            'match_id': match_id,
                            'tail_prob': tail_prob,
                            'row_index': row,
                            'combination_content': combination_content
                        })
                    else:
                        print(f"  ❌ 行 {row+1} 不符合条件 ({tail_prob} > {threshold})")

                except (ValueError, AttributeError) as e:
                    print(f"  ⚠️ 处理行 {row+1} 时出错: {e}")
                    continue

            print(f"📋 筛选完成，找到 {len(qualifying_combinations)} 个符合条件的组合")

            # 6. 处理筛选结果
            if qualifying_combinations:
                # 按比赛ID分组，每个比赛保留最佳组合
                matches_by_id = {}
                for combo in qualifying_combinations:
                    match_id = combo['match_id']
                    if match_id not in matches_by_id:
                        matches_by_id[match_id] = combo
                    else:
                        # 保留尾部概率更小的组合
                        if combo['tail_prob'] < matches_by_id[match_id]['tail_prob']:
                            matches_by_id[match_id] = combo

                final_matches = list(matches_by_id.values())
                final_matches.sort(key=lambda x: x['tail_prob'])  # 按尾部概率排序

                print(f"📋 去重后有 {len(final_matches)} 场比赛")

                # 7. 获取比赛详细信息并填充观察清单表格
                self.recent_matches_table.setRowCount(len(final_matches))

                # 遍历每场比赛，获取详细信息并填充表格
                for i, match in enumerate(final_matches):
                    match_id = match['match_id']

                    # 从数据库获取比赛详细信息
                    try:
                        match_info = self.database.get_match_info(match_id) if self.database else None
                        if match_info:
                            # 格式化显示时间
                            display_time = "未知时间"
                            if match_info.get('accurate_datetime'):
                                display_time = match_info['accurate_datetime']
                            elif match_info.get('accurate_date'):
                                time_part = match_info.get('match_time', '00:00:00')
                                display_time = f"{match_info['accurate_date']} {time_part}"
                            elif match_info.get('match_date'):
                                time_part = match_info.get('match_time', '00:00:00')
                                display_time = f"{match_info['match_date']} {time_part}"

                            league = match_info.get('league', '未知联赛')
                            home_team = match_info.get('home_team', '未知')
                            away_team = match_info.get('away_team', '未知')

                            # 获取比赛结果
                            home_score = match_info.get('home_score')
                            away_score = match_info.get('away_score')
                            if home_score is not None and away_score is not None:
                                match_result = f"{home_score}:{away_score}"
                            else:
                                match_result = "未开始"
                        else:
                            display_time = "未知时间"
                            league = "未知联赛"
                            home_team = "未知"
                            away_team = "未知"
                            match_result = "未知"
                    except Exception as e:
                        print(f"获取比赛 {match_id} 信息失败: {e}")
                        display_time = "未知时间"
                        league = "未知联赛"
                        home_team = "未知"
                        away_team = "未知"
                        match_result = "未知"

                    # 填充表格
                    self.recent_matches_table.setItem(i, 0, QTableWidgetItem(str(match_id)))
                    self.recent_matches_table.setItem(i, 1, QTableWidgetItem(display_time))
                    self.recent_matches_table.setItem(i, 2, QTableWidgetItem(league))
                    self.recent_matches_table.setItem(i, 3, QTableWidgetItem(home_team))
                    self.recent_matches_table.setItem(i, 4, QTableWidgetItem(away_team))
                    self.recent_matches_table.setItem(i, 5, QTableWidgetItem(match_result))

                # 调整列宽
                self.recent_matches_table.resizeColumnsToContents()

                # 启用相关按钮
                self.recent_matches_analyze_button.setEnabled(True)

                # 显示结果
                best_prob = final_matches[0]['tail_prob']  # 第一个是最佳的（已排序）
                self.update_status(f"观察清单更新完成！筛选出 {len(final_matches)} 场符合条件的比赛（尾部概率 <= {threshold}），最佳尾部概率: {best_prob:.6f}")
                QMessageBox.information(self, "成功", f"观察清单更新完成！\n筛选出 {len(final_matches)} 场符合条件的比赛\n符合条件的组合: {len(qualifying_combinations)} 个\n尾部概率阈值: <= {threshold}\n最佳尾部概率: {best_prob:.6f}")

            else:
                self.update_status(f"未找到尾部概率 <= {threshold} 的组合")
                QMessageBox.information(self, "结果", f"未找到尾部概率小于等于 {threshold} 的组合")

        except Exception as e:
            error_msg = f"加入观察清单失败: {str(e)}"
            print(f"❌ {error_msg}")
            logger.error(error_msg)
            QMessageBox.critical(self, "错误", error_msg)

    def get_match_detail_for_watchlist(self, match_id: str) -> dict:
        """获取比赛详细信息用于观察清单"""
        try:
            if not self.database:
                return None

            # 获取比赛基本信息
            match_info = self.database.get_match_info(match_id)
            if not match_info:
                return None

            # 格式化显示时间
            display_time = "未知时间"
            if match_info.get('accurate_datetime'):
                display_time = match_info['accurate_datetime']
            elif match_info.get('accurate_date'):
                time_part = match_info.get('match_time', '00:00:00')
                display_time = f"{match_info['accurate_date']} {time_part}"
            elif match_info.get('match_date'):
                time_part = match_info.get('match_time', '00:00:00')
                display_time = f"{match_info['match_date']} {time_part}"

            # 获取比赛结果
            match_result = self.get_match_result_text(match_info)

            return {
                'match_id': match_id,
                'display_time': display_time,
                'league': match_info.get('league', '未知联赛'),
                'home_team': match_info.get('home_team', '未知'),
                'away_team': match_info.get('away_team', '未知'),
                'match_result': match_result
            }

        except Exception as e:
            logger.warning(f"获取比赛 {match_id} 详细信息失败: {e}")
            return None

    def start_odds_trend_analysis(self):
        """开始升降统计分析"""
        try:
            # 验证输入
            match_id = self.match_id_input.text().strip()
            if not match_id:
                QMessageBox.warning(self, "警告", "请输入比赛ID")
                return

            company_name = self.company_combo.currentText()
            if not company_name:
                QMessageBox.warning(self, "警告", "请选择博彩公司")
                return

            if not self.database:
                QMessageBox.warning(self, "警告", "请选择数据库")
                return

            # 检查比赛是否存在
            if not self.database.check_match_exists(match_id):
                QMessageBox.warning(self, "警告", f"比赛 {match_id} 不存在")
                return

            self.update_status(f"开始分析比赛 {match_id} 的赔率升降统计...")

            # 获取赔率数据
            odds_data = self.database.get_match_odds_by_company(match_id, company_name)

            if not odds_data:
                QMessageBox.warning(self, "警告", f"比赛 {match_id} 没有 {company_name} 的赔率数据")
                return

            if len(odds_data) < 2:
                QMessageBox.warning(self, "警告", f"赔率数据不足，需要至少2条记录进行升降统计")
                return

            # 执行升降统计
            trend_results = self.analyze_odds_trends(match_id, company_name, odds_data)

            # 填充升降统计表格
            self.populate_odds_trend_table(trend_results)

            self.update_status(f"升降统计完成！分析了 {len(odds_data)} 条赔率数据")

        except Exception as e:
            error_msg = f"升降统计失败: {str(e)}"
            logger.error(error_msg)
            QMessageBox.critical(self, "错误", error_msg)

    def analyze_odds_trends(self, match_id: str, company_name: str, odds_data: list) -> list:
        """分析赔率升降趋势"""
        try:
            results = []

            # 添加调试输出：显示原始数据
            print(f"\n🔍 升降统计调试 - 比赛ID: {match_id}, 公司: {company_name}")
            print(f"📊 原始赔率数据 ({len(odds_data)} 条):")
            print("序号  日期       时间     主胜    平局    客胜    时间戳")
            print("-" * 70)

            for i, odds in enumerate(odds_data):
                home_odds = odds.get('home_odds', 'N/A')
                draw_odds = odds.get('draw_odds', 'N/A')
                away_odds = odds.get('away_odds', 'N/A')

                # 格式化显示，确保对齐
                home_str = f"{home_odds:.3f}" if home_odds != 'N/A' else 'N/A'
                draw_str = f"{draw_odds:.3f}" if draw_odds != 'N/A' else 'N/A'
                away_str = f"{away_odds:.3f}" if away_odds != 'N/A' else 'N/A'

                # 创建时间戳用于排序验证
                date_str = odds.get('date', '')
                time_str = odds.get('time', '')
                timestamp = f"{date_str} {time_str}"

                print(f"{i+1:2d}   {odds.get('date', 'N/A')} {odds.get('time', 'N/A')} "
                      f"{home_str:>7} {draw_str:>7} {away_str:>7} {timestamp}")

            # 第一步：处理同一分钟内的多组数据
            processed_data, minute_stats = self.process_same_minute_data(odds_data)

            print(f"\n📊 处理后数据 ({len(processed_data)} 条，合并了同一分钟内的数据)")
            print("序号  日期       时间     主胜    平局    客胜")
            print("-" * 55)

            for i, odds in enumerate(processed_data):
                home_str = f"{odds['home_odds']:.3f}"
                draw_str = f"{odds['draw_odds']:.3f}"
                away_str = f"{odds['away_odds']:.3f}"

                print(f"{i+1:2d}   {odds['date']} {odds['time']} "
                      f"{home_str:>7} {draw_str:>7} {away_str:>7}")

            # 显示同一分钟内的波动统计
            if minute_stats['total_changes'] > 0:
                print(f"\n📈 同一分钟内波动统计:")
                print(f"  数据变化总次数: {minute_stats['total_changes']}")
                print(f"  主胜波动范围差值: {minute_stats['home_range_avg']:.3f}")
                print(f"  平局波动范围差值: {minute_stats['draw_range_avg']:.3f}")
                print(f"  客胜波动范围差值: {minute_stats['away_range_avg']:.3f}")

            # 分析主胜、平局、客胜三种结果的赔率变化（使用处理后的数据）
            for result_type, odds_key in [("主胜", "home_odds"), ("平局", "draw_odds"), ("客胜", "away_odds")]:

                print(f"\n--- {result_type} 赔率分析 ---")

                # 提取该结果类型的赔率序列（使用处理后的数据）
                odds_sequence = []
                for odds in processed_data:
                    if odds.get(odds_key) is not None:
                        odds_sequence.append(float(odds[odds_key]))

                print(f"赔率序列: {odds_sequence}")

                if len(odds_sequence) < 2:
                    print("数据不足，跳过")
                    continue

                # 统计升降次数
                down_count = 0  # 下降次数
                up_count = 0    # 上升次数
                changes = []

                for i in range(1, len(odds_sequence)):
                    prev_odds = odds_sequence[i-1]
                    curr_odds = odds_sequence[i]

                    if curr_odds < prev_odds:
                        down_count += 1
                        diff = prev_odds - curr_odds
                        changes.append(f"步骤{i:2d}: {prev_odds:.3f} → {curr_odds:.3f} (下降 {diff:.3f})")
                    elif curr_odds > prev_odds:
                        up_count += 1
                        diff = curr_odds - prev_odds
                        changes.append(f"步骤{i:2d}: {prev_odds:.3f} → {curr_odds:.3f} (上升 {diff:.3f})")
                    else:
                        changes.append(f"步骤{i:2d}: {prev_odds:.3f} → {curr_odds:.3f} (不变)")

                # 显示详细变化过程
                print("详细变化过程:")
                for change in changes:
                    print(f"  {change}")

                print(f"\n🔢 升降统计:")
                print(f"  总共 {len(odds_sequence)-1} 次变化")
                print(f"  下降次数: {down_count}")
                print(f"  上升次数: {up_count}")
                print(f"  不变次数: {len(odds_sequence)-1-down_count-up_count}")

                # 计算初始赔率、最终赔率和差值
                initial_odds = odds_sequence[0]
                final_odds = odds_sequence[-1]
                odds_diff = final_odds - initial_odds
                change_rate = (odds_diff / initial_odds) * 100 if initial_odds != 0 else 0

                print(f"📊 统计结果:")
                print(f"  下降次数: {down_count}")
                print(f"  上升次数: {up_count}")
                print(f"  初始赔率: {initial_odds:.3f}")
                print(f"  最终赔率: {final_odds:.3f}")
                print(f"  赔率差值: {odds_diff:+.3f}")
                print(f"  变化率: {change_rate:+.2f}%")
                print(f"  数据点数: {len(odds_sequence)}")

                results.append({
                    'match_id': match_id,
                    'company_name': company_name,
                    'result_type': result_type,
                    'down_count': down_count,
                    'up_count': up_count,
                    'initial_odds': initial_odds,
                    'final_odds': final_odds,
                    'odds_diff': odds_diff,
                    'change_rate': change_rate,
                    'data_points': len(odds_sequence)
                })

            # 添加同一分钟内波动统计行
            if minute_stats['total_changes'] > 0:
                results.append({
                    'match_id': match_id,
                    'company_name': company_name,
                    'result_type': '分钟内波动',
                    'down_count': minute_stats['total_changes'],  # 用下降次数列显示总变化次数
                    'up_count': 0,  # 上升次数列留空
                    'initial_odds': minute_stats['home_range_avg'],  # 用初始赔率列显示主胜波动
                    'final_odds': minute_stats['draw_range_avg'],   # 用最终赔率列显示平局波动
                    'odds_diff': minute_stats['away_range_avg'],    # 用赔率差值列显示客胜波动
                    'change_rate': 0,  # 变化率列留空
                    'data_points': len([g for g in minute_stats['minute_groups'] if len(g) > 1])  # 有多组数据的分钟数
                })

            return results

        except Exception as e:
            logger.error(f"分析赔率升降趋势失败: {e}")
            return []

    def process_same_minute_data(self, odds_data: list) -> tuple:
        """处理同一分钟内的多组数据"""
        try:
            # 按分钟分组
            minute_groups = {}
            for odds in odds_data:
                date_str = odds.get('date', '')
                time_str = odds.get('time', '')

                # 提取到分钟级别的时间
                if ':' in time_str:
                    minute_key = f"{date_str} {time_str[:5]}"  # 只取到分钟 HH:MM
                else:
                    minute_key = f"{date_str} {time_str}"

                if minute_key not in minute_groups:
                    minute_groups[minute_key] = []
                minute_groups[minute_key].append(odds)

            print(f"\n📊 分钟分组结果: {len(minute_groups)} 个不同分钟")

            # 处理每个分钟组
            processed_data = []
            total_changes = 0
            home_ranges = []
            draw_ranges = []
            away_ranges = []

            for minute_key, group in sorted(minute_groups.items()):
                if len(group) == 1:
                    # 只有一条数据，直接使用
                    processed_data.append(group[0])
                    print(f"  {minute_key}: 1条数据")
                else:
                    # 多条数据，计算平均值和波动范围
                    print(f"  {minute_key}: {len(group)}条数据 - 需要合并")

                    # 提取有效赔率数据
                    home_odds_list = [float(odds['home_odds']) for odds in group if odds.get('home_odds') is not None]
                    draw_odds_list = [float(odds['draw_odds']) for odds in group if odds.get('draw_odds') is not None]
                    away_odds_list = [float(odds['away_odds']) for odds in group if odds.get('away_odds') is not None]

                    if home_odds_list and draw_odds_list and away_odds_list:
                        # 计算平均值
                        avg_home = sum(home_odds_list) / len(home_odds_list)
                        avg_draw = sum(draw_odds_list) / len(draw_odds_list)
                        avg_away = sum(away_odds_list) / len(away_odds_list)

                        # 计算波动范围
                        home_range = max(home_odds_list) - min(home_odds_list)
                        draw_range = max(draw_odds_list) - min(draw_odds_list)
                        away_range = max(away_odds_list) - min(away_odds_list)

                        # 统计变化次数（该分钟内的数据条数-1）
                        changes_in_minute = len(group) - 1
                        total_changes += changes_in_minute

                        # 记录波动范围
                        home_ranges.append(home_range)
                        draw_ranges.append(draw_range)
                        away_ranges.append(away_range)

                        print(f"    主胜: {min(home_odds_list):.3f}-{max(home_odds_list):.3f} (平均{avg_home:.3f}, 波动{home_range:.3f})")
                        print(f"    平局: {min(draw_odds_list):.3f}-{max(draw_odds_list):.3f} (平均{avg_draw:.3f}, 波动{draw_range:.3f})")
                        print(f"    客胜: {min(away_odds_list):.3f}-{max(away_odds_list):.3f} (平均{avg_away:.3f}, 波动{away_range:.3f})")
                        print(f"    变化次数: {changes_in_minute}")

                        # 创建合并后的数据
                        merged_odds = {
                            'date': group[0]['date'],
                            'time': minute_key.split(' ')[1],  # 只保留时间部分
                            'home_odds': avg_home,
                            'draw_odds': avg_draw,
                            'away_odds': avg_away
                        }
                        processed_data.append(merged_odds)
                    else:
                        # 数据不完整，使用第一条
                        processed_data.append(group[0])

            # 计算平均波动范围
            home_range_avg = sum(home_ranges) / len(home_ranges) if home_ranges else 0
            draw_range_avg = sum(draw_ranges) / len(draw_ranges) if draw_ranges else 0
            away_range_avg = sum(away_ranges) / len(away_ranges) if away_ranges else 0

            minute_stats = {
                'total_changes': total_changes,
                'home_range_avg': home_range_avg,
                'draw_range_avg': draw_range_avg,
                'away_range_avg': away_range_avg,
                'minute_groups': list(minute_groups.values())
            }

            return processed_data, minute_stats

        except Exception as e:
            logger.error(f"处理同一分钟数据失败: {e}")
            return odds_data, {'total_changes': 0, 'home_range_avg': 0, 'draw_range_avg': 0, 'away_range_avg': 0, 'minute_groups': []}

    def populate_odds_trend_table(self, trend_results: list):
        """填充升降统计表格"""
        try:
            self.odds_trend_table.setRowCount(len(trend_results))

            for row, result in enumerate(trend_results):
                # 比赛ID
                self.odds_trend_table.setItem(row, 0, QTableWidgetItem(str(result['match_id'])))

                # 博彩公司
                self.odds_trend_table.setItem(row, 1, QTableWidgetItem(result['company_name']))

                # 结果类型
                result_item = QTableWidgetItem(result['result_type'])
                # 根据结果类型设置背景颜色
                if result['result_type'] == "主胜":
                    result_item.setBackground(QColor(255, 235, 235))  # 浅红色
                elif result['result_type'] == "平局":
                    result_item.setBackground(QColor(235, 255, 235))  # 浅绿色
                elif result['result_type'] == "客胜":
                    result_item.setBackground(QColor(235, 235, 255))  # 浅蓝色
                elif result['result_type'] == "分钟内波动":
                    result_item.setBackground(QColor(255, 255, 200))  # 黄色背景表示特殊统计
                self.odds_trend_table.setItem(row, 2, result_item)

                # 处理分钟内波动行的特殊显示
                if result['result_type'] == "分钟内波动":
                    # 下降次数列显示总变化次数
                    down_item = QTableWidgetItem(f"总变化:{result['down_count']}")
                    down_item.setBackground(QColor(255, 255, 200))
                    self.odds_trend_table.setItem(row, 3, down_item)

                    # 上升次数列留空
                    up_item = QTableWidgetItem("-")
                    up_item.setBackground(QColor(255, 255, 200))
                    self.odds_trend_table.setItem(row, 4, up_item)

                    # 初始赔率列显示主胜波动
                    initial_item = QTableWidgetItem(f"主胜:{result['initial_odds']:.3f}")
                    initial_item.setBackground(QColor(255, 255, 200))
                    self.odds_trend_table.setItem(row, 5, initial_item)

                    # 最终赔率列显示平局波动
                    final_item = QTableWidgetItem(f"平局:{result['final_odds']:.3f}")
                    final_item.setBackground(QColor(255, 255, 200))
                    self.odds_trend_table.setItem(row, 6, final_item)

                    # 赔率差值列显示客胜波动
                    diff_item = QTableWidgetItem(f"客胜:{result['odds_diff']:.3f}")
                    diff_item.setBackground(QColor(255, 255, 200))
                    self.odds_trend_table.setItem(row, 7, diff_item)

                    # 变化率列留空
                    rate_item = QTableWidgetItem("-")
                    rate_item.setBackground(QColor(255, 255, 200))
                    self.odds_trend_table.setItem(row, 8, rate_item)

                    # 数据点数显示有多组数据的分钟数
                    points_item = QTableWidgetItem(f"分钟数:{result['data_points']}")
                    points_item.setBackground(QColor(255, 255, 200))
                    self.odds_trend_table.setItem(row, 9, points_item)

                else:
                    # 正常的升降统计行
                    # 下降次数
                    down_item = QTableWidgetItem(str(result['down_count']))
                    if result['down_count'] > 0:
                        down_item.setBackground(QColor(255, 200, 200))  # 红色背景表示下降
                    self.odds_trend_table.setItem(row, 3, down_item)

                    # 上升次数
                    up_item = QTableWidgetItem(str(result['up_count']))
                    if result['up_count'] > 0:
                        up_item.setBackground(QColor(200, 255, 200))  # 绿色背景表示上升
                    self.odds_trend_table.setItem(row, 4, up_item)

                    # 初始赔率
                    self.odds_trend_table.setItem(row, 5, QTableWidgetItem(f"{result['initial_odds']:.3f}"))

                    # 最终赔率
                    self.odds_trend_table.setItem(row, 6, QTableWidgetItem(f"{result['final_odds']:.3f}"))

                    # 赔率差值
                    diff_item = QTableWidgetItem(f"{result['odds_diff']:+.3f}")
                    if result['odds_diff'] > 0:
                        diff_item.setBackground(QColor(200, 255, 200))  # 绿色表示上升
                    elif result['odds_diff'] < 0:
                        diff_item.setBackground(QColor(255, 200, 200))  # 红色表示下降
                    self.odds_trend_table.setItem(row, 7, diff_item)

                    # 变化率
                    rate_item = QTableWidgetItem(f"{result['change_rate']:+.2f}%")
                    if result['change_rate'] > 0:
                        rate_item.setBackground(QColor(200, 255, 200))  # 绿色表示上升
                    elif result['change_rate'] < 0:
                        rate_item.setBackground(QColor(255, 200, 200))  # 红色表示下降
                    self.odds_trend_table.setItem(row, 8, rate_item)

                    # 数据点数
                    self.odds_trend_table.setItem(row, 9, QTableWidgetItem(str(result['data_points'])))

            # 调整列宽
            self.odds_trend_table.resizeColumnsToContents()

        except Exception as e:
            logger.error(f"填充升降统计表格失败: {e}")

def main():
    """主函数"""
    # 配置日志
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )

    app = QApplication(sys.argv)

    # 设置应用程序样式
    app.setStyle('Fusion')

    window = OddsCombinationMainWindow()
    window.show()

    sys.exit(app.exec_())

if __name__ == '__main__':
    main()

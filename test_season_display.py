#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试赛季显示功能
验证数据库中的赛季信息是否正确显示
"""

import sqlite3
import logging
from database import OddsDatabase
from season_utils import get_season_for_match, infer_season_from_match_id

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_database_season_data():
    """测试数据库中的赛季数据"""
    try:
        print("🔍 测试数据库中的赛季数据...")
        
        # 连接数据库
        db = OddsDatabase()
        
        # 获取所有比赛
        matches = db.get_all_matches()
        
        print(f"📊 数据库中共有 {len(matches)} 场比赛")
        
        # 统计赛季分布
        season_stats = {}
        missing_season_count = 0
        
        # 显示前10场比赛的详细信息
        print("\n📋 前10场比赛的详细信息:")
        print("-" * 100)
        print(f"{'比赛ID':<10} {'联赛':<15} {'赛季':<10} {'轮次':<10} {'对阵':<30} {'比赛时间':<20}")
        print("-" * 100)
        
        for i, match in enumerate(matches[:10]):
            match_id = match.get('match_id', '')
            league = match.get('league', '')[:14]  # 限制长度
            season = match.get('season', '')
            round_info = match.get('round_info', '')
            home_team = match.get('home_team', '')
            away_team = match.get('away_team', '')
            match_time = match.get('match_time', '')[:19]  # 限制长度
            
            # 格式化轮次信息
            if round_info:
                import re
                round_match = re.search(r'第(\d+)轮', round_info)
                if round_match:
                    round_display = f"第{round_match.group(1)}轮"
                else:
                    round_display = round_info[:9]
            else:
                round_display = "未知"
            
            # 格式化对阵信息
            teams = f"{home_team} vs {away_team}"
            if len(teams) > 29:
                teams = teams[:26] + "..."
            
            print(f"{match_id:<10} {league:<15} {season:<10} {round_display:<10} {teams:<30} {match_time:<20}")
        
        # 统计所有比赛的赛季分布
        for match in matches:
            season = match.get('season', '')
            if season:
                season_stats[season] = season_stats.get(season, 0) + 1
            else:
                missing_season_count += 1
        
        print("\n📈 赛季分布统计:")
        print("-" * 40)
        for season, count in sorted(season_stats.items()):
            percentage = (count / len(matches)) * 100
            print(f"{season:<10}: {count:>4} 场比赛 ({percentage:5.1f}%)")
        
        if missing_season_count > 0:
            percentage = (missing_season_count / len(matches)) * 100
            print(f"{'缺失赛季':<10}: {missing_season_count:>4} 场比赛 ({percentage:5.1f}%)")
        
        print("-" * 40)
        print(f"{'总计':<10}: {len(matches):>4} 场比赛")
        
        # 测试赛季推断功能
        print("\n🧪 测试赛季推断功能:")
        print("-" * 50)
        test_match_ids = []
        
        # 获取一些测试用的比赛ID
        for match in matches[:5]:
            test_match_ids.append(match.get('match_id', ''))
        
        for match_id in test_match_ids:
            if match_id:
                inferred_season = infer_season_from_match_id(match_id)
                comprehensive_season = get_season_for_match(match_id=match_id)
                print(f"比赛ID {match_id}: 推断赛季={inferred_season}, 综合赛季={comprehensive_season}")
        
        return True
        
    except Exception as e:
        logger.error(f"测试失败: {e}")
        return False

def test_season_column_exists():
    """测试赛季字段是否存在"""
    try:
        print("\n🔧 测试数据库表结构...")
        
        with sqlite3.connect("odds_data.db") as conn:
            cursor = conn.cursor()
            
            # 检查matches表的结构
            cursor.execute("PRAGMA table_info(matches)")
            columns = cursor.fetchall()
            
            print("📋 matches表的字段:")
            print("-" * 60)
            print(f"{'序号':<4} {'字段名':<20} {'类型':<15} {'非空':<6} {'默认值':<10}")
            print("-" * 60)
            
            season_column_exists = False
            for column in columns:
                cid, name, type_, notnull, default_value, pk = column
                print(f"{cid:<4} {name:<20} {type_:<15} {notnull:<6} {str(default_value):<10}")
                if name == 'season':
                    season_column_exists = True
            
            print("-" * 60)
            
            if season_column_exists:
                print("✅ season字段存在")
                
                # 检查赛季数据的分布
                cursor.execute("SELECT season, COUNT(*) FROM matches GROUP BY season ORDER BY season")
                season_distribution = cursor.fetchall()
                
                print("\n📊 数据库中的赛季分布:")
                for season, count in season_distribution:
                    print(f"  {season}: {count} 场比赛")
                
            else:
                print("❌ season字段不存在")
                
            return season_column_exists
            
    except Exception as e:
        logger.error(f"检查表结构失败: {e}")
        return False

def main():
    """主函数"""
    print("🚀 开始测试赛季显示功能")
    print("=" * 80)
    
    # 测试数据库表结构
    if not test_season_column_exists():
        print("❌ 数据库表结构测试失败")
        return
    
    # 测试数据库中的赛季数据
    if not test_database_season_data():
        print("❌ 赛季数据测试失败")
        return
    
    print("\n" + "=" * 80)
    print("✅ 所有测试通过！赛季显示功能正常工作")
    
    print("\n💡 使用说明:")
    print("1. 启动程序: python odds_scraper_ui.py")
    print("2. 在比赛列表中可以看到新增的'赛季'列")
    print("3. 赛季信息会根据比赛ID自动推断:")
    print("   - ID > 2700000: 2025赛季")
    print("   - ID ≤ 2700000: 2024赛季")
    print("4. 新抓取的比赛会自动从URL中提取赛季信息")

if __name__ == "__main__":
    main()

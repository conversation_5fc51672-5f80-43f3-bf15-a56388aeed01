#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
机器学习模型训练器
实现多种机器学习模型，进行训练和调参
"""

import pandas as pd
import numpy as np
import json
import os
from datetime import datetime
import matplotlib.pyplot as plt
import seaborn as sns

from sklearn.model_selection import train_test_split, cross_val_score, GridSearchCV, StratifiedKFold
from sklearn.linear_model import LogisticRegression
from sklearn.ensemble import RandomForestClassifier, GradientBoostingClassifier
from sklearn.svm import SVC
from sklearn.naive_bayes import GaussianNB
from sklearn.neighbors import KNeighborsClassifier
from sklearn.preprocessing import StandardScaler, LabelEncoder
from sklearn.metrics import classification_report, confusion_matrix, accuracy_score, precision_recall_fscore_support
from sklearn.metrics import roc_auc_score, roc_curve, precision_recall_curve

try:
    import xgboost as xgb
    XGBOOST_AVAILABLE = True
except ImportError:
    XGBOOST_AVAILABLE = False
    print("XGBoost not available, will skip XGBoost models")

try:
    import lightgbm as lgb
    LIGHTGBM_AVAILABLE = True
except ImportError:
    LIGHTGBM_AVAILABLE = False
    print("LightGBM not available, will skip LightGBM models")

import warnings
warnings.filterwarnings('ignore')

class MLModelTrainer:
    def __init__(self, processed_data_file=None):
        self.processed_data_file = processed_data_file
        self.data = None
        self.X = None
        self.y = None
        self.feature_names = None
        self.target_encoder = LabelEncoder()
        self.scaler = StandardScaler()
        
        self.models = {}
        self.model_results = {}
        
    def load_processed_data(self, filename=None):
        """加载处理后的数据"""
        if filename:
            self.processed_data_file = filename
        
        if not self.processed_data_file:
            # 查找最新的处理数据文件
            files = [f for f in os.listdir('.') if f.startswith('processed_features_') and f.endswith('.csv')]
            if files:
                self.processed_data_file = sorted(files)[-1]
                print(f"自动选择最新文件: {self.processed_data_file}")
            else:
                print("未找到处理数据文件")
                return False
        
        self.data = pd.read_csv(self.processed_data_file)
        print(f"加载处理数据: {self.processed_data_file}")
        print(f"数据形状: {self.data.shape}")
        
        return True
    
    def prepare_features_and_target(self):
        """准备特征和目标变量"""
        print("\n=== 准备特征和目标变量 ===")
        
        # 排除非特征列
        exclude_cols = ['match_id', 'company', 'match_result', 'home_team', 'away_team', 'target']
        feature_cols = [col for col in self.data.columns if col not in exclude_cols]
        
        self.X = self.data[feature_cols]
        self.y = self.data['match_result']
        self.feature_names = feature_cols
        
        print(f"特征数量: {len(feature_cols)}")
        print(f"样本数量: {len(self.X)}")
        print(f"类别分布: {dict(self.y.value_counts())}")
        
        # 编码目标变量
        self.y_encoded = self.target_encoder.fit_transform(self.y)
        
        # 标准化特征
        self.X_scaled = self.scaler.fit_transform(self.X)
        
        return True
    
    def initialize_models(self):
        """初始化所有模型"""
        print("\n=== 初始化模型 ===")
        
        self.models = {
            'logistic_regression': LogisticRegression(
                random_state=42, 
                max_iter=1000,
                class_weight='balanced'
            ),
            'random_forest': RandomForestClassifier(
                random_state=42,
                n_estimators=100,
                class_weight='balanced'
            ),
            'gradient_boosting': GradientBoostingClassifier(
                random_state=42,
                n_estimators=100
            ),
            'svm': SVC(
                random_state=42,
                probability=True,
                class_weight='balanced'
            ),
            'naive_bayes': GaussianNB(),
            'knn': KNeighborsClassifier(n_neighbors=5)
        }
        
        # 添加XGBoost（如果可用）
        if XGBOOST_AVAILABLE:
            self.models['xgboost'] = xgb.XGBClassifier(
                random_state=42,
                eval_metric='mlogloss',
                use_label_encoder=False
            )
        
        # 添加LightGBM（如果可用）
        if LIGHTGBM_AVAILABLE:
            self.models['lightgbm'] = lgb.LGBMClassifier(
                random_state=42,
                verbose=-1
            )
        
        print(f"初始化了 {len(self.models)} 个模型")
        for model_name in self.models.keys():
            print(f"  - {model_name}")
        
        return True
    
    def train_and_evaluate_models(self):
        """训练和评估所有模型"""
        print("\n=== 训练和评估模型 ===")
        
        # 使用分层交叉验证
        cv = StratifiedKFold(n_splits=5, shuffle=True, random_state=42)
        
        for model_name, model in self.models.items():
            print(f"\n训练 {model_name}...")
            
            try:
                # 交叉验证评分
                cv_scores = cross_val_score(
                    model, self.X_scaled, self.y_encoded, 
                    cv=cv, scoring='accuracy'
                )
                
                # 训练完整模型
                model.fit(self.X_scaled, self.y_encoded)
                
                # 预测
                y_pred = model.predict(self.X_scaled)
                y_pred_proba = model.predict_proba(self.X_scaled)
                
                # 计算评估指标
                accuracy = accuracy_score(self.y_encoded, y_pred)
                precision, recall, f1, _ = precision_recall_fscore_support(
                    self.y_encoded, y_pred, average='weighted'
                )
                
                # 多分类AUC（使用ovr策略）
                try:
                    auc_score = roc_auc_score(
                        self.y_encoded, y_pred_proba, 
                        multi_class='ovr', average='weighted'
                    )
                except:
                    auc_score = 0.0
                
                # 保存结果
                self.model_results[model_name] = {
                    'model': model,
                    'cv_scores': cv_scores,
                    'cv_mean': cv_scores.mean(),
                    'cv_std': cv_scores.std(),
                    'accuracy': accuracy,
                    'precision': precision,
                    'recall': recall,
                    'f1_score': f1,
                    'auc_score': auc_score,
                    'predictions': y_pred,
                    'probabilities': y_pred_proba
                }
                
                print(f"  交叉验证准确率: {cv_scores.mean():.3f} (±{cv_scores.std():.3f})")
                print(f"  训练集准确率: {accuracy:.3f}")
                print(f"  F1分数: {f1:.3f}")
                print(f"  AUC分数: {auc_score:.3f}")
                
            except Exception as e:
                print(f"  训练 {model_name} 时出错: {e}")
                continue
        
        return True
    
    def hyperparameter_tuning(self, top_models=3):
        """对表现最好的模型进行超参数调优"""
        print(f"\n=== 超参数调优（前{top_models}个模型）===")
        
        # 按交叉验证分数排序
        sorted_models = sorted(
            self.model_results.items(), 
            key=lambda x: x[1]['cv_mean'], 
            reverse=True
        )
        
        # 定义超参数网格
        param_grids = {
            'logistic_regression': {
                'C': [0.1, 1, 10],
                'solver': ['liblinear', 'lbfgs']
            },
            'random_forest': {
                'n_estimators': [50, 100, 200],
                'max_depth': [None, 10, 20],
                'min_samples_split': [2, 5]
            },
            'gradient_boosting': {
                'n_estimators': [50, 100, 200],
                'learning_rate': [0.01, 0.1, 0.2],
                'max_depth': [3, 5, 7]
            },
            'svm': {
                'C': [0.1, 1, 10],
                'kernel': ['rbf', 'linear']
            }
        }
        
        if XGBOOST_AVAILABLE:
            param_grids['xgboost'] = {
                'n_estimators': [50, 100, 200],
                'learning_rate': [0.01, 0.1, 0.2],
                'max_depth': [3, 5, 7]
            }
        
        tuned_results = {}
        
        for i, (model_name, result) in enumerate(sorted_models[:top_models]):
            if model_name not in param_grids:
                print(f"  跳过 {model_name}（无超参数网格）")
                continue
            
            print(f"  调优 {model_name}...")
            
            try:
                # 创建新的模型实例
                base_model = type(result['model'])()
                
                # 网格搜索
                grid_search = GridSearchCV(
                    base_model, 
                    param_grids[model_name],
                    cv=3,  # 减少CV折数以节省时间
                    scoring='accuracy',
                    n_jobs=-1
                )
                
                grid_search.fit(self.X_scaled, self.y_encoded)
                
                # 评估调优后的模型
                best_model = grid_search.best_estimator_
                y_pred = best_model.predict(self.X_scaled)
                y_pred_proba = best_model.predict_proba(self.X_scaled)
                
                accuracy = accuracy_score(self.y_encoded, y_pred)
                precision, recall, f1, _ = precision_recall_fscore_support(
                    self.y_encoded, y_pred, average='weighted'
                )
                
                tuned_results[f'{model_name}_tuned'] = {
                    'model': best_model,
                    'best_params': grid_search.best_params_,
                    'best_cv_score': grid_search.best_score_,
                    'accuracy': accuracy,
                    'precision': precision,
                    'recall': recall,
                    'f1_score': f1,
                    'predictions': y_pred,
                    'probabilities': y_pred_proba
                }
                
                print(f"    最佳参数: {grid_search.best_params_}")
                print(f"    最佳CV分数: {grid_search.best_score_:.3f}")
                print(f"    调优后准确率: {accuracy:.3f}")
                
            except Exception as e:
                print(f"    调优 {model_name} 时出错: {e}")
                continue
        
        # 合并调优结果
        self.model_results.update(tuned_results)
        
        return True
    
    def generate_model_comparison(self):
        """生成模型比较报告"""
        print("\n=== 模型比较报告 ===")
        
        comparison_data = []
        
        for model_name, result in self.model_results.items():
            comparison_data.append({
                'Model': model_name,
                'CV_Mean': result.get('cv_mean', result.get('best_cv_score', 0)),
                'CV_Std': result.get('cv_std', 0),
                'Accuracy': result['accuracy'],
                'Precision': result['precision'],
                'Recall': result['recall'],
                'F1_Score': result['f1_score'],
                'AUC_Score': result.get('auc_score', 0)
            })
        
        comparison_df = pd.DataFrame(comparison_data)
        comparison_df = comparison_df.sort_values('Accuracy', ascending=False)
        
        print(comparison_df.round(3))
        
        # 保存比较结果
        comparison_file = f"model_comparison_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
        comparison_df.to_csv(comparison_file, index=False)
        print(f"\n模型比较结果已保存到: {comparison_file}")
        
        return comparison_df
    
    def analyze_feature_importance(self, top_n=20):
        """分析特征重要性"""
        print(f"\n=== 特征重要性分析（前{top_n}个特征）===")
        
        # 选择有特征重要性的模型
        importance_models = {}
        
        for model_name, result in self.model_results.items():
            model = result['model']
            
            if hasattr(model, 'feature_importances_'):
                importance_models[model_name] = model.feature_importances_
            elif hasattr(model, 'coef_'):
                # 对于线性模型，使用系数的绝对值
                importance_models[model_name] = np.abs(model.coef_[0])
        
        if not importance_models:
            print("没有可分析特征重要性的模型")
            return None
        
        # 创建特征重要性DataFrame
        importance_df = pd.DataFrame(importance_models, index=self.feature_names)
        
        # 计算平均重要性
        importance_df['Average'] = importance_df.mean(axis=1)
        importance_df = importance_df.sort_values('Average', ascending=False)
        
        print(f"前{top_n}个重要特征:")
        for i, (feature, row) in enumerate(importance_df.head(top_n).iterrows(), 1):
            print(f"  {i:2d}. {feature}: {row['Average']:.4f}")
        
        # 保存特征重要性
        importance_file = f"feature_importance_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
        importance_df.to_csv(importance_file)
        print(f"\n特征重要性已保存到: {importance_file}")
        
        return importance_df
    
    def save_trained_models(self):
        """保存训练好的模型"""
        print("\n=== 保存训练模型 ===")
        
        # 选择最佳模型
        best_model_name = max(
            self.model_results.keys(), 
            key=lambda x: self.model_results[x]['accuracy']
        )
        
        best_model = self.model_results[best_model_name]['model']
        
        # 保存模型信息
        model_info = {
            'best_model_name': best_model_name,
            'best_accuracy': self.model_results[best_model_name]['accuracy'],
            'feature_names': self.feature_names,
            'target_classes': list(self.target_encoder.classes_),
            'model_results': {}
        }
        
        # 保存所有模型的结果（不包括模型对象）
        for model_name, result in self.model_results.items():
            model_info['model_results'][model_name] = {
                'accuracy': result['accuracy'],
                'precision': result['precision'],
                'recall': result['recall'],
                'f1_score': result['f1_score'],
                'auc_score': result.get('auc_score', 0),
                'cv_mean': result.get('cv_mean', result.get('best_cv_score', 0)),
                'cv_std': result.get('cv_std', 0)
            }
        
        # 保存模型信息
        info_file = f"trained_models_info_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(info_file, 'w', encoding='utf-8') as f:
            json.dump(model_info, f, ensure_ascii=False, indent=2)
        
        print(f"模型信息已保存到: {info_file}")
        print(f"最佳模型: {best_model_name} (准确率: {model_info['best_accuracy']:.3f})")
        
        return info_file

def main():
    trainer = MLModelTrainer()
    
    # 加载处理后的数据
    if not trainer.load_processed_data():
        return
    
    # 准备特征和目标变量
    trainer.prepare_features_and_target()
    
    # 初始化模型
    trainer.initialize_models()
    
    # 训练和评估模型
    trainer.train_and_evaluate_models()
    
    # 超参数调优
    trainer.hyperparameter_tuning()
    
    # 生成模型比较报告
    comparison_df = trainer.generate_model_comparison()
    
    # 分析特征重要性
    importance_df = trainer.analyze_feature_importance()
    
    # 保存训练好的模型
    trainer.save_trained_models()

if __name__ == "__main__":
    main()

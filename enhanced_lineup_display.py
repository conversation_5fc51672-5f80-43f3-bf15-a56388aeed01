#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增强的阵容信息显示组件
提供更直观的阵容和阵型展示

功能特点:
1. 阵型图形化显示
2. 球员信息表格化
3. 主客队对比展示
4. 教练和替补信息
5. 阵容统计分析
"""

import tkinter as tk
from tkinter import ttk
import json
from typing import Dict, List, Optional, Any
import logging

logger = logging.getLogger(__name__)

class EnhancedLineupDisplay:
    """增强的阵容信息显示器"""
    
    def __init__(self, parent_frame):
        """初始化显示器"""
        self.parent_frame = parent_frame
        
        # 位置映射
        self.position_mapping = {
            '门将': 'GK', '守门员': 'GK',
            '后卫': 'DF', '中后卫': 'CB', '边后卫': 'FB', '左后卫': 'LB', '右后卫': 'RB',
            '中场': 'MF', '中前卫': 'CM', '前腰': 'AM', '后腰': 'DM', '边前卫': 'WM',
            '前锋': 'FW', '中锋': 'CF', '边锋': 'WF', '左边锋': 'LW', '右边锋': 'RW'
        }
        
        # 创建界面
        self.create_widgets()
    
    def create_widgets(self):
        """创建界面组件"""
        # 主容器
        main_frame = ttk.Frame(self.parent_frame)
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # 工具栏
        self.create_toolbar(main_frame)
        
        # 创建左右分栏
        paned_window = ttk.PanedWindow(main_frame, orient=tk.HORIZONTAL)
        paned_window.pack(fill=tk.BOTH, expand=True, pady=(10, 0))
        
        # 左侧：阵型和基本信息
        left_frame = ttk.Frame(paned_window)
        paned_window.add(left_frame, weight=1)
        
        # 右侧：详细球员信息
        right_frame = ttk.Frame(paned_window)
        paned_window.add(right_frame, weight=2)
        
        # 创建左侧内容
        self.create_formation_display(left_frame)
        
        # 创建右侧内容
        self.create_player_details(right_frame)
    
    def create_toolbar(self, parent):
        """创建工具栏"""
        toolbar_frame = ttk.Frame(parent)
        toolbar_frame.pack(fill=tk.X, pady=(0, 5))
        
        # 显示选项
        ttk.Label(toolbar_frame, text="显示内容:").pack(side=tk.LEFT, padx=(0, 5))
        
        self.show_starters = tk.BooleanVar(value=True)
        self.show_substitutes = tk.BooleanVar(value=True)
        self.show_coach = tk.BooleanVar(value=True)
        
        ttk.Checkbutton(toolbar_frame, text="首发阵容", variable=self.show_starters,
                       command=self.refresh_display).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Checkbutton(toolbar_frame, text="替补球员", variable=self.show_substitutes,
                       command=self.refresh_display).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Checkbutton(toolbar_frame, text="教练信息", variable=self.show_coach,
                       command=self.refresh_display).pack(side=tk.LEFT, padx=(0, 10))
        
        # 刷新按钮
        ttk.Button(toolbar_frame, text="刷新", command=self.refresh_display).pack(side=tk.RIGHT)
    
    def create_formation_display(self, parent):
        """创建阵型显示区域"""
        # 阵型信息框
        formation_frame = ttk.LabelFrame(parent, text="阵型信息", padding="10")
        formation_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 5))
        
        # 主队阵型
        home_frame = ttk.Frame(formation_frame)
        home_frame.pack(fill=tk.X, pady=(0, 10))
        
        ttk.Label(home_frame, text="🏠 主队阵型:", font=("Arial", 10, "bold")).pack(side=tk.LEFT)
        self.home_formation_label = ttk.Label(home_frame, text="未知", font=("Arial", 12, "bold"), foreground="red")
        self.home_formation_label.pack(side=tk.LEFT, padx=(10, 0))
        
        # 客队阵型
        away_frame = ttk.Frame(formation_frame)
        away_frame.pack(fill=tk.X, pady=(0, 10))
        
        ttk.Label(away_frame, text="✈️ 客队阵型:", font=("Arial", 10, "bold")).pack(side=tk.LEFT)
        self.away_formation_label = ttk.Label(away_frame, text="未知", font=("Arial", 12, "bold"), foreground="blue")
        self.away_formation_label.pack(side=tk.LEFT, padx=(10, 0))
        
        # 阵容统计
        stats_frame = ttk.LabelFrame(formation_frame, text="阵容统计", padding="5")
        stats_frame.pack(fill=tk.X, pady=(10, 0))
        
        self.lineup_stats_text = tk.Text(stats_frame, height=8, wrap=tk.WORD, font=("Consolas", 9))
        stats_scrollbar = ttk.Scrollbar(stats_frame, orient=tk.VERTICAL, command=self.lineup_stats_text.yview)
        self.lineup_stats_text.configure(yscrollcommand=stats_scrollbar.set)
        
        self.lineup_stats_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        stats_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
    
    def create_player_details(self, parent):
        """创建球员详细信息区域"""
        # 创建Notebook来分别显示首发、替补、教练
        self.player_notebook = ttk.Notebook(parent)
        self.player_notebook.pack(fill=tk.BOTH, expand=True)

        # 首发阵容标签页
        self.create_unified_lineup_tab("首发阵容", "starters")

        # 替补球员标签页
        self.create_unified_lineup_tab("替补球员", "substitutes")

        # 教练信息标签页
        self.create_coach_tab()

    def create_unified_lineup_tab(self, tab_name: str, lineup_type: str):
        """创建统一的阵容标签页"""
        frame = ttk.Frame(self.player_notebook)
        self.player_notebook.add(frame, text=tab_name)

        # 创建统一的球员表格
        columns = ("team", "number", "name", "position", "age", "nationality")
        tree = ttk.Treeview(frame, columns=columns, show="headings", height=15)

        # 设置列标题
        tree.heading("team", text="队伍")
        tree.heading("number", text="号码")
        tree.heading("name", text="姓名")
        tree.heading("position", text="位置")
        tree.heading("age", text="年龄")
        tree.heading("nationality", text="国籍")

        # 设置列宽
        tree.column("team", width=60)
        tree.column("number", width=50)
        tree.column("name", width=120)
        tree.column("position", width=60)
        tree.column("age", width=50)
        tree.column("nationality", width=80)

        # 滚动条
        scrollbar = ttk.Scrollbar(frame, orient=tk.VERTICAL, command=tree.yview)
        tree.configure(yscrollcommand=scrollbar.set)

        # 布局
        tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(10, 0), pady=10)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y, pady=10)

        # 配置颜色标签
        tree.tag_configure("home_team", background="#FFE5E5")     # 主队 - 浅红色
        tree.tag_configure("away_team", background="#E5F3FF")     # 客队 - 浅蓝色
        tree.tag_configure("gk", background="#FFE5CC")           # 门将 - 橙色
        tree.tag_configure("df", background="#E5F3FF")           # 后卫 - 蓝色
        tree.tag_configure("mf", background="#E5FFE5")           # 中场 - 绿色
        tree.tag_configure("fw", background="#FFE5E5")           # 前锋 - 红色

        # 保存树引用
        setattr(self, f'{lineup_type}_tree', tree)

    def create_coach_tab(self):
        """创建教练信息标签页"""
        frame = ttk.Frame(self.player_notebook)
        self.player_notebook.add(frame, text="教练信息")

        # 创建左右分栏显示主客队教练
        paned_window = ttk.PanedWindow(frame, orient=tk.HORIZONTAL)
        paned_window.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # 主队教练
        home_coach_frame = ttk.LabelFrame(paned_window, text="🏠 主队教练", padding="10")
        paned_window.add(home_coach_frame, weight=1)

        self.home_coach_text = tk.Text(home_coach_frame, height=10, wrap=tk.WORD, font=("Arial", 10))
        self.home_coach_text.pack(fill=tk.BOTH, expand=True)

        # 客队教练
        away_coach_frame = ttk.LabelFrame(paned_window, text="✈️ 客队教练", padding="10")
        paned_window.add(away_coach_frame, weight=1)

        self.away_coach_text = tk.Text(away_coach_frame, height=10, wrap=tk.WORD, font=("Arial", 10))
        self.away_coach_text.pack(fill=tk.BOTH, expand=True)
    
    def create_team_tab(self, tab_name: str, team_type: str):
        """创建球队标签页"""
        frame = ttk.Frame(self.player_notebook)
        self.player_notebook.add(frame, text=tab_name)
        
        # 创建子Notebook来分别显示首发和替补
        sub_notebook = ttk.Notebook(frame)
        sub_notebook.pack(fill=tk.BOTH, expand=True)
        
        # 首发球员
        starters_frame = ttk.Frame(sub_notebook)
        sub_notebook.add(starters_frame, text="首发阵容")
        
        starters_tree = self.create_player_tree(starters_frame)
        setattr(self, f'{team_type}_starters_tree', starters_tree)
        
        # 替补球员
        subs_frame = ttk.Frame(sub_notebook)
        sub_notebook.add(subs_frame, text="替补球员")
        
        subs_tree = self.create_player_tree(subs_frame)
        setattr(self, f'{team_type}_subs_tree', subs_tree)
        
        # 教练信息
        coach_frame = ttk.Frame(sub_notebook)
        sub_notebook.add(coach_frame, text="教练组")
        
        coach_text = tk.Text(coach_frame, height=5, wrap=tk.WORD, font=("Arial", 10))
        coach_text.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        setattr(self, f'{team_type}_coach_text', coach_text)
    
    def create_player_tree(self, parent):
        """创建球员信息表格"""
        # 表格列
        columns = ("number", "name", "position", "age", "nationality")
        tree = ttk.Treeview(parent, columns=columns, show="headings", height=12)
        
        # 设置列标题
        tree.heading("number", text="号码")
        tree.heading("name", text="姓名")
        tree.heading("position", text="位置")
        tree.heading("age", text="年龄")
        tree.heading("nationality", text="国籍")
        
        # 设置列宽
        tree.column("number", width=50)
        tree.column("name", width=120)
        tree.column("position", width=60)
        tree.column("age", width=50)
        tree.column("nationality", width=80)
        
        # 滚动条
        scrollbar = ttk.Scrollbar(parent, orient=tk.VERTICAL, command=tree.yview)
        tree.configure(yscrollcommand=scrollbar.set)
        
        # 布局
        tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(10, 0), pady=10)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y, pady=10)
        
        return tree
    
    def display_lineup(self, enhanced_data: Dict[str, Any]):
        """显示阵容信息"""
        try:
            # 清空所有显示
            self.clear_display()
            
            if 'lineups' not in enhanced_data:
                self.show_no_data_message()
                return
            
            lineups = enhanced_data['lineups']
            
            # 显示阵型信息
            home_formation = lineups.get('home_formation', '未知')
            away_formation = lineups.get('away_formation', '未知')
            
            self.home_formation_label.config(text=home_formation)
            self.away_formation_label.config(text=away_formation)
            
            # 显示主队阵容
            if 'home_lineup' in lineups:
                self.display_team_lineup(lineups['home_lineup'], 'home')
            
            # 显示客队阵容
            if 'away_lineup' in lineups:
                self.display_team_lineup(lineups['away_lineup'], 'away')
            
            # 生成阵容统计
            self.generate_lineup_stats(lineups)
            
        except Exception as e:
            logger.error(f"显示阵容信息失败: {e}")
            self.show_error_message(str(e))
    
    def display_team_lineup(self, lineup_data: Dict, team_type: str):
        """显示球队阵容"""
        try:
            team_name = "🏠 主队" if team_type == "home" else "✈️ 客队"

            # 显示首发球员
            if 'players' in lineup_data:
                for player in lineup_data['players']:
                    self.add_player_to_unified_tree(self.starters_tree, player, team_name, team_type)

            # 显示替补球员
            if 'substitutes' in lineup_data:
                for player in lineup_data['substitutes']:
                    self.add_player_to_unified_tree(self.substitutes_tree, player, team_name, team_type)

            # 显示教练信息
            if 'coach' in lineup_data:
                coach_text = self.home_coach_text if team_type == "home" else self.away_coach_text
                coach_info = f"主教练: {lineup_data['coach']}\n\n"
                coach_info += "教练组信息:\n"
                coach_info += "- 战术风格: " + self.analyze_formation_style(lineup_data.get('formation', '')) + "\n"
                coach_info += "- 执教经验: 待补充\n"
                coach_info += "- 历史战绩: 待统计\n"
                coach_info += f"- 本场阵型: {lineup_data.get('formation', '未知')}"

                coach_text.delete(1.0, tk.END)
                coach_text.insert(1.0, coach_info)

        except Exception as e:
            logger.error(f"显示{team_type}队阵容失败: {e}")

    def add_player_to_unified_tree(self, tree: ttk.Treeview, player: Dict, team_name: str, team_type: str):
        """添加球员到统一表格"""
        try:
            name = player.get('name', '未知')
            number = player.get('number', '')
            position = self.extract_position(player)
            age = player.get('age', '')
            nationality = player.get('nationality', '')

            # 根据队伍和位置设置标签
            tags = []

            # 队伍标签
            if team_type == "home":
                tags.append("home_team")
            else:
                tags.append("away_team")

            # 位置标签
            position_tag = self.get_position_tag(position)
            if position_tag:
                tags.append(position_tag)

            tree.insert("", "end", values=(
                team_name, number, name, position, age, nationality
            ), tags=tuple(tags))

        except Exception as e:
            logger.error(f"添加球员信息失败: {e}")
    
    def add_player_to_tree(self, tree: ttk.Treeview, player: Dict, team_type: str):
        """添加球员到表格"""
        try:
            name = player.get('name', '未知')
            number = player.get('number', '')
            position = self.extract_position(player)
            age = player.get('age', '')
            nationality = player.get('nationality', '')
            
            # 根据位置设置颜色标签
            tag = self.get_position_tag(position)
            
            tree.insert("", "end", values=(
                number, name, position, age, nationality
            ), tags=(tag,))
            
            # 配置位置颜色标签
            tree.tag_configure("gk", background="#FFE5CC")    # 门将 - 橙色
            tree.tag_configure("df", background="#E5F3FF")    # 后卫 - 蓝色
            tree.tag_configure("mf", background="#E5FFE5")    # 中场 - 绿色
            tree.tag_configure("fw", background="#FFE5E5")    # 前锋 - 红色
            
        except Exception as e:
            logger.error(f"添加球员信息失败: {e}")
    
    def extract_position(self, player: Dict) -> str:
        """提取球员位置"""
        # 从不同可能的字段中提取位置信息
        position_fields = ['position', 'pos', '位置']
        
        for field in position_fields:
            if field in player and player[field]:
                return str(player[field])
        
        return '未知'
    
    def get_position_tag(self, position: str) -> str:
        """根据位置获取标签"""
        position_lower = position.lower()
        
        if any(pos in position_lower for pos in ['gk', '门将', '守门']):
            return "gk"
        elif any(pos in position_lower for pos in ['df', 'cb', 'fb', 'lb', 'rb', '后卫']):
            return "df"
        elif any(pos in position_lower for pos in ['mf', 'cm', 'am', 'dm', 'wm', '中场']):
            return "mf"
        elif any(pos in position_lower for pos in ['fw', 'cf', 'wf', 'lw', 'rw', '前锋']):
            return "fw"
        else:
            return ""
    
    def generate_lineup_stats(self, lineups: Dict):
        """生成阵容统计"""
        try:
            stats_text = "📊 阵容对比分析\n"
            stats_text += "=" * 30 + "\n\n"
            
            # 统计各队人数
            home_lineup = lineups.get('home_lineup', {})
            away_lineup = lineups.get('away_lineup', {})
            
            home_starters = len(home_lineup.get('players', []))
            home_subs = len(home_lineup.get('substitutes', []))
            away_starters = len(away_lineup.get('players', []))
            away_subs = len(away_lineup.get('substitutes', []))
            
            stats_text += f"👥 人员配置:\n"
            stats_text += f"主队: {home_starters} 首发 + {home_subs} 替补\n"
            stats_text += f"客队: {away_starters} 首发 + {away_subs} 替补\n\n"
            
            # 阵型分析
            home_formation = lineups.get('home_formation', '未知')
            away_formation = lineups.get('away_formation', '未知')
            
            stats_text += f"⚽ 阵型对比:\n"
            stats_text += f"主队: {home_formation}\n"
            stats_text += f"客队: {away_formation}\n\n"
            
            # 战术风格分析（基于阵型）
            stats_text += f"🎯 战术风格:\n"
            stats_text += f"主队: {self.analyze_formation_style(home_formation)}\n"
            stats_text += f"客队: {self.analyze_formation_style(away_formation)}\n\n"
            
            # 教练信息
            home_coach = home_lineup.get('coach', '未知')
            away_coach = away_lineup.get('coach', '未知')
            
            stats_text += f"👨‍💼 教练对比:\n"
            stats_text += f"主队: {home_coach}\n"
            stats_text += f"客队: {away_coach}\n"
            
            self.lineup_stats_text.delete(1.0, tk.END)
            self.lineup_stats_text.insert(1.0, stats_text)
            
        except Exception as e:
            logger.error(f"生成阵容统计失败: {e}")
    
    def analyze_formation_style(self, formation: str) -> str:
        """分析阵型风格"""
        if not formation or formation == '未知':
            return "未知风格"
        
        formation_styles = {
            '4-4-2': '平衡型，攻守兼备',
            '4-3-3': '进攻型，边路突破',
            '3-5-2': '中场控制型',
            '4-5-1': '防守反击型',
            '3-4-3': '高压进攻型',
            '5-3-2': '防守稳固型',
            '4-2-3-1': '现代平衡型'
        }
        
        return formation_styles.get(formation, '特殊战术')
    
    def clear_display(self):
        """清空显示"""
        # 清空阵型标签
        self.home_formation_label.config(text="未知")
        self.away_formation_label.config(text="未知")

        # 清空统一球员表格
        for item in self.starters_tree.get_children():
            self.starters_tree.delete(item)

        for item in self.substitutes_tree.get_children():
            self.substitutes_tree.delete(item)

        # 清空教练信息
        self.home_coach_text.delete(1.0, tk.END)
        self.away_coach_text.delete(1.0, tk.END)

        # 清空统计信息
        self.lineup_stats_text.delete(1.0, tk.END)
    
    def show_no_data_message(self):
        """显示无数据消息"""
        self.lineup_stats_text.delete(1.0, tk.END)
        self.lineup_stats_text.insert(1.0, "❌ 暂无阵容数据\n请点击'抓取详细数据'按钮获取数据")
    
    def show_error_message(self, error_msg: str):
        """显示错误消息"""
        self.lineup_stats_text.delete(1.0, tk.END)
        self.lineup_stats_text.insert(1.0, f"❌ 显示错误: {error_msg}")
    
    def refresh_display(self):
        """刷新显示"""
        # 这个方法会被UI调用来刷新数据
        pass

def test_enhanced_lineup_display():
    """测试增强阵容显示"""
    root = tk.Tk()
    root.title("统一阵容显示测试")
    root.geometry("1200x800")

    # 创建显示器
    display = EnhancedLineupDisplay(root)

    # 测试数据
    test_data = {
        'lineups': {
            'home_formation': '4-3-3',
            'away_formation': '3-5-2',
            'home_lineup': {
                'players': [
                    {'name': '主队门将', 'number': '1', 'position': '门将', 'age': '28', 'nationality': '中国'},
                    {'name': '主队右后卫', 'number': '2', 'position': '右后卫', 'age': '25', 'nationality': '中国'},
                    {'name': '主队中后卫', 'number': '3', 'position': '中后卫', 'age': '29', 'nationality': '巴西'},
                    {'name': '主队左后卫', 'number': '4', 'position': '左后卫', 'age': '26', 'nationality': '中国'},
                    {'name': '主队中前卫', 'number': '8', 'position': '中前卫', 'age': '27', 'nationality': '西班牙'},
                    {'name': '主队前腰', 'number': '10', 'position': '前腰', 'age': '24', 'nationality': '阿根廷'},
                    {'name': '主队中锋', 'number': '9', 'position': '中锋', 'age': '30', 'nationality': '英格兰'}
                ],
                'substitutes': [
                    {'name': '替补门将', 'number': '12', 'position': '门将', 'age': '32', 'nationality': '中国'},
                    {'name': '替补前锋', 'number': '21', 'position': '前锋', 'age': '22', 'nationality': '中国'}
                ],
                'coach': '主教练A',
                'formation': '4-3-3'
            },
            'away_lineup': {
                'players': [
                    {'name': '客队门将', 'number': '1', 'position': '门将', 'age': '31', 'nationality': '德国'},
                    {'name': '客队中后卫1', 'number': '4', 'position': '中后卫', 'age': '28', 'nationality': '意大利'},
                    {'name': '客队中后卫2', 'number': '5', 'position': '中后卫', 'age': '26', 'nationality': '法国'},
                    {'name': '客队前腰', 'number': '10', 'position': '前腰', 'age': '25', 'nationality': '葡萄牙'},
                    {'name': '客队边锋', 'number': '11', 'position': '边锋', 'age': '23', 'nationality': '荷兰'}
                ],
                'substitutes': [
                    {'name': '客队替补', 'number': '15', 'position': '中场', 'age': '24', 'nationality': '比利时'}
                ],
                'coach': '主教练B',
                'formation': '3-5-2'
            }
        }
    }

    # 显示测试数据
    display.display_lineup(test_data)

    root.mainloop()

if __name__ == '__main__':
    test_enhanced_lineup_display()

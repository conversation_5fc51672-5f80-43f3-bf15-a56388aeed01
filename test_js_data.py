#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试JavaScript数据源
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from enhanced_odds_scraper import EnhancedOddsScraper
import logging
import re

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_js_data():
    """测试JavaScript数据源"""
    print("🧪 测试JavaScript数据源 - 比赛2399071")
    print("=" * 60)
    
    scraper = EnhancedOddsScraper()
    match_id = "2399071"
    
    # 从页面中提取的JavaScript URL
    js_url = f"https://1x2d.titan007.com/{match_id}.js"
    
    print(f"📋 测试JavaScript URL: {js_url}")
    
    content = scraper.get_page_content(js_url)
    
    if not content:
        print(f"❌ 无法获取JavaScript内容")
        return
        
    print(f"✅ JavaScript可访问，内容长度: {len(content)}")
    
    # 保存JavaScript内容到文件
    with open(f"js_data_{match_id}.js", "w", encoding="utf-8") as f:
        f.write(content)
    print(f"📄 JavaScript内容已保存到: js_data_{match_id}.js")
    
    # 检查关键词
    keywords = [
        'OddsHistory.aspx',
        'bet365',
        'cid=281',
        'id=',
        'sid=',
        'record',
        'odds'
    ]
    
    print(f"\n🔍 关键词检查:")
    for keyword in keywords:
        count = content.count(keyword)
        print(f"  {keyword}: {count} 次")
    
    # 查找OddsHistory链接
    odds_history_pattern = r'OddsHistory\.aspx\?[^"\']*'
    matches = re.findall(odds_history_pattern, content)
    
    if matches:
        print(f"\n✅ 找到 {len(matches)} 个OddsHistory链接:")
        for i, match in enumerate(matches[:10], 1):  # 只显示前10个
            print(f"  {i}. {match}")
            
            # 检查是否是bet365的链接
            if 'cid=281' in match:
                print(f"     ✅ 这是bet365的链接！")
                
                # 提取record ID
                id_match = re.search(r'id=(\d+)', match)
                if id_match:
                    record_id = id_match.group(1)
                    print(f"     📊 Record ID: {record_id}")
                    
                    # 验证这个record ID是否正确
                    correct_record_id = "129831588"
                    if record_id == correct_record_id:
                        print(f"     ✅ Record ID正确！")
                    else:
                        print(f"     ❌ Record ID错误，应该是: {correct_record_id}")
    else:
        print(f"\n❌ 未找到OddsHistory链接")
        
        # 显示JavaScript内容的前1000个字符
        print(f"\n📄 JavaScript前1000个字符:")
        print("-" * 40)
        print(content[:1000])
        print("-" * 40)

if __name__ == "__main__":
    test_js_data()

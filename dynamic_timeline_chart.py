#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
动态时间线图表模块
显示博彩公司开盘时间线，并在每个时间点显示当时的赔率信息
"""

try:
    import matplotlib.pyplot as plt
    import matplotlib.dates as mdates
    from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg, NavigationToolbar2Tk
    from matplotlib.widgets import Slider
    import pandas as pd
    import numpy as np

    # 设置中文字体
    plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'Arial Unicode MS']
    plt.rcParams['axes.unicode_minus'] = False
    MATPLOTLIB_AVAILABLE = True
except ImportError:
    # 如果matplotlib不可用，创建占位符
    MATPLOTLIB_AVAILABLE = False
    print("警告: matplotlib不可用，动态时间线图表功能将被禁用")

from datetime import datetime, timedelta
from typing import List, Dict, Optional, Tuple
import logging
from collections import defaultdict
import tkinter as tk
from tkinter import ttk

logger = logging.getLogger(__name__)

class DynamicTimelineChart:
    def __init__(self, parent_frame):
        """初始化动态时间线图表"""
        self.parent_frame = parent_frame
        self.figure = None
        self.canvas = None
        self.toolbar = None
        self.ax = None
        self.timeline_data = []
        self.time_points = []
        self.company_positions = {}
        self.matplotlib_available = MATPLOTLIB_AVAILABLE

        # 横向控制相关
        self.control_frame = None
        self.zoom_scale = tk.DoubleVar(value=1.0)
        self.scroll_scale = tk.DoubleVar(value=0.0)
        self.original_xlim = None

    def create_dynamic_timeline_chart(self, match_info: Dict, odds_data: List[Dict]) -> bool:
        """创建动态时间线图表"""
        if not self.matplotlib_available:
            logger.warning("matplotlib不可用，无法创建动态时间线图表")
            return False

        try:
            # 清除之前的图表
            if self.canvas:
                self.canvas.get_tk_widget().destroy()
            if self.toolbar:
                self.toolbar.destroy()

            # 处理数据
            self.timeline_data, self.time_points = self._prepare_dynamic_timeline_data(match_info, odds_data)

            if not self.timeline_data:
                logger.warning("没有有效的动态时间线数据")
                return False

            # 创建图表
            self.figure, self.ax = plt.subplots(figsize=(16, 10))

            # 绘制动态时间线
            self._plot_dynamic_timeline(match_info)

            # 创建控制面板
            self._create_control_panel()

            # 创建Canvas并嵌入到Tkinter
            self.canvas = FigureCanvasTkAgg(self.figure, self.parent_frame)
            self.canvas.draw()

            # 添加工具栏（包含缩放、平移等功能）
            self.toolbar = NavigationToolbar2Tk(self.canvas, self.parent_frame)
            self.toolbar.update()

            # 布局
            self.control_frame.pack(side='top', fill='x', pady=(0, 5))
            self.toolbar.pack(side='top', fill='x')
            self.canvas.get_tk_widget().pack(fill='both', expand=True)

            # 保存原始X轴范围
            self.original_xlim = self.ax.get_xlim()

            logger.info("动态时间线图表创建成功")
            return True

        except Exception as e:
            logger.error(f"创建动态时间线图表失败: {e}")
            return False

    def _prepare_dynamic_timeline_data(self, match_info: Dict, odds_data: List[Dict]) -> Tuple[List[Dict], List[datetime]]:
        """准备动态时间线数据 - 只在关键开盘时间点显示数据"""
        try:
            # 获取比赛开始时间
            match_time_str = match_info.get('match_time')
            if not match_time_str:
                logger.error("比赛时间信息缺失")
                return [], []

            match_time = datetime.strptime(match_time_str, "%Y-%m-%d %H:%M:%S")

            # 按公司整理数据，找到每个公司的首次开盘时间
            company_first_odds = {}
            company_all_odds = defaultdict(list)

            for odds in odds_data:
                company_name = odds.get('company_name')
                date_str = odds.get('date')  # MM-DD格式
                time_str = odds.get('time')  # HH:MM格式

                if not all([company_name, date_str, time_str]):
                    continue

                try:
                    # 构造完整的日期时间
                    year = match_time.year
                    full_datetime_str = f"{year}-{date_str} {time_str}:00"
                    odds_time = datetime.strptime(full_datetime_str, "%Y-%m-%d %H:%M:%S")

                    # 只处理开赛前的数据
                    if odds_time <= match_time:
                        odds_record = {
                            'time': odds_time,
                            'home_odds': odds.get('home_odds'),
                            'draw_odds': odds.get('draw_odds'),
                            'away_odds': odds.get('away_odds'),
                            'return_rate': odds.get('return_rate'),
                            'company_id': odds.get('company_id', '')
                        }

                        company_all_odds[company_name].append(odds_record)

                        # 记录首次开盘时间
                        if company_name not in company_first_odds:
                            company_first_odds[company_name] = odds_record
                        elif odds_time < company_first_odds[company_name]['time']:
                            company_first_odds[company_name] = odds_record

                except ValueError as e:
                    logger.warning(f"时间解析失败: {full_datetime_str}, 错误: {e}")
                    continue

            # 对每个公司的数据按时间排序
            for company_name in company_all_odds:
                company_all_odds[company_name].sort(key=lambda x: x['time'])

            # 获取所有公司的开盘时间点，这些是关键时间点
            key_time_points = []
            for company_name, first_odds in company_first_odds.items():
                key_time_points.append(first_odds['time'])

            # 按时间排序关键时间点
            key_time_points.sort()

            if not key_time_points:
                return [], []

            # 为每个公司准备时间线数据
            timeline_data = []

            for company_name, first_odds in company_first_odds.items():
                company_timeline = {
                    'company_name': company_name,
                    'company_id': first_odds['company_id'],
                    'first_time': first_odds['time'],
                    'key_points': []  # 只存储关键时间点的数据
                }

                # 对于每个关键时间点，找到该公司在那个时间的赔率状态
                for key_time in key_time_points:
                    # 找到这个时间点时该公司的最新赔率（如果已开盘）
                    current_odds = None

                    # 如果该公司在这个时间点已经开盘
                    if key_time >= first_odds['time']:
                        for odds in company_all_odds[company_name]:
                            if odds['time'] <= key_time:
                                current_odds = odds
                            else:
                                break

                        if current_odds:
                            company_timeline['key_points'].append({
                                'time': key_time,
                                'odds': current_odds,
                                'hours_before_match': (match_time - key_time).total_seconds() / 3600,
                                'is_opening': (key_time == first_odds['time'])  # 标记是否为该公司的开盘时间
                            })

                timeline_data.append(company_timeline)

            # 按首次开盘时间排序
            timeline_data.sort(key=lambda x: x['first_time'])

            logger.info(f"准备了 {len(timeline_data)} 家公司的关键时间点数据，{len(key_time_points)} 个关键时间点")
            return timeline_data, key_time_points

        except Exception as e:
            logger.error(f"准备动态时间线数据失败: {e}")
            return [], []

    def _plot_dynamic_timeline(self, match_info: Dict):
        """绘制动态时间线图表 - 只在关键开盘时间点显示数据"""
        try:
            if not self.timeline_data or not self.time_points:
                return

            # 设置公司位置
            companies = [item['company_name'] for item in self.timeline_data]
            y_positions = list(range(len(companies)))
            self.company_positions = {company: pos for pos, company in enumerate(companies)}

            # 计算时间范围（小时）
            match_time = datetime.strptime(match_info.get('match_time'), "%Y-%m-%d %H:%M:%S")
            hours_before = [(match_time - tp).total_seconds() / 3600 for tp in self.time_points]
            max_hours = max(hours_before) if hours_before else 24

            # 设置图表尺寸和范围
            self.ax.set_xlim(-max_hours * 0.05, max_hours * 1.2)
            self.ax.set_ylim(-0.5, len(companies) - 0.5)

            # 绘制每个公司的时间线
            colors = plt.cm.Set3(np.linspace(0, 1, len(companies)))

            for i, company_data in enumerate(self.timeline_data):
                company_name = company_data['company_name']
                y_pos = i
                color = colors[i]

                # 绘制基础时间线（从开盘到比赛开始）
                first_time = company_data['first_time']
                if first_time:
                    first_hours_before = (match_time - first_time).total_seconds() / 3600

                    # 绘制从开盘到比赛开始的线段
                    self.ax.barh(y_pos, first_hours_before, height=0.4,
                               color=color, alpha=0.6, label=company_name)

                # 只在关键时间点标记数据
                for point_data in company_data['key_points']:
                    hours_before = point_data['hours_before_match']
                    odds = point_data['odds']
                    is_opening = point_data['is_opening']

                    # 绘制关键时间点
                    if is_opening:
                        # 开盘时间点用大一点的圆圈标记
                        self.ax.scatter(hours_before, y_pos, s=100, color=color,
                                      alpha=1.0, zorder=6, edgecolors='black', linewidth=2,
                                      marker='o')
                    else:
                        # 其他关键时间点用小圆点标记
                        self.ax.scatter(hours_before, y_pos, s=60, color=color,
                                      alpha=0.8, zorder=5, edgecolors='white', linewidth=1,
                                      marker='s')

                    # 添加赔率信息文本
                    if is_opening:
                        # 开盘时间点显示开盘赔率（横向排列）
                        odds_text = f"{odds['home_odds']:.2f} | {odds['draw_odds']:.2f} | {odds['away_odds']:.2f}"
                        self.ax.text(hours_before, y_pos + 0.25, odds_text,
                                   ha='center', va='bottom', fontsize=8, fontweight='bold',
                                   bbox=dict(boxstyle='round,pad=0.3', facecolor=color, alpha=0.8))
                    else:
                        # 其他时间点显示当时赔率（横向排列，较小字体）
                        odds_text = f"{odds['home_odds']:.2f} | {odds['draw_odds']:.2f} | {odds['away_odds']:.2f}"
                        self.ax.text(hours_before, y_pos - 0.25, odds_text,
                                   ha='center', va='top', fontsize=7,
                                   bbox=dict(boxstyle='round,pad=0.2', facecolor='white', alpha=0.9, edgecolor=color))

            # 绘制垂直线连接同一时间点的不同公司
            for key_time in self.time_points:
                hours_before = (match_time - key_time).total_seconds() / 3600

                # 找到在这个时间点有数据的公司
                companies_at_time = []
                for i, company_data in enumerate(self.timeline_data):
                    for point_data in company_data['key_points']:
                        if point_data['time'] == key_time:
                            companies_at_time.append((i, point_data))
                            break

                # 如果有多个公司在这个时间点有数据，绘制垂直连接线
                if len(companies_at_time) > 1:
                    y_positions_at_time = [pos for pos, _ in companies_at_time]
                    min_y = min(y_positions_at_time)
                    max_y = max(y_positions_at_time)

                    self.ax.plot([hours_before, hours_before], [min_y - 0.3, max_y + 0.3],
                               color='gray', linestyle=':', alpha=0.5, linewidth=1)

                    # 在垂直线顶部添加时间标签
                    time_label = key_time.strftime('%H:%M')
                    self.ax.text(hours_before, max_y + 0.4, time_label,
                               ha='center', va='bottom', fontsize=9,
                               bbox=dict(boxstyle='round,pad=0.2', facecolor='lightgray', alpha=0.8))

            # 设置Y轴
            self.ax.set_yticks(y_positions)
            self.ax.set_yticklabels(companies)
            self.ax.set_ylabel('博彩公司', fontsize=12)

            # 设置X轴
            self.ax.set_xlabel('距离开赛时间 (小时)', fontsize=12)
            self.ax.invert_xaxis()  # 反转X轴，使时间从左到右递减

            # 添加开赛时间线
            self.ax.axvline(x=0, color='red', linestyle='--', linewidth=2, alpha=0.8, label='开赛时间')

            # 设置标题
            match_title = f"{match_info.get('home_team', '')} vs {match_info.get('away_team', '')}"
            match_time_str = match_info.get('match_time', '')
            self.ax.set_title(f'博彩公司关键时间点赔率对比\n{match_title} ({match_time_str})',
                            fontsize=14, fontweight='bold', pad=20)

            # 添加网格
            self.ax.grid(True, axis='x', alpha=0.3)
            self.ax.grid(True, axis='y', alpha=0.2)

            # 添加统计信息
            stats_text = f"""关键时间点统计:
• 参与公司: {len(companies)} 家
• 关键时间点: {len(self.time_points)} 个
• 时间跨度: {max_hours:.1f} 小时
• 显示模式: 仅关键开盘时间"""

            self.ax.text(0.02, 0.98, stats_text, transform=self.ax.transAxes,
                        verticalalignment='top', bbox=dict(boxstyle='round', facecolor='lightblue', alpha=0.8),
                        fontsize=10)

            # 添加说明
            legend_text = """图表说明:
• 横轴: 距离开赛时间(小时)
• 纵轴: 博彩公司
• ●: 公司开盘时间点(显示赔率)
• ■: 其他公司开盘时的当时赔率
• 虚线: 同一时间点连接线
• 红线: 比赛开始时间"""

            self.ax.text(0.98, 0.02, legend_text, transform=self.ax.transAxes,
                        verticalalignment='bottom', horizontalalignment='right',
                        bbox=dict(boxstyle='round', facecolor='lightyellow', alpha=0.8),
                        fontsize=9)

            # 调整布局
            self.figure.tight_layout()

        except Exception as e:
            logger.error(f"绘制动态时间线图表失败: {e}")
            raise

    def _create_control_panel(self):
        """创建横向控制面板"""
        if self.control_frame:
            self.control_frame.destroy()

        self.control_frame = ttk.Frame(self.parent_frame)

        # 横向缩放控制
        ttk.Label(self.control_frame, text="横向缩放:").pack(side=tk.LEFT, padx=(0, 5))
        zoom_scale = ttk.Scale(self.control_frame, from_=0.5, to=5.0,
                              variable=self.zoom_scale, orient=tk.HORIZONTAL,
                              length=150, command=self._on_zoom_change)
        zoom_scale.pack(side=tk.LEFT, padx=(0, 10))

        # 显示缩放值
        self.zoom_label = ttk.Label(self.control_frame, text="1.0x")
        self.zoom_label.pack(side=tk.LEFT, padx=(0, 15))

        # 横向滚动控制
        ttk.Label(self.control_frame, text="横向滚动:").pack(side=tk.LEFT, padx=(0, 5))
        scroll_scale = ttk.Scale(self.control_frame, from_=0.0, to=1.0,
                                variable=self.scroll_scale, orient=tk.HORIZONTAL,
                                length=200, command=self._on_scroll_change)
        scroll_scale.pack(side=tk.LEFT, padx=(0, 10))

        # 重置按钮
        ttk.Button(self.control_frame, text="重置视图",
                  command=self._reset_view).pack(side=tk.LEFT, padx=(15, 0))

    def _on_zoom_change(self, value):
        """处理缩放变化"""
        try:
            zoom_factor = float(value)
            self.zoom_label.config(text=f"{zoom_factor:.1f}x")
            self._update_view()
        except Exception as e:
            logger.error(f"缩放变化处理失败: {e}")

    def _on_scroll_change(self, value):
        """处理滚动变化"""
        try:
            self._update_view()
        except Exception as e:
            logger.error(f"滚动变化处理失败: {e}")

    def _update_view(self):
        """更新视图"""
        if not self.ax or not self.original_xlim:
            return

        try:
            zoom_factor = self.zoom_scale.get()
            scroll_factor = self.scroll_scale.get()

            # 计算新的X轴范围
            original_width = self.original_xlim[1] - self.original_xlim[0]
            new_width = original_width / zoom_factor

            # 计算滚动偏移
            max_offset = original_width - new_width
            offset = max_offset * scroll_factor

            new_left = self.original_xlim[0] + offset
            new_right = new_left + new_width

            # 应用新的X轴范围
            self.ax.set_xlim(new_left, new_right)
            self.canvas.draw()

        except Exception as e:
            logger.error(f"更新视图失败: {e}")

    def _reset_view(self):
        """重置视图"""
        try:
            self.zoom_scale.set(1.0)
            self.scroll_scale.set(0.0)
            if self.ax and self.original_xlim:
                self.ax.set_xlim(self.original_xlim)
                self.canvas.draw()
        except Exception as e:
            logger.error(f"重置视图失败: {e}")

    def clear_chart(self):
        """清除图表"""
        if self.canvas:
            self.canvas.get_tk_widget().destroy()
            self.canvas = None
        if self.toolbar:
            self.toolbar.destroy()
            self.toolbar = None
        if self.control_frame:
            self.control_frame.destroy()
            self.control_frame = None
        if self.figure and self.matplotlib_available:
            plt.close(self.figure)
            self.figure = None
        self.ax = None
        self.timeline_data = []
        self.time_points = []
        self.company_positions = {}
        self.original_xlim = None

    def save_chart(self, filename: str = None):
        """保存图表"""
        if not self.matplotlib_available or not self.figure:
            return False

        try:
            if filename is None:
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                filename = f"dynamic_timeline_chart_{timestamp}.png"

            self.figure.savefig(filename, dpi=300, bbox_inches='tight')
            logger.info(f"动态时间线图表已保存到: {filename}")
            return True

        except Exception as e:
            logger.error(f"保存图表失败: {e}")
            return False

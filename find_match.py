#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sqlite3

def find_matches():
    conn = sqlite3.connect('odds_data.db')
    cursor = conn.cursor()
    
    # 查找蒙彼利埃或马赛相关的比赛
    cursor.execute('''
        SELECT match_id, home_team, away_team, match_time 
        FROM matches 
        WHERE home_team LIKE "%蒙彼利埃%" OR away_team LIKE "%蒙彼利埃%" 
           OR home_team LIKE "%马赛%" OR away_team LIKE "%马赛%"
    ''')
    
    results = cursor.fetchall()
    print("找到的比赛:")
    for r in results:
        print(f'{r[0]}: {r[1]} vs {r[2]} - {r[3]}')
    
    conn.close()

if __name__ == "__main__":
    find_matches()

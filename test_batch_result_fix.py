#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试批量抓取结果修复
"""

import sys
import os

def test_batch_result_compatibility():
    """测试批量抓取结果兼容性"""
    
    print("🔍 测试批量抓取结果数据结构兼容性")
    print("=" * 50)
    
    # 模拟不同的数据结构
    test_cases = [
        {
            "name": "普通批量抓取数据结构",
            "data": {
                'round': 1,
                'match_id': '2711656',
                'teams': '埃格尔森德 vs 奥勒松',
                'match_time': '2025-07-22 23:00:00',
                'score': '1-0',
                'status': '已完成',
                'odds_status': '已抓取'
            }
        },
        {
            "name": "按日期抓取数据结构（新）",
            "data": {
                'match_id': '2711656',
                'league': '挪甲',
                'home_team': '埃格尔森德',
                'away_team': '奥勒松',
                'match_time': '2025-07-22 23:00:00',
                'odds_count': 767,
                'status': '成功'
            }
        },
        {
            "name": "按日期抓取数据结构（失败）",
            "data": {
                'match_id': '2711657',
                'league': '挪甲',
                'home_team': '特罗姆瑟',
                'away_team': '桑德菲杰',
                'match_time': '2025-07-22 23:30:00',
                'odds_count': 0,
                'status': '失败: 网络超时'
            }
        },
        {
            "name": "兼容性数据结构",
            "data": {
                'match_id': '2711658',
                'teams': '莫尔德 vs 利勒斯特罗姆',
                'match_time': '2025-07-22 23:45:00',
                'status': '未知',
                'odds_status': '待抓取'
            }
        }
    ]
    
    print("📊 测试数据结构处理逻辑:")
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n{i}. {test_case['name']}")
        data = test_case['data']
        
        # 模拟处理逻辑
        try:
            if 'round' in data:
                # 普通批量抓取的数据结构
                result = (
                    data['round'],
                    data['match_id'],
                    data['teams'],
                    data['match_time'],
                    data['score'],
                    data['status'],
                    data['odds_status']
                )
                print(f"   ✅ 普通批量抓取格式: {result}")
                
            else:
                # 按日期抓取的数据结构
                teams = f"{data.get('home_team', '')} vs {data.get('away_team', '')}"
                
                if 'league' in data and 'home_team' in data:
                    # 新的按日期抓取数据结构
                    result = (
                        data.get('league', ''),  # 使用联赛名称作为轮次
                        data['match_id'],
                        teams,
                        data.get('match_time', ''),
                        '',  # 按日期抓取没有比分信息
                        data['status'],
                        f"赔率: {data.get('odds_count', 0)} 条" if 'odds_count' in data else "待抓取"
                    )
                    print(f"   ✅ 按日期抓取格式: {result}")
                    
                else:
                    # 兼容其他可能的数据结构
                    result = (
                        data.get('round', ''),
                        data.get('match_id', ''),
                        data.get('teams', teams),
                        data.get('match_time', ''),
                        data.get('score', ''),
                        data.get('status', ''),
                        data.get('odds_status', '')
                    )
                    print(f"   ✅ 兼容性格式: {result}")
                    
        except Exception as e:
            print(f"   ❌ 处理失败: {e}")
    
    print(f"\n✅ 数据结构兼容性测试完成")

def simulate_ui_table_display():
    """模拟UI表格显示"""
    
    print(f"\n🖥️ 模拟UI表格显示效果")
    print("-" * 50)
    
    # 模拟表格数据
    table_data = [
        ("挪甲", "2711656", "埃格尔森德 vs 奥勒松", "2025-07-22 23:00:00", "", "成功", "赔率: 767 条"),
        ("挪甲", "2711657", "特罗姆瑟 vs 桑德菲杰", "2025-07-22 23:30:00", "", "失败: 网络超时", "赔率: 0 条"),
        ("欧冠杯", "2826828", "古比斯 vs 阿拉木图凯拉特", "2025-07-22 23:00:00", "", "成功", "赔率: 542 条"),
        ("球会友谊", "2832922", "GKS卡托威斯女足 vs 史洛特女足", "2025-07-22 22:00:00", "", "成功", "赔率: 234 条"),
    ]
    
    # 表格标题
    headers = ["联赛/轮次", "比赛ID", "对阵", "比赛时间", "比分", "状态", "赔率状态"]
    
    # 计算列宽
    col_widths = [max(len(str(row[i])) for row in [headers] + table_data) + 2 for i in range(len(headers))]
    
    # 打印表格
    def print_row(row, widths):
        return "│" + "│".join(f" {str(cell):<{width-1}}" for cell, width in zip(row, widths)) + "│"
    
    # 表格边框
    top_border = "┌" + "┬".join("─" * width for width in col_widths) + "┐"
    middle_border = "├" + "┼".join("─" * width for width in col_widths) + "┤"
    bottom_border = "└" + "┴".join("─" * width for width in col_widths) + "┘"
    
    print(top_border)
    print(print_row(headers, col_widths))
    print(middle_border)
    
    for row in table_data:
        print(print_row(row, col_widths))
    
    print(bottom_border)
    
    print(f"\n📊 表格说明:")
    print("- 联赛/轮次列：按日期抓取显示联赛名称，普通批量抓取显示轮次")
    print("- 比分列：按日期抓取为空（因为是未来比赛），普通批量抓取显示实际比分")
    print("- 赔率状态列：显示抓取的赔率条数或状态信息")

def test_error_scenarios():
    """测试错误场景"""
    
    print(f"\n🚨 测试错误场景处理")
    print("-" * 30)
    
    error_cases = [
        {
            "name": "缺少必要字段",
            "data": {
                'status': '成功'
            }
        },
        {
            "name": "空数据",
            "data": {}
        },
        {
            "name": "None值",
            "data": {
                'match_id': None,
                'league': None,
                'home_team': None,
                'away_team': None
            }
        }
    ]
    
    for i, case in enumerate(error_cases, 1):
        print(f"\n{i}. {case['name']}")
        data = case['data']
        
        try:
            # 模拟错误处理逻辑
            teams = f"{data.get('home_team', '')} vs {data.get('away_team', '')}"
            
            result = (
                data.get('league', ''),
                data.get('match_id', ''),
                teams if teams != " vs " else "",
                data.get('match_time', ''),
                '',
                data.get('status', ''),
                f"赔率: {data.get('odds_count', 0)} 条" if 'odds_count' in data else "待抓取"
            )
            
            print(f"   ✅ 错误处理成功: {result}")
            
        except Exception as e:
            print(f"   ❌ 错误处理失败: {e}")

if __name__ == "__main__":
    test_batch_result_compatibility()
    simulate_ui_table_display()
    test_error_scenarios()
    
    print(f"\n🎉 测试完成！")
    print("修复后的代码应该能够兼容不同的数据结构。")
    print("现在可以在主程序中测试按日期抓取功能，应该不会再出现 KeyError: 'round' 错误。")

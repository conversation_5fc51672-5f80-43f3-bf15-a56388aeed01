#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
优化的赔率组合分析器 - 高性能版本
支持批量查询、缓存和并发处理
"""

import logging
from typing import List, Dict, Optional
from concurrent.futures import ThreadPoolExecutor, as_completed
import time
from odds_combination_database import OddsCombinationDatabase
from odds_combination_analyzer import OddsCombinationAnalyzer

logger = logging.getLogger(__name__)

class OptimizedOddsCombinationAnalyzer(OddsCombinationAnalyzer):
    """优化的赔率组合分析器"""
    
    def __init__(self, db_path: str = None, max_workers: int = 4, batch_size: int = 100):
        """
        初始化优化分析器
        
        Args:
            db_path: 数据库路径
            max_workers: 最大并发线程数
            batch_size: 批量处理大小
        """
        super().__init__(db_path)
        self.max_workers = max_workers
        self.batch_size = batch_size
        logger.info(f"优化分析器初始化: 线程数={max_workers}, 批量大小={batch_size}")
    
    def analyze_odds_combinations_optimized(self, match_id: str, company_name: str,
                                          combination_size: int, only_historical: bool = True) -> Dict:
        """
        优化的赔率组合分析
        
        Args:
            match_id: 观察的比赛ID
            company_name: 博彩公司名称
            combination_size: 组合大小
            
        Returns:
            分析结果
        """
        start_time = time.time()
        logger.info(f"开始优化分析比赛 {match_id} 的赔率组合")
        
        # 检查比赛是否存在
        if not self.database.check_match_exists(match_id):
            return {
                'success': False,
                'error': f'比赛 {match_id} 不存在'
            }
        
        # 获取观察比赛的赔率数据
        target_odds = self.database.get_match_odds_by_company(match_id, company_name)
        
        if not target_odds:
            return {
                'success': False,
                'error': f'比赛 {match_id} 没有 {company_name} 的赔率数据'
            }
        
        if len(target_odds) < combination_size:
            return {
                'success': False,
                'error': f'赔率数据不足，需要至少 {combination_size} 条记录，实际只有 {len(target_odds)} 条'
            }
        
        # 生成目标组合
        target_combinations = self.generate_odds_combinations(target_odds, combination_size)
        logger.info(f"生成了 {len(target_combinations)} 个目标组合")
        
        # 获取所有其他比赛
        other_matches = self.database.get_all_other_matches(match_id, only_historical)
        if only_historical:
            logger.info(f"将在 {len(other_matches)} 场历史比赛中查找匹配")
        else:
            logger.info(f"将在 {len(other_matches)} 场其他比赛中查找匹配")
        
        # 使用优化的批量并发分析
        combination_results = self._analyze_combinations_batch_concurrent(
            target_combinations, other_matches, company_name
        )
        
        analysis_time = time.time() - start_time
        logger.info(f"优化分析完成，耗时 {analysis_time:.2f} 秒")
        
        return {
            'success': True,
            'target_match_id': match_id,
            'company_name': company_name,
            'combination_size': combination_size,
            'total_combinations': len(target_combinations),
            'total_other_matches': len(other_matches),
            'results': combination_results,
            'analysis_time': analysis_time
        }
    
    def _analyze_combinations_batch_concurrent(self, target_combinations: List[List[Dict]], 
                                             other_matches: List[str], 
                                             company_name: str) -> List[Dict]:
        """
        批量并发分析组合
        
        Args:
            target_combinations: 目标组合列表
            other_matches: 其他比赛ID列表
            company_name: 博彩公司名称
            
        Returns:
            分析结果列表
        """
        logger.info("开始批量并发分析...")
        
        # 预先批量获取所有比赛的赔率数据和结果
        logger.info("预加载比赛数据...")
        all_match_odds = self._preload_match_odds(other_matches, company_name)
        all_match_results = self._preload_match_results(other_matches)
        
        # 并发分析每个组合
        combination_results = []
        
        with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
            # 提交所有组合分析任务
            future_to_index = {}
            
            for i, target_combination in enumerate(target_combinations):
                future = executor.submit(
                    self._analyze_single_combination_optimized,
                    target_combination, 
                    other_matches,
                    all_match_odds,
                    all_match_results,
                    i + 1,
                    len(target_combinations)
                )
                future_to_index[future] = i
            
            # 收集结果
            results = [None] * len(target_combinations)
            
            for future in as_completed(future_to_index):
                index = future_to_index[future]
                try:
                    result = future.result()
                    results[index] = result
                except Exception as e:
                    logger.error(f"分析第 {index + 1} 个组合失败: {e}")
                    results[index] = {
                        'combination_index': index,
                        'target_combination': target_combinations[index],
                        'match_count': 0,
                        'matched_matches': [],
                        'result_stats': {'win': 0, 'draw': 0, 'loss': 0, 'unknown': 0},
                        'error': str(e)
                    }
        
        # 过滤掉None结果
        combination_results = [r for r in results if r is not None]
        
        logger.info(f"批量并发分析完成，处理了 {len(combination_results)} 个组合")
        return combination_results
    
    def _preload_match_odds(self, match_ids: List[str], company_name: str) -> Dict[str, List[Dict]]:
        """预加载所有比赛的赔率数据"""
        logger.info(f"预加载 {len(match_ids)} 场比赛的赔率数据...")
        
        # 分批加载以避免内存过度使用
        all_odds = {}
        
        for i in range(0, len(match_ids), self.batch_size):
            batch_matches = match_ids[i:i + self.batch_size]
            batch_odds = self.database.get_batch_match_odds(batch_matches, company_name)
            all_odds.update(batch_odds)
            
            if (i // self.batch_size + 1) % 10 == 0:  # 每10批显示一次进度
                logger.info(f"已加载 {min(i + self.batch_size, len(match_ids))}/{len(match_ids)} 场比赛的赔率数据")
        
        logger.info("赔率数据预加载完成")
        return all_odds
    
    def _preload_match_results(self, match_ids: List[str]) -> Dict[str, Optional[Dict]]:
        """预加载所有比赛的结果数据"""
        logger.info(f"预加载 {len(match_ids)} 场比赛的结果数据...")
        
        # 分批加载
        all_results = {}
        
        for i in range(0, len(match_ids), self.batch_size):
            batch_matches = match_ids[i:i + self.batch_size]
            batch_results = self.database.get_batch_match_results(batch_matches)
            all_results.update(batch_results)
            
            if (i // self.batch_size + 1) % 10 == 0:  # 每10批显示一次进度
                logger.info(f"已加载 {min(i + self.batch_size, len(match_ids))}/{len(match_ids)} 场比赛的结果数据")
        
        logger.info("比赛结果数据预加载完成")
        return all_results
    
    def _analyze_single_combination_optimized(self, target_combination: List[Dict],
                                            other_matches: List[str],
                                            all_match_odds: Dict[str, List[Dict]],
                                            all_match_results: Dict[str, Optional[Dict]],
                                            current_index: int,
                                            total_combinations: int) -> Dict:
        """
        优化的单个组合分析（使用预加载的数据）
        
        Args:
            target_combination: 目标组合
            other_matches: 其他比赛ID列表
            all_match_odds: 预加载的赔率数据
            all_match_results: 预加载的比赛结果
            current_index: 当前组合索引
            total_combinations: 总组合数
            
        Returns:
            分析结果
        """
        if current_index % 10 == 0:  # 每10个组合显示一次进度
            logger.info(f"分析第 {current_index}/{total_combinations} 个组合")
        
        match_count = 0
        matched_matches = []
        result_stats = {'win': 0, 'draw': 0, 'loss': 0, 'unknown': 0}
        
        for other_match_id in other_matches:
            # 使用预加载的赔率数据
            match_odds = all_match_odds.get(other_match_id, [])
            
            if len(match_odds) < len(target_combination):
                continue
            
            # 生成该比赛的所有可能组合
            candidate_combinations = self.generate_odds_combinations(match_odds, len(target_combination))
            
            # 检查是否有匹配
            match_positions = []
            for i, candidate in enumerate(candidate_combinations):
                if self.combination_match(target_combination, candidate):
                    match_positions.append(i)
            
            if match_positions:
                # 该比赛有匹配，计数+1
                match_count += 1
                
                # 使用预加载的比赛结果
                match_result = all_match_results.get(other_match_id)
                result_type = self.calculate_match_result_type(match_result) if match_result else 'unknown'
                result_stats[result_type] += 1
                
                # 记录匹配详情
                for position in match_positions:
                    matched_matches.append({
                        'match_id': other_match_id,
                        'result_type': result_type,
                        'match_position': position,
                        'match_result': match_result
                    })
        
        return {
            'combination_index': current_index - 1,  # 转为0基索引
            'target_combination': target_combination,
            'match_count': match_count,
            'matched_matches': matched_matches,
            'result_stats': result_stats
        }

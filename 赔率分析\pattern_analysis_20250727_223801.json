{"pattern_statistics": {"home": {"explosive": {"sample_size": 12, "accuracy": 0.08333333333333333, "confidence_interval": [0.014865094404917095, 0.35387991114111694], "result_distribution": {"draw": 0.9166666666666666, "home_win": 0.08333333333333333}, "result_counts": {"draw": 11, "home_win": 1}}, "rebound": {"sample_size": 12, "accuracy": 0.4166666666666667, "confidence_interval": [0.19326031365875665, 0.6804886874504503], "result_distribution": {"home_win": 0.4166666666666667, "draw": 0.3333333333333333, "away_win": 0.25}, "result_counts": {"home_win": 5, "draw": 4, "away_win": 3}}, "other": {"sample_size": 19, "accuracy": 0.7894736842105263, "confidence_interval": [0.5666572155618583, 0.9149232336708375], "result_distribution": {"home_win": 0.7894736842105263, "draw": 0.15789473684210525, "away_win": 0.05263157894736842}, "result_counts": {"home_win": 15, "draw": 3, "away_win": 1}}, "down_pressure": {"sample_size": 4, "accuracy": 0.75, "confidence_interval": [0.30064184258240184, 0.9544127391902995], "result_distribution": {"home_win": 0.75, "away_win": 0.25}, "result_counts": {"home_win": 3, "away_win": 1}}}, "draw": {"other": {"sample_size": 27, "accuracy": 0.4074074074074074, "confidence_interval": [0.24514757074395555, 0.592732990319353], "result_distribution": {"home_win": 0.5555555555555556, "draw": 0.4074074074074074, "away_win": 0.037037037037037035}, "result_counts": {"home_win": 15, "draw": 11, "away_win": 1}}, "down_pressure": {"sample_size": 3, "accuracy": 0.3333333333333333, "confidence_interval": [0.06149194472039615, 0.7923403991979523], "result_distribution": {"home_win": 0.6666666666666666, "draw": 0.3333333333333333}, "result_counts": {"home_win": 2, "draw": 1}}, "cooling": {"sample_size": 8, "accuracy": 0.125, "confidence_interval": [0.02241749145005667, 0.4708881822128535], "result_distribution": {"home_win": 0.625, "away_win": 0.25, "draw": 0.125}, "result_counts": {"home_win": 5, "away_win": 2, "draw": 1}}, "stable": {"sample_size": 3, "accuracy": 0.3333333333333333, "confidence_interval": [0.06149194472039615, 0.7923403991979523], "result_distribution": {"home_win": 0.6666666666666666, "draw": 0.3333333333333333}, "result_counts": {"home_win": 2, "draw": 1}}, "rebound": {"sample_size": 5, "accuracy": 0.4, "confidence_interval": [0.11762077423264788, 0.7692757187239869], "result_distribution": {"draw": 0.4, "away_win": 0.4, "home_win": 0.2}, "result_counts": {"draw": 2, "away_win": 2, "home_win": 1}}}, "away": {"other": {"sample_size": 21, "accuracy": 0.09523809523809523, "confidence_interval": [0.026518767438074548, 0.28914139073918255], "result_distribution": {"home_win": 0.6666666666666666, "draw": 0.23809523809523808, "away_win": 0.09523809523809523}, "result_counts": {"home_win": 14, "draw": 5, "away_win": 2}}, "cooling": {"sample_size": 10, "accuracy": 0.2, "confidence_interval": [0.05668215145437519, 0.5098375284633583], "result_distribution": {"draw": 0.8, "away_win": 0.2}, "result_counts": {"draw": 8, "away_win": 2}}, "down_pressure": {"sample_size": 12, "accuracy": 0.08333333333333333, "confidence_interval": [0.014865094404917095, 0.35387991114111694], "result_distribution": {"home_win": 0.75, "draw": 0.16666666666666666, "away_win": 0.08333333333333333}, "result_counts": {"home_win": 9, "draw": 2, "away_win": 1}}, "explosive": {"sample_size": 3, "accuracy": 0, "confidence_interval": [5.551115123125783e-17, 0.5614970317550454], "result_distribution": {"home_win": 0.6666666666666666, "draw": 0.3333333333333333}, "result_counts": {"home_win": 2, "draw": 1}}}, "combined": {"other": {"sample_size": 22, "result_distribution": {"home_win": 0.6818181818181818, "draw": 0.22727272727272727, "away_win": 0.09090909090909091}, "result_counts": {"home_win": 15, "draw": 5, "away_win": 2}, "entropy": 1.1770225546116342}, "mixed": {"sample_size": 18, "result_distribution": {"home_win": 0.5, "draw": 0.4444444444444444, "away_win": 0.05555555555555555}, "result_counts": {"home_win": 9, "draw": 8, "away_win": 1}, "entropy": 1.2516291673878228}, "down_pressure": {"sample_size": 3, "result_distribution": {"home_win": 0.6666666666666666, "draw": 0.3333333333333333}, "result_counts": {"home_win": 2, "draw": 1}, "entropy": 0.9182958340544896}, "rebound": {"sample_size": 3, "result_distribution": {"draw": 0.6666666666666666, "away_win": 0.3333333333333333}, "result_counts": {"draw": 2, "away_win": 1}, "entropy": 0.9182958340544896}}}, "significance_tests": {"home": {"chi2_statistic": 28.09472934472935, "p_value": 0.001743261421039656, "degrees_of_freedom": 10, "is_significant": true, "contingency_table": {"away_win": {"cooling": 0, "down_pressure": 1, "explosive": 0, "other": 1, "rebound": 3, "stable": 0}, "draw": {"cooling": 0, "down_pressure": 0, "explosive": 11, "other": 3, "rebound": 4, "stable": 0}, "home_win": {"cooling": 1, "down_pressure": 3, "explosive": 1, "other": 15, "rebound": 5, "stable": 1}}}, "draw": {"chi2_statistic": 12.658178220955998, "p_value": 0.3943673352621783, "degrees_of_freedom": 12, "is_significant": false, "contingency_table": {"away_win": {"cooling": 2, "down_pressure": 0, "explosive": 0, "oscillation": 0, "other": 1, "rebound": 2, "stable": 0}, "draw": {"cooling": 1, "down_pressure": 1, "explosive": 1, "oscillation": 1, "other": 11, "rebound": 2, "stable": 1}, "home_win": {"cooling": 5, "down_pressure": 2, "explosive": 0, "oscillation": 1, "other": 15, "rebound": 1, "stable": 2}}}, "away": {"chi2_statistic": 20.23388888888889, "p_value": 0.06278891861962321, "degrees_of_freedom": 12, "is_significant": false, "contingency_table": {"away_win": {"cooling": 2, "down_pressure": 1, "explosive": 0, "oscillation": 0, "other": 2, "rebound": 0, "stable": 0}, "draw": {"cooling": 8, "down_pressure": 2, "explosive": 1, "oscillation": 0, "other": 5, "rebound": 1, "stable": 1}, "home_win": {"cooling": 0, "down_pressure": 9, "explosive": 2, "oscillation": 1, "other": 14, "rebound": 0, "stable": 0}}}, "combined": {"chi2_statistic": 13.153811620478285, "p_value": 0.2151987621071078, "degrees_of_freedom": 10, "is_significant": false, "contingency_table": {"away_win": {"cooling": 1, "down_pressure": 0, "explosive": 0, "mixed": 1, "other": 2, "rebound": 1}, "draw": {"cooling": 1, "down_pressure": 1, "explosive": 1, "mixed": 8, "other": 5, "rebound": 2}, "home_win": {"cooling": 0, "down_pressure": 2, "explosive": 0, "mixed": 9, "other": 15, "rebound": 0}}}}, "predictive_power": {"home": {"explosive": {"accuracy": 0.08333333333333333, "baseline_accuracy": 0.5306122448979592, "improvement": -0.4472789115646259, "improvement_ratio": 0.15705128205128202, "information_gain": 0.9380415005040881, "sample_size": 12}, "rebound": {"accuracy": 0.4166666666666667, "baseline_accuracy": 0.5306122448979592, "improvement": -0.11394557823129253, "improvement_ratio": 0.7852564102564102, "information_gain": -0.20272681853007768, "sample_size": 12}, "other": {"accuracy": 0.7894736842105263, "baseline_accuracy": 0.5306122448979592, "improvement": 0.2588614393125671, "improvement_ratio": 1.4878542510121457, "information_gain": 0.43857538664262297, "sample_size": 19}, "down_pressure": {"accuracy": 0.75, "baseline_accuracy": 0.5306122448979592, "improvement": 0.21938775510204078, "improvement_ratio": 1.4134615384615383, "information_gain": 0.5405802263485889, "sample_size": 4}}, "draw": {"other": {"accuracy": 0.4074074074074074, "baseline_accuracy": 0.3673469387755102, "improvement": 0.04006046863189716, "improvement_ratio": 1.1090534979423867, "information_gain": 0.17686369083455133, "sample_size": 27}, "down_pressure": {"accuracy": 0.3333333333333333, "baseline_accuracy": 0.3673469387755102, "improvement": -0.03401360544217691, "improvement_ratio": 0.9074074074074073, "information_gain": 0.4335625167532322, "sample_size": 3}, "cooling": {"accuracy": 0.125, "baseline_accuracy": 0.3673469387755102, "improvement": -0.24234693877551022, "improvement_ratio": 0.34027777777777773, "information_gain": 0.053063410112323295, "sample_size": 8}, "stable": {"accuracy": 0.3333333333333333, "baseline_accuracy": 0.3673469387755102, "improvement": -0.03401360544217691, "improvement_ratio": 0.9074074074074073, "information_gain": 0.4335625167532322, "sample_size": 3}, "rebound": {"accuracy": 0.4, "baseline_accuracy": 0.3673469387755102, "improvement": 0.0326530612244898, "improvement_ratio": 1.0888888888888888, "information_gain": -0.17006974407964037, "sample_size": 5}}, "away": {"other": {"accuracy": 0.09523809523809523, "baseline_accuracy": 0.10204081632653061, "improvement": -0.006802721088435382, "improvement_ratio": 0.9333333333333332, "information_gain": 0.14585566056435506, "sample_size": 21}, "cooling": {"accuracy": 0.2, "baseline_accuracy": 0.10204081632653061, "improvement": 0.0979591836734694, "improvement_ratio": 1.96, "information_gain": 0.6299302559203594, "sample_size": 10}, "down_pressure": {"accuracy": 0.08333333333333333, "baseline_accuracy": 0.10204081632653061, "improvement": -0.018707482993197286, "improvement_ratio": 0.8166666666666667, "information_gain": 0.31100626783496654, "sample_size": 12}, "explosive": {"accuracy": 0, "baseline_accuracy": 0.10204081632653061, "improvement": -0.10204081632653061, "improvement_ratio": 0.0, "information_gain": 0.4335625167532322, "sample_size": 3}}}, "valuable_patterns": {"home": [{"pattern": "other", "accuracy": 0.7894736842105263, "improvement": 0.2588614393125671, "sample_size": 19, "information_gain": 0.43857538664262297}], "draw": [], "away": []}, "trading_signals": [{"match_id": 2596803, "company": "bwin", "actual_result": "draw", "signals": [{"odds_type": "home", "pattern": "other", "expected_outcome": "home_win", "confidence": 0.7894736842105263, "improvement": 0.2588614393125671}]}, {"match_id": 2596803, "company": "澳门", "actual_result": "draw", "signals": [{"odds_type": "home", "pattern": "other", "expected_outcome": "home_win", "confidence": 0.7894736842105263, "improvement": 0.2588614393125671}]}, {"match_id": 2596803, "company": "竞彩官方", "actual_result": "draw", "signals": [{"odds_type": "home", "pattern": "other", "expected_outcome": "home_win", "confidence": 0.7894736842105263, "improvement": 0.2588614393125671}]}, {"match_id": 2598971, "company": "bet365", "actual_result": "home_win", "signals": [{"odds_type": "home", "pattern": "other", "expected_outcome": "home_win", "confidence": 0.7894736842105263, "improvement": 0.2588614393125671}]}, {"match_id": 2598971, "company": "pinnacle", "actual_result": "home_win", "signals": [{"odds_type": "home", "pattern": "other", "expected_outcome": "home_win", "confidence": 0.7894736842105263, "improvement": 0.2588614393125671}]}, {"match_id": 2598971, "company": "金宝博", "actual_result": "home_win", "signals": [{"odds_type": "home", "pattern": "other", "expected_outcome": "home_win", "confidence": 0.7894736842105263, "improvement": 0.2588614393125671}]}, {"match_id": 2598971, "company": "威廉希尔", "actual_result": "home_win", "signals": [{"odds_type": "home", "pattern": "other", "expected_outcome": "home_win", "confidence": 0.7894736842105263, "improvement": 0.2588614393125671}]}, {"match_id": 2598971, "company": "立博", "actual_result": "home_win", "signals": [{"odds_type": "home", "pattern": "other", "expected_outcome": "home_win", "confidence": 0.7894736842105263, "improvement": 0.2588614393125671}]}, {"match_id": 2598971, "company": "betathome", "actual_result": "home_win", "signals": [{"odds_type": "home", "pattern": "other", "expected_outcome": "home_win", "confidence": 0.7894736842105263, "improvement": 0.2588614393125671}]}, {"match_id": 2598971, "company": "香港马会", "actual_result": "home_win", "signals": [{"odds_type": "home", "pattern": "other", "expected_outcome": "home_win", "confidence": 0.7894736842105263, "improvement": 0.2588614393125671}]}, {"match_id": 2741454, "company": "bet365", "actual_result": "home_win", "signals": [{"odds_type": "home", "pattern": "other", "expected_outcome": "home_win", "confidence": 0.7894736842105263, "improvement": 0.2588614393125671}]}, {"match_id": 2741454, "company": "威廉希尔", "actual_result": "home_win", "signals": [{"odds_type": "home", "pattern": "other", "expected_outcome": "home_win", "confidence": 0.7894736842105263, "improvement": 0.2588614393125671}]}, {"match_id": 2741454, "company": "立博", "actual_result": "home_win", "signals": [{"odds_type": "home", "pattern": "other", "expected_outcome": "home_win", "confidence": 0.7894736842105263, "improvement": 0.2588614393125671}]}, {"match_id": 2741454, "company": "伟德", "actual_result": "home_win", "signals": [{"odds_type": "home", "pattern": "other", "expected_outcome": "home_win", "confidence": 0.7894736842105263, "improvement": 0.2588614393125671}]}, {"match_id": 2511567, "company": "伟德", "actual_result": "away_win", "signals": [{"odds_type": "home", "pattern": "other", "expected_outcome": "home_win", "confidence": 0.7894736842105263, "improvement": 0.2588614393125671}]}, {"match_id": 2511566, "company": "bet365", "actual_result": "home_win", "signals": [{"odds_type": "home", "pattern": "other", "expected_outcome": "home_win", "confidence": 0.7894736842105263, "improvement": 0.2588614393125671}]}, {"match_id": 2511566, "company": "威廉希尔", "actual_result": "home_win", "signals": [{"odds_type": "home", "pattern": "other", "expected_outcome": "home_win", "confidence": 0.7894736842105263, "improvement": 0.2588614393125671}]}, {"match_id": 2511566, "company": "伟德", "actual_result": "home_win", "signals": [{"odds_type": "home", "pattern": "other", "expected_outcome": "home_win", "confidence": 0.7894736842105263, "improvement": 0.2588614393125671}]}, {"match_id": 2511566, "company": "易胜博", "actual_result": "home_win", "signals": [{"odds_type": "home", "pattern": "other", "expected_outcome": "home_win", "confidence": 0.7894736842105263, "improvement": 0.2588614393125671}]}], "signal_statistics": {"home_other": {"count": 19, "correct": 15}}}
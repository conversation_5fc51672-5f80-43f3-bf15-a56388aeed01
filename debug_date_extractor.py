#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试版日期比赛提取器
用于分析页面结构和调试解析问题
"""

import requests
import re
import logging
from bs4 import BeautifulSoup
from typing import Dict, List, Optional
from datetime import datetime

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class DebugDateMatchExtractor:
    def __init__(self):
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1'
        })

    def debug_extract_matches(self, date_url: str) -> Dict[str, List[Dict]]:
        """调试版比赛提取"""
        try:
            logger.info(f"🔍 开始调试提取: {date_url}")
            
            # 获取页面内容
            response = self.session.get(date_url, timeout=30)
            response.raise_for_status()
            response.encoding = 'utf-8'
            
            logger.info(f"✅ 页面获取成功，状态码: {response.status_code}")
            logger.info(f"📄 页面大小: {len(response.text)} 字符")
            
            # 解析HTML
            soup = BeautifulSoup(response.text, 'html.parser')
            
            # 调试页面结构
            self._debug_page_structure(soup)
            
            # 尝试不同的解析策略
            matches_by_league = self._try_different_parsing_strategies(soup, date_url)
            
            total_matches = sum(len(matches) for matches in matches_by_league.values())
            logger.info(f"🎯 最终结果: {len(matches_by_league)} 个联赛，{total_matches} 场比赛")
            
            return matches_by_league
            
        except Exception as e:
            logger.error(f"❌ 调试提取失败: {e}")
            return {}

    def _debug_page_structure(self, soup: BeautifulSoup):
        """调试页面结构"""
        logger.info("🔍 分析页面结构...")
        
        # 查找可能的比赛容器
        possible_containers = [
            soup.find_all('table'),
            soup.find_all('div', class_=re.compile(r'match|game|fixture')),
            soup.find_all('tr'),
            soup.find_all('div', class_=re.compile(r'list|item')),
            soup.find_all('ul'),
            soup.find_all('li')
        ]
        
        for i, containers in enumerate(possible_containers):
            container_types = ['table', 'div.match*', 'tr', 'div.list*', 'ul', 'li']
            logger.info(f"📊 {container_types[i]}: {len(containers)} 个")
        
        # 查找包含数字的元素（可能是比赛ID）
        elements_with_numbers = soup.find_all(text=re.compile(r'\d{6,}'))
        logger.info(f"🔢 包含6位以上数字的文本: {len(elements_with_numbers)} 个")
        if elements_with_numbers:
            logger.info(f"📝 示例: {elements_with_numbers[:3]}")
        
        # 查找链接
        links = soup.find_all('a', href=True)
        match_links = [link for link in links if re.search(r'\d{6,}', link.get('href', ''))]
        logger.info(f"🔗 总链接数: {len(links)}")
        logger.info(f"🎯 包含数字的链接: {len(match_links)}")
        if match_links:
            logger.info(f"📝 示例链接: {match_links[0].get('href')}")

    def _try_different_parsing_strategies(self, soup: BeautifulSoup, date_url: str) -> Dict[str, List[Dict]]:
        """尝试不同的解析策略"""
        strategies = [
            self._strategy_table_rows,
            self._strategy_div_containers,
            self._strategy_link_extraction,
            self._strategy_script_data
        ]
        
        for i, strategy in enumerate(strategies, 1):
            logger.info(f"🧪 尝试策略 {i}: {strategy.__name__}")
            try:
                result = strategy(soup, date_url)
                if result:
                    total_matches = sum(len(matches) for matches in result.values())
                    logger.info(f"✅ 策略 {i} 成功: {len(result)} 个联赛，{total_matches} 场比赛")
                    return result
                else:
                    logger.info(f"❌ 策略 {i} 未找到比赛")
            except Exception as e:
                logger.error(f"❌ 策略 {i} 失败: {e}")
        
        return {}

    def _strategy_table_rows(self, soup: BeautifulSoup, date_url: str) -> Dict[str, List[Dict]]:
        """策略1: 表格行解析"""
        matches_by_league = {}
        
        # 查找所有表格
        tables = soup.find_all('table')
        logger.info(f"📊 找到 {len(tables)} 个表格")
        
        for table in tables:
            rows = table.find_all('tr')
            logger.info(f"📋 表格包含 {len(rows)} 行")
            
            current_league = "未知联赛"
            
            for row in rows:
                # 检查是否是联赛标题
                if self._is_league_header(row):
                    league_name = self._extract_league_from_row(row)
                    if league_name:
                        current_league = league_name
                        logger.info(f"🏆 发现联赛: {current_league}")
                        continue
                
                # 提取比赛信息
                match_info = self._extract_match_from_row(row, current_league, date_url)
                if match_info:
                    league = match_info['league']
                    if league not in matches_by_league:
                        matches_by_league[league] = []
                    matches_by_league[league].append(match_info)
                    logger.info(f"⚽ 找到比赛: {match_info['home_team']} vs {match_info['away_team']}")
        
        return matches_by_league

    def _strategy_div_containers(self, soup: BeautifulSoup, date_url: str) -> Dict[str, List[Dict]]:
        """策略2: DIV容器解析"""
        matches_by_league = {}
        
        # 查找可能的比赛容器
        containers = soup.find_all('div', class_=re.compile(r'match|game|fixture|item'))
        logger.info(f"📦 找到 {len(containers)} 个可能的比赛容器")
        
        for container in containers:
            match_info = self._extract_match_from_div(container, date_url)
            if match_info:
                league = match_info['league']
                if league not in matches_by_league:
                    matches_by_league[league] = []
                matches_by_league[league].append(match_info)
                logger.info(f"⚽ 找到比赛: {match_info['home_team']} vs {match_info['away_team']}")
        
        return matches_by_league

    def _strategy_link_extraction(self, soup: BeautifulSoup, date_url: str) -> Dict[str, List[Dict]]:
        """策略3: 链接提取"""
        matches_by_league = {}
        
        # 查找包含比赛ID的链接
        links = soup.find_all('a', href=True)
        match_links = []
        
        for link in links:
            href = link.get('href', '')
            # 查找包含比赛ID的链接
            match_id_patterns = [
                r'/(\d{7,})',
                r'id=(\d{7,})',
                r'match[_-]?(\d{7,})',
                r'fixture[/_](\d{7,})'
            ]
            
            for pattern in match_id_patterns:
                match = re.search(pattern, href)
                if match:
                    match_id = match.group(1)
                    match_text = link.get_text(strip=True)
                    match_links.append({
                        'match_id': match_id,
                        'text': match_text,
                        'href': href,
                        'link': link
                    })
                    break
        
        logger.info(f"🔗 找到 {len(match_links)} 个比赛链接")
        
        # 从链接中提取比赛信息
        for link_info in match_links:
            match_info = self._extract_match_from_link(link_info, date_url)
            if match_info:
                league = match_info['league']
                if league not in matches_by_league:
                    matches_by_league[league] = []
                matches_by_league[league].append(match_info)
                logger.info(f"⚽ 找到比赛: {match_info['home_team']} vs {match_info['away_team']}")
        
        return matches_by_league

    def _strategy_script_data(self, soup: BeautifulSoup, date_url: str) -> Dict[str, List[Dict]]:
        """策略4: JavaScript数据提取"""
        matches_by_league = {}
        
        # 查找script标签中的数据
        scripts = soup.find_all('script')
        logger.info(f"📜 找到 {len(scripts)} 个script标签")
        
        for script in scripts:
            script_text = script.get_text()
            if not script_text:
                continue
            
            # 查找可能的比赛数据
            match_patterns = [
                r'match.*?(\d{7,})',
                r'fixture.*?(\d{7,})',
                r'game.*?(\d{7,})'
            ]
            
            for pattern in match_patterns:
                matches = re.findall(pattern, script_text, re.IGNORECASE)
                if matches:
                    logger.info(f"📊 在script中找到 {len(matches)} 个可能的比赛ID")
                    # 这里可以进一步解析JavaScript数据
                    break
        
        return matches_by_league

    def _is_league_header(self, row) -> bool:
        """判断是否是联赛标题行"""
        # 检查是否跨列
        colspan_cells = row.find_all(['td', 'th'], attrs={'colspan': True})
        if colspan_cells:
            return True
        
        # 检查文本内容
        row_text = row.get_text(strip=True)
        if row_text and len(row_text.split()) <= 3 and not any(char.isdigit() for char in row_text):
            return True
        
        return False

    def _extract_league_from_row(self, row) -> Optional[str]:
        """从行中提取联赛名称"""
        row_text = row.get_text(strip=True)
        if row_text and len(row_text) > 1:
            return row_text
        return None

    def _extract_match_from_row(self, row, current_league: str, date_url: str) -> Optional[Dict]:
        """从表格行中提取比赛信息"""
        try:
            # 查找比赛ID链接
            links = row.find_all('a', href=True)
            match_id = None
            
            for link in links:
                href = link.get('href', '')
                match = re.search(r'(\d{7,})', href)
                if match:
                    match_id = match.group(1)
                    break
            
            if not match_id:
                return None
            
            # 提取队伍名称
            cells = row.find_all(['td', 'th'])
            if len(cells) < 3:
                return None
            
            # 尝试从不同位置提取队伍名称
            teams_text = ""
            for cell in cells:
                cell_text = cell.get_text(strip=True)
                if 'vs' in cell_text.lower() or ' - ' in cell_text:
                    teams_text = cell_text
                    break
            
            if not teams_text:
                # 尝试组合相邻单元格
                for i in range(len(cells) - 1):
                    combined = f"{cells[i].get_text(strip=True)} vs {cells[i+1].get_text(strip=True)}"
                    if len(combined) > 5:  # 简单验证
                        teams_text = combined
                        break
            
            if not teams_text:
                return None
            
            # 解析队伍名称
            teams = self._parse_teams_from_text(teams_text)
            if not teams:
                return None
            
            home_team, away_team = teams
            
            # 提取时间
            match_time = self._extract_time_from_row(row, date_url)
            
            return {
                'match_id': match_id,
                'home_team': home_team,
                'away_team': away_team,
                'match_time': match_time,
                'league': current_league
            }
            
        except Exception as e:
            logger.debug(f"从行提取比赛信息失败: {e}")
            return None

    def _extract_match_from_div(self, div, date_url: str) -> Optional[Dict]:
        """从DIV容器中提取比赛信息"""
        # 实现DIV解析逻辑
        return None

    def _extract_match_from_link(self, link_info: Dict, date_url: str) -> Optional[Dict]:
        """从链接信息中提取比赛信息"""
        try:
            match_id = link_info['match_id']
            text = link_info['text']
            
            # 尝试从链接文本中解析队伍名称
            teams = self._parse_teams_from_text(text)
            if not teams:
                return None
            
            home_team, away_team = teams
            
            # 从URL提取日期作为比赛时间
            date_match = re.search(r'date=(\d{4}-\d{2}-\d{2})', date_url)
            if date_match:
                date_str = date_match.group(1)
                match_time = f"{date_str} 00:00:00"
            else:
                match_time = f"{datetime.now().strftime('%Y-%m-%d')} 00:00:00"
            
            return {
                'match_id': match_id,
                'home_team': home_team,
                'away_team': away_team,
                'match_time': match_time,
                'league': '未知联赛'
            }
            
        except Exception as e:
            logger.debug(f"从链接提取比赛信息失败: {e}")
            return None

    def _parse_teams_from_text(self, text: str) -> Optional[tuple]:
        """从文本中解析队伍名称"""
        # 尝试不同的分隔符
        separators = [' vs ', ' VS ', ' - ', ' : ', '对']
        
        for sep in separators:
            if sep in text:
                parts = text.split(sep, 1)
                if len(parts) == 2:
                    home_team = parts[0].strip()
                    away_team = parts[1].strip()
                    if home_team and away_team:
                        return (home_team, away_team)
        
        return None

    def _extract_time_from_row(self, row, date_url: str) -> str:
        """从行中提取时间"""
        # 从URL中提取日期
        date_match = re.search(r'date=(\d{4}-\d{2}-\d{2})', date_url)
        if date_match:
            date_str = date_match.group(1)
        else:
            date_str = datetime.now().strftime('%Y-%m-%d')
        
        # 查找时间信息
        cells = row.find_all(['td', 'th'])
        for cell in cells:
            cell_text = cell.get_text(strip=True)
            time_match = re.search(r'(\d{1,2}):(\d{2})', cell_text)
            if time_match:
                hour = time_match.group(1).zfill(2)
                minute = time_match.group(2)
                return f"{date_str} {hour}:{minute}:00"
        
        return f"{date_str} 00:00:00"


def test_debug_extractor():
    """测试调试提取器"""
    extractor = DebugDateMatchExtractor()
    
    # 测试URL
    test_url = "https://m.titan007.com/Schedule.htm?date=2025-07-22"
    
    print("🔍 开始调试测试...")
    result = extractor.debug_extract_matches(test_url)
    
    print(f"\n📊 调试结果:")
    if result:
        for league, matches in result.items():
            print(f"🏆 {league}: {len(matches)} 场比赛")
            for match in matches[:3]:  # 显示前3场
                print(f"  ⚽ {match['match_id']}: {match['home_team']} vs {match['away_team']}")
    else:
        print("❌ 未找到任何比赛")


if __name__ == "__main__":
    test_debug_extractor()

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
赔率组合观察 - 演示分析脚本
展示如何使用分析功能（命令行版本）
"""

import sys
import os

# 添加父目录到Python路径
parent_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, parent_dir)

from odds_combination_database import OddsCombinationDatabase
from odds_combination_analyzer import OddsCombinationAnalyzer

def demo_analysis():
    """演示分析过程"""
    print("=" * 60)
    print("赔率组合观察分析工具 - 演示")
    print("=" * 60)
    
    # 参数设置
    match_id = "2701757"
    company_name = "bet365"
    combination_size = 3
    
    print(f"观察比赛ID: {match_id}")
    print(f"博彩公司: {company_name}")
    print(f"组合大小: {combination_size}")
    print("-" * 60)
    
    try:
        # 初始化分析器
        print("正在初始化分析器...")
        analyzer = OddsCombinationAnalyzer()
        
        # 获取比赛信息
        print("正在获取比赛信息...")
        match_info = analyzer.database.get_match_info(match_id)
        if match_info:
            print(f"比赛: {match_info['home_team']} vs {match_info['away_team']}")
            print(f"联赛: {match_info.get('league', '未知')}")
        
        # 获取赔率数据
        print("正在获取赔率数据...")
        odds_data = analyzer.database.get_match_odds_by_company(match_id, company_name)
        print(f"获取到 {len(odds_data)} 条赔率数据")
        
        if len(odds_data) < combination_size:
            print(f"❌ 赔率数据不足，需要至少 {combination_size} 条")
            return
        
        # 显示赔率数据
        print("\n赔率数据预览:")
        print("序号  日期     时间    主胜   平局   客胜")
        print("-" * 50)
        for i, odds in enumerate(odds_data[:10]):  # 只显示前10条
            print(f"{i+1:2d}   {odds['date']} {odds['time']} {odds['home_odds']:5.2f} {odds['draw_odds']:5.2f} {odds['away_odds']:5.2f}")
        if len(odds_data) > 10:
            print(f"... 还有 {len(odds_data) - 10} 条数据")
        
        # 生成组合
        print(f"\n正在生成 {combination_size} 组赔率组合...")
        combinations = analyzer.generate_odds_combinations(odds_data, combination_size)
        print(f"生成了 {len(combinations)} 个组合")
        
        # 显示前3个组合
        print("\n前3个组合预览:")
        for i, combo in enumerate(combinations[:3]):
            print(f"\n组合 {i+1}:")
            for j, odds in enumerate(combo):
                print(f"  {j+1}: {odds['home_odds']} {odds['draw_odds']} {odds['away_odds']}")
        
        # 获取其他比赛
        print("\n正在获取其他比赛...")
        other_matches = analyzer.database.get_all_other_matches(match_id)
        print(f"将在 {len(other_matches)} 场其他比赛中查找匹配")
        
        # 分析前3个组合（演示用）
        print("\n开始分析前3个组合...")
        print("=" * 60)
        
        for i, target_combination in enumerate(combinations[:3]):
            print(f"\n分析组合 {i+1}:")
            print("组合内容:")
            for j, odds in enumerate(target_combination):
                print(f"  {j+1}: 主{odds['home_odds']} 平{odds['draw_odds']} 客{odds['away_odds']}")
            
            match_count = 0
            result_stats = {'win': 0, 'draw': 0, 'loss': 0, 'unknown': 0}
            matched_details = []
            
            # 在前10场其他比赛中查找匹配（演示用，减少时间）
            for other_match_id in other_matches[:10]:
                match_positions = analyzer.find_matching_combinations_in_match(
                    target_combination, other_match_id, company_name
                )
                
                if match_positions:
                    match_count += 1
                    
                    # 获取比赛结果
                    match_result = analyzer.database.get_match_result(other_match_id)
                    result_type = analyzer.calculate_match_result_type(match_result) if match_result else 'unknown'
                    result_stats[result_type] += 1
                    
                    matched_details.append({
                        'match_id': other_match_id,
                        'result_type': result_type,
                        'positions': match_positions,
                        'match_result': match_result
                    })
            
            print(f"匹配结果: {match_count} 场比赛")
            print(f"结果分布: 主胜{result_stats['win']} 平局{result_stats['draw']} 客胜{result_stats['loss']} 未知{result_stats['unknown']}")
            
            if matched_details:
                print("匹配详情:")
                for detail in matched_details[:3]:  # 只显示前3个
                    result_text = {'win': '主胜', 'draw': '平局', 'loss': '客胜', 'unknown': '未知'}[detail['result_type']]
                    print(f"  比赛{detail['match_id']}: {result_text} (位置: {detail['positions']})")
        
        print("\n" + "=" * 60)
        print("演示完成！")
        print("注意：这只是前3个组合在前10场比赛中的匹配结果")
        print("完整分析请使用UI界面或调用完整的analyze_odds_combinations方法")
        
    except Exception as e:
        print(f"❌ 演示过程中发生错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    demo_analysis()

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
赔率价值分析系统启动脚本
一键运行完整的数据分析流程
"""

import os
import sys
import subprocess
import time
from datetime import datetime

class AnalysisRunner:
    def __init__(self):
        self.start_time = datetime.now()
        self.steps_completed = 0
        self.total_steps = 10  # 增加第三阶段的4个步骤
        
    def print_header(self):
        """打印项目头部信息"""
        print("=" * 80)
        print("🏈 赔率价值分析系统 - 自动化分析流程")
        print("=" * 80)
        print(f"开始时间: {self.start_time.strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"工作目录: {os.getcwd()}")
        print()
        
    def print_step(self, step_name, description):
        """打印步骤信息"""
        self.steps_completed += 1
        print(f"📊 步骤 {self.steps_completed}/{self.total_steps}: {step_name}")
        print(f"   {description}")
        print("-" * 60)
        
    def run_script(self, script_name, description):
        """运行Python脚本"""
        self.print_step(script_name, description)
        
        if not os.path.exists(script_name):
            print(f"❌ 错误: 脚本文件 {script_name} 不存在")
            return False
        
        try:
            print(f"🚀 正在运行: python {script_name}")
            start_time = time.time()
            
            result = subprocess.run([sys.executable, script_name], 
                                  capture_output=True, text=True, encoding='utf-8')
            
            end_time = time.time()
            duration = end_time - start_time
            
            if result.returncode == 0:
                print(f"✅ 成功完成 ({duration:.1f}秒)")
                if result.stdout:
                    print("📋 输出:")
                    print(result.stdout)
            else:
                print(f"❌ 执行失败 (返回码: {result.returncode})")
                if result.stderr:
                    print("🚨 错误信息:")
                    print(result.stderr)
                return False
                
        except Exception as e:
            print(f"❌ 运行脚本时出错: {e}")
            return False
        
        print()
        return True
    
    def check_dependencies(self):
        """检查依赖项"""
        print("🔍 检查依赖项...")
        
        required_modules = ['pandas', 'numpy', 'sqlite3', 'json', 'datetime']
        missing_modules = []
        
        for module in required_modules:
            try:
                __import__(module)
                print(f"  ✅ {module}")
            except ImportError:
                missing_modules.append(module)
                print(f"  ❌ {module}")
        
        if missing_modules:
            print(f"\n⚠️  缺少依赖项: {', '.join(missing_modules)}")
            print("请安装缺少的模块后重试")
            return False
        
        print("✅ 所有依赖项检查通过\n")
        return True
    
    def check_database_files(self):
        """检查数据库文件"""
        print("🗄️  检查数据库文件...")
        
        main_db = "../odds_data.db"
        league_db_dir = "../league_databases"
        
        if os.path.exists(main_db):
            size_mb = os.path.getsize(main_db) / (1024 * 1024)
            print(f"  ✅ 主数据库: {main_db} ({size_mb:.1f} MB)")
        else:
            print(f"  ❌ 主数据库不存在: {main_db}")
            return False
        
        if os.path.exists(league_db_dir):
            league_files = [f for f in os.listdir(league_db_dir) if f.endswith('.db')]
            print(f"  ✅ 联赛数据库目录: {league_db_dir} ({len(league_files)}个文件)")
            for file in league_files:
                print(f"     - {file}")
        else:
            print(f"  ⚠️  联赛数据库目录不存在: {league_db_dir}")
        
        print("✅ 数据库文件检查完成\n")
        return True
    
    def run_full_analysis(self):
        """运行完整分析流程"""
        self.print_header()
        
        # 检查环境
        if not self.check_dependencies():
            return False
        
        if not self.check_database_files():
            return False
        
        # 步骤1: 数据库结构分析
        if not self.run_script("database_analyzer.py", 
                              "分析数据库结构，识别字段特征和数据质量"):
            return False
        
        # 步骤2: 赔率时间序列提取
        if not self.run_script("odds_timeseries_extractor.py", 
                              "提取比赛赔率时间序列，按公司和时间维度组织"):
            return False
        
        # 步骤3: 时间轴标准化
        if not self.run_script("time_axis_normalizer.py",
                              "标准化时间轴，插值到统一时间网格"):
            return False

        # 步骤4: 特征工程
        if not self.run_script("odds_feature_engineering.py",
                              "从标准化时间序列中提取141个行为特征"):
            return False

        # 步骤5: 模式标签设计
        if not self.run_script("pattern_labeling.py",
                              "设计6种赔率行为模式并自动标记"):
            return False

        # 步骤6: 模式关联分析
        if not self.run_script("pattern_outcome_analysis.py",
                              "分析模式与比赛结果的关联性，生成交易信号"):
            return False

        # 步骤7: 交叉公司同步分析
        if not self.run_script("cross_company_sync_analysis.py",
                              "检测多家公司同步变盘行为，分析市场联动性"):
            return False

        # 步骤8: 领涨公司识别
        if not self.run_script("leading_company_identifier.py",
                              "识别市场领导者和风向标公司"):
            return False

        # 步骤9: 公司一致性评分
        if not self.run_script("company_consistency_scorer.py",
                              "构建多维度一致性评分体系"):
            return False

        # 步骤10: 反常行为检测
        if not self.run_script("anomaly_behavior_detector.py",
                              "检测对着干公司和异常变盘行为"):
            return False

        # 完成总结
        self.print_completion_summary()
        return True
    
    def print_completion_summary(self):
        """打印完成总结"""
        end_time = datetime.now()
        duration = end_time - self.start_time
        
        print("=" * 80)
        print("🎉 分析流程完成!")
        print("=" * 80)
        print(f"开始时间: {self.start_time.strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"结束时间: {end_time.strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"总耗时: {duration}")
        print(f"完成步骤: {self.steps_completed}/{self.total_steps}")
        print()
        
        # 列出生成的文件
        print("📁 生成的文件:")
        output_files = []

        for file in os.listdir('.'):
            if (file.endswith(('.json', '.csv', '.png')) and
                any(prefix in file for prefix in ['database_analysis', 'odds_timeseries', 'normalized_odds',
                                                 'odds_features', 'labeled_patterns', 'pattern_analysis', 'pattern_distribution',
                                                 'sync_analysis', 'leading_analysis', 'consistency_analysis', 'anomaly_analysis'])):
                output_files.append(file)
        
        if output_files:
            for file in sorted(output_files):
                size_kb = os.path.getsize(file) / 1024
                print(f"  📄 {file} ({size_kb:.1f} KB)")
        else:
            print("  ⚠️  没有找到输出文件")
        
        print()
        print("📖 下一步建议:")
        print("  1. 查看 database_analysis_results.json 了解数据概况")
        print("  2. 查看最新的 anomaly_analysis_*.json 文件了解异常分析结果")
        print("  3. 查看 leading_analysis_*.json 文件了解市场领导者分析")
        print("  4. 阅读 第三阶段完成总结.md 了解多公司联动分析结果")
        print("  5. 开始第四阶段的机器学习模型构建")
        print()
    
    def run_quick_demo(self, max_matches=2):
        """运行快速演示（处理少量数据）"""
        print("🚀 快速演示模式 - 处理少量数据进行测试")
        print(f"   最多处理 {max_matches} 场比赛")
        print()
        
        # 修改提取器参数
        demo_extractor_code = f"""
# 临时修改：只处理{max_matches}场比赛
if __name__ == "__main__":
    extractor = OddsTimeseriesExtractor()
    results = extractor.run_extraction(top_matches_per_db={max_matches})
    
    print(f"\\n=== 快速演示完成 ===")
    total_matches = sum(len(db_results) for db_results in results.values())
    print(f"总共提取了 {{total_matches}} 场比赛的时间序列数据")
"""
        
        # 备份原始文件
        if os.path.exists("odds_timeseries_extractor.py"):
            with open("odds_timeseries_extractor.py", 'r', encoding='utf-8') as f:
                original_content = f.read()
            
            # 临时修改main函数
            modified_content = original_content.replace(
                'results = extractor.run_extraction(top_matches_per_db=3)',
                f'results = extractor.run_extraction(top_matches_per_db={max_matches})'
            )
            
            with open("odds_timeseries_extractor.py", 'w', encoding='utf-8') as f:
                f.write(modified_content)
            
            # 运行分析
            success = self.run_full_analysis()
            
            # 恢复原始文件
            with open("odds_timeseries_extractor.py", 'w', encoding='utf-8') as f:
                f.write(original_content)
            
            return success
        
        return self.run_full_analysis()

def main():
    """主函数"""
    runner = AnalysisRunner()
    
    # 检查命令行参数
    if len(sys.argv) > 1:
        if sys.argv[1] == "--demo":
            success = runner.run_quick_demo(max_matches=1)
        elif sys.argv[1] == "--help":
            print("赔率价值分析系统启动脚本")
            print()
            print("用法:")
            print("  python run_analysis.py          # 运行完整分析")
            print("  python run_analysis.py --demo   # 运行快速演示")
            print("  python run_analysis.py --help   # 显示帮助")
            return
        else:
            print(f"未知参数: {sys.argv[1]}")
            print("使用 --help 查看帮助")
            return
    else:
        success = runner.run_full_analysis()
    
    if success:
        print("🎯 分析完成！系统已准备好进行下一阶段开发。")
    else:
        print("💥 分析过程中出现错误，请检查日志信息。")

if __name__ == "__main__":
    main()

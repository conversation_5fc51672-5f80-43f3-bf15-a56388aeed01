#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试所有日期处理功能的修复
验证各个模块是否都能正确处理时间信息
"""

import sys
import os
from datetime import datetime

def test_date_match_extractor():
    """测试按日期抓取功能"""
    
    print("🔧 测试按日期抓取功能")
    print("=" * 50)
    
    try:
        from date_match_extractor import DateMatchExtractor
        
        extractor = DateMatchExtractor()
        print("✅ DateMatchExtractor 创建成功")
        
        # 测试准确时间提取方法
        test_match_id = "2709881"
        print(f"\n📋 测试准确时间提取方法:")
        print(f"比赛ID: {test_match_id}")
        
        accurate_time = extractor._extract_accurate_match_time(test_match_id)
        
        if accurate_time and accurate_time.get('success'):
            print(f"✅ 准确时间提取成功")
            print(f"  完整时间: {accurate_time['match_time']}")
            print(f"  来源: {accurate_time['source']}")
        else:
            print(f"❌ 准确时间提取失败: {accurate_time.get('error', '未知错误')}")
        
        # 测试完整的日期页面抓取（使用一个简单的测试）
        print(f"\n📋 测试日期页面抓取功能:")
        print("注意：这会进行实际的网络请求")
        
        # 这里只测试方法是否存在，不进行实际抓取以避免网络问题
        if hasattr(extractor, 'extract_matches_from_date_page'):
            print("✅ extract_matches_from_date_page 方法存在")
        else:
            print("❌ extract_matches_from_date_page 方法不存在")
            
    except Exception as e:
        print(f"❌ DateMatchExtractor 测试失败: {e}")

def test_match_time_scraper():
    """测试比赛时间抓取器"""
    
    print(f"\n🔧 测试比赛时间抓取器")
    print("=" * 50)
    
    try:
        from match_time_scraper import MatchTimeScraper
        
        scraper = MatchTimeScraper()
        print("✅ MatchTimeScraper 创建成功")
        
        # 测试准确时间提取方法
        test_match_id = "2709881"
        print(f"\n📋 测试准确时间提取方法:")
        print(f"比赛ID: {test_match_id}")
        
        accurate_time = scraper._extract_accurate_match_time(test_match_id)
        
        if accurate_time and accurate_time.get('success'):
            print(f"✅ 准确时间提取成功")
            print(f"  完整时间: {accurate_time['match_time']}")
            print(f"  来源: {accurate_time['source']}")
        else:
            print(f"❌ 准确时间提取失败: {accurate_time.get('error', '未知错误')}")
        
        # 测试完整的时间抓取功能
        print(f"\n📋 测试完整时间抓取功能:")
        
        time_info = scraper.scrape_match_time(test_match_id)
        
        if time_info and not time_info.get('error'):
            print(f"✅ 时间抓取成功")
            print(f"  完整时间: {time_info.get('full_datetime', 'N/A')}")
            print(f"  来源: {time_info.get('source', 'N/A')}")
            
            # 检查是否使用了准确时间提取
            if time_info.get('source') == 'analysis_page':
                print(f"  ✅ 使用了准确时间提取")
            else:
                print(f"  ⚠️ 使用了备用方法: {time_info.get('source')}")
        else:
            print(f"❌ 时间抓取失败: {time_info.get('error', '未知错误')}")
            
    except Exception as e:
        print(f"❌ MatchTimeScraper 测试失败: {e}")

def test_enhanced_odds_scraper():
    """测试增强赔率抓取器（已修复）"""
    
    print(f"\n🔧 测试增强赔率抓取器")
    print("=" * 50)
    
    try:
        from enhanced_odds_scraper import EnhancedOddsScraper
        
        scraper = EnhancedOddsScraper()
        print("✅ EnhancedOddsScraper 创建成功")
        
        # 测试问题比赛ID
        test_match_id = "2709881"
        print(f"\n📋 测试比赛信息提取:")
        print(f"比赛ID: {test_match_id}")
        
        match_info = scraper.extract_match_info(test_match_id)
        
        if match_info:
            print(f"✅ 比赛信息提取成功")
            print(f"  比赛时间: {match_info.get('match_time', 'N/A')}")
            print(f"  时间来源: {match_info.get('time_source', 'N/A')}")
            
            # 验证年份是否正确
            match_time = match_info.get('match_time', '')
            if match_time.startswith('2025'):
                print(f"  ✅ 年份正确：2025年")
            elif match_time.startswith('2023'):
                print(f"  ❌ 年份错误：仍然是2023年")
            else:
                year = match_time[:4] if match_time else 'Unknown'
                print(f"  ⚠️ 年份: {year}")
        else:
            print(f"❌ 比赛信息提取失败")
            
    except Exception as e:
        print(f"❌ EnhancedOddsScraper 测试失败: {e}")

def test_match_info_extractor():
    """测试比赛信息提取器（已修复）"""
    
    print(f"\n🔧 测试比赛信息提取器")
    print("=" * 50)
    
    try:
        from match_info_extractor import MatchInfoExtractor
        
        extractor = MatchInfoExtractor()
        print("✅ MatchInfoExtractor 创建成功")
        
        # 测试问题比赛ID
        test_match_id = "2709881"
        print(f"\n📋 测试比赛信息提取:")
        print(f"比赛ID: {test_match_id}")
        
        match_info = extractor.extract_match_info(test_match_id)
        
        if match_info:
            print(f"✅ 比赛信息提取成功")
            print(f"  比赛时间: {match_info.get('match_time', 'N/A')}")
            print(f"  时间来源: {match_info.get('time_source', 'N/A')}")
            
            # 验证年份是否正确
            match_time = match_info.get('match_time', '')
            if match_time.startswith('2025'):
                print(f"  ✅ 年份正确：2025年")
            elif match_time.startswith('2023'):
                print(f"  ❌ 年份错误：仍然是2023年")
            else:
                year = match_time[:4] if match_time else 'Unknown'
                print(f"  ⚠️ 年份: {year}")
        else:
            print(f"❌ 比赛信息提取失败")
            
    except Exception as e:
        print(f"❌ MatchInfoExtractor 测试失败: {e}")

def test_summary():
    """测试总结"""
    
    print(f"\n" + "=" * 60)
    print("📋 修复总结")
    print("=" * 60)
    
    print("✅ 已修复的模块:")
    print("1. enhanced_odds_scraper.py - 增强赔率抓取器")
    print("2. match_info_extractor.py - 比赛信息提取器")
    print("3. date_match_extractor.py - 按日期抓取功能")
    print("4. match_time_scraper.py - 比赛时间抓取器")
    
    print(f"\n🎯 修复内容:")
    print("1. 所有模块都支持从分析页面获取准确时间")
    print("2. 优先使用 var headMatchTime 获取准确年份")
    print("3. 如果准确时间提取失败，自动回退到原有方法")
    print("4. 统一了时间处理逻辑，减少年份推测依赖")
    
    print(f"\n⚠️ 仍需关注的模块:")
    print("1. league_match_extractor.py - 联赛批量抓取")
    print("2. debug_date_extractor.py - 调试工具")
    print("3. season_utils.py - 赛季工具（基于ID推测）")
    
    print(f"\n🚀 建议:")
    print("1. 测试完赛后更新功能，验证年份问题是否彻底解决")
    print("2. 测试按日期抓取功能，确保使用准确时间")
    print("3. 考虑为联赛批量抓取也添加准确时间提取")

def main():
    """主函数"""
    
    print("🚀 所有日期处理功能修复测试")
    print("=" * 60)
    
    # 测试各个模块
    test_enhanced_odds_scraper()
    test_match_info_extractor()
    test_date_match_extractor()
    test_match_time_scraper()
    
    # 总结
    test_summary()

if __name__ == "__main__":
    main()

# 赔率价值分析系统

## 项目简介

这是一个基于现有赔率数据库的系统化分析工具，旨在识别赔率与真实概率之间的错配机会，发掘有价值的下注点位。系统通过分析赔率的时间走势与市场共识行为，结合数据挖掘与机器学习技术，实现结构化特征生成、策略筛选与模型训练的完整流程。

## 核心特性

- 🔍 **深度数据分析**：自动分析数据库结构和数据质量
- ⏰ **时间序列处理**：提取和标准化赔率变化时间序列
- 📊 **特征工程**：从赔率走势中提取关键行为特征
- 🤖 **模式识别**：识别不同的赔率行为模式
- 📈 **策略回测**：验证投注策略的历史表现
- 🎯 **价值发现**：识别市场错配机会

## 系统架构

```
数据层          处理层          分析层          应用层
┌─────────┐    ┌─────────┐    ┌─────────┐    ┌─────────┐
│ 原始DB  │ -> │ 时间序列 │ -> │ 特征工程 │ -> │ 策略生成 │
│ 联赛DB  │    │ 标准化  │    │ 模式识别 │    │ 风险控制 │
└─────────┘    └─────────┘    └─────────┘    └─────────┘
```

## 快速开始

### 环境要求

- Python 3.7+
- pandas, numpy, sqlite3
- 现有的赔率数据库文件

### 安装依赖

```bash
pip install pandas numpy matplotlib seaborn
```

### 一键运行

```bash
# 完整分析流程
python run_analysis.py

# 快速演示（处理少量数据）
python run_analysis.py --demo

# 查看帮助
python run_analysis.py --help
```

### 分步运行

```bash
# 1. 数据库结构分析
python database_analyzer.py

# 2. 时间序列提取
python odds_timeseries_extractor.py

# 3. 时间轴标准化
python time_axis_normalizer.py
```

## 文件说明

### 核心模块

| 文件名 | 功能描述 |
|--------|----------|
| `database_analyzer.py` | 数据库结构深度分析器 |
| `odds_timeseries_extractor.py` | 赔率时间序列提取器 |
| `time_axis_normalizer.py` | 时间轴标准化处理器 |
| `run_analysis.py` | 一键启动脚本 |

### 输出文件

| 文件名模式 | 内容描述 |
|------------|----------|
| `database_analysis_results.json` | 数据库结构分析结果 |
| `odds_timeseries_*.json` | 原始时间序列数据 |
| `normalized_odds_*.json` | 标准化后的时间序列数据 |

### 文档

| 文件名 | 内容描述 |
|--------|----------|
| `README.md` | 项目说明文档 |
| `开发计划总结.md` | 详细的开发计划和技术架构 |

## 数据结构

### 标准化时间轴

系统将所有赔率变化时间转换为相对于比赛开始时间的标准时间轴：

```python
standard_time_points = [
    -168, -144, -120, -96, -72, -48, -24, -12, -6, -3, -1, -0.5, 0
]  # 从7天前到比赛开始（小时）
```

### 数据格式

标准化后的数据格式：

```json
{
  "match_id": "2596803",
  "match_info": {
    "home_team": "赫塔菲",
    "away_team": "巴塞罗那",
    "match_time": "2025-01-19 04:00:00"
  },
  "companies_data": {
    "威廉希尔": {
      "time_points": [-168, -144, -120, ...],
      "home_odds": [6.0, 6.0, 6.7, ...],
      "draw_odds": [4.2, 4.2, 4.3, ...],
      "away_odds": [1.5, 1.5, 1.4, ...]
    }
  }
}
```

## 使用示例

### 基础分析

```python
from database_analyzer import DatabaseAnalyzer

# 创建分析器
analyzer = DatabaseAnalyzer()

# 运行分析
results = analyzer.run_full_analysis()

# 查看结果
print(f"主数据库记录数: {results['main']['total_records']}")
print(f"博彩公司数量: {len(results['main']['companies'])}")
```

### 时间序列提取

```python
from odds_timeseries_extractor import OddsTimeseriesExtractor

# 创建提取器
extractor = OddsTimeseriesExtractor()

# 提取前5场比赛的时间序列
results = extractor.run_extraction(top_matches_per_db=5)

# 分析结果
for match_id, data in results['main'].items():
    print(f"比赛 {match_id}: {data['companies_count']}家公司")
    print(f"时间跨度: {data['time_span']['duration_hours']:.1f}小时")
```

### 时间轴标准化

```python
from time_axis_normalizer import TimeAxisNormalizer

# 创建标准化器
normalizer = TimeAxisNormalizer("odds_timeseries_20250727_221947.json")

# 加载和标准化数据
timeseries_data = normalizer.load_timeseries_data()
normalized_data = normalizer.normalize_all_matches(timeseries_data)

# 保存结果
normalizer.save_normalized_data()
```

## 开发路线图

### 已完成 ✅

- [x] 数据库结构深度分析
- [x] 赔率时间序列提取
- [x] 时间轴标准化处理
- [x] 基础数据处理框架

### 进行中 🚧

- [ ] 赔率行为特征工程
- [ ] 行为模式标签设计
- [ ] 模式与赛果关联分析

### 计划中 📋

- [ ] 多公司联动行为建模
- [ ] 机器学习模型构建
- [ ] 策略开发与回测
- [ ] 自动化监控系统

## 技术特点

### 数据安全

- **只读访问**：所有数据库连接使用只读模式
- **数据完整性**：不修改原始数据，所有处理结果存储到新文件
- **可追溯性**：每个处理步骤都保存详细日志

### 性能优化

- **批量处理**：大数据量时使用分批处理策略
- **内存管理**：及时释放不需要的数据对象
- **模块化设计**：每个功能独立，便于维护和扩展

### 代码质量

- **异常处理**：完善的错误处理和恢复机制
- **文档完整**：详细的代码注释和使用说明
- **可配置性**：关键参数可通过配置文件调整

## 常见问题

### Q: 系统对数据库有什么要求？

A: 系统需要包含odds表的SQLite数据库，表结构应包含match_id、company_name、date、time、home_odds、draw_odds、away_odds等字段。

### Q: 如何处理大量数据？

A: 系统支持分批处理，可以通过调整`top_matches_per_db`参数控制每次处理的比赛数量。

### Q: 生成的文件很大怎么办？

A: 可以使用快速演示模式(`--demo`)先处理少量数据，或者分批处理不同时间段的数据。

### Q: 如何扩展到新的博彩公司？

A: 系统会自动识别数据库中的所有公司，无需手动配置。

## 贡献指南

1. Fork 项目
2. 创建功能分支
3. 提交更改
4. 推送到分支
5. 创建 Pull Request

## 许可证

本项目仅供学习和研究使用。

## 联系方式

如有问题或建议，请通过以下方式联系：

- 项目文档：查看 `开发计划总结.md`
- 技术问题：检查代码注释和错误日志
- 功能建议：参考开发路线图

---

**免责声明**：本系统仅用于数据分析和学术研究，不构成投资建议。使用者应自行承担投资风险。

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
寻找真实的record ID - 比赛2398985
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from enhanced_odds_scraper import EnhancedOddsScraper
import requests
import re

def find_real_record_ids():
    """寻找真实的record ID"""
    print("🔍 寻找真实的record ID - 比赛2398985")
    print("=" * 60)
    
    scraper = EnhancedOddsScraper()
    match_id = "2398985"
    
    print(f"🎯 目标比赛: {match_id}")
    print("2023-08-12 03:00 英超")
    print("-" * 40)
    
    # 方法1：尝试访问compensate页面，查找JavaScript中的record ID
    print("\n📋 方法1：从compensate页面查找record ID")
    print("-" * 40)
    
    compensate_url = f"https://1x2.titan007.com/compensate/{match_id}.htm"
    print(f"访问: {compensate_url}")
    
    content = scraper.get_page_content(compensate_url)
    if content:
        print(f"✅ 页面内容长度: {len(content)}")
        
        # 查找JavaScript中的record ID模式
        # 通常格式类似：game="281|*********|Bet 365|..."
        game_pattern = r'game\s*=\s*["\']([^"\']+)["\']'
        game_matches = re.findall(game_pattern, content)
        
        if game_matches:
            print(f"📜 找到game变量: {len(game_matches)} 个")
            for i, game_data in enumerate(game_matches, 1):
                print(f"  {i}. {game_data[:100]}...")
                
                # 解析game数据
                companies = game_data.split('","')
                for company_data in companies[:5]:  # 只看前5个
                    company_data = company_data.strip('"')
                    parts = company_data.split('|')
                    if len(parts) >= 3:
                        company_id = parts[0]
                        record_id = parts[1]
                        company_name = parts[2]
                        print(f"    公司ID: {company_id}, Record ID: {record_id}, 名称: {company_name}")
        else:
            print("❌ 未找到game变量")
            
            # 查找其他可能的record ID模式
            record_patterns = [
                r'(\d{9,})',  # 9位以上的数字
                r'id[=:]\s*["\']?(\d{8,})["\']?',  # id=数字
                r'record[=:]\s*["\']?(\d{8,})["\']?',  # record=数字
            ]
            
            for pattern in record_patterns:
                matches = re.findall(pattern, content)
                if matches:
                    print(f"📋 模式 '{pattern}': 找到 {len(matches)} 个匹配")
                    for match in matches[:10]:  # 显示前10个
                        print(f"    - {match}")
    else:
        print("❌ 无法获取页面内容")
    
    # 方法2：尝试基于已知规律推测并验证
    print(f"\n📋 方法2：基于已知规律推测并验证")
    print("-" * 40)
    
    # 已知：比赛2213559的威廉希尔record_id是*********
    # 比赛2398985比2213559大了185426
    # 尝试线性推测
    
    known_william_base = *********  # 比赛2213559的威廉希尔
    match_id_diff = int(match_id) - 2213559
    
    # 尝试不同的系数
    coefficients = [1, 5, 10, 50, 100]
    
    for coeff in coefficients:
        predicted_william = known_william_base + match_id_diff * coeff
        predicted_bet365 = predicted_william + (281 - 115) * 100  # bet365比威廉希尔高16600
        
        print(f"\n🔮 系数 {coeff}:")
        print(f"  威廉希尔推测: {predicted_william}")
        print(f"  bet365推测: {predicted_bet365}")
        
        # 测试威廉希尔链接
        william_url = f"https://op1.titan007.com/OddsHistory.aspx?id={predicted_william}&sid={match_id}&cid=115&l=0"
        try:
            response = requests.head(william_url, timeout=5)
            william_status = "✅ 有效" if response.status_code == 200 else f"❌ 无效({response.status_code})"
        except:
            william_status = "❌ 无法访问"
        
        # 测试bet365链接
        bet365_url = f"https://op1.titan007.com/OddsHistory.aspx?id={predicted_bet365}&sid={match_id}&cid=281&l=0"
        try:
            response = requests.head(bet365_url, timeout=5)
            bet365_status = "✅ 有效" if response.status_code == 200 else f"❌ 无效({response.status_code})"
        except:
            bet365_status = "❌ 无法访问"
        
        print(f"  威廉希尔验证: {william_status}")
        print(f"  bet365验证: {bet365_status}")
        
        # 如果找到有效的，进一步测试
        if "✅" in william_status:
            print(f"  🎉 找到可能有效的威廉希尔record_id: {predicted_william}")
            
            # 测试是否能获取到数据
            try:
                records = scraper.parse_company_odds_history_page(william_url, "威廉希尔")
                if records:
                    print(f"    📊 成功获取 {len(records)} 条记录")
                    # 检查时间是否正确（应该是08月份）
                    august_records = [r for r in records if '08-' in r.get('change_time', '')]
                    if august_records:
                        print(f"    ✅ 包含 {len(august_records)} 条08月份记录")
                        print(f"    🎯 这很可能是正确的record_id!")
                        
                        # 显示前几条记录
                        for i, record in enumerate(records[:3], 1):
                            home_odds = record.get('home_odds', 'N/A')
                            draw_odds = record.get('draw_odds', 'N/A')
                            away_odds = record.get('away_odds', 'N/A')
                            change_time = record.get('change_time', 'N/A')
                            print(f"      {i}. {home_odds} {draw_odds} {away_odds} ({change_time})")
                    else:
                        print(f"    ⚠️ 未找到08月份记录，可能不正确")
                else:
                    print(f"    ❌ 无法解析数据")
            except Exception as e:
                print(f"    ❌ 解析失败: {e}")

if __name__ == "__main__":
    find_real_record_ids()

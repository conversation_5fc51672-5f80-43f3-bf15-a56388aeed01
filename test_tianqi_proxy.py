#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
天启代理测试脚本
测试不同的代理使用方法
"""

import requests
import json
import time
from urllib.parse import urlencode

class TianqiProxyTester:
    """天启代理测试器"""
    
    def __init__(self, api_key):
        self.api_key = api_key
        self.base_url = "https://www.tianqiip.com"
        
    def test_get_proxy_list(self):
        """测试获取代理列表的方法"""
        print("🔍 测试方法1: 获取代理列表")
        print("=" * 50)
        
        # 常见的API端点
        endpoints = [
            "/api/getip",
            "/api/proxy",
            "/getip",
            "/proxy",
            "/api/v1/getip",
            "/api/v2/getip"
        ]
        
        # 常见的参数组合
        param_sets = [
            {"key": self.api_key, "num": 1, "type": "json"},
            {"token": self.api_key, "num": 1, "format": "json"},
            {"apikey": self.api_key, "count": 1, "format": "json"},
            {"secret": self.api_key, "num": 1},
            {"key": self.api_key, "count": 1},
            {"key": self.api_key, "num": 1, "format": "txt"},
            {"key": self.api_key, "num": 1, "format": "json", "protocol": "http"},
            {"key": self.api_key, "num": 1, "format": "json", "protocol": "https"},
        ]
        
        for endpoint in endpoints:
            for i, params in enumerate(param_sets):
                try:
                    url = f"{self.base_url}{endpoint}"
                    print(f"\n🧪 测试: {endpoint} - 参数组合{i+1}")
                    print(f"URL: {url}")
                    print(f"参数: {params}")
                    
                    response = requests.get(url, params=params, timeout=10)
                    print(f"状态码: {response.status_code}")
                    print(f"响应头: {dict(response.headers)}")
                    print(f"响应内容: {response.text[:200]}...")
                    
                    if response.status_code == 200:
                        print("✅ 请求成功！")
                        try:
                            json_data = response.json()
                            print(f"JSON数据: {json_data}")
                        except:
                            print("响应不是JSON格式")
                    else:
                        print(f"❌ 请求失败: {response.status_code}")
                        
                except Exception as e:
                    print(f"❌ 请求异常: {e}")
                
                time.sleep(1)  # 避免请求过快
    
    def test_proxy_authentication(self):
        """测试代理认证方法"""
        print("\n🔍 测试方法2: 代理认证")
        print("=" * 50)
        
        # 常见的代理服务器地址
        proxy_hosts = [
            "proxy.tianqiip.com",
            "api.tianqiip.com", 
            "tunnel.tianqiip.com",
            "forward.tianqiip.com"
        ]
        
        # 常见端口
        ports = [8080, 3128, 1080, 8888, 9999]
        
        for host in proxy_hosts:
            for port in ports:
                try:
                    print(f"\n🧪 测试代理: {host}:{port}")
                    
                    # 方法1: 用户名密码认证
                    proxies = {
                        'http': f'http://{self.api_key}:password@{host}:{port}',
                        'https': f'http://{self.api_key}:password@{host}:{port}'
                    }
                    
                    print(f"代理配置: {proxies}")
                    
                    response = requests.get(
                        'http://httpbin.org/ip', 
                        proxies=proxies, 
                        timeout=10
                    )
                    
                    if response.status_code == 200:
                        print("✅ 代理连接成功！")
                        print(f"IP信息: {response.text}")
                        return True
                    else:
                        print(f"❌ 代理连接失败: {response.status_code}")
                        
                except Exception as e:
                    print(f"❌ 代理测试异常: {e}")
                
                time.sleep(1)
        
        return False
    
    def test_api_documentation(self):
        """测试API文档页面"""
        print("\n🔍 测试方法3: 查找API文档")
        print("=" * 50)
        
        doc_urls = [
            f"{self.base_url}/api",
            f"{self.base_url}/doc",
            f"{self.base_url}/help",
            f"{self.base_url}/api/doc",
            f"{self.base_url}/documentation",
            f"{self.base_url}/user",
            f"{self.base_url}/dashboard"
        ]
        
        for url in doc_urls:
            try:
                print(f"\n🧪 访问: {url}")
                response = requests.get(url, timeout=10)
                print(f"状态码: {response.status_code}")
                
                if response.status_code == 200:
                    content = response.text.lower()
                    if any(keyword in content for keyword in ['api', 'proxy', 'getip', 'key', 'token']):
                        print("✅ 可能包含API信息！")
                        print(f"页面内容片段: {response.text[:300]}...")
                    else:
                        print("❌ 未发现API相关信息")
                else:
                    print(f"❌ 访问失败: {response.status_code}")
                    
            except Exception as e:
                print(f"❌ 访问异常: {e}")
            
            time.sleep(1)
    
    def test_common_patterns(self):
        """测试常见的代理API模式"""
        print("\n🔍 测试方法4: 常见API模式")
        print("=" * 50)
        
        # 测试不同的请求方式
        test_cases = [
            {
                "method": "GET",
                "url": f"{self.base_url}/api/getip",
                "params": {"key": self.api_key, "num": 1, "type": "json"}
            },
            {
                "method": "POST", 
                "url": f"{self.base_url}/api/getip",
                "data": {"key": self.api_key, "num": 1, "type": "json"}
            },
            {
                "method": "GET",
                "url": f"{self.base_url}/getip",
                "headers": {"Authorization": f"Bearer {self.api_key}"}
            },
            {
                "method": "GET",
                "url": f"{self.base_url}/api/proxy",
                "headers": {"X-API-Key": self.api_key}
            }
        ]
        
        for i, case in enumerate(test_cases):
            try:
                print(f"\n🧪 测试案例 {i+1}: {case['method']} {case['url']}")
                
                if case['method'] == 'GET':
                    response = requests.get(
                        case['url'],
                        params=case.get('params'),
                        headers=case.get('headers'),
                        timeout=10
                    )
                else:
                    response = requests.post(
                        case['url'],
                        data=case.get('data'),
                        headers=case.get('headers'),
                        timeout=10
                    )
                
                print(f"状态码: {response.status_code}")
                print(f"响应: {response.text[:200]}...")
                
                if response.status_code == 200:
                    print("✅ 请求成功！")
                else:
                    print(f"❌ 请求失败")
                    
            except Exception as e:
                print(f"❌ 请求异常: {e}")
            
            time.sleep(1)

def main():
    """主函数"""
    print("🎯 天启代理测试工具")
    print("=" * 60)
    
    # 读取API密钥
    try:
        with open('ip_keys.txt', 'r') as f:
            api_key = f.read().strip()
        print(f"📋 API密钥: {api_key}")
    except Exception as e:
        print(f"❌ 读取密钥失败: {e}")
        return
    
    # 创建测试器
    tester = TianqiProxyTester(api_key)
    
    # 执行所有测试
    try:
        tester.test_get_proxy_list()
        tester.test_proxy_authentication() 
        tester.test_api_documentation()
        tester.test_common_patterns()
        
        print("\n🎯 测试完成！")
        print("请查看上面的输出，找到成功的方法。")
        
    except KeyboardInterrupt:
        print("\n👋 用户取消测试")
    except Exception as e:
        print(f"❌ 测试失败: {e}")

if __name__ == "__main__":
    main()

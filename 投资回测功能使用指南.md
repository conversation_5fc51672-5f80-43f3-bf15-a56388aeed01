# 投资回测功能使用指南

## 🎯 功能概述

新增的**投资回测**功能允许您对历史比赛数据应用各种投注策略进行回测分析，评估不同投注策略的盈利能力和风险水平。

## 📍 功能位置

在主界面中，您会看到一个新的标签页：**"投资回测"**，位于"比赛筛选"标签页之后。

## 🔧 界面布局

### 左侧：策略设置面板
- 包含多种投注策略，每个策略都有独立的启用开关
- 支持同时启用多个投注策略
- 回测参数设置（初始资金、单注金额等）
- 回测操作按钮

### 右侧：回测结果面板
- 回测概览：显示总体回测结果统计
- 详细结果：显示每笔投注的详细信息
- 支持导出回测报告

## 📋 投注策略详解

### 1. 主胜策略 🏠
- **功能**：投注主队获胜
- **参数**：
  - 最小赔率：主胜赔率的下限
  - 最大赔率：主胜赔率的上限
- **逻辑**：当主胜赔率在设定范围内时进行投注

### 2. 平局策略 ⚖️
- **功能**：投注比赛平局
- **参数**：
  - 最小赔率：平局赔率的下限
  - 最大赔率：平局赔率的上限
- **逻辑**：当平局赔率在设定范围内时进行投注

### 3. 客胜策略 ✈️
- **功能**：投注客队获胜
- **参数**：
  - 最小赔率：客胜赔率的下限
  - 最大赔率：客胜赔率的上限
- **逻辑**：当客胜赔率在设定范围内时进行投注

### 4. 凯利指数策略 📊
- **功能**：基于凯利指数进行投注
- **参数**：
  - 凯利指数阈值：最小凯利指数要求
  - 投注类型：
    - 最佳凯利指数：选择凯利指数最高的选项
    - 所有正凯利：投注所有超过阈值的选项
- **逻辑**：根据凯利指数的大小决定投注选择

### 5. 低赔率策略 📉
- **功能**：投注低赔率选项（相对安全）
- **参数**：
  - 赔率上限：最大赔率限制
  - 投注选择：
    - 最低赔率：选择最低的赔率
    - 主胜优先：优先选择主胜，否则选最低赔率
- **逻辑**：在低赔率范围内选择投注对象

### 6. 高赔率策略 📈
- **功能**：投注高赔率选项（高风险高回报）
- **参数**：
  - 赔率下限：最小赔率要求
  - 投注选择：
    - 最高赔率：选择最高的赔率
    - 客胜优先：优先选择客胜，否则选最高赔率
- **逻辑**：在高赔率范围内选择投注对象

## 🎮 回测参数设置

### 初始资金
- **功能**：设置回测的起始资金
- **默认值**：10,000
- **说明**：所有盈亏计算都基于此初始资金

### 单注金额策略
- **固定金额**：每次投注固定的金额
- **固定比例**：每次投注当前资金的固定比例

### 单注金额/比例
- **固定金额模式**：具体的投注金额（如100）
- **固定比例模式**：投注比例百分比（如5表示5%）

## 🚀 使用流程

### 步骤1：准备数据
1. 确保数据库中有完整的比赛和赔率数据
2. 可选：使用"比赛筛选"功能筛选特定的比赛范围

### 步骤2：设置投注策略
1. 点击"投资回测"标签页
2. 根据需要启用一个或多个投注策略
3. 调整每个策略的参数设置

### 步骤3：设置回测参数
1. 设置初始资金（如10,000）
2. 选择单注金额策略（固定金额或固定比例）
3. 设置具体的投注金额或比例

### 步骤4：执行回测
1. 点击"开始回测"按钮
2. 系统会自动处理当前比赛列表中的所有比赛
3. 实时显示回测进度和结果

### 步骤5：分析结果
1. 查看回测概览中的总体统计
2. 浏览详细结果表格中的每笔投注
3. 可选：导出回测报告进行进一步分析

## 📊 回测结果解读

### 回测概览
```
回测完成！

初始资金: 10,000.00
最终资金: 11,250.00
总盈亏: +1,250.00
投资回报率: +12.50%

总投注次数: 45
获胜次数: 28
胜率: 62.22%

平均每注盈亏: 27.78
```

### 关键指标说明
- **投资回报率(ROI)**：总盈亏相对于初始资金的百分比
- **胜率**：获胜投注占总投注的比例
- **平均每注盈亏**：每次投注的平均盈利或亏损

### 详细结果表格
- **比赛ID**：比赛的唯一标识
- **对阵**：比赛双方队伍
- **比赛时间**：比赛开始时间
- **策略**：使用的投注策略
- **投注类型**：具体的投注选择（主胜/平局/客胜）
- **投注金额**：实际投注的金额
- **赔率**：投注时的赔率
- **结果**：投注结果（胜/负）
- **盈亏**：该笔投注的盈利或亏损

## 💡 使用技巧

### 1. 策略组合
- 可以同时启用多个策略进行组合投注
- 不同策略可能对同一场比赛产生不同的投注决策
- 建议先单独测试各策略，再进行组合

### 2. 参数调优
- 根据历史数据调整策略参数
- 观察不同参数设置对回测结果的影响
- 寻找最优的参数组合

### 3. 数据筛选
- 结合"比赛筛选"功能，对特定时间段或联赛进行回测
- 可以测试策略在不同市场环境下的表现
- 避免使用过少的数据样本

### 4. 风险控制
- 合理设置单注金额，避免过度投注
- 关注最大回撤和连续亏损情况
- 考虑使用固定比例模式进行资金管理

## ⚠️ 注意事项

### 数据要求
1. **完整性**：需要比赛有完整的比分和赔率数据
2. **准确性**：回测结果基于历史数据，实际投注可能有差异
3. **时效性**：使用最新的数据进行回测

### 回测局限性
1. **历史表现不代表未来**：过去的盈利不保证未来的成功
2. **市场变化**：赔率市场和博彩环境可能发生变化
3. **交易成本**：实际投注可能涉及手续费等成本

### 风险提示
1. **投资有风险**：任何投注策略都存在亏损风险
2. **理性投资**：不要基于单次回测结果做出重大决策
3. **资金管理**：合理控制投注金额，不要超出承受能力

## 🔄 与其他功能的集成

### 比赛筛选集成
- 回测会自动使用当前"比赛列表"中的比赛
- 如果已经应用了筛选条件，回测只针对筛选后的比赛
- 可以先筛选特定条件的比赛，再进行针对性回测

### 数据库兼容
- 支持单一数据库和联赛分库模式
- 自动适应当前选择的数据库
- 确保回测数据的一致性

## 📈 未来扩展

该回测功能设计为可扩展的架构，未来可以添加更多策略：
- 组合投注策略
- 动态资金管理策略
- 机器学习预测策略
- 套利投注策略

---

**版本**: v1.0  
**更新时间**: 2025年6月28日  
**兼容性**: 与现有所有功能完全兼容

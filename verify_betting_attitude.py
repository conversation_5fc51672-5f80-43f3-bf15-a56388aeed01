#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证博彩态度筛选的具体情况 - 使用正确的动态时间线逻辑
"""

import sqlite3
from datetime import datetime

def get_other_companies_odds_at_time(cursor, match_id, target_company, target_date, target_time, odds_type):
    """获取其他公司在指定时间点的赔率（参考动态时间线逻辑）"""
    try:
        # 构造目标时间点
        target_datetime_str = f"2025-{target_date} {target_time}:00"
        target_datetime = datetime.strptime(target_datetime_str, "%Y-%m-%d %H:%M:%S")

        # 获取所有其他公司的赔率数据
        cursor.execute(f'''
            SELECT company_name, date, time, {odds_type}
            FROM odds
            WHERE match_id = ? AND company_name != ? AND {odds_type} IS NOT NULL
            ORDER BY company_name, date ASC, time ASC
        ''', (match_id, target_company))

        all_other_odds = cursor.fetchall()

        # 按公司分组
        company_odds = {}
        for record in all_other_odds:
            company_name = record['company_name']
            if company_name not in company_odds:
                company_odds[company_name] = []

            try:
                # 构造该记录的时间
                record_datetime_str = f"2025-{record['date']} {record['time']}:00"
                record_datetime = datetime.strptime(record_datetime_str, "%Y-%m-%d %H:%M:%S")

                company_odds[company_name].append({
                    'datetime': record_datetime,
                    'odds': float(record[odds_type])
                })
            except ValueError:
                continue

        # 对每个公司找到在目标时间点的赔率
        result_odds = []

        for company_name, odds_list in company_odds.items():
            # 按时间排序
            odds_list.sort(key=lambda x: x['datetime'])

            # 找到该公司在目标时间点的赔率
            target_odds_value = None

            # 首先检查是否在目标时间点有数据
            for odds_record in odds_list:
                if odds_record['datetime'] == target_datetime:
                    target_odds_value = odds_record['odds']
                    print(f"  {company_name}: 在目标时间点有数据 {target_odds_value}")
                    break

            # 如果目标时间点没有数据，找最近的之前时间点
            if target_odds_value is None:
                latest_before_target = None
                for odds_record in odds_list:
                    if odds_record['datetime'] < target_datetime:
                        latest_before_target = odds_record
                    else:
                        break  # 已经超过目标时间点

                if latest_before_target:
                    target_odds_value = latest_before_target['odds']
                    time_diff = target_datetime - latest_before_target['datetime']
                    print(f"  {company_name}: 使用之前时间点数据 {target_odds_value} (时间差: {time_diff})")

            # 如果该公司在目标时间点或之前有数据，添加其赔率
            if target_odds_value is not None:
                result_odds.append({
                    'company_name': company_name,
                    odds_type: target_odds_value
                })

        print(f"在时间点 {target_date} {target_time} 找到 {len(result_odds)} 家其他公司的赔率")
        return result_odds

    except Exception as e:
        print(f"获取其他公司赔率失败: {e}")
        return []

def verify_betting_attitude_for_odds_type(match_id, target_company, odds_type, threshold, odds_type_name):
    """验证特定赔率类型的博彩态度筛选"""
    print(f"🔍 验证博彩态度筛选 - {odds_type_name}")
    print("=" * 60)

    print(f"比赛ID: {match_id}")
    print(f"目标公司: {target_company}")
    print(f"赔率类型: {odds_type_name}")
    print(f"阈值: {threshold}")
    print()

    
    try:
        with sqlite3.connect("odds_data.db") as conn:
            conn.row_factory = sqlite3.Row
            cursor = conn.cursor()
            
            # 获取比赛信息
            cursor.execute('SELECT home_team, away_team, match_time FROM matches WHERE match_id = ?', (match_id,))
            match_info = cursor.fetchone()
            if match_info:
                print(f"比赛: {match_info['home_team']} vs {match_info['away_team']}")
                print(f"时间: {match_info['match_time']}")
            
            # 获取澳门的开盘数据（第一个时间点）
            cursor.execute(f'''
                SELECT date, time, {odds_type}
                FROM odds
                WHERE match_id = ? AND company_name = ? AND {odds_type} IS NOT NULL
                ORDER BY date ASC, time ASC
                LIMIT 1
            ''', (match_id, target_company))

            target_record = cursor.fetchone()
            if not target_record:
                print(f"❌ 没有找到 {target_company} 的数据")
                return

            target_date = target_record['date']
            target_time = target_record['time']
            target_odds = float(target_record[odds_type])

            print(f"\n📊 {target_company} 开盘数据:")
            print(f"  开盘时间: {target_date} {target_time}")
            print(f"  主胜赔率: {target_odds}")

            # 使用正确的动态时间线逻辑获取其他公司赔率
            other_companies_odds = get_other_companies_odds_at_time(
                cursor, match_id, target_company, target_date, target_time, odds_type
            )

            print(f"\n🏢 在该时间点或之前已开盘的其他公司 ({len(other_companies_odds)} 家):")
            if len(other_companies_odds) == 0:
                print("  ❌ 没有其他公司在该时间点或之前开盘")
                return

            other_odds_values = []
            for i, record in enumerate(other_companies_odds):
                odds_value = float(record[odds_type])
                other_odds_values.append(odds_value)
                print(f"  {i+1:2d}. {record['company_name']:12s}: {odds_value}")

            # 计算结果
            avg_other_odds = sum(other_odds_values) / len(other_odds_values)
            ratio = target_odds / avg_other_odds

            print(f"\n🧮 正确的计算结果:")
            print(f"  {target_company} 赔率: {target_odds}")
            print(f"  其他公司平均: {avg_other_odds:.6f}")
            print(f"  比率: {target_odds} ÷ {avg_other_odds:.6f} = {ratio:.6f}")
            print(f"  阈值: {threshold}")
            print(f"  条件: {ratio:.6f} >= {threshold} = {ratio >= threshold}")

            if ratio >= threshold:
                print(f"  ✅ 满足博彩态度条件")
            else:
                print(f"  ❌ 不满足博彩态度条件")
            
            # 先查看这场比赛的所有数据
            print(f"\n" + "=" * 60)
            print("🔍 查看比赛 2511950 的所有赔率数据")

            cursor.execute(f'''
                SELECT DISTINCT date, time, COUNT(*) as company_count
                FROM odds
                WHERE match_id = ? AND {odds_type} IS NOT NULL
                GROUP BY date, time
                ORDER BY date, time
            ''', (match_id,))

            all_time_points = cursor.fetchall()
            print(f"找到 {len(all_time_points)} 个时间点:")
            for i, (date, time, count) in enumerate(all_time_points):
                print(f"  {i+1}. {date} {time}: {count}家公司")

            # 现在检查您截图中看到的时间点 (22:15)
            print(f"\n" + "=" * 60)
            print("🕐 检查您截图中的具体时间点 22:15")

            # 查找22:15时间点的数据 - 更精确的查询
            cursor.execute(f'''
                SELECT company_name, {odds_type}, date, time
                FROM odds
                WHERE match_id = ? AND {odds_type} IS NOT NULL
                AND (time = '22:15' OR time LIKE '%22:15%')
                ORDER BY company_name
            ''', (match_id,))

            companies_22_15 = cursor.fetchall()

            if companies_22_15:
                print(f"📅 22:15时间点找到 {len(companies_22_15)} 家公司数据:")

                target_odds_22_15 = None
                other_odds_22_15 = []

                for record in companies_22_15:
                    company = record['company_name']
                    odds = float(record[odds_type])
                    date = record['date']
                    time = record['time']
                    print(f"  {company:12s}: {odds} ({date} {time})")

                    if company == target_company:
                        target_odds_22_15 = odds
                    else:
                        other_odds_22_15.append(odds)

                if target_odds_22_15 is not None and len(other_odds_22_15) > 0:
                    avg_other_22_15 = sum(other_odds_22_15) / len(other_odds_22_15)
                    ratio_22_15 = target_odds_22_15 / avg_other_22_15

                    print(f"\n📊 22:15时间点计算结果:")
                    print(f"  🎯 {target_company}主胜赔率: {target_odds_22_15}")
                    print(f"  📊 其他{len(other_odds_22_15)}家平均: {avg_other_22_15:.4f}")
                    print(f"  📈 比率: {ratio_22_15:.4f}")
                    print(f"  🎚️ 阈值: {threshold}")
                    print(f"  ✅ 通过筛选: {ratio_22_15 >= threshold}")

                    # 与您的数据对比
                    print(f"\n🔍 与您截图数据对比:")
                    print(f"  您的澳门赔率: 1.84")
                    print(f"  您的其他平均: 1.8226")
                    print(f"  您的比率: 1.009")
                    print(f"  数据库澳门赔率: {target_odds_22_15}")
                    print(f"  数据库其他平均: {avg_other_22_15:.4f}")
                    print(f"  数据库比率: {ratio_22_15:.4f}")

                    if ratio_22_15 >= threshold:
                        print(f"  🎉 这个时间点符合条件！")
                    else:
                        print(f"  ❌ 这个时间点不符合条件")
                else:
                    print(f"  ❌ {target_company}在此时间点无数据或其他公司数据不足")
            else:
                print("❌ 22:15时间点没有找到数据")
                        
    except Exception as e:
        print(f"❌ 验证失败: {e}")

if __name__ == "__main__":
    verify_betting_attitude()

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试并发抓取功能
验证新增的代理和并发模式是否正常工作
"""

import tkinter as tk
from odds_scraper_ui import OddsScraperGUI

def test_ui_components():
    """测试UI组件是否正常创建"""
    print("🧪 测试UI组件...")
    
    try:
        root = tk.Tk()
        app = OddsScraperGUI(root)
        
        # 检查新增的变量是否存在
        required_vars = [
            'network_mode_var',
            'proxy_api_var', 
            'proxy_status_var',
            'concurrent_threads_var',
            'proxy_list',
            'working_proxies',
            'proxy_failure_count',
            'proxy_pool_lock'
        ]
        
        missing_vars = []
        for var_name in required_vars:
            if not hasattr(app, var_name):
                missing_vars.append(var_name)
        
        if missing_vars:
            print(f"❌ 缺少变量: {missing_vars}")
            return False
        else:
            print("✅ 所有必需的变量都存在")
        
        # 检查新增的方法是否存在
        required_methods = [
            'on_network_mode_change',
            'fetch_proxy_list',
            'test_proxy_list',
            'get_available_proxy',
            'mark_proxy_failure',
            'scrape_with_proxy',
            'concurrent_batch_scraping_worker',
            'scrape_single_match_with_proxy'
        ]
        
        missing_methods = []
        for method_name in required_methods:
            if not hasattr(app, method_name):
                missing_methods.append(method_name)
        
        if missing_methods:
            print(f"❌ 缺少方法: {missing_methods}")
            return False
        else:
            print("✅ 所有必需的方法都存在")
        
        # 检查UI组件是否存在
        required_ui_components = [
            'proxy_config_frame',
            'proxy_api_entry'
        ]
        
        missing_ui = []
        for component_name in required_ui_components:
            if not hasattr(app, component_name):
                missing_ui.append(component_name)
        
        if missing_ui:
            print(f"❌ 缺少UI组件: {missing_ui}")
            return False
        else:
            print("✅ 所有必需的UI组件都存在")
        
        # 测试网络模式切换
        print("\n🔄 测试网络模式切换...")
        
        # 测试普通模式
        app.network_mode_var.set("normal")
        app.on_network_mode_change()
        print("✅ 普通模式切换成功")
        
        # 测试并发模式
        app.network_mode_var.set("concurrent")
        app.on_network_mode_change()
        print("✅ 并发模式切换成功")
        
        # 测试代理API默认值
        default_api = app.proxy_api_var.get()
        if "tianqiip.com" in default_api:
            print("✅ 代理API默认值设置正确")
        else:
            print(f"⚠️  代理API默认值可能不正确: {default_api}")
        
        root.destroy()
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def test_proxy_methods():
    """测试代理相关方法"""
    print("\n🌐 测试代理相关方法...")
    
    try:
        root = tk.Tk()
        app = OddsScraperGUI(root)
        
        # 测试代理列表初始化
        if isinstance(app.proxy_list, list):
            print("✅ 代理列表初始化正确")
        else:
            print("❌ 代理列表初始化失败")
            return False
        
        # 测试代理失败计数初始化
        if isinstance(app.proxy_failure_count, dict):
            print("✅ 代理失败计数初始化正确")
        else:
            print("❌ 代理失败计数初始化失败")
            return False
        
        # 测试获取可用代理（空列表情况）
        proxy = app.get_available_proxy()
        if proxy is None:
            print("✅ 空代理列表时正确返回None")
        else:
            print("❌ 空代理列表时应该返回None")
        
        # 模拟添加代理
        test_proxy = {"ip": "127.0.0.1", "port": "8080", "type": "http"}
        app.proxy_list = [test_proxy]
        app.proxy_failure_count["127.0.0.1:8080"] = 0
        
        # 测试获取可用代理
        proxy = app.get_available_proxy()
        if proxy == test_proxy:
            print("✅ 获取可用代理功能正常")
        else:
            print("❌ 获取可用代理功能异常")
        
        # 测试标记代理失败
        app.mark_proxy_failure(test_proxy)
        if app.proxy_failure_count["127.0.0.1:8080"] == 1:
            print("✅ 标记代理失败功能正常")
        else:
            print("❌ 标记代理失败功能异常")
        
        root.destroy()
        return True
        
    except Exception as e:
        print(f"❌ 代理方法测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🎯 并发抓取功能测试")
    print("=" * 60)
    
    success_count = 0
    total_tests = 2
    
    # 测试UI组件
    if test_ui_components():
        success_count += 1
    
    # 测试代理方法
    if test_proxy_methods():
        success_count += 1
    
    print("\n" + "=" * 60)
    print(f"📊 测试结果: {success_count}/{total_tests} 通过")
    
    if success_count == total_tests:
        print("🎉 所有测试通过！并发抓取功能已成功集成")
        print("\n💡 使用说明:")
        print("1. 启动程序: python odds_scraper_ui.py")
        print("2. 进入'联赛批量抓取'标签页")
        print("3. 选择'并发模式'")
        print("4. 输入代理API地址（已预设天启代理）")
        print("5. 点击'获取代理'按钮")
        print("6. 点击'测试代理'验证连接")
        print("7. 设置并发线程数")
        print("8. 开始批量抓取")
    else:
        print("❌ 部分测试失败，请检查代码")
    
    return success_count == total_tests

if __name__ == "__main__":
    main()

# 🚀 比赛详细数据抓取功能 - 使用说明

## 📋 功能概述

新增的比赛详细数据抓取功能可以从比赛分析页面获取丰富的数据，包括：

- ✅ **技术统计数据** (28项) - 角球、射门、控球率等
- ✅ **阵容信息** - 首发、替补、阵型、教练
- ✅ **球员详细统计** - 每个球员的28项技术指标
- ✅ **进失球概率** - 不同时间段的进失球概率分析
- ✅ **半场/全场统计** - 历史平均数据对比
- ✅ **比赛事件** - 进球、黄牌、换人等事件记录

## 🏗️ 模块架构

### 核心模块

1. **`match_detail_scraper.py`** - 比赛详细数据抓取器
   - 从 `https://m.titan007.com/Analy/ShiJian/{match_id}.htm` 抓取数据
   - 解析页面中的JSON数据
   - 提供灵活的数据提取方法

2. **`flexible_data_saver.py`** - 灵活数据保存器
   - 向后兼容的数据库扩展
   - JSON格式存储，适应数据结构变化
   - 支持数据增项、缺项的优雅处理

3. **`database_schema_extension.py`** - 数据库架构扩展
   - 自动化的表结构升级
   - 数据库备份和恢复
   - 架构验证和监控

4. **`enhanced_match_data_system.py`** - 集成系统
   - 整合所有功能模块
   - 提供统一的接口
   - 批量处理和状态监控

## 🚀 快速开始

### 1. 基本使用

```python
from enhanced_match_data_system import EnhancedMatchDataSystem

# 创建系统实例
system = EnhancedMatchDataSystem()

# 初始化系统（首次使用）
init_result = system.initialize_system()
if init_result['success']:
    print("✅ 系统初始化成功")
else:
    print(f"❌ 初始化失败: {init_result['error']}")

# 处理单个比赛数据
match_id = "2511566"
result = system.process_match_data(match_id, save_data=True)

if result['success']:
    print("✅ 数据处理成功")
    print(f"数据完整度: {result['data_summary']['completeness_score']:.1f}%")
else:
    print(f"❌ 处理失败: {result['error']}")
```

### 2. 批量处理

```python
# 批量处理多个比赛
match_ids = ["2511566", "2511567", "2511568"]
batch_result = system.batch_process_matches(
    match_ids, 
    save_data=True, 
    delay_seconds=1.0
)

print(f"成功率: {batch_result['success_rate']:.1f}%")
print(f"总耗时: {batch_result['total_time']} 秒")
```

### 3. 单独使用各模块

```python
# 只抓取数据，不保存
from match_detail_scraper import MatchDetailScraper

scraper = MatchDetailScraper()
data = scraper.scrape_match_details("2511566")

# 只保存数据
from flexible_data_saver import FlexibleMatchDataSaver

saver = FlexibleMatchDataSaver()
result = saver.save_extended_match_data("2511566", data)

# 只管理数据库架构
from database_schema_extension import DatabaseSchemaManager

manager = DatabaseSchemaManager()
manager.upgrade_schema()
manager.print_schema_info()
```

## 📊 数据库架构

### 新增表结构

1. **`technical_stats`** - 技术统计表
   ```sql
   - match_id: 比赛ID
   - stat_type: 统计类型 ('current', 'historical')
   - stats_json: JSON格式的统计数据
   - created_at, updated_at: 时间戳
   ```

2. **`lineups`** - 阵容表
   ```sql
   - match_id: 比赛ID
   - team_type: 队伍类型 ('home', 'away')
   - lineup_json: JSON格式的阵容数据
   - formation: 阵型信息
   ```

3. **`player_stats`** - 球员统计表
   ```sql
   - match_id: 比赛ID
   - player_id: 球员ID
   - player_name: 球员姓名
   - team_type: 队伍类型
   - stats_json: JSON格式的球员统计
   ```

4. **`goal_probability`** - 进失球概率表
   ```sql
   - match_id: 比赛ID
   - probability_type: 概率类型 ('Count_30', 'Count_50')
   - probability_json: JSON格式的概率数据
   ```

### 扩展字段

**`matches`** 表新增字段：
- `home_formation`: 主队阵型
- `away_formation`: 客队阵型
- `match_events_json`: 比赛事件JSON
- `extended_data_json`: 扩展数据JSON

## 🔧 配置选项

### 数据库配置

```python
# 使用自定义数据库路径
system = EnhancedMatchDataSystem(db_path='custom_odds_data.db')
```

### 抓取配置

```python
# 自定义请求头
scraper = MatchDetailScraper()
scraper.headers['User-Agent'] = 'Custom User Agent'

# 设置超时时间
scraper.session.timeout = 15
```

### 保存配置

```python
# 只保存特定类型的数据
data_to_save = {
    'technical_stats': extracted_data['technical_stats'],
    'lineup': extracted_data['lineup']
    # 不保存球员统计等其他数据
}
saver.save_extended_match_data(match_id, data_to_save)
```

## ⚠️ 注意事项

### 1. 数据保护
- ✅ **向后兼容**: 不会破坏现有的 `matches` 和 `odds` 表
- ✅ **自动备份**: 架构升级前自动备份数据库
- ✅ **优雅降级**: 新功能失败不影响原有功能

### 2. 性能考虑
- 🔄 **请求间隔**: 批量处理时建议设置1-2秒间隔
- 💾 **数据量**: JSON存储会增加数据库大小
- 🔍 **查询优化**: 关键字段单独存储便于查询

### 3. 错误处理
- 📝 **详细日志**: 所有操作都有详细的日志记录
- 🛡️ **异常隔离**: 单个数据失败不影响整体处理
- 🔄 **重试机制**: 网络错误时可以重新处理

## 📈 数据质量评估

系统会自动评估数据质量：

- **技术统计**: 根据统计项目数量评估 (优秀/良好/一般)
- **阵容信息**: 根据球员完整性评估 (完整/部分/缺失)
- **球员统计**: 根据球员数量评估 (详细/基本/简单)
- **完整度评分**: 0-100分的整体完整度评分

## 🔍 故障排除

### 常见问题

1. **抓取失败**
   ```
   原因: 网络连接问题或页面结构变化
   解决: 检查网络连接，查看日志中的具体错误信息
   ```

2. **数据保存失败**
   ```
   原因: 数据库权限或磁盘空间不足
   解决: 检查数据库文件权限和磁盘空间
   ```

3. **架构升级失败**
   ```
   原因: 数据库被其他程序占用
   解决: 关闭其他使用数据库的程序，重新运行
   ```

### 调试模式

```python
import logging
logging.basicConfig(level=logging.DEBUG)

# 这样可以看到详细的调试信息
```

## 🎯 最佳实践

1. **首次使用**
   - 先在测试环境验证功能
   - 备份现有数据库
   - 小批量测试后再大规模使用

2. **日常使用**
   - 定期检查系统状态
   - 监控数据库大小增长
   - 根据需要清理历史数据

3. **性能优化**
   - 批量处理时设置合适的间隔
   - 定期重建索引提高查询性能
   - 考虑数据分表策略

## 📞 技术支持

如果遇到问题，请：

1. 查看日志文件中的详细错误信息
2. 检查系统状态: `system.print_system_status()`
3. 验证数据库架构: `manager.verify_schema()`
4. 提供具体的错误信息和复现步骤

---

**🎉 现在您可以获取比以往更丰富、更详细的比赛数据了！**

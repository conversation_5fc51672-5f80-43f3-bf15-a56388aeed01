#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据库管理模块
用于创建和管理足球赔率数据的SQLite数据库
"""

import sqlite3
import os
from datetime import datetime
from typing import Dict, List, Optional
import logging

logger = logging.getLogger(__name__)

class OddsDatabase:
    def __init__(self, db_path: str = "odds_data.db"):
        """初始化数据库连接"""
        self.db_path = db_path
        self.init_database()
    
    def init_database(self):
        """初始化数据库表结构"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # 创建比赛信息表
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS matches (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        match_id TEXT UNIQUE NOT NULL,
                        league TEXT,
                        season TEXT,
                        round_info TEXT,
                        home_team TEXT,
                        away_team TEXT,
                        match_time TEXT,
                        match_date TEXT,
                        accurate_datetime TEXT,
                        accurate_date TEXT,
                        accurate_time TEXT,
                        weekday TEXT,
                        match_year TEXT,
                        match_month TEXT,
                        match_day TEXT,
                        match_hour TEXT,
                        match_minute TEXT,
                        time_source TEXT,
                        home_score TEXT,
                        away_score TEXT,
                        half_score TEXT,
                        match_state TEXT,
                        weather TEXT,
                        temperature TEXT,
                        raw_league_text TEXT,
                        extraction_time TEXT,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                    )
                ''')
                
                # 创建赔率数据表
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS odds (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        match_id TEXT NOT NULL,
                        company_name TEXT,
                        company_id TEXT,
                        date TEXT,
                        time TEXT,
                        home_odds REAL,
                        draw_odds REAL,
                        away_odds REAL,
                        return_rate REAL,
                        kelly_home REAL,
                        kelly_draw REAL,
                        kelly_away REAL,
                        extraction_time TEXT,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        FOREIGN KEY (match_id) REFERENCES matches (match_id)
                    )
                ''')
                
                # 创建索引
                cursor.execute('CREATE INDEX IF NOT EXISTS idx_match_id ON odds (match_id)')
                cursor.execute('CREATE INDEX IF NOT EXISTS idx_company ON odds (company_name)')
                cursor.execute('CREATE INDEX IF NOT EXISTS idx_match_time ON matches (match_time)')
                
                conn.commit()
                logger.info(f"数据库初始化完成: {self.db_path}")
                
        except sqlite3.Error as e:
            logger.error(f"数据库初始化失败: {e}")
            raise
    
    def save_match_info(self, match_info: Dict) -> bool:
        """保存比赛信息"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # 使用INSERT OR REPLACE来处理重复数据
                cursor.execute('''
                    INSERT OR REPLACE INTO matches (
                        match_id, league, season, round_info, home_team, away_team,
                        match_time, match_date, accurate_datetime, accurate_date, accurate_time,
                        weekday, match_year, match_month, match_day, match_hour, match_minute,
                        time_source, home_score, away_score, half_score, match_state,
                        weather, temperature, raw_league_text, extraction_time
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', (
                    match_info.get('match_id'),
                    match_info.get('league'),
                    match_info.get('season'),
                    match_info.get('round'),
                    match_info.get('home_team'),
                    match_info.get('away_team'),
                    match_info.get('match_time'),
                    match_info.get('match_date'),
                    match_info.get('accurate_datetime'),
                    match_info.get('accurate_date'),
                    match_info.get('accurate_time'),
                    match_info.get('weekday'),
                    match_info.get('match_year'),
                    match_info.get('match_month'),
                    match_info.get('match_day'),
                    match_info.get('match_hour'),
                    match_info.get('match_minute'),
                    match_info.get('time_source'),
                    match_info.get('home_score'),
                    match_info.get('away_score'),
                    match_info.get('half_score'),
                    match_info.get('match_state'),
                    match_info.get('weather'),
                    match_info.get('temperature'),
                    match_info.get('raw_league_text'),
                    match_info.get('extraction_time')
                ))
                
                conn.commit()
                logger.info(f"比赛信息已保存: {match_info.get('match_id')}")
                return True
                
        except sqlite3.Error as e:
            logger.error(f"保存比赛信息失败: {e}")
            return False
    
    def save_odds_data(self, match_id: str, odds_data: List[Dict]) -> bool:
        """保存赔率数据"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # 先删除该比赛的旧赔率数据
                cursor.execute('DELETE FROM odds WHERE match_id = ?', (match_id,))
                
                # 插入新的赔率数据
                for odds in odds_data:
                    cursor.execute('''
                        INSERT INTO odds (
                            match_id, company_name, company_id, date, time,
                            home_odds, draw_odds, away_odds, return_rate,
                            kelly_home, kelly_draw, kelly_away, extraction_time
                        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                    ''', (
                        match_id,
                        odds.get('company_name'),
                        odds.get('company_id'),
                        odds.get('date'),
                        odds.get('time'),
                        odds.get('home_odds'),
                        odds.get('draw_odds'),
                        odds.get('away_odds'),
                        odds.get('return_rate'),
                        odds.get('kelly_home'),
                        odds.get('kelly_draw'),
                        odds.get('kelly_away'),
                        odds.get('extraction_time', datetime.now().isoformat())
                    ))
                
                conn.commit()
                logger.info(f"赔率数据已保存: {match_id}, {len(odds_data)} 条记录")
                return True
                
        except sqlite3.Error as e:
            logger.error(f"保存赔率数据失败: {e}")
            return False
    
    def get_match_info(self, match_id: str) -> Optional[Dict]:
        """获取比赛信息"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                conn.row_factory = sqlite3.Row
                cursor = conn.cursor()
                
                cursor.execute('SELECT * FROM matches WHERE match_id = ?', (match_id,))
                row = cursor.fetchone()
                
                if row:
                    return dict(row)
                return None
                
        except sqlite3.Error as e:
            logger.error(f"获取比赛信息失败: {e}")
            return None
    
    def get_odds_data(self, match_id: str) -> List[Dict]:
        """获取赔率数据"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                conn.row_factory = sqlite3.Row
                cursor = conn.cursor()
                
                cursor.execute('''
                    SELECT * FROM odds 
                    WHERE match_id = ? 
                    ORDER BY company_name, date, time
                ''', (match_id,))
                
                rows = cursor.fetchall()
                return [dict(row) for row in rows]
                
        except sqlite3.Error as e:
            logger.error(f"获取赔率数据失败: {e}")
            return []
    
    def get_all_matches(self) -> List[Dict]:
        """获取所有比赛列表"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                conn.row_factory = sqlite3.Row
                cursor = conn.cursor()
                
                cursor.execute('''
                    SELECT match_id, league, season, round_info, home_team, away_team,
                           match_time, match_state, home_score, away_score,
                           accurate_datetime, accurate_date, accurate_time, weekday,
                           match_year, match_month, match_day, match_hour, match_minute,
                           time_source, created_at
                    FROM matches
                    ORDER BY accurate_datetime DESC, match_time DESC, created_at DESC
                ''')
                
                rows = cursor.fetchall()
                return [dict(row) for row in rows]
                
        except sqlite3.Error as e:
            logger.error(f"获取比赛列表失败: {e}")
            return []
    
    def get_database_stats(self) -> Dict:
        """获取数据库统计信息"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # 统计比赛数量
                cursor.execute('SELECT COUNT(*) FROM matches')
                match_count = cursor.fetchone()[0]
                
                # 统计赔率记录数量
                cursor.execute('SELECT COUNT(*) FROM odds')
                odds_count = cursor.fetchone()[0]
                
                # 统计博彩公司数量
                cursor.execute('SELECT COUNT(DISTINCT company_name) FROM odds')
                company_count = cursor.fetchone()[0]
                
                # 获取数据库文件大小
                db_size = os.path.getsize(self.db_path) if os.path.exists(self.db_path) else 0
                
                return {
                    'match_count': match_count,
                    'odds_count': odds_count,
                    'company_count': company_count,
                    'db_size': db_size,
                    'db_size_mb': round(db_size / 1024 / 1024, 2)
                }
                
        except sqlite3.Error as e:
            logger.error(f"获取数据库统计失败: {e}")
            return {}
    
    def delete_match(self, match_id: str) -> bool:
        """删除比赛及其相关数据"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # 删除赔率数据
                cursor.execute('DELETE FROM odds WHERE match_id = ?', (match_id,))
                
                # 删除比赛信息
                cursor.execute('DELETE FROM matches WHERE match_id = ?', (match_id,))
                
                conn.commit()
                logger.info(f"比赛数据已删除: {match_id}")
                return True
                
        except sqlite3.Error as e:
            logger.error(f"删除比赛数据失败: {e}")
            return False
    
    def export_to_csv(self, match_id: str, output_dir: str = ".") -> Dict[str, str]:
        """导出数据到CSV文件"""
        import pandas as pd
        
        try:
            # 导出比赛信息
            match_info = self.get_match_info(match_id)
            odds_data = self.get_odds_data(match_id)
            
            files = {}
            
            if match_info:
                match_df = pd.DataFrame([match_info])
                match_file = os.path.join(output_dir, f"match_{match_id}_info.csv")
                match_df.to_csv(match_file, index=False, encoding='utf-8-sig')
                files['match_info'] = match_file
            
            if odds_data:
                odds_df = pd.DataFrame(odds_data)
                odds_file = os.path.join(output_dir, f"match_{match_id}_odds.csv")
                odds_df.to_csv(odds_file, index=False, encoding='utf-8-sig')
                files['odds_data'] = odds_file
            
            logger.info(f"数据已导出: {files}")
            return files
            
        except Exception as e:
            logger.error(f"导出数据失败: {e}")
            return {}

    def match_exists(self, match_id: str) -> bool:
        """检查比赛是否已存在"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute('SELECT COUNT(*) FROM matches WHERE match_id = ?', (match_id,))
                count = cursor.fetchone()[0]
                return count > 0
        except sqlite3.Error as e:
            logger.error(f"检查比赛是否存在失败: {e}")
            return False

    def odds_exist(self, match_id: str) -> bool:
        """检查比赛的赔率数据是否已存在"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute('SELECT COUNT(*) FROM odds WHERE match_id = ?', (match_id,))
                count = cursor.fetchone()[0]
                return count > 0
        except sqlite3.Error as e:
            logger.error(f"检查赔率数据是否存在失败: {e}")
            return False

    def get_match_data_summary(self, match_id: str) -> Dict:
        """获取比赛数据摘要"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()

                # 检查比赛信息
                cursor.execute('SELECT COUNT(*) FROM matches WHERE match_id = ?', (match_id,))
                match_exists = cursor.fetchone()[0] > 0

                # 检查赔率数据
                cursor.execute('SELECT COUNT(*) FROM odds WHERE match_id = ?', (match_id,))
                odds_count = cursor.fetchone()[0]

                # 获取博彩公司数量
                cursor.execute('SELECT COUNT(DISTINCT company_name) FROM odds WHERE match_id = ?', (match_id,))
                company_count = cursor.fetchone()[0]

                # 获取最后更新时间
                cursor.execute('SELECT MAX(extraction_time) FROM odds WHERE match_id = ?', (match_id,))
                last_update = cursor.fetchone()[0]

                # 检查详细数据是否存在
                has_enhanced_data = False
                enhanced_data_counts = {}

                # 检查各个扩展表
                extended_tables = ['technical_stats', 'lineups', 'player_stats', 'goal_probability']

                for table in extended_tables:
                    try:
                        cursor.execute(f'SELECT COUNT(*) FROM {table} WHERE match_id = ?', (match_id,))
                        count = cursor.fetchone()[0]
                        enhanced_data_counts[table] = count
                        if count > 0:
                            has_enhanced_data = True
                    except sqlite3.OperationalError:
                        # 表不存在，跳过
                        enhanced_data_counts[table] = 0
                        continue

                return {
                    'match_exists': match_exists,
                    'odds_count': odds_count,
                    'company_count': company_count,
                    'last_update': last_update,
                    'has_complete_data': match_exists and odds_count > 0,
                    'has_enhanced_data': has_enhanced_data,
                    'enhanced_data_counts': enhanced_data_counts
                }

        except sqlite3.Error as e:
            logger.error(f"获取比赛数据摘要失败: {e}")
            return {
                'match_exists': False,
                'odds_count': 0,
                'company_count': 0,
                'last_update': None,
                'has_complete_data': False,
                'has_enhanced_data': False,
                'enhanced_data_counts': {}
            }

    def cleanup_old_data(self, days_to_keep: int = 30) -> Dict:
        """清理旧数据"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()

                # 计算截止日期
                cutoff_date = datetime.now().replace(hour=0, minute=0, second=0, microsecond=0)
                cutoff_date = cutoff_date.replace(day=cutoff_date.day - days_to_keep)
                cutoff_str = cutoff_date.isoformat()

                # 统计要删除的数据
                cursor.execute('SELECT COUNT(*) FROM matches WHERE created_at < ?', (cutoff_str,))
                matches_to_delete = cursor.fetchone()[0]

                cursor.execute('''
                    SELECT COUNT(*) FROM odds
                    WHERE match_id IN (
                        SELECT match_id FROM matches WHERE created_at < ?
                    )
                ''', (cutoff_str,))
                odds_to_delete = cursor.fetchone()[0]

                # 删除旧的赔率数据
                cursor.execute('''
                    DELETE FROM odds
                    WHERE match_id IN (
                        SELECT match_id FROM matches WHERE created_at < ?
                    )
                ''', (cutoff_str,))

                # 删除旧的比赛数据
                cursor.execute('DELETE FROM matches WHERE created_at < ?', (cutoff_str,))

                conn.commit()

                # 执行VACUUM来回收空间
                cursor.execute('VACUUM')

                logger.info(f"清理完成: 删除了 {matches_to_delete} 场比赛和 {odds_to_delete} 条赔率记录")

                return {
                    'matches_deleted': matches_to_delete,
                    'odds_deleted': odds_to_delete,
                    'success': True
                }

        except sqlite3.Error as e:
            logger.error(f"清理旧数据失败: {e}")
            return {
                'matches_deleted': 0,
                'odds_deleted': 0,
                'success': False,
                'error': str(e)
            }

    def optimize_database(self) -> Dict:
        """优化数据库"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()

                # 获取优化前的大小
                old_size = os.path.getsize(self.db_path)

                # 重建索引
                cursor.execute('REINDEX')

                # 分析表统计信息
                cursor.execute('ANALYZE')

                # 压缩数据库
                cursor.execute('VACUUM')

                conn.commit()

                # 获取优化后的大小
                new_size = os.path.getsize(self.db_path)
                saved_space = old_size - new_size

                logger.info(f"数据库优化完成: 节省了 {saved_space / 1024 / 1024:.2f} MB 空间")

                return {
                    'old_size': old_size,
                    'new_size': new_size,
                    'saved_space': saved_space,
                    'saved_space_mb': round(saved_space / 1024 / 1024, 2),
                    'success': True
                }

        except sqlite3.Error as e:
            logger.error(f"数据库优化失败: {e}")
            return {
                'success': False,
                'error': str(e)
            }

    def backup_database(self, backup_path: str = None) -> Dict:
        """备份数据库"""
        try:
            if backup_path is None:
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                backup_path = f"odds_data_backup_{timestamp}.db"

            # 使用SQLite的备份API
            with sqlite3.connect(self.db_path) as source:
                with sqlite3.connect(backup_path) as backup:
                    source.backup(backup)

            backup_size = os.path.getsize(backup_path)

            logger.info(f"数据库备份完成: {backup_path}")

            return {
                'backup_path': backup_path,
                'backup_size': backup_size,
                'backup_size_mb': round(backup_size / 1024 / 1024, 2),
                'success': True
            }

        except Exception as e:
            logger.error(f"数据库备份失败: {e}")
            return {
                'success': False,
                'error': str(e)
            }

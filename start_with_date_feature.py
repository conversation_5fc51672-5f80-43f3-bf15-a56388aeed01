#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
启动带有按日期抓取功能的主程序
"""

import sys
import os

def main():
    """启动主程序"""
    
    print("🚀 启动足球赔率抓取器（含按日期抓取功能）")
    print("=" * 60)
    
    print("📋 新功能说明:")
    print("✅ 按日期抓取功能已添加到'联赛批量抓取'标签")
    print("✅ 支持从日期URL自动提取比赛信息")
    print("✅ 提供可视化的联赛选择界面")
    print("✅ 支持全选、反选等批量操作")
    print("✅ 复用现有的抓取逻辑和参数设置")
    print("")
    
    print("📝 使用步骤:")
    print("1. 启动程序后，切换到'联赛批量抓取'标签")
    print("2. 在'日期URL'输入框中输入日期页面URL")
    print("   示例: https://m.titan007.com/Schedule.htm?date=2025-07-22")
    print("3. 点击'按日期抓取'按钮")
    print("4. 在弹出的对话框中选择要抓取的联赛")
    print("5. 点击'确定'开始批量抓取")
    print("")
    
    print("🎯 功能特点:")
    print("- 智能解析日期页面，自动提取所有比赛")
    print("- 按联赛分组显示，便于选择")
    print("- 支持跨联赛选择比赛")
    print("- 显示每个联赛的比赛预览")
    print("- 实时统计选择的联赛数和比赛数")
    print("- 支持普通模式和并发模式抓取")
    print("")
    
    print("⚠️ 注意事项:")
    print("- 确保输入的日期URL格式正确")
    print("- 需要稳定的网络连接")
    print("- 建议合理设置抓取延迟")
    print("- 选择比赛数量时考虑系统性能")
    print("")
    
    print("🎉 正在启动程序...")
    
    try:
        # 启动主程序
        from odds_scraper_ui import main as start_main_ui
        start_main_ui()
        
    except KeyboardInterrupt:
        print("\n👋 用户中断，程序退出")
    except Exception as e:
        print(f"\n❌ 程序启动失败: {e}")
        print("\n🔧 故障排除建议:")
        print("1. 检查是否安装了所有必需的依赖包")
        print("2. 确认Python版本兼容性")
        print("3. 检查文件完整性")
        print("4. 查看详细错误信息进行调试")

if __name__ == "__main__":
    main()

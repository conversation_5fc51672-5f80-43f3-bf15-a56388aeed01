#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
高级特征工程与数据预处理
整合所有特征数据，处理缺失值、异常值，构建最终的特征矩阵
"""

import pandas as pd
import numpy as np
import json
import os
from datetime import datetime
from sklearn.preprocessing import StandardScaler, LabelEncoder, RobustScaler
from sklearn.impute import SimpleImputer, KNNImputer
from sklearn.feature_selection import SelectKBest, f_classif, mutual_info_classif
from sklearn.decomposition import PCA
import warnings
warnings.filterwarnings('ignore')

class AdvancedFeatureEngineer:
    def __init__(self):
        self.feature_data = None
        self.anomaly_data = None
        self.sync_data = None
        self.leading_data = None
        self.consistency_data = None
        
        self.final_features = None
        self.target_encoder = LabelEncoder()
        self.feature_scaler = RobustScaler()
        self.feature_selector = None
        
    def load_all_data(self):
        """加载所有相关数据文件"""
        print("=== 加载所有数据文件 ===")
        
        # 加载特征数据
        feature_files = [f for f in os.listdir('.') if f.startswith('odds_features_') and f.endswith('.csv')]
        if feature_files:
            latest_feature_file = sorted(feature_files)[-1]
            self.feature_data = pd.read_csv(latest_feature_file)
            print(f"加载特征数据: {latest_feature_file} ({len(self.feature_data)}条记录)")
        
        # 加载异常数据
        anomaly_files = [f for f in os.listdir('.') if f.startswith('anomaly_analysis_') and f.endswith('_with_anomalies.csv')]
        if anomaly_files:
            latest_anomaly_file = sorted(anomaly_files)[-1]
            self.anomaly_data = pd.read_csv(latest_anomaly_file)
            print(f"加载异常数据: {latest_anomaly_file}")
        
        # 加载同步分析数据
        sync_files = [f for f in os.listdir('.') if f.startswith('sync_analysis_') and f.endswith('.json')]
        if sync_files:
            latest_sync_file = sorted(sync_files)[-1]
            with open(latest_sync_file, 'r', encoding='utf-8') as f:
                self.sync_data = json.load(f)
            print(f"加载同步数据: {latest_sync_file}")
        
        # 加载领涨分析数据
        leading_files = [f for f in os.listdir('.') if f.startswith('leading_analysis_') and f.endswith('.json')]
        if leading_files:
            latest_leading_file = sorted(leading_files)[-1]
            with open(latest_leading_file, 'r', encoding='utf-8') as f:
                self.leading_data = json.load(f)
            print(f"加载领涨数据: {latest_leading_file}")
        
        # 加载一致性数据
        consistency_files = [f for f in os.listdir('.') if f.startswith('consistency_analysis_') and f.endswith('.json')]
        if consistency_files:
            latest_consistency_file = sorted(consistency_files)[-1]
            with open(latest_consistency_file, 'r', encoding='utf-8') as f:
                self.consistency_data = json.load(f)
            print(f"加载一致性数据: {latest_consistency_file}")
        
        return self.feature_data is not None
    
    def create_company_features(self):
        """创建公司级别的特征"""
        print("\n=== 创建公司级别特征 ===")
        
        if self.feature_data is None:
            return None
        
        company_features = {}
        
        # 从同步数据中提取公司特征
        if self.sync_data:
            company_sync_stats = {}
            for db_name, db_results in self.sync_data.items():
                for match_id, match_result in db_results.items():
                    for event in match_result.get('sync_events', []):
                        for company in event.get('companies', []):
                            if company not in company_sync_stats:
                                company_sync_stats[company] = {
                                    'sync_count': 0,
                                    'avg_sync_score': 0,
                                    'sync_scores': []
                                }
                            company_sync_stats[company]['sync_count'] += 1
                            sync_score = event.get('sync_analysis', {}).get('sync_score', 0)
                            company_sync_stats[company]['sync_scores'].append(sync_score)
            
            # 计算平均值
            for company, stats in company_sync_stats.items():
                if stats['sync_scores']:
                    stats['avg_sync_score'] = np.mean(stats['sync_scores'])
                    stats['sync_volatility'] = np.std(stats['sync_scores'])
            
            company_features['sync_stats'] = company_sync_stats
        
        # 从领涨数据中提取公司特征
        if self.leading_data:
            company_leading_stats = {}
            for db_name, db_results in self.leading_data.items():
                for match_id, match_result in db_results.items():
                    for event in match_result.get('leading_events', []):
                        leader = event.get('leader', {}).get('company')
                        if leader:
                            if leader not in company_leading_stats:
                                company_leading_stats[leader] = {
                                    'leading_count': 0,
                                    'avg_follow_strength': 0,
                                    'follow_strengths': []
                                }
                            company_leading_stats[leader]['leading_count'] += 1
                            follow_strength = event.get('follow_strength', 0)
                            company_leading_stats[leader]['follow_strengths'].append(follow_strength)
            
            # 计算平均值
            for company, stats in company_leading_stats.items():
                if stats['follow_strengths']:
                    stats['avg_follow_strength'] = np.mean(stats['follow_strengths'])
                    stats['leading_volatility'] = np.std(stats['follow_strengths'])
            
            company_features['leading_stats'] = company_leading_stats
        
        return company_features
    
    def merge_all_features(self):
        """合并所有特征数据"""
        print("\n=== 合并所有特征数据 ===")
        
        if self.feature_data is None:
            return None
        
        # 从基础特征数据开始
        merged_data = self.feature_data.copy()
        
        # 合并异常标记
        if self.anomaly_data is not None:
            anomaly_cols = ['is_anomaly', 'anomaly_score']
            for col in anomaly_cols:
                if col in self.anomaly_data.columns:
                    # 基于match_id和company进行合并，确保数据类型一致
                    anomaly_subset = self.anomaly_data[['match_id', 'company', col]].drop_duplicates()
                    # 确保match_id类型一致
                    anomaly_subset['match_id'] = anomaly_subset['match_id'].astype(str)
                    merged_data['match_id'] = merged_data['match_id'].astype(str)
                    merged_data = merged_data.merge(
                        anomaly_subset,
                        on=['match_id', 'company'],
                        how='left'
                    )
        
        # 添加公司级别特征
        company_features = self.create_company_features()
        if company_features:
            # 同步特征
            if 'sync_stats' in company_features:
                sync_stats = company_features['sync_stats']
                merged_data['company_sync_count'] = merged_data['company'].map(
                    lambda x: sync_stats.get(x, {}).get('sync_count', 0)
                )
                merged_data['company_avg_sync_score'] = merged_data['company'].map(
                    lambda x: sync_stats.get(x, {}).get('avg_sync_score', 0)
                )
                merged_data['company_sync_volatility'] = merged_data['company'].map(
                    lambda x: sync_stats.get(x, {}).get('sync_volatility', 0)
                )
            
            # 领涨特征
            if 'leading_stats' in company_features:
                leading_stats = company_features['leading_stats']
                merged_data['company_leading_count'] = merged_data['company'].map(
                    lambda x: leading_stats.get(x, {}).get('leading_count', 0)
                )
                merged_data['company_avg_follow_strength'] = merged_data['company'].map(
                    lambda x: leading_stats.get(x, {}).get('avg_follow_strength', 0)
                )
                merged_data['company_leading_volatility'] = merged_data['company'].map(
                    lambda x: leading_stats.get(x, {}).get('leading_volatility', 0)
                )
        
        # 添加比赛级别的一致性特征
        if self.consistency_data:
            consistency_features = self.extract_consistency_features()
            if consistency_features is not None:
                # 确保match_id类型一致
                consistency_features['match_id'] = consistency_features['match_id'].astype(str)
                merged_data['match_id'] = merged_data['match_id'].astype(str)
                merged_data = merged_data.merge(
                    consistency_features,
                    on='match_id',
                    how='left'
                )
        
        print(f"合并后数据形状: {merged_data.shape}")
        return merged_data
    
    def extract_consistency_features(self):
        """从一致性数据中提取特征"""
        consistency_features = []
        
        for db_name, db_results in self.consistency_data.items():
            for match_id, result in db_results.items():
                match_features = {'match_id': match_id}
                
                # 提取综合一致性特征
                comp_consistency = result.get('comprehensive_consistency', {})
                
                consistency_scores = []
                for odds_type, scores in comp_consistency.items():
                    comp_score = scores.get('comprehensive_score', 0)
                    consistency_scores.append(comp_score)
                    
                    # 各赔率类型的一致性
                    match_features[f'consistency_{odds_type}'] = comp_score
                    
                    # 各维度一致性
                    component_scores = scores.get('component_scores', {})
                    for component, score in component_scores.items():
                        match_features[f'{component}_consistency_{odds_type}'] = score
                
                # 整体一致性统计
                if consistency_scores:
                    match_features['avg_consistency'] = np.mean(consistency_scores)
                    match_features['std_consistency'] = np.std(consistency_scores)
                    match_features['min_consistency'] = np.min(consistency_scores)
                    match_features['max_consistency'] = np.max(consistency_scores)
                
                consistency_features.append(match_features)
        
        return pd.DataFrame(consistency_features) if consistency_features else None
    
    def handle_missing_values(self, data):
        """处理缺失值"""
        print("\n=== 处理缺失值 ===")
        
        # 分析缺失值情况
        missing_stats = data.isnull().sum()
        missing_features = missing_stats[missing_stats > 0]
        
        if len(missing_features) > 0:
            print(f"发现 {len(missing_features)} 个特征有缺失值:")
            for feature, count in missing_features.head(10).items():
                print(f"  {feature}: {count} ({count/len(data)*100:.1f}%)")
        
        # 分离数值特征和分类特征
        numeric_features = data.select_dtypes(include=[np.number]).columns.tolist()
        categorical_features = data.select_dtypes(include=['object']).columns.tolist()
        
        # 排除目标变量和标识符
        exclude_cols = ['match_id', 'company', 'match_result', 'home_team', 'away_team']
        numeric_features = [f for f in numeric_features if f not in exclude_cols]
        categorical_features = [f for f in categorical_features if f not in exclude_cols]
        
        # 处理数值特征的缺失值
        if numeric_features:
            # 对于缺失率低的特征使用KNN填充，高的使用中位数填充
            low_missing_features = []
            high_missing_features = []
            
            for feature in numeric_features:
                missing_rate = data[feature].isnull().sum() / len(data)
                if missing_rate < 0.3:
                    low_missing_features.append(feature)
                else:
                    high_missing_features.append(feature)
            
            # KNN填充（缺失率低的特征）
            if low_missing_features:
                knn_imputer = KNNImputer(n_neighbors=5)
                data[low_missing_features] = knn_imputer.fit_transform(data[low_missing_features])
            
            # 中位数填充（缺失率高的特征）
            if high_missing_features:
                median_imputer = SimpleImputer(strategy='median')
                data[high_missing_features] = median_imputer.fit_transform(data[high_missing_features])
        
        # 处理分类特征的缺失值
        if categorical_features:
            mode_imputer = SimpleImputer(strategy='most_frequent')
            data[categorical_features] = mode_imputer.fit_transform(data[categorical_features])
        
        print(f"缺失值处理完成，剩余缺失值: {data.isnull().sum().sum()}")
        return data
    
    def handle_outliers(self, data):
        """处理异常值"""
        print("\n=== 处理异常值 ===")
        
        numeric_features = data.select_dtypes(include=[np.number]).columns.tolist()
        exclude_cols = ['match_id', 'company', 'match_result', 'home_team', 'away_team']
        numeric_features = [f for f in numeric_features if f not in exclude_cols]
        
        outlier_counts = {}
        
        for feature in numeric_features:
            if data[feature].dtype in [np.float64, np.int64]:
                # 使用IQR方法检测异常值
                Q1 = data[feature].quantile(0.25)
                Q3 = data[feature].quantile(0.75)
                IQR = Q3 - Q1
                
                lower_bound = Q1 - 1.5 * IQR
                upper_bound = Q3 + 1.5 * IQR
                
                outliers = (data[feature] < lower_bound) | (data[feature] > upper_bound)
                outlier_count = outliers.sum()
                
                if outlier_count > 0:
                    outlier_counts[feature] = outlier_count
                    
                    # 使用Winsorization处理异常值（限制在5%-95%分位数范围内）
                    data[feature] = np.clip(
                        data[feature], 
                        data[feature].quantile(0.05), 
                        data[feature].quantile(0.95)
                    )
        
        if outlier_counts:
            print(f"处理了 {len(outlier_counts)} 个特征的异常值:")
            for feature, count in sorted(outlier_counts.items(), key=lambda x: x[1], reverse=True)[:10]:
                print(f"  {feature}: {count}个异常值")
        
        return data
    
    def create_interaction_features(self, data):
        """创建交互特征"""
        print("\n=== 创建交互特征 ===")
        
        # 创建一些重要的交互特征
        interaction_features = []
        
        # 赔率类型间的交互
        odds_types = ['home', 'draw', 'away']
        for i, odds1 in enumerate(odds_types):
            for j, odds2 in enumerate(odds_types):
                if i < j:
                    # 波动性交互
                    vol1_col = f'volatility_{odds1}'
                    vol2_col = f'volatility_{odds2}'
                    if vol1_col in data.columns and vol2_col in data.columns:
                        interaction_name = f'volatility_interaction_{odds1}_{odds2}'
                        data[interaction_name] = data[vol1_col] * data[vol2_col]
                        interaction_features.append(interaction_name)
                    
                    # 趋势交互
                    trend1_col = f'trend_slope_{odds1}'
                    trend2_col = f'trend_slope_{odds2}'
                    if trend1_col in data.columns and trend2_col in data.columns:
                        interaction_name = f'trend_interaction_{odds1}_{odds2}'
                        data[interaction_name] = data[trend1_col] * data[trend2_col]
                        interaction_features.append(interaction_name)
        
        # 公司特征与赔率特征的交互
        company_features = [col for col in data.columns if col.startswith('company_')]
        odds_features = [col for col in data.columns if any(odds in col for odds in odds_types)]
        
        # 选择几个重要的交互
        if 'company_avg_sync_score' in data.columns and 'volatility_home' in data.columns:
            data['sync_volatility_interaction'] = data['company_avg_sync_score'] * data['volatility_home']
            interaction_features.append('sync_volatility_interaction')
        
        if 'company_leading_count' in data.columns and 'trend_slope_home' in data.columns:
            data['leading_trend_interaction'] = data['company_leading_count'] * data['trend_slope_home']
            interaction_features.append('leading_trend_interaction')
        
        print(f"创建了 {len(interaction_features)} 个交互特征")
        return data
    
    def prepare_final_dataset(self):
        """准备最终的数据集"""
        print("\n=== 准备最终数据集 ===")
        
        # 合并所有特征
        merged_data = self.merge_all_features()
        if merged_data is None:
            print("数据合并失败")
            return None
        
        # 处理缺失值
        merged_data = self.handle_missing_values(merged_data)
        
        # 处理异常值
        merged_data = self.handle_outliers(merged_data)
        
        # 创建交互特征
        merged_data = self.create_interaction_features(merged_data)
        
        # 过滤有效记录（有比赛结果的记录）
        valid_data = merged_data[merged_data['match_result'].notna()].copy()
        
        print(f"最终数据集形状: {valid_data.shape}")
        print(f"特征数量: {len(valid_data.columns) - 5}")  # 减去标识符列
        
        # 编码目标变量
        valid_data['target'] = self.target_encoder.fit_transform(valid_data['match_result'])
        
        self.final_features = valid_data
        return valid_data
    
    def save_processed_data(self, filename_prefix="processed_features"):
        """保存处理后的数据"""
        if self.final_features is None:
            print("没有处理后的数据可保存")
            return None
        
        output_file = f"{filename_prefix}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
        self.final_features.to_csv(output_file, index=False, encoding='utf-8')
        
        # 保存特征信息
        feature_info = {
            'total_features': len(self.final_features.columns),
            'total_samples': len(self.final_features),
            'target_classes': list(self.target_encoder.classes_),
            'feature_columns': list(self.final_features.columns),
            'class_distribution': self.final_features['match_result'].value_counts().to_dict()
        }
        
        info_file = output_file.replace('.csv', '_info.json')
        with open(info_file, 'w', encoding='utf-8') as f:
            json.dump(feature_info, f, ensure_ascii=False, indent=2)
        
        print(f"\n处理后的数据已保存到: {output_file}")
        print(f"特征信息已保存到: {info_file}")
        
        return output_file

def main():
    engineer = AdvancedFeatureEngineer()
    
    # 加载所有数据
    if not engineer.load_all_data():
        print("数据加载失败")
        return
    
    # 准备最终数据集
    final_data = engineer.prepare_final_dataset()
    
    if final_data is not None:
        # 保存处理后的数据
        engineer.save_processed_data()
        
        # 输出数据集统计信息
        print(f"\n=== 最终数据集统计 ===")
        print(f"样本数: {len(final_data)}")
        print(f"特征数: {len(final_data.columns) - 5}")
        print(f"类别分布:")
        for result, count in final_data['match_result'].value_counts().items():
            print(f"  {result}: {count} ({count/len(final_data)*100:.1f}%)")

if __name__ == "__main__":
    main()

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
模型验证与评估
使用时间序列交叉验证，评估模型性能，防止数据泄漏
"""

import pandas as pd
import numpy as np
import json
import os
from datetime import datetime
import matplotlib.pyplot as plt
import seaborn as sns

from sklearn.model_selection import TimeSeriesSplit, cross_val_score
from sklearn.linear_model import LogisticRegression
from sklearn.ensemble import RandomForestClassifier, GradientBoostingClassifier
from sklearn.preprocessing import StandardScaler, LabelEncoder
from sklearn.metrics import (
    classification_report, confusion_matrix, accuracy_score, 
    precision_recall_fscore_support, roc_auc_score, log_loss,
    precision_recall_curve, roc_curve
)
from sklearn.calibration import calibration_curve, CalibratedClassifierCV
import warnings
warnings.filterwarnings('ignore')

class ModelValidationEvaluator:
    def __init__(self, processed_data_file=None):
        self.processed_data_file = processed_data_file
        self.data = None
        self.X = None
        self.y = None
        self.feature_names = None
        self.target_encoder = LabelEncoder()
        self.scaler = StandardScaler()
        
        self.validation_results = {}
        
    def load_processed_data(self, filename=None):
        """加载处理后的数据"""
        if filename:
            self.processed_data_file = filename
        
        if not self.processed_data_file:
            # 查找最新的处理数据文件
            files = [f for f in os.listdir('.') if f.startswith('processed_features_') and f.endswith('.csv')]
            if files:
                self.processed_data_file = sorted(files)[-1]
                print(f"自动选择最新文件: {self.processed_data_file}")
            else:
                print("未找到处理数据文件")
                return False
        
        self.data = pd.read_csv(self.processed_data_file)
        print(f"加载处理数据: {self.processed_data_file}")
        print(f"数据形状: {self.data.shape}")
        
        return True
    
    def prepare_data_for_validation(self):
        """为验证准备数据"""
        print("\n=== 准备验证数据 ===")
        
        # 排除非特征列
        exclude_cols = ['match_id', 'company', 'match_result', 'home_team', 'away_team', 'target']
        feature_cols = [col for col in self.data.columns if col not in exclude_cols]
        
        self.X = self.data[feature_cols]
        self.y = self.data['match_result']
        self.feature_names = feature_cols
        
        # 编码目标变量
        self.y_encoded = self.target_encoder.fit_transform(self.y)
        
        # 标准化特征
        self.X_scaled = self.scaler.fit_transform(self.X)
        
        print(f"特征数量: {len(feature_cols)}")
        print(f"样本数量: {len(self.X)}")
        print(f"类别分布: {dict(self.y.value_counts())}")
        
        return True
    
    def time_series_cross_validation(self, model, model_name, n_splits=3):
        """时间序列交叉验证"""
        print(f"\n=== {model_name} 时间序列交叉验证 ===")

        # 由于样本数较少，减少分割数
        actual_splits = min(n_splits, len(self.X_scaled) // 10)  # 确保每折至少有10个样本
        if actual_splits < 2:
            actual_splits = 2

        print(f"  使用 {actual_splits} 折交叉验证")

        # 创建时间序列分割器
        tscv = TimeSeriesSplit(n_splits=actual_splits)
        
        fold_results = []
        all_predictions = []
        all_true_labels = []
        all_probabilities = []
        
        for fold, (train_idx, test_idx) in enumerate(tscv.split(self.X_scaled), 1):
            print(f"  折 {fold}/{actual_splits}")

            # 分割数据
            X_train, X_test = self.X_scaled[train_idx], self.X_scaled[test_idx]
            y_train, y_test = self.y_encoded[train_idx], self.y_encoded[test_idx]

            # 检查训练集是否包含所有类别
            unique_classes_train = np.unique(y_train)
            unique_classes_test = np.unique(y_test)

            print(f"    训练集类别: {unique_classes_train}, 测试集类别: {unique_classes_test}")

            # 如果训练集类别不足，跳过这一折
            if len(unique_classes_train) < 2:
                print(f"    跳过折 {fold}：训练集类别不足")
                continue

            # 训练模型
            try:
                model_copy = type(model)(**model.get_params())
                model_copy.fit(X_train, y_train)
            except Exception as e:
                print(f"    训练失败: {e}")
                continue
            
            # 预测
            y_pred = model_copy.predict(X_test)
            y_pred_proba = model_copy.predict_proba(X_test)
            
            # 计算指标
            accuracy = accuracy_score(y_test, y_pred)
            precision, recall, f1, _ = precision_recall_fscore_support(
                y_test, y_pred, average='weighted', zero_division=0
            )
            
            # 计算对数损失
            try:
                logloss = log_loss(y_test, y_pred_proba)
            except:
                logloss = np.nan
            
            # 计算AUC（多分类）
            try:
                auc = roc_auc_score(y_test, y_pred_proba, multi_class='ovr', average='weighted')
            except:
                auc = np.nan
            
            fold_result = {
                'fold': fold,
                'train_size': len(train_idx),
                'test_size': len(test_idx),
                'accuracy': accuracy,
                'precision': precision,
                'recall': recall,
                'f1_score': f1,
                'log_loss': logloss,
                'auc_score': auc
            }
            
            fold_results.append(fold_result)
            all_predictions.extend(y_pred)
            all_true_labels.extend(y_test)
            all_probabilities.extend(y_pred_proba)
            
            print(f"    训练集大小: {len(train_idx)}, 测试集大小: {len(test_idx)}")
            print(f"    准确率: {accuracy:.3f}, F1: {f1:.3f}")
        
        # 计算总体统计
        overall_accuracy = accuracy_score(all_true_labels, all_predictions)
        overall_precision, overall_recall, overall_f1, _ = precision_recall_fscore_support(
            all_true_labels, all_predictions, average='weighted', zero_division=0
        )
        
        # 计算各折的平均值和标准差
        metrics = ['accuracy', 'precision', 'recall', 'f1_score', 'log_loss', 'auc_score']
        cv_stats = {}
        
        for metric in metrics:
            values = [fold[metric] for fold in fold_results if not np.isnan(fold[metric])]
            if values:
                cv_stats[f'{metric}_mean'] = np.mean(values)
                cv_stats[f'{metric}_std'] = np.std(values)
            else:
                cv_stats[f'{metric}_mean'] = np.nan
                cv_stats[f'{metric}_std'] = np.nan
        
        validation_result = {
            'model_name': model_name,
            'fold_results': fold_results,
            'cv_stats': cv_stats,
            'overall_accuracy': overall_accuracy,
            'overall_precision': overall_precision,
            'overall_recall': overall_recall,
            'overall_f1': overall_f1,
            'all_predictions': all_predictions,
            'all_true_labels': all_true_labels,
            'all_probabilities': all_probabilities
        }
        
        print(f"  总体结果:")
        print(f"    准确率: {overall_accuracy:.3f} (CV: {cv_stats['accuracy_mean']:.3f}±{cv_stats['accuracy_std']:.3f})")
        print(f"    F1分数: {overall_f1:.3f} (CV: {cv_stats['f1_score_mean']:.3f}±{cv_stats['f1_score_std']:.3f})")
        
        return validation_result
    
    def validate_multiple_models(self):
        """验证多个模型"""
        print("\n=== 多模型验证 ===")
        
        # 定义要验证的模型
        models_to_validate = {
            'logistic_regression': LogisticRegression(
                random_state=42, max_iter=1000, class_weight='balanced'
            ),
            'random_forest': RandomForestClassifier(
                random_state=42, n_estimators=100, class_weight='balanced'
            ),
            'gradient_boosting': GradientBoostingClassifier(
                random_state=42, n_estimators=100
            )
        }
        
        for model_name, model in models_to_validate.items():
            validation_result = self.time_series_cross_validation(model, model_name)
            self.validation_results[model_name] = validation_result
        
        return self.validation_results
    
    def analyze_prediction_stability(self):
        """分析预测稳定性"""
        print("\n=== 预测稳定性分析 ===")
        
        stability_analysis = {}
        
        for model_name, result in self.validation_results.items():
            fold_results = result['fold_results']
            
            # 计算各折间的性能变异性
            accuracies = [fold['accuracy'] for fold in fold_results]
            f1_scores = [fold['f1_score'] for fold in fold_results]
            
            stability_metrics = {
                'accuracy_cv': np.std(accuracies) / np.mean(accuracies) if np.mean(accuracies) > 0 else np.inf,
                'f1_cv': np.std(f1_scores) / np.mean(f1_scores) if np.mean(f1_scores) > 0 else np.inf,
                'accuracy_range': max(accuracies) - min(accuracies),
                'f1_range': max(f1_scores) - min(f1_scores),
                'consistent_performance': np.std(accuracies) < 0.05  # 标准差小于5%认为稳定
            }
            
            stability_analysis[model_name] = stability_metrics
            
            print(f"  {model_name}:")
            print(f"    准确率变异系数: {stability_metrics['accuracy_cv']:.3f}")
            print(f"    F1变异系数: {stability_metrics['f1_cv']:.3f}")
            print(f"    性能稳定: {'是' if stability_metrics['consistent_performance'] else '否'}")
        
        return stability_analysis
    
    def evaluate_class_specific_performance(self):
        """评估类别特定性能"""
        print("\n=== 类别特定性能评估 ===")
        
        class_performance = {}
        
        for model_name, result in self.validation_results.items():
            true_labels = result['all_true_labels']
            predictions = result['all_predictions']
            
            # 生成分类报告
            class_report = classification_report(
                true_labels, predictions, 
                target_names=self.target_encoder.classes_,
                output_dict=True, zero_division=0
            )
            
            # 生成混淆矩阵
            conf_matrix = confusion_matrix(true_labels, predictions)
            
            class_performance[model_name] = {
                'classification_report': class_report,
                'confusion_matrix': conf_matrix.tolist()
            }
            
            print(f"  {model_name} 分类报告:")
            for class_name in self.target_encoder.classes_:
                if class_name in class_report:
                    metrics = class_report[class_name]
                    print(f"    {class_name}: P={metrics['precision']:.3f}, "
                          f"R={metrics['recall']:.3f}, F1={metrics['f1-score']:.3f}")
        
        return class_performance
    
    def detect_overfitting(self):
        """检测过拟合"""
        print("\n=== 过拟合检测 ===")
        
        overfitting_analysis = {}
        
        for model_name, result in self.validation_results.items():
            fold_results = result['fold_results']
            
            # 分析训练集和验证集性能差异
            # 注意：这里我们使用交叉验证的结果作为验证性能
            cv_accuracy = result['cv_stats']['accuracy_mean']
            
            # 训练一个完整模型来获取训练性能
            if model_name == 'logistic_regression':
                full_model = LogisticRegression(random_state=42, max_iter=1000, class_weight='balanced')
            elif model_name == 'random_forest':
                full_model = RandomForestClassifier(random_state=42, n_estimators=100, class_weight='balanced')
            elif model_name == 'gradient_boosting':
                full_model = GradientBoostingClassifier(random_state=42, n_estimators=100)
            
            full_model.fit(self.X_scaled, self.y_encoded)
            train_accuracy = full_model.score(self.X_scaled, self.y_encoded)
            
            # 计算过拟合指标
            overfitting_gap = train_accuracy - cv_accuracy
            overfitting_ratio = overfitting_gap / train_accuracy if train_accuracy > 0 else 0
            
            is_overfitting = overfitting_gap > 0.1  # 差距超过10%认为过拟合
            
            overfitting_analysis[model_name] = {
                'train_accuracy': train_accuracy,
                'cv_accuracy': cv_accuracy,
                'overfitting_gap': overfitting_gap,
                'overfitting_ratio': overfitting_ratio,
                'is_overfitting': is_overfitting
            }
            
            print(f"  {model_name}:")
            print(f"    训练准确率: {train_accuracy:.3f}")
            print(f"    交叉验证准确率: {cv_accuracy:.3f}")
            print(f"    过拟合差距: {overfitting_gap:.3f}")
            print(f"    是否过拟合: {'是' if is_overfitting else '否'}")
        
        return overfitting_analysis
    
    def generate_validation_report(self):
        """生成验证报告"""
        print("\n=== 生成验证报告 ===")
        
        # 分析预测稳定性
        stability_analysis = self.analyze_prediction_stability()
        
        # 评估类别特定性能
        class_performance = self.evaluate_class_specific_performance()
        
        # 检测过拟合
        overfitting_analysis = self.detect_overfitting()
        
        # 汇总报告
        validation_report = {
            'validation_results': self.validation_results,
            'stability_analysis': stability_analysis,
            'class_performance': class_performance,
            'overfitting_analysis': overfitting_analysis,
            'summary': self._generate_summary()
        }
        
        return validation_report
    
    def _generate_summary(self):
        """生成总结"""
        summary = {
            'best_model': None,
            'best_cv_accuracy': 0,
            'most_stable_model': None,
            'recommendations': []
        }
        
        # 找到最佳模型
        for model_name, result in self.validation_results.items():
            cv_accuracy = result['cv_stats']['accuracy_mean']
            if cv_accuracy > summary['best_cv_accuracy']:
                summary['best_cv_accuracy'] = cv_accuracy
                summary['best_model'] = model_name
        
        # 找到最稳定的模型
        min_cv = float('inf')
        for model_name, result in self.validation_results.items():
            cv_std = result['cv_stats']['accuracy_std']
            if cv_std < min_cv:
                min_cv = cv_std
                summary['most_stable_model'] = model_name
        
        # 生成建议
        if summary['best_cv_accuracy'] > 0.8:
            summary['recommendations'].append("模型性能良好，可以考虑部署")
        else:
            summary['recommendations'].append("模型性能需要改进，建议增加更多特征或数据")
        
        if min_cv < 0.05:
            summary['recommendations'].append("模型稳定性良好")
        else:
            summary['recommendations'].append("模型稳定性需要改进，建议使用更多数据或正则化")
        
        return summary
    
    def save_validation_results(self, filename_prefix="model_validation"):
        """保存验证结果"""
        validation_report = self.generate_validation_report()
        
        # 转换为可序列化的格式
        serializable_report = self._make_serializable(validation_report)
        
        output_file = f"{filename_prefix}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(serializable_report, f, ensure_ascii=False, indent=2)
        
        print(f"\n验证结果已保存到: {output_file}")
        
        # 输出总结
        summary = validation_report['summary']
        print(f"\n=== 验证总结 ===")
        print(f"最佳模型: {summary['best_model']} (CV准确率: {summary['best_cv_accuracy']:.3f})")
        print(f"最稳定模型: {summary['most_stable_model']}")
        print(f"建议:")
        for rec in summary['recommendations']:
            print(f"  - {rec}")
        
        return output_file
    
    def _make_serializable(self, obj):
        """将对象转换为可序列化的格式"""
        if isinstance(obj, dict):
            return {k: self._make_serializable(v) for k, v in obj.items()}
        elif isinstance(obj, (list, tuple)):
            return [self._make_serializable(item) for item in obj]
        elif isinstance(obj, (np.integer, np.int64)):
            return int(obj)
        elif isinstance(obj, (np.floating, np.float64)):
            return float(obj)
        elif isinstance(obj, (np.bool_, bool)):
            return bool(obj)
        elif isinstance(obj, np.ndarray):
            return obj.tolist()
        elif pd.isna(obj) or obj is None:
            return None
        else:
            return str(obj)

def main():
    evaluator = ModelValidationEvaluator()
    
    # 加载处理后的数据
    if not evaluator.load_processed_data():
        return
    
    # 准备验证数据
    evaluator.prepare_data_for_validation()
    
    # 验证多个模型
    evaluator.validate_multiple_models()
    
    # 保存验证结果
    evaluator.save_validation_results()

if __name__ == "__main__":
    main()

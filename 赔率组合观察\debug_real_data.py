#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试真实数据
从实际的回测过程中获取详细信息
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def add_debug_to_backtest():
    """为回测功能添加调试输出"""
    
    print("🔧 修改回测功能以添加调试输出")
    print("=" * 50)
    
    # 读取当前的UI文件
    ui_file_path = "赔率组合观察/odds_combination_ui.py"
    
    try:
        with open(ui_file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查是否已经有调试输出
        if "DEBUG:" in content:
            print("✅ 调试输出已存在")
            return
        
        # 在perform_backtest方法中添加调试输出
        old_code = '''                        # 计算收益
                        if actual_result == "主胜":
                            return_value = home_odds
                            total_return += return_value
                            win_count += 1
                        else:
                            return_value = 0'''
        
        new_code = '''                        # 计算收益
                        if actual_result == "主胜":
                            return_value = home_odds
                            total_return += return_value
                            win_count += 1
                        else:
                            return_value = 0
                        
                        # DEBUG: 输出详细信息
                        print(f"DEBUG: 比赛{match_id}")
                        print(f"  组合内容: {combination_content}")
                        print(f"  实际结果: {actual_result}")
                        print(f"  总主胜: {total_wins_all}, 总匹配: {total_matches_all}")
                        print(f"  胜率: {win_rate:.3f}")
                        print(f"  主胜赔率: {home_odds}")
                        print(f"  投资价值: {investment_value:.3f}")
                        print(f"  回报: {return_value}")
                        print(f"  净利润: {return_value - 1}")
                        print("-" * 40)'''
        
        if old_code in content:
            content = content.replace(old_code, new_code)
            
            # 写回文件
            with open(ui_file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            
            print("✅ 成功添加调试输出到回测功能")
            print("📝 现在运行回测时会显示详细的调试信息")
            print("")
            print("🎯 使用方法:")
            print("1. 启动UI程序")
            print("2. 进行ID区间分析")
            print("3. 执行回测")
            print("4. 查看控制台的DEBUG输出")
            
        else:
            print("❌ 未找到目标代码段，可能代码结构已改变")
            
    except Exception as e:
        print(f"❌ 修改文件时发生错误: {e}")

def create_manual_test():
    """创建手动测试脚本"""
    
    test_script = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
手动测试回测功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def manual_backtest_test():
    """手动测试回测逻辑"""
    
    # 从图片中看到的实际数据
    test_cases = [
        {
            'match_id': '2596914',
            'combination_content': '1:(2.6,3.5,2.6)|2:(2.6,3.3,2.6)',  # 假设的格式
            'actual_result': '主胜',
            'total_wins_all': 11,
            'total_matches_all': 12,
            'expected_profit': 1.6  # 预期利润
        }
    ]
    
    def extract_home_odds_from_combination(combination_content: str) -> float:
        """从组合内容中提取第二组赔率的主胜赔率"""
        try:
            parts = combination_content.split('|')
            if len(parts) >= 2:
                second_part = parts[1]  # 第二组
                if ':(' in second_part and ')' in second_part:
                    odds_str = second_part.split(':(')[1].split(')')[0]
                    odds_values = odds_str.split(',')
                    if len(odds_values) >= 1:
                        return float(odds_values[0])  # 主胜赔率
            return 0.0
        except Exception as e:
            print(f"解析错误: {e}")
            return 0.0
    
    print("🧪 手动测试回测逻辑")
    print("=" * 50)
    
    for test_case in test_cases:
        print(f"\\n📊 测试案例: {test_case['match_id']}")
        print(f"组合内容: {test_case['combination_content']}")
        
        # 提取赔率
        home_odds = extract_home_odds_from_combination(test_case['combination_content'])
        print(f"提取的主胜赔率: {home_odds}")
        
        # 计算投资价值
        win_rate = test_case['total_wins_all'] / test_case['total_matches_all']
        investment_value = win_rate * home_odds
        
        print(f"胜率: {win_rate:.3f}")
        print(f"投资价值: {investment_value:.3f}")
        
        # 检查投资条件
        investment_threshold = 1.05
        match_threshold = 5
        
        if (test_case['total_wins_all'] >= match_threshold and 
            investment_value >= investment_threshold):
            
            print("✅ 满足投资条件")
            
            # 计算收益
            if test_case['actual_result'] == "主胜":
                return_value = home_odds
                profit = return_value - 1
                print(f"回报: {return_value}")
                print(f"净利润: {profit}")
                
                if profit != test_case['expected_profit']:
                    print(f"⚠️ 利润不符合预期! 预期: {test_case['expected_profit']}, 实际: {profit}")
                else:
                    print("✅ 利润计算正确")
            else:
                print("❌ 实际结果不是主胜")
        else:
            print("❌ 不满足投资条件")

if __name__ == "__main__":
    manual_backtest_test()
'''
    
    with open("赔率组合观察/manual_test_backtest.py", 'w', encoding='utf-8') as f:
        f.write(test_script)
    
    print("✅ 创建了手动测试脚本: manual_test_backtest.py")

if __name__ == "__main__":
    print("🔍 调试回测功能")
    print("=" * 50)
    
    add_debug_to_backtest()
    print("")
    create_manual_test()
    
    print("")
    print("🎯 下一步:")
    print("1. 运行UI程序并执行回测")
    print("2. 查看控制台的DEBUG输出")
    print("3. 或者运行 manual_test_backtest.py 进行手动测试")

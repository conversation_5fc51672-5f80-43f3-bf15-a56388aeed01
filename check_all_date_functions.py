#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
全面检查项目中所有涉及日期推测的功能
找出哪些地方还在使用推测而不是真实抓取
"""

import os
import re
from typing import List, Dict

def analyze_file_for_date_issues(file_path: str) -> Dict:
    """分析单个文件中的日期处理问题"""
    
    issues = {
        'file': file_path,
        'date_inference': [],      # 日期推测
        'year_inference': [],      # 年份推测
        'time_patterns': [],       # 时间模式匹配
        'url_date_extraction': [], # 从URL提取日期
        'current_year_usage': [],  # 使用当前年份
        'hardcoded_dates': [],     # 硬编码日期
        'needs_accurate_time': []  # 需要准确时间提取
    }
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
            lines = content.split('\n')
        
        for i, line in enumerate(lines, 1):
            line_lower = line.lower()
            
            # 检查日期推测相关代码
            if any(keyword in line_lower for keyword in ['infer', '推测', 'guess', '猜测']):
                if any(date_word in line_lower for date_word in ['year', 'date', 'time', '年份', '日期', '时间']):
                    issues['date_inference'].append({
                        'line': i,
                        'content': line.strip(),
                        'type': 'date_inference'
                    })
            
            # 检查年份推测
            if any(keyword in line_lower for keyword in ['_infer_match_year', 'infer_season', '推测年份']):
                issues['year_inference'].append({
                    'line': i,
                    'content': line.strip(),
                    'type': 'year_inference'
                })
            
            # 检查时间模式匹配（可能需要改进）
            if 'time_pattern' in line_lower or 'date_pattern' in line_lower:
                issues['time_patterns'].append({
                    'line': i,
                    'content': line.strip(),
                    'type': 'pattern_matching'
                })
            
            # 检查从URL提取日期
            if 'date=' in line and 'url' in line_lower:
                issues['url_date_extraction'].append({
                    'line': i,
                    'content': line.strip(),
                    'type': 'url_date_extraction'
                })
            
            # 检查使用当前年份
            if any(keyword in line for keyword in ['datetime.now().year', 'current_year', '当前年份']):
                issues['current_year_usage'].append({
                    'line': i,
                    'content': line.strip(),
                    'type': 'current_year_usage'
                })
            
            # 检查硬编码的年份范围
            if re.search(r'match_id.*[<>]=?\s*\d{7}', line):
                issues['hardcoded_dates'].append({
                    'line': i,
                    'content': line.strip(),
                    'type': 'hardcoded_id_range'
                })
            
            # 检查可能需要准确时间提取的地方
            if any(keyword in line_lower for keyword in ['extract_match_info', 'parse_league_text', '_extract_match_time']):
                if 'accurate_time' not in line_lower:
                    issues['needs_accurate_time'].append({
                        'line': i,
                        'content': line.strip(),
                        'type': 'potential_improvement'
                    })
    
    except Exception as e:
        print(f"分析文件 {file_path} 时出错: {e}")
    
    return issues

def find_all_python_files() -> List[str]:
    """找到所有Python文件"""
    python_files = []
    
    for root, dirs, files in os.walk('.'):
        # 跳过一些目录
        dirs[:] = [d for d in dirs if not d.startswith('.') and d not in ['__pycache__', 'venv', 'env']]
        
        for file in files:
            if file.endswith('.py'):
                file_path = os.path.join(root, file)
                python_files.append(file_path)
    
    return python_files

def analyze_all_files():
    """分析所有文件"""
    
    print("🔍 全面检查项目中的日期处理功能")
    print("=" * 60)
    
    python_files = find_all_python_files()
    print(f"找到 {len(python_files)} 个Python文件")
    
    all_issues = []
    
    for file_path in python_files:
        issues = analyze_file_for_date_issues(file_path)
        
        # 只保留有问题的文件
        has_issues = any(issues[key] for key in issues if key != 'file')
        if has_issues:
            all_issues.append(issues)
    
    return all_issues

def print_analysis_results(all_issues: List[Dict]):
    """打印分析结果"""
    
    print(f"\n📋 分析结果总览")
    print("=" * 60)
    
    if not all_issues:
        print("✅ 未发现明显的日期处理问题")
        return
    
    # 按问题类型统计
    issue_stats = {
        'date_inference': 0,
        'year_inference': 0,
        'time_patterns': 0,
        'url_date_extraction': 0,
        'current_year_usage': 0,
        'hardcoded_dates': 0,
        'needs_accurate_time': 0
    }
    
    for file_issues in all_issues:
        for issue_type in issue_stats:
            issue_stats[issue_type] += len(file_issues[issue_type])
    
    print("问题类型统计:")
    for issue_type, count in issue_stats.items():
        if count > 0:
            type_names = {
                'date_inference': '日期推测',
                'year_inference': '年份推测',
                'time_patterns': '时间模式匹配',
                'url_date_extraction': 'URL日期提取',
                'current_year_usage': '使用当前年份',
                'hardcoded_dates': '硬编码日期范围',
                'needs_accurate_time': '可能需要准确时间提取'
            }
            print(f"  {type_names[issue_type]}: {count} 处")
    
    print(f"\n📁 详细分析结果")
    print("=" * 60)
    
    for file_issues in all_issues:
        file_path = file_issues['file']
        print(f"\n📄 文件: {file_path}")
        print("-" * 40)
        
        for issue_type, issues in file_issues.items():
            if issue_type == 'file' or not issues:
                continue
            
            type_names = {
                'date_inference': '🔮 日期推测',
                'year_inference': '📅 年份推测',
                'time_patterns': '🕐 时间模式',
                'url_date_extraction': '🔗 URL日期提取',
                'current_year_usage': '📆 当前年份',
                'hardcoded_dates': '🔢 硬编码范围',
                'needs_accurate_time': '⚡ 可改进'
            }
            
            print(f"\n{type_names[issue_type]} ({len(issues)} 处):")
            for issue in issues:
                print(f"  第{issue['line']:3d}行: {issue['content']}")

def suggest_improvements():
    """建议改进方案"""
    
    print(f"\n💡 改进建议")
    print("=" * 60)
    
    suggestions = [
        {
            'title': '1. 优先使用准确时间提取',
            'description': '对于所有比赛信息提取功能，优先从分析页面获取准确时间',
            'files': ['date_match_extractor.py', 'league_match_extractor.py', 'debug_date_extractor.py']
        },
        {
            'title': '2. 统一时间处理逻辑',
            'description': '将准确时间提取功能集成到所有相关模块中',
            'files': ['match_time_scraper.py']
        },
        {
            'title': '3. 减少年份推测依赖',
            'description': '尽可能使用真实数据源，减少基于ID范围的年份推测',
            'files': ['season_utils.py']
        },
        {
            'title': '4. 改进URL日期提取',
            'description': '对于从URL提取日期的功能，考虑验证日期的准确性',
            'files': ['date_match_extractor.py', 'debug_date_extractor.py']
        }
    ]
    
    for suggestion in suggestions:
        print(f"\n{suggestion['title']}")
        print(f"描述: {suggestion['description']}")
        print(f"涉及文件: {', '.join(suggestion['files'])}")

def main():
    """主函数"""
    
    print("🚀 项目日期处理功能全面检查")
    print("=" * 60)
    
    # 分析所有文件
    all_issues = analyze_all_files()
    
    # 打印结果
    print_analysis_results(all_issues)
    
    # 建议改进
    suggest_improvements()
    
    print(f"\n" + "=" * 60)
    print("📋 检查总结")
    print("=" * 60)
    
    if all_issues:
        print(f"发现 {len(all_issues)} 个文件存在日期处理相关问题")
        print("建议按优先级逐步改进这些功能")
    else:
        print("✅ 未发现明显的日期处理问题")
    
    print("\n🎯 重点关注:")
    print("1. 所有使用年份推测的地方")
    print("2. 从URL提取日期但未验证的地方")
    print("3. 可以改用准确时间提取的地方")

if __name__ == "__main__":
    main()

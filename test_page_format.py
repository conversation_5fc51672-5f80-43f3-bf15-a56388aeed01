#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试页面格式和数据解析
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from enhanced_odds_scraper import EnhancedOddsScraper
import logging

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_page_format():
    """测试页面格式和数据解析"""
    print("🧪 测试页面格式和数据解析")
    print("=" * 60)
    
    scraper = EnhancedOddsScraper()
    
    # 测试bet365的页面
    bet365_url = "https://op1.titan007.com/OddsHistory.aspx?id=129831588&sid=2399071&cid=281&l=0"
    
    print(f"🎯 测试bet365页面: {bet365_url}")
    print("-" * 40)
    
    # 获取页面内容
    content = scraper.get_page_content(bet365_url)
    
    if not content:
        print(f"❌ 无法获取页面内容")
        return
        
    print(f"✅ 页面可访问，内容长度: {len(content)}")
    
    # 保存页面内容到文件
    with open("bet365_page.html", "w", encoding="utf-8") as f:
        f.write(content)
    print(f"📄 页面内容已保存到: bet365_page.html")
    
    # 解析页面
    records = scraper.parse_company_odds_history_page(bet365_url, "bet365")
    
    if records:
        print(f"✅ 解析成功：获取到 {len(records)} 条记录")
        
        print(f"\n📊 前3条记录详细信息:")
        for i, record in enumerate(records[:3], 1):
            print(f"  记录 {i}:")
            print(f"    主胜赔率: {record.get('home_odds', 'N/A')}")
            print(f"    平局赔率: {record.get('draw_odds', 'N/A')}")
            print(f"    客胜赔率: {record.get('away_odds', 'N/A')}")
            print(f"    返还率: {record.get('return_rate', 'N/A'):.2f}%")
            print(f"    凯利主胜: {record.get('kelly_home', 'N/A'):.3f}")
            print(f"    凯利平局: {record.get('kelly_draw', 'N/A'):.3f}")
            print(f"    凯利客胜: {record.get('kelly_away', 'N/A'):.3f}")
            print(f"    时间: {record.get('change_time', 'N/A')}")
            print()
        
        # 对比期望的数据
        print(f"📋 与期望数据对比:")
        expected_data = [
            {"home": 2.87, "draw": 3.1, "away": 2.6, "return_rate": 94.73, "kelly_home": 0.94, "kelly_draw": 0.94, "kelly_away": 0.96, "time": "10-04 02:16"},
            {"home": 2.88, "draw": 3.1, "away": 2.6, "return_rate": 94.84, "kelly_home": 0.95, "kelly_draw": 0.94, "kelly_away": 0.96, "time": "10-04 02:16"},
            {"home": 2.9, "draw": 3.1, "away": 2.55, "return_rate": 94.38, "kelly_home": 0.95, "kelly_draw": 0.94, "kelly_away": 0.94, "time": "10-04 00:59"}
        ]
        
        for i, (actual, expected) in enumerate(zip(records[:3], expected_data), 1):
            print(f"  记录 {i} 对比:")
            
            # 赔率对比
            home_match = abs(actual.get('home_odds', 0) - expected['home']) < 0.01
            draw_match = abs(actual.get('draw_odds', 0) - expected['draw']) < 0.01
            away_match = abs(actual.get('away_odds', 0) - expected['away']) < 0.01
            
            print(f"    赔率: {'✅' if home_match and draw_match and away_match else '❌'}")
            print(f"      实际: {actual.get('home_odds', 'N/A')} {actual.get('draw_odds', 'N/A')} {actual.get('away_odds', 'N/A')}")
            print(f"      期望: {expected['home']} {expected['draw']} {expected['away']}")
            
            # 返还率对比
            return_rate_match = abs(actual.get('return_rate', 0) - expected['return_rate']) < 1.0
            print(f"    返还率: {'✅' if return_rate_match else '❌'}")
            print(f"      实际: {actual.get('return_rate', 'N/A'):.2f}%")
            print(f"      期望: {expected['return_rate']}%")
            
            # 凯利指数对比
            kelly_home_match = abs(actual.get('kelly_home', 0) - (expected['kelly_home'] - 1)) < 0.1  # 注意凯利指数的计算方式
            print(f"    凯利指数: {'⚠️需要修正' if not kelly_home_match else '✅'}")
            print(f"      实际凯利主胜: {actual.get('kelly_home', 'N/A'):.3f}")
            print(f"      期望凯利主胜: {expected['kelly_home']}")
            print()
            
    else:
        print(f"❌ 解析失败：未获取到任何记录")
        
        # 检查页面内容
        print(f"\n🔍 检查页面内容:")
        if 'table' in content.lower():
            print(f"  ✅ 页面包含表格")
        else:
            print(f"  ❌ 页面不包含表格")
            
        if '2.87' in content:
            print(f"  ✅ 页面包含期望的赔率数据")
        else:
            print(f"  ❌ 页面不包含期望的赔率数据")

if __name__ == "__main__":
    test_page_format()

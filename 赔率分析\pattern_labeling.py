#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
赔率行为模式标签设计
自动识别和标记不同的赔率走势行为模式
"""

import pandas as pd
import numpy as np
import json
import os
from datetime import datetime
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.cluster import KMeans
from sklearn.preprocessing import StandardScaler
from collections import defaultdict, Counter

class PatternLabeler:
    def __init__(self, features_file=None):
        self.features_file = features_file
        self.features_data = None
        self.pattern_rules = self._define_pattern_rules()
        self.labeled_data = None
        
    def _define_pattern_rules(self):
        """定义赔率行为模式的识别规则"""
        return {
            'down_pressure': {
                'name': '下压型',
                'description': '持续下降或临场压低',
                'rules': {
                    'trend_slope_negative': True,  # 趋势斜率为负
                    'total_change_negative': True,  # 总变化为负
                    'late_momentum_negative': True,  # 临场动量为负
                    'min_change_threshold': -0.05  # 最小变化阈值
                }
            },
            'rebound': {
                'name': '反弹型',
                'description': '先跌后升（主力退场、假动作）',
                'rules': {
                    'early_trend_negative': True,  # 前期趋势为负
                    'late_trend_positive': True,   # 后期趋势为正
                    'trend_change_positive': True,  # 趋势变化为正
                    'min_rebound_ratio': 0.5  # 最小反弹比例
                }
            },
            'cooling': {
                'name': '冷却型',
                'description': '临场波动很小或接近开盘价',
                'rules': {
                    'low_volatility': True,  # 低波动性
                    'small_total_change': True,  # 总变化小
                    'low_late_volatility': True,  # 临场波动小
                    'max_volatility_threshold': 0.1  # 最大波动阈值
                }
            },
            'explosive': {
                'name': '爆拉型',
                'description': '短时间大波动（>5%），突发消息',
                'rules': {
                    'high_volatility': True,  # 高波动性
                    'large_single_change': True,  # 单次大变化
                    'high_direction_changes': True,  # 方向变化多
                    'min_volatility_threshold': 0.3  # 最小波动阈值
                }
            },
            'oscillation': {
                'name': '震荡型',
                'description': '频繁上下波动',
                'rules': {
                    'high_direction_changes': True,  # 方向变化多
                    'medium_volatility': True,  # 中等波动
                    'low_trend_consistency': True,  # 趋势一致性低
                    'min_direction_changes': 3  # 最小方向变化次数
                }
            },
            'stable': {
                'name': '稳定型',
                'description': '变化幅度小于阈值',
                'rules': {
                    'low_volatility': True,  # 低波动性
                    'small_range': True,  # 变化范围小
                    'high_trend_consistency': True,  # 趋势一致性高
                    'max_change_threshold': 0.05  # 最大变化阈值
                }
            }
        }
    
    def load_features_data(self, filename=None):
        """加载特征数据"""
        if filename:
            self.features_file = filename
        
        if not self.features_file:
            # 查找最新的特征文件
            files = [f for f in os.listdir('.') if f.startswith('odds_features_') and f.endswith('.csv')]
            if files:
                self.features_file = sorted(files)[-1]
                print(f"自动选择最新文件: {self.features_file}")
            else:
                print("未找到特征数据文件")
                return None
        
        self.features_data = pd.read_csv(self.features_file)
        print(f"加载特征数据: {self.features_file}")
        print(f"数据形状: {self.features_data.shape}")
        return self.features_data
    
    def apply_pattern_rules(self, row, odds_type='home'):
        """对单行数据应用模式识别规则"""
        patterns = []
        
        # 获取相关特征
        trend_slope = row.get(f'trend_slope_{odds_type}', 0)
        total_change_pct = row.get(f'total_change_pct_{odds_type}', 0)
        volatility = row.get(f'volatility_{odds_type}', 0)
        direction_changes = row.get(f'direction_changes_{odds_type}', 0)
        max_single_change = row.get(f'max_single_change_{odds_type}', 0)
        late_volatility = row.get(f'late_volatility_{odds_type}', 0)
        final_momentum = row.get(f'final_momentum_{odds_type}', 0)
        early_trend = row.get(f'early_trend_{odds_type}', 0)
        late_trend = row.get(f'late_trend_{odds_type}', 0)
        trend_change = row.get(f'trend_change_{odds_type}', 0)
        trend_consistency = row.get(f'trend_consistency_{odds_type}', 0)
        range_value = row.get(f'range_{odds_type}', 0)
        max_change = row.get(f'max_change_{odds_type}', 0)
        
        # 下压型
        if (trend_slope < 0 and 
            total_change_pct < -2 and 
            final_momentum < 0):
            patterns.append('down_pressure')
        
        # 反弹型
        if (early_trend < 0 and 
            late_trend > 0 and 
            trend_change > 0 and
            abs(late_trend) > abs(early_trend) * 0.5):
            patterns.append('rebound')
        
        # 冷却型
        if (volatility < 0.1 and 
            abs(total_change_pct) < 2 and 
            late_volatility < 0.05):
            patterns.append('cooling')
        
        # 爆拉型
        if (volatility > 0.3 or 
            abs(max_single_change) > 0.5 or
            direction_changes > 5):
            patterns.append('explosive')
        
        # 震荡型
        if (direction_changes >= 3 and 
            0.1 <= volatility <= 0.3 and 
            trend_consistency == 0):
            patterns.append('oscillation')
        
        # 稳定型
        if (volatility < 0.1 and 
            range_value < 0.2 and 
            trend_consistency == 1 and
            max_change < 0.1):
            patterns.append('stable')
        
        # 如果没有匹配任何模式，标记为其他
        if not patterns:
            patterns.append('other')
        
        return patterns
    
    def label_all_patterns(self):
        """为所有数据标记模式"""
        if self.features_data is None:
            print("请先加载特征数据")
            return None
        
        print("\n=== 开始模式标记 ===")
        
        labeled_data = self.features_data.copy()
        
        # 为每种赔率类型标记模式
        for odds_type in ['home', 'draw', 'away']:
            print(f"处理 {odds_type} 赔率模式...")
            
            patterns_list = []
            primary_patterns = []
            
            for idx, row in labeled_data.iterrows():
                patterns = self.apply_pattern_rules(row, odds_type)
                patterns_list.append(','.join(patterns))
                primary_patterns.append(patterns[0])  # 主要模式
            
            labeled_data[f'patterns_{odds_type}'] = patterns_list
            labeled_data[f'primary_pattern_{odds_type}'] = primary_patterns
        
        # 综合模式分析
        print("生成综合模式...")
        combined_patterns = []
        
        for idx, row in labeled_data.iterrows():
            home_pattern = row['primary_pattern_home']
            draw_pattern = row['primary_pattern_draw']
            away_pattern = row['primary_pattern_away']
            
            # 如果三个模式相同，使用该模式
            if home_pattern == draw_pattern == away_pattern:
                combined_patterns.append(home_pattern)
            # 如果有两个相同，使用多数模式
            elif home_pattern == draw_pattern:
                combined_patterns.append(home_pattern)
            elif home_pattern == away_pattern:
                combined_patterns.append(home_pattern)
            elif draw_pattern == away_pattern:
                combined_patterns.append(draw_pattern)
            # 否则标记为混合模式
            else:
                combined_patterns.append('mixed')
        
        labeled_data['combined_pattern'] = combined_patterns
        
        self.labeled_data = labeled_data
        return labeled_data
    
    def analyze_patterns(self):
        """分析模式分布和特征"""
        if self.labeled_data is None:
            print("请先进行模式标记")
            return
        
        print("\n=== 模式分析 ===")
        
        # 各类型赔率的模式分布
        for odds_type in ['home', 'draw', 'away']:
            pattern_counts = self.labeled_data[f'primary_pattern_{odds_type}'].value_counts()
            print(f"\n{odds_type.upper()} 赔率模式分布:")
            for pattern, count in pattern_counts.items():
                percentage = count / len(self.labeled_data) * 100
                pattern_name = self.pattern_rules.get(pattern, {}).get('name', pattern)
                print(f"  {pattern_name} ({pattern}): {count} ({percentage:.1f}%)")
        
        # 综合模式分布
        combined_counts = self.labeled_data['combined_pattern'].value_counts()
        print(f"\n综合模式分布:")
        for pattern, count in combined_counts.items():
            percentage = count / len(self.labeled_data) * 100
            pattern_name = self.pattern_rules.get(pattern, {}).get('name', pattern)
            print(f"  {pattern_name} ({pattern}): {count} ({percentage:.1f}%)")
        
        # 按比赛结果分析模式
        if 'match_result' in self.labeled_data.columns:
            print(f"\n按比赛结果分析模式:")
            result_pattern_crosstab = pd.crosstab(
                self.labeled_data['match_result'], 
                self.labeled_data['combined_pattern'],
                normalize='index'
            ) * 100
            
            print(result_pattern_crosstab.round(1))
    
    def pattern_effectiveness_analysis(self):
        """分析模式的有效性"""
        if self.labeled_data is None or 'match_result' not in self.labeled_data.columns:
            print("需要标记数据和比赛结果进行有效性分析")
            return
        
        print("\n=== 模式有效性分析 ===")
        
        effectiveness_results = {}
        
        for odds_type in ['home', 'draw', 'away']:
            print(f"\n{odds_type.upper()} 赔率模式有效性:")
            
            pattern_effectiveness = {}
            
            for pattern in self.labeled_data[f'primary_pattern_{odds_type}'].unique():
                pattern_data = self.labeled_data[
                    self.labeled_data[f'primary_pattern_{odds_type}'] == pattern
                ]
                
                if len(pattern_data) < 3:  # 样本太少跳过
                    continue
                
                # 计算该模式下各种结果的概率
                result_probs = pattern_data['match_result'].value_counts(normalize=True)
                
                # 计算预期准确率（如果该模式预测对应结果）
                if odds_type == 'home':
                    expected_accuracy = result_probs.get('home_win', 0)
                elif odds_type == 'away':
                    expected_accuracy = result_probs.get('away_win', 0)
                else:  # draw
                    expected_accuracy = result_probs.get('draw', 0)
                
                pattern_effectiveness[pattern] = {
                    'sample_size': len(pattern_data),
                    'result_distribution': result_probs.to_dict(),
                    'expected_accuracy': expected_accuracy
                }
                
                pattern_name = self.pattern_rules.get(pattern, {}).get('name', pattern)
                print(f"  {pattern_name} ({pattern}):")
                print(f"    样本数: {len(pattern_data)}")
                print(f"    预期准确率: {expected_accuracy:.3f}")
                for result, prob in result_probs.items():
                    print(f"    {result}: {prob:.3f}")
            
            effectiveness_results[odds_type] = pattern_effectiveness
        
        return effectiveness_results
    
    def save_labeled_data(self, filename_prefix="labeled_patterns"):
        """保存标记后的数据"""
        if self.labeled_data is None:
            print("没有标记数据可保存")
            return None
        
        # 保存CSV格式
        csv_file = f"{filename_prefix}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
        self.labeled_data.to_csv(csv_file, index=False, encoding='utf-8')
        print(f"\n标记数据已保存到: {csv_file}")
        
        # 保存模式统计
        stats_file = csv_file.replace('.csv', '_stats.json')
        
        stats = {
            'pattern_rules': self.pattern_rules,
            'pattern_distribution': {},
            'total_samples': len(self.labeled_data)
        }
        
        for odds_type in ['home', 'draw', 'away']:
            pattern_counts = self.labeled_data[f'primary_pattern_{odds_type}'].value_counts()
            stats['pattern_distribution'][odds_type] = pattern_counts.to_dict()
        
        combined_counts = self.labeled_data['combined_pattern'].value_counts()
        stats['pattern_distribution']['combined'] = combined_counts.to_dict()
        
        with open(stats_file, 'w', encoding='utf-8') as f:
            json.dump(stats, f, ensure_ascii=False, indent=2)
        
        print(f"模式统计已保存到: {stats_file}")
        
        return csv_file
    
    def visualize_patterns(self):
        """可视化模式分布"""
        if self.labeled_data is None:
            print("请先进行模式标记")
            return
        
        plt.rcParams['font.sans-serif'] = ['SimHei']  # 支持中文
        plt.rcParams['axes.unicode_minus'] = False
        
        fig, axes = plt.subplots(2, 2, figsize=(15, 12))
        
        # 综合模式分布
        combined_counts = self.labeled_data['combined_pattern'].value_counts()
        axes[0, 0].pie(combined_counts.values, labels=combined_counts.index, autopct='%1.1f%%')
        axes[0, 0].set_title('综合模式分布')
        
        # 各赔率类型模式分布
        for i, odds_type in enumerate(['home', 'draw', 'away']):
            row = (i + 1) // 2
            col = (i + 1) % 2
            
            pattern_counts = self.labeled_data[f'primary_pattern_{odds_type}'].value_counts()
            axes[row, col].bar(range(len(pattern_counts)), pattern_counts.values)
            axes[row, col].set_xticks(range(len(pattern_counts)))
            axes[row, col].set_xticklabels(pattern_counts.index, rotation=45)
            axes[row, col].set_title(f'{odds_type.upper()} 赔率模式分布')
            axes[row, col].set_ylabel('数量')
        
        plt.tight_layout()
        
        # 保存图片
        plot_file = f"pattern_distribution_{datetime.now().strftime('%Y%m%d_%H%M%S')}.png"
        plt.savefig(plot_file, dpi=300, bbox_inches='tight')
        print(f"模式分布图已保存到: {plot_file}")
        
        plt.show()

def main():
    labeler = PatternLabeler()
    
    # 加载特征数据
    features_data = labeler.load_features_data()
    if features_data is None:
        return
    
    # 标记模式
    labeled_data = labeler.label_all_patterns()
    
    # 分析模式
    labeler.analyze_patterns()
    
    # 有效性分析
    effectiveness = labeler.pattern_effectiveness_analysis()
    
    # 保存结果
    labeler.save_labeled_data()
    
    # 可视化（可选）
    try:
        labeler.visualize_patterns()
    except Exception as e:
        print(f"可视化失败: {e}")

if __name__ == "__main__":
    main()

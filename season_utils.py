#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
赛季信息工具函数
提供从URL中提取赛季信息的功能

功能：
1. 从联赛URL中提取赛季信息
2. 根据比赛ID推断赛季信息
3. 赛季信息格式化和验证

使用方法：
    from season_utils import extract_season_from_url, infer_season_from_match_id
    
    season = extract_season_from_url("https://m.titan007.com/info/fixture/2025/21_165_4.htm")
    season = infer_season_from_match_id("2741460")
"""

import re
import logging
from typing import Optional

logger = logging.getLogger(__name__)

def extract_season_from_url(url: str) -> Optional[str]:
    """
    从URL中提取赛季信息
    
    Args:
        url: 联赛URL
        
    Returns:
        赛季字符串，如 "2025" 或 "2024-2025"，失败时返回None
        
    Examples:
        >>> extract_season_from_url("https://m.titan007.com/info/fixture/2025/21_165_4.htm")
        "2025"
        >>> extract_season_from_url("https://m.titan007.com/info/Fixture/2024-2025/36_0_0.htm")
        "2024-2025"
    """
    try:
        if not url:
            return None
            
        # 处理类似 https://m.titan007.com/info/fixture/2025/21_165_4.htm 的URL
        pattern1 = r'/info/fixture/(\d{4})/\d+_\d+_\d+\.htm'
        match1 = re.search(pattern1, url, re.IGNORECASE)
        if match1:
            season = match1.group(1)
            logger.info(f"从URL提取到赛季: {season}")
            return season
        
        # 处理类似 https://m.titan007.com/info/Fixture/2024-2025/36_0_0.htm 的URL
        pattern2 = r'/info/Fixture/(\d{4}-\d{4})/\d+_\d+_\d+\.htm'
        match2 = re.search(pattern2, url, re.IGNORECASE)
        if match2:
            season = match2.group(1)
            logger.info(f"从URL提取到赛季: {season}")
            return season
        
        # 处理其他可能的格式
        # 例如：/season/2025/ 或 /2024-25/ 等
        pattern3 = r'/(?:season/)?(\d{4}(?:-\d{2,4})?)'
        match3 = re.search(pattern3, url, re.IGNORECASE)
        if match3:
            season = match3.group(1)
            logger.info(f"从URL提取到赛季: {season}")
            return season
        
        logger.warning(f"无法从URL中提取赛季信息: {url}")
        return None
        
    except Exception as e:
        logger.error(f"提取赛季信息失败: {e}")
        return None

def infer_season_from_match_id(match_id: str) -> Optional[str]:
    """
    根据比赛ID推断赛季信息
    
    Args:
        match_id: 比赛ID
        
    Returns:
        推断的赛季字符串，失败时返回None
        
    Examples:
        >>> infer_season_from_match_id("2741460")
        "2025"
        >>> infer_season_from_match_id("2699999")
        "2024"
    """
    try:
        if not match_id:
            return None
            
        # 转换为整数进行比较
        match_id_int = int(match_id)
        
        # 根据ID范围推断赛季
        if match_id_int > 2700000:
            return "2025"
        else:
            return "2024"
            
    except (ValueError, TypeError):
        logger.warning(f"无法解析比赛ID: {match_id}")
        return None

def format_season_display(season: str) -> str:
    """
    格式化赛季显示
    
    Args:
        season: 原始赛季字符串
        
    Returns:
        格式化后的赛季字符串
        
    Examples:
        >>> format_season_display("2025")
        "2025"
        >>> format_season_display("2024-2025")
        "2024-25"
    """
    try:
        if not season:
            return "未知"
            
        # 如果是跨年赛季，简化显示
        if '-' in season:
            parts = season.split('-')
            if len(parts) == 2:
                start_year = parts[0]
                end_year = parts[1]
                
                # 如果结束年份是4位数，简化为2位数
                if len(end_year) == 4:
                    end_year = end_year[-2:]
                
                return f"{start_year}-{end_year}"
        
        return season
        
    except Exception as e:
        logger.error(f"格式化赛季显示失败: {e}")
        return season or "未知"

def validate_season(season: str) -> bool:
    """
    验证赛季格式是否有效
    
    Args:
        season: 赛季字符串
        
    Returns:
        是否为有效的赛季格式
        
    Examples:
        >>> validate_season("2025")
        True
        >>> validate_season("2024-2025")
        True
        >>> validate_season("invalid")
        False
    """
    try:
        if not season:
            return False
            
        # 单年份格式：2025
        if re.match(r'^\d{4}$', season):
            year = int(season)
            return 2020 <= year <= 2030  # 合理的年份范围
        
        # 跨年格式：2024-2025 或 2024-25
        if re.match(r'^\d{4}-\d{2,4}$', season):
            parts = season.split('-')
            start_year = int(parts[0])
            end_year_str = parts[1]
            
            # 如果是2位数，补全为4位数
            if len(end_year_str) == 2:
                end_year = int(f"20{end_year_str}")
            else:
                end_year = int(end_year_str)
            
            # 验证年份范围和逻辑
            return (2020 <= start_year <= 2030 and 
                    2020 <= end_year <= 2030 and 
                    end_year == start_year + 1)
        
        return False
        
    except (ValueError, TypeError):
        return False

def get_season_for_match(match_id: str = None, league_url: str = None) -> str:
    """
    为比赛获取赛季信息的综合函数
    
    Args:
        match_id: 比赛ID（可选）
        league_url: 联赛URL（可选）
        
    Returns:
        赛季字符串，优先使用URL提取，其次使用ID推断
        
    Examples:
        >>> get_season_for_match(league_url="https://m.titan007.com/info/fixture/2025/21_165_4.htm")
        "2025"
        >>> get_season_for_match(match_id="2741460")
        "2025"
    """
    try:
        # 优先从URL中提取
        if league_url:
            season = extract_season_from_url(league_url)
            if season and validate_season(season):
                return season
        
        # 其次从比赛ID推断
        if match_id:
            season = infer_season_from_match_id(match_id)
            if season and validate_season(season):
                return season
        
        # 默认返回当前年份
        from datetime import datetime
        current_year = datetime.now().year
        return str(current_year)
        
    except Exception as e:
        logger.error(f"获取赛季信息失败: {e}")
        return "未知"

# 测试函数
def test_season_utils():
    """测试赛季工具函数"""
    print("测试赛季工具函数...")
    
    # 测试URL提取
    test_urls = [
        "https://m.titan007.com/info/fixture/2025/21_165_4.htm",
        "https://m.titan007.com/info/Fixture/2024-2025/36_0_0.htm",
        "https://example.com/season/2023/",
        "invalid_url"
    ]
    
    for url in test_urls:
        season = extract_season_from_url(url)
        print(f"URL: {url} -> 赛季: {season}")
    
    # 测试ID推断
    test_ids = ["2741460", "2699999", "invalid_id"]
    
    for match_id in test_ids:
        season = infer_season_from_match_id(match_id)
        print(f"比赛ID: {match_id} -> 赛季: {season}")
    
    # 测试格式化
    test_seasons = ["2025", "2024-2025", "2023-24", ""]
    
    for season in test_seasons:
        formatted = format_season_display(season)
        valid = validate_season(season)
        print(f"赛季: {season} -> 格式化: {formatted}, 有效: {valid}")

if __name__ == "__main__":
    test_season_utils()

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试UI中的数据库连接
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from PyQt5.QtWidgets import QApplication
from odds_combination_ui import OddsCombinationMainWindow

def main():
    """测试UI中的数据库连接"""
    print("🔍 测试UI中的数据库连接")
    print("=" * 50)
    print("📝 测试步骤:")
    print("1. 启动UI")
    print("2. 选择数据库")
    print("3. 在控制台输入测试命令")
    print("")
    print("🎯 测试命令:")
    print("在UI启动后，在此控制台输入以下命令进行测试：")
    print("")
    print("# 测试数据库连接")
    print("window.database")
    print("")
    print("# 测试比赛结果获取")
    print("result = window.get_match_result_for_backtest('2596914')")
    print("print(f'比赛2596914结果: {result}')")
    print("")
    print("⚠️ 注意：请先在UI中选择数据库！")
    print("")
    
    app = QApplication(sys.argv)
    
    # 创建主窗口
    window = OddsCombinationMainWindow()
    window.show()
    
    # 添加一个简单的测试方法
    def test_database_in_ui():
        """在UI中测试数据库连接"""
        print("\n🧪 UI数据库连接测试:")
        print(f"数据库对象: {window.database}")
        
        if window.database:
            print("✅ 数据库对象存在")
            
            # 测试比赛结果获取
            test_match_id = "2596914"
            result = window.get_match_result_for_backtest(test_match_id)
            print(f"比赛{test_match_id}结果: {result}")
            
        else:
            print("❌ 数据库对象不存在，请先选择数据库")
    
    # 将测试方法绑定到窗口对象
    window.test_database = test_database_in_ui
    
    print("✅ UI已启动")
    print("📋 在UI中选择数据库后，可以在Python控制台中运行:")
    print("   window.test_database()")
    print("   或者直接测试:")
    print("   window.get_match_result_for_backtest('2596914')")
    
    # 运行应用
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()

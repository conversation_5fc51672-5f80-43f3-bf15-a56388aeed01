#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增强的统计数据显示组件
提供更直观、结构化的数据展示界面

功能特点:
1. 表格化显示技术统计
2. 数据分类和图标
3. 主客队对比和差值计算
4. 颜色编码和视觉优化
5. 排序和筛选功能
"""

import tkinter as tk
from tkinter import ttk
import json
from typing import Dict, List, Optional, Any
import logging

logger = logging.getLogger(__name__)

class EnhancedStatsDisplay:
    """增强的统计数据显示器"""
    
    def __init__(self, parent_frame):
        """初始化显示器"""
        self.parent_frame = parent_frame
        
        # 统计数据分类配置
        self.stat_categories = {
            '🏹 进攻数据': {
                'keywords': ['射门', '射正', '角球', '越位', '进攻', 'shots', 'corners', 'offside'],
                'color': '#FF6B6B'  # 红色系
            },
            '🛡️ 防守数据': {
                'keywords': ['犯规', '黄牌', '红牌', '抢断', '解围', 'fouls', 'cards', 'tackles'],
                'color': '#4ECDC4'  # 青色系
            },
            '📊 控制数据': {
                'keywords': ['控球', '传球', '成功率', 'possession', 'passes', 'accuracy'],
                'color': '#45B7D1'  # 蓝色系
            },
            '⚽ 其他数据': {
                'keywords': [],  # 默认分类
                'color': '#96CEB4'  # 绿色系
            }
        }
        
        # 创建界面
        self.create_widgets()
    
    def create_widgets(self):
        """创建界面组件"""
        # 主容器
        main_frame = ttk.Frame(self.parent_frame)
        main_frame.pack(fill=tk.BOTH, expand=True)

        # 工具栏
        self.create_toolbar(main_frame)

        # 创建统一的技术统计表格
        self.create_unified_stats_table(main_frame)

    def create_unified_stats_table(self, parent):
        """创建统一的技术统计表格"""
        # 表格框架
        table_frame = ttk.LabelFrame(parent, text="技术统计对比", padding="10")
        table_frame.pack(fill=tk.BOTH, expand=True, pady=(10, 0))

        # 创建表格 - 调整列顺序：类别、主队、统计项目、客队、差值、优势方
        columns = ("category", "home_value", "stat_name", "away_value", "difference", "advantage")
        self.unified_tree = ttk.Treeview(table_frame, columns=columns, show="headings", height=20)

        # 设置列标题
        self.unified_tree.heading("category", text="类别")
        self.unified_tree.heading("home_value", text="主队")
        self.unified_tree.heading("stat_name", text="统计项目")
        self.unified_tree.heading("away_value", text="客队")
        self.unified_tree.heading("difference", text="差值")
        self.unified_tree.heading("advantage", text="优势方")

        # 设置列宽 - 缩小间距，优化显示
        self.unified_tree.column("category", width=60, anchor="center")
        self.unified_tree.column("home_value", width=70, anchor="center")
        self.unified_tree.column("stat_name", width=100, anchor="center")
        self.unified_tree.column("away_value", width=70, anchor="center")
        self.unified_tree.column("difference", width=70, anchor="center")
        self.unified_tree.column("advantage", width=70, anchor="center")

        # 滚动条
        scrollbar = ttk.Scrollbar(table_frame, orient=tk.VERTICAL, command=self.unified_tree.yview)
        self.unified_tree.configure(yscrollcommand=scrollbar.set)

        # 布局
        self.unified_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # 配置颜色标签
        self.unified_tree.tag_configure("home_advantage", background="#FFE5E5")  # 浅红色
        self.unified_tree.tag_configure("away_advantage", background="#E5F3FF")  # 浅蓝色
        self.unified_tree.tag_configure("attack", background="#FFF0F0")          # 进攻类别
        self.unified_tree.tag_configure("defense", background="#F0F8FF")         # 防守类别
        self.unified_tree.tag_configure("control", background="#F0FFF0")         # 控制类别
        self.unified_tree.tag_configure("other", background="#F8F8F8")           # 其他类别
    
    def create_toolbar(self, parent):
        """创建工具栏"""
        toolbar_frame = ttk.Frame(parent)
        toolbar_frame.pack(fill=tk.X, pady=(0, 5))
        
        # 显示模式选择
        ttk.Label(toolbar_frame, text="显示模式:").pack(side=tk.LEFT, padx=(0, 5))
        
        self.display_mode = tk.StringVar(value="category")
        ttk.Radiobutton(toolbar_frame, text="分类显示", variable=self.display_mode, 
                       value="category", command=self.refresh_display).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Radiobutton(toolbar_frame, text="对比显示", variable=self.display_mode, 
                       value="comparison", command=self.refresh_display).pack(side=tk.LEFT, padx=(0, 10))
        
        # 数据类型选择
        ttk.Label(toolbar_frame, text="数据类型:").pack(side=tk.LEFT, padx=(20, 5))
        
        self.data_type = tk.StringVar(value="current")
        ttk.Radiobutton(toolbar_frame, text="当前比赛", variable=self.data_type, 
                       value="current", command=self.refresh_display).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Radiobutton(toolbar_frame, text="历史平均", variable=self.data_type, 
                       value="historical", command=self.refresh_display).pack(side=tk.LEFT, padx=(0, 10))
        
        # 刷新按钮
        ttk.Button(toolbar_frame, text="刷新", command=self.refresh_display).pack(side=tk.RIGHT)
    
    def create_category_tab(self, category_name, config):
        """创建分类标签页"""
        frame = ttk.Frame(self.notebook)
        self.notebook.add(frame, text=category_name)
        self.category_frames[category_name] = frame
        
        # 创建表格
        columns = ("stat_name", "home_value", "away_value", "difference", "advantage")
        tree = ttk.Treeview(frame, columns=columns, show="headings", height=12)
        
        # 设置列标题
        tree.heading("stat_name", text="统计项目")
        tree.heading("home_value", text="主队")
        tree.heading("away_value", text="客队")
        tree.heading("difference", text="差值")
        tree.heading("advantage", text="优势方")
        
        # 设置列宽
        tree.column("stat_name", width=120)
        tree.column("home_value", width=80)
        tree.column("away_value", width=80)
        tree.column("difference", width=80)
        tree.column("advantage", width=80)
        
        # 滚动条
        scrollbar = ttk.Scrollbar(frame, orient=tk.VERTICAL, command=tree.yview)
        tree.configure(yscrollcommand=scrollbar.set)
        
        # 布局
        tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        self.category_trees[category_name] = tree
    
    def create_comparison_tab(self):
        """创建综合对比标签页"""
        frame = ttk.Frame(self.notebook)
        self.notebook.add(frame, text="📊 综合对比")
        
        # 创建对比表格
        columns = ("category", "home_score", "away_score", "home_advantage", "summary")
        self.comparison_tree = ttk.Treeview(frame, columns=columns, show="headings", height=12)
        
        # 设置列标题
        self.comparison_tree.heading("category", text="数据类别")
        self.comparison_tree.heading("home_score", text="主队得分")
        self.comparison_tree.heading("away_score", text="客队得分")
        self.comparison_tree.heading("home_advantage", text="主队优势")
        self.comparison_tree.heading("summary", text="总结")
        
        # 设置列宽
        self.comparison_tree.column("category", width=100)
        self.comparison_tree.column("home_score", width=80)
        self.comparison_tree.column("away_score", width=80)
        self.comparison_tree.column("home_advantage", width=100)
        self.comparison_tree.column("summary", width=200)
        
        # 滚动条
        comp_scrollbar = ttk.Scrollbar(frame, orient=tk.VERTICAL, command=self.comparison_tree.yview)
        self.comparison_tree.configure(yscrollcommand=comp_scrollbar.set)
        
        # 布局
        self.comparison_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        comp_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
    
    def categorize_stat(self, stat_name: str) -> str:
        """将统计项目分类"""
        stat_name_lower = stat_name.lower()
        
        for category, config in self.stat_categories.items():
            if category == '⚽ 其他数据':  # 默认分类，最后检查
                continue
                
            keywords = config['keywords']
            for keyword in keywords:
                if keyword.lower() in stat_name_lower:
                    return category
        
        return '⚽ 其他数据'  # 默认分类
    
    def calculate_difference(self, home_value: str, away_value: str) -> tuple:
        """计算数值差异"""
        try:
            # 处理百分比
            if '%' in home_value:
                home_num = float(home_value.replace('%', ''))
                away_num = float(away_value.replace('%', ''))
                diff = home_num - away_num
                return f"{diff:+.1f}%", "主队" if diff > 0 else "客队" if diff < 0 else "平"
            
            # 处理普通数值
            home_num = float(home_value)
            away_num = float(away_value)
            diff = home_num - away_num
            return f"{diff:+.0f}", "主队" if diff > 0 else "客队" if diff < 0 else "平"
            
        except (ValueError, TypeError):
            return "N/A", "N/A"
    
    def display_stats(self, enhanced_data: Dict[str, Any]):
        """显示统计数据"""
        try:
            # 清空统一表格
            for item in self.unified_tree.get_children():
                self.unified_tree.delete(item)

            # 获取当前选择的数据类型
            data_type = self.data_type.get()
            stats_key = f'technical_stats_{data_type}'

            if stats_key not in enhanced_data:
                self.show_no_data_message()
                return

            stats_data = enhanced_data[stats_key]

            # 按分类整理数据并显示
            for stat_name, stat_data in stats_data.items():
                category = self.categorize_stat(stat_name)
                category_short = self.get_category_short_name(category)

                home_value = str(stat_data.get('home', 'N/A'))
                away_value = str(stat_data.get('away', 'N/A'))

                difference, advantage = self.calculate_difference(home_value, away_value)

                # 确定标签
                tags = []

                # 优势方标签
                if advantage == "主队":
                    tags.append("home_advantage")
                elif advantage == "客队":
                    tags.append("away_advantage")

                # 类别标签
                if "进攻" in category:
                    tags.append("attack")
                elif "防守" in category:
                    tags.append("defense")
                elif "控制" in category:
                    tags.append("control")
                else:
                    tags.append("other")

                # 插入数据 - 调整顺序：类别、主队、统计项目、客队、差值、优势方
                self.unified_tree.insert("", "end", values=(
                    category_short,
                    home_value,
                    stat_name,
                    away_value,
                    difference,
                    advantage
                ), tags=tuple(tags))

        except Exception as e:
            logger.error(f"显示统计数据失败: {e}")
            self.show_error_message(str(e))

    def get_category_short_name(self, category: str) -> str:
        """获取类别简称"""
        category_mapping = {
            '🏹 进攻数据': '进攻',
            '🛡️ 防守数据': '防守',
            '📊 控制数据': '控制',
            '⚽ 其他数据': '其他'
        }
        return category_mapping.get(category, '其他')
    
    def generate_comparison_summary(self, categorized_stats: Dict[str, List]):
        """生成综合对比摘要"""
        try:
            for category, stats_list in categorized_stats.items():
                if not stats_list or category == '⚽ 其他数据':
                    continue
                
                # 计算该类别的优势统计
                home_advantages = sum(1 for stat in stats_list if stat['advantage'] == "主队")
                away_advantages = sum(1 for stat in stats_list if stat['advantage'] == "客队")
                total_stats = len(stats_list)
                
                # 计算得分
                home_score = round((home_advantages / total_stats) * 100) if total_stats > 0 else 0
                away_score = round((away_advantages / total_stats) * 100) if total_stats > 0 else 0
                
                # 判断优势
                if home_score > away_score:
                    advantage = f"+{home_score - away_score}%"
                    summary = f"主队在{category}方面占优"
                elif away_score > home_score:
                    advantage = f"-{away_score - home_score}%"
                    summary = f"客队在{category}方面占优"
                else:
                    advantage = "0%"
                    summary = f"{category}方面势均力敌"
                
                # 添加到对比表格
                tag = ""
                if home_score > away_score:
                    tag = "home_category_advantage"
                elif away_score > home_score:
                    tag = "away_category_advantage"
                
                self.comparison_tree.insert("", "end", values=(
                    category,
                    f"{home_score}%",
                    f"{away_score}%",
                    advantage,
                    summary
                ), tags=(tag,))
            
            # 配置分类优势颜色
            self.comparison_tree.tag_configure("home_category_advantage", background="#FFE5E5")
            self.comparison_tree.tag_configure("away_category_advantage", background="#E5F3FF")
            
        except Exception as e:
            logger.error(f"生成对比摘要失败: {e}")
    
    def show_no_data_message(self):
        """显示无数据消息"""
        self.unified_tree.insert("", "end", values=("", "", "暂无数据", "请点击'抓取详细数据'按钮获取数据", "", ""))

    def show_error_message(self, error_msg: str):
        """显示错误消息"""
        self.unified_tree.insert("", "end", values=("", "", f"错误: {error_msg}", "", "", ""))
    
    def refresh_display(self):
        """刷新显示"""
        # 这个方法会被UI调用来刷新数据
        # 具体的数据获取逻辑由调用方实现
        pass
    
    def clear_display(self):
        """清空显示"""
        for item in self.unified_tree.get_children():
            self.unified_tree.delete(item)

def test_enhanced_stats_display():
    """测试增强统计显示"""
    root = tk.Tk()
    root.title("统一技术统计显示测试")
    root.geometry("900x700")

    # 创建显示器
    display = EnhancedStatsDisplay(root)

    # 测试数据
    test_data = {
        'technical_stats_current': {
            '射门': {'home': '15', 'away': '12'},
            '射正': {'home': '7', 'away': '4'},
            '角球': {'home': '9', 'away': '5'},
            '控球率': {'home': '58%', 'away': '42%'},
            '传球': {'home': '456', 'away': '321'},
            '传球成功率': {'home': '85%', 'away': '78%'},
            '犯规': {'home': '12', 'away': '15'},
            '黄牌': {'home': '2', 'away': '1'},
            '越位': {'home': '3', 'away': '2'},
            '抢断': {'home': '18', 'away': '22'},
            '解围': {'home': '25', 'away': '19'},
            '任意球': {'home': '8', 'away': '11'}
        }
    }

    # 显示测试数据
    display.display_stats(test_data)

    root.mainloop()

if __name__ == '__main__':
    test_enhanced_stats_display()

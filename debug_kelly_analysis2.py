#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试凯利分析2筛选功能
验证具体的筛选过程和结果
"""

import sqlite3
import sys
import os

def debug_kelly_analysis2(match_id, kelly_type="kelly_home", stats_count=5, kelly_threshold=1.1, 
                         selected_companies=None, meaningless_threshold=1):
    """调试单场比赛的凯利分析2"""
    if selected_companies is None:
        selected_companies = ["Betfair", "bet365", "betfair", "betathome", "pinnacle"]
    
    print(f"=== 调试比赛 {match_id} 的凯利分析2 ===")
    print(f"参数: 凯利类型={kelly_type}, 统计数量={stats_count}, 凯利门槛={kelly_threshold}")
    print(f"监控公司: {selected_companies}")
    print(f"无意义公司门槛: {meaningless_threshold}")
    print()
    
    try:
        with sqlite3.connect("odds_data.db") as conn:
            conn.row_factory = sqlite3.Row
            cursor = conn.cursor()
            
            # 获取该比赛的所有赔率数据，按凯利指数降序排列
            cursor.execute(f'''
                SELECT {kelly_type}, return_rate, company_name
                FROM odds
                WHERE match_id = ? AND {kelly_type} IS NOT NULL AND return_rate IS NOT NULL AND company_name IS NOT NULL
                ORDER BY {kelly_type} DESC
            ''', (match_id,))
            
            odds_data = cursor.fetchall()
            
            print(f"总共找到 {len(odds_data)} 条赔率数据")
            
            if len(odds_data) < stats_count:
                print(f"❌ 数据不足：需要{stats_count}条，实际{len(odds_data)}条")
                return False
            
            # 显示所有数据（前20条）
            print(f"\n前20条赔率数据（按{kelly_type}降序）:")
            for i, row in enumerate(odds_data[:20]):
                kelly_val = float(row[0])
                return_rate = float(row[1])
                company = row[2]
                mark = "★" if company in selected_companies else " "
                print(f"  {i+1:2d}. {mark} {company:15s} - 凯利:{kelly_val:.3f}, 返还率:{return_rate:.2f}%")
            
            # 取前N家的数据
            top_odds = odds_data[:stats_count]
            
            print(f"\n前{stats_count}家数据:")
            kelly_values = []
            return_rates = []
            company_list = []
            
            for i, row in enumerate(top_odds):
                kelly_val = float(row[0])
                return_rate = float(row[1])
                company = row[2]
                
                kelly_values.append(kelly_val)
                return_rates.append(return_rate)
                company_list.append(company)
                
                mark = "★" if company in selected_companies else " "
                print(f"  {i+1}. {mark} {company:15s} - 凯利:{kelly_val:.3f}, 返还率:{return_rate:.2f}%")
            
            # 计算平均值
            avg_kelly = sum(kelly_values) / len(kelly_values)
            avg_return_rate = sum(return_rates) / len(return_rates)
            
            print(f"\n统计结果:")
            print(f"  平均凯利指数: {avg_kelly:.3f}")
            print(f"  平均返还率: {avg_return_rate:.2f}%")
            print(f"  前{stats_count}家公司列表: {company_list}")
            
            # 统计选中的博彩公司在公司列表中出现的次数
            print(f"\n观察次数统计:")
            observation_count = 0
            for company in selected_companies:
                count = company_list.count(company)
                observation_count += count
                if count > 0:
                    print(f"  {company}: 出现 {count} 次")
            
            print(f"  总观察次数: {observation_count}")
            
            # 判断是否符合筛选条件
            kelly_qualified = avg_kelly > kelly_threshold
            observation_qualified = observation_count <= meaningless_threshold
            
            print(f"\n筛选条件判断:")
            print(f"  凯利条件: {avg_kelly:.3f} > {kelly_threshold} = {kelly_qualified}")
            print(f"  观察条件: {observation_count} <= {meaningless_threshold} = {observation_qualified}")
            
            result = kelly_qualified and observation_qualified
            print(f"  最终结果: {result}")
            
            return result
            
    except Exception as e:
        print(f"❌ 调试失败: {e}")
        return False

def find_sample_matches():
    """找到一些样本比赛进行调试"""
    print("=== 寻找样本比赛 ===")
    try:
        with sqlite3.connect("odds_data.db") as conn:
            conn.row_factory = sqlite3.Row
            cursor = conn.cursor()
            
            # 找到有足够数据的比赛
            cursor.execute('''
                SELECT match_id, COUNT(*) as company_count
                FROM odds 
                WHERE kelly_home IS NOT NULL AND company_name IS NOT NULL
                GROUP BY match_id
                HAVING company_count >= 10
                ORDER BY company_count DESC
                LIMIT 5
            ''')
            
            matches = cursor.fetchall()
            print(f"找到 {len(matches)} 场有足够数据的比赛:")
            
            for match in matches:
                match_id = match[0]
                company_count = match[1]
                print(f"  比赛 {match_id}: {company_count} 家博彩公司")
            
            return [match[0] for match in matches]
            
    except Exception as e:
        print(f"❌ 寻找样本比赛失败: {e}")
        return []

def main():
    """主调试函数"""
    print("🔍 凯利分析2筛选功能调试")
    print("=" * 60)
    
    # 检查数据库文件是否存在
    if not os.path.exists("odds_data.db"):
        print("❌ 数据库文件 odds_data.db 不存在")
        return False
    
    # 找到样本比赛
    sample_matches = find_sample_matches()
    if not sample_matches:
        print("❌ 没有找到合适的样本比赛")
        return False
    
    # 测试第一个样本比赛
    match_id = sample_matches[0]
    
    print("\n" + "=" * 60)
    print("测试案例1: 应该被排除的情况（观察次数过多）")
    result1 = debug_kelly_analysis2(
        match_id=match_id,
        kelly_type="kelly_home",
        stats_count=5,
        kelly_threshold=1.0,  # 较低的门槛，容易满足
        selected_companies=["pinnacle", "bet365"],  # 常见的公司
        meaningless_threshold=1  # 很严格的门槛
    )
    
    print("\n" + "=" * 60)
    print("测试案例2: 应该通过的情况（观察次数为0）")
    result2 = debug_kelly_analysis2(
        match_id=match_id,
        kelly_type="kelly_home",
        stats_count=5,
        kelly_threshold=1.0,
        selected_companies=["不存在的公司A", "不存在的公司B"],  # 不存在的公司
        meaningless_threshold=0
    )
    
    print("\n" + "=" * 60)
    print("测试案例3: 边界情况（观察次数等于门槛）")
    result3 = debug_kelly_analysis2(
        match_id=match_id,
        kelly_type="kelly_home",
        stats_count=10,
        kelly_threshold=1.0,
        selected_companies=["pinnacle"],  # 只监控一家公司
        meaningless_threshold=2  # 允许出现2次
    )
    
    print("\n" + "=" * 60)
    print(f"📊 调试结果总结:")
    print(f"  案例1（应该被排除）: {result1}")
    print(f"  案例2（应该通过）: {result2}")
    print(f"  案例3（边界情况）: {result3}")
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试数据库连接和比赛结果获取
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_database_connection():
    """测试数据库连接"""
    
    print("🔍 测试数据库连接和比赛结果获取")
    print("=" * 50)
    
    try:
        from odds_combination_database import OddsCombinationDatabase
        
        # 查找数据库文件
        db_files = []
        for file in os.listdir('.'):
            if file.endswith('.db'):
                db_files.append(file)
        
        if not db_files:
            print("❌ 未找到数据库文件")
            return
        
        db_path = db_files[0]
        print(f"📂 使用数据库: {db_path}")
        
        # 创建数据库连接
        database = OddsCombinationDatabase(db_path)
        print("✅ 数据库连接创建成功")
        
        # 测试比赛结果获取
        test_matches = ["2596914", "2596918", "2596925"]
        
        print(f"\n🎯 测试比赛结果获取:")
        for match_id in test_matches:
            print(f"\n--- 比赛 {match_id} ---")
            
            # 使用get_match_info方法
            match_info = database.get_match_info(match_id)
            if match_info:
                print(f"✅ 找到比赛信息:")
                print(f"  主队: {match_info.get('home_team')}")
                print(f"  客队: {match_info.get('away_team')}")
                print(f"  主队得分: {match_info.get('home_score')}")
                print(f"  客队得分: {match_info.get('away_score')}")
                print(f"  比赛状态: {match_info.get('match_state')}")
                
                # 手动判断结果
                home_score = match_info.get('home_score')
                away_score = match_info.get('away_score')
                
                if home_score is not None and away_score is not None:
                    try:
                        home_score_int = int(home_score)
                        away_score_int = int(away_score)
                        
                        if home_score_int > away_score_int:
                            result = "主胜"
                        elif home_score_int < away_score_int:
                            result = "客胜"
                        else:
                            result = "平局"
                        
                        print(f"  📊 判断结果: {result}")
                        
                    except (ValueError, TypeError) as e:
                        print(f"  ❌ 得分转换失败: {e}")
                else:
                    print(f"  ❌ 得分数据缺失")
            else:
                print(f"❌ 未找到比赛信息")
                
            # 使用get_match_result方法
            match_result = database.get_match_result(match_id)
            if match_result:
                print(f"  📋 get_match_result结果: {match_result}")
            else:
                print(f"  ❌ get_match_result返回空")
        
        print(f"\n✅ 测试完成")
        
        # 模拟UI中的使用方式
        print(f"\n🔧 模拟UI中的使用:")
        
        def get_match_result_for_backtest_test(match_id: str) -> str:
            """模拟回测中的比赛结果获取"""
            try:
                print(f"DEBUG: 开始获取比赛 {match_id} 的结果")
                
                match_info = database.get_match_info(match_id)
                print(f"DEBUG: 获取到的比赛信息: {match_info}")
                
                if match_info:
                    home_score = match_info.get('home_score')
                    away_score = match_info.get('away_score')
                    
                    print(f"DEBUG: 主队得分: {home_score}, 客队得分: {away_score}")
                    
                    if home_score is not None and away_score is not None:
                        try:
                            home_score_int = int(home_score)
                            away_score_int = int(away_score)
                            print(f"DEBUG: 转换后的得分: {home_score_int} vs {away_score_int}")
                            
                            if home_score_int > away_score_int:
                                print(f"DEBUG: 判断结果: 主胜")
                                return "主胜"
                            elif home_score_int < away_score_int:
                                print(f"DEBUG: 判断结果: 客胜")
                                return "客胜"
                            else:
                                print(f"DEBUG: 判断结果: 平局")
                                return "平局"
                        except (ValueError, TypeError) as e:
                            print(f"DEBUG: 得分转换失败: {e}")
                            return "未知"
                    else:
                        print(f"DEBUG: 得分数据为空")
                else:
                    print(f"DEBUG: 未找到比赛记录")
                
                return "未知"
                
            except Exception as e:
                print(f"DEBUG: 获取比赛结果时发生错误: {e}")
                return "未知"
        
        # 测试模拟函数
        for match_id in ["2596914"]:
            print(f"\n🧪 测试比赛 {match_id}:")
            result = get_match_result_for_backtest_test(match_id)
            print(f"最终结果: {result}")
        
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_database_connection()

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试pinnacle博彩态度2筛选问题
"""

import sqlite3
import tkinter as tk
import sys
import os

# 添加当前目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from odds_scraper_ui import OddsScraperGUI

def debug_pinnacle_filtering():
    """调试pinnacle的筛选问题"""
    print("🔍 调试pinnacle博彩态度2筛选问题")
    print("=" * 60)
    
    try:
        # 创建主窗口
        root = tk.Tk()
        root.withdraw()  # 隐藏主窗口
        
        # 创建UI实例
        app = OddsScraperGUI(root)
        
        print("📊 检查数据库中的总比赛数...")
        
        # 检查数据库中的总比赛数
        with sqlite3.connect("odds_data.db") as conn:
            cursor = conn.cursor()
            cursor.execute('SELECT COUNT(DISTINCT match_id) FROM odds WHERE home_odds IS NOT NULL')
            total_matches = cursor.fetchone()[0]
            print(f"数据库中有主胜赔率的比赛总数: {total_matches}")
        
        print("\n🧪 测试pinnacle博彩态度2筛选...")
        
        # 设置筛选参数 - 完全按照您的设置
        widgets = app.filter_widgets['betting_attitude2']
        widgets['enable'].set(True)
        widgets['odds_type'].set('home_odds')
        widgets['threshold'].set('10')
        widgets['company'].set('pinnacle')
        
        print(f"设置参数:")
        print(f"  启用: {widgets['enable'].get()}")
        print(f"  赔率类型: {widgets['odds_type'].get()}")
        print(f"  阈值: {widgets['threshold'].get()}")
        print(f"  公司: {widgets['company'].get()}")
        
        # 直接测试博彩态度2筛选
        print("\n🔍 直接测试博彩态度2筛选...")
        betting_attitude2_matches = app.apply_betting_attitude2_filter(
            'home_odds', 10, 'pinnacle'
        )
        
        print(f"博彩态度2筛选结果: {len(betting_attitude2_matches)} 场比赛")
        
        if len(betting_attitude2_matches) == total_matches:
            print("⚠️  警告: 博彩态度2筛选返回了所有比赛！")
            print("这可能意味着:")
            print("  1. pinnacle在所有比赛中都满足条件")
            print("  2. 筛选逻辑有问题")
            print("  3. 数据问题")
        elif len(betting_attitude2_matches) == 0:
            print("⚠️  警告: 博彩态度2筛选没有返回任何比赛！")
        else:
            print(f"✅ 博彩态度2筛选正常，返回 {len(betting_attitude2_matches)} 场比赛")
        
        # 检查是否有其他筛选条件启用
        print("\n🔍 检查其他筛选条件...")
        other_filters_enabled = []
        
        for filter_name, filter_widgets in app.filter_widgets.items():
            if filter_name != 'betting_attitude2' and 'enable' in filter_widgets:
                if filter_widgets['enable'].get():
                    other_filters_enabled.append(filter_name)
        
        if other_filters_enabled:
            print(f"⚠️  发现其他启用的筛选条件: {other_filters_enabled}")
            print("这可能影响最终结果")
        else:
            print("✅ 没有其他筛选条件启用")
        
        # 模拟完整的筛选过程
        print("\n🧪 模拟完整筛选过程...")
        
        # 禁用所有其他筛选条件
        for filter_name, filter_widgets in app.filter_widgets.items():
            if filter_name != 'betting_attitude2' and 'enable' in filter_widgets:
                filter_widgets['enable'].set(False)
        
        # 重新测试
        app.apply_filters()
        
        print(f"完整筛选后的结果: {len(app.filtered_matches)} 场比赛")
        print(f"筛选状态: {app.filter_status_var.get()}")
        
        # 销毁窗口
        root.destroy()
        
    except Exception as e:
        print(f"❌ 调试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    debug_pinnacle_filtering()

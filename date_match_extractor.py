#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
日期比赛提取器
从指定日期的网页中提取比赛信息
"""

import requests
import re
import logging
from bs4 import BeautifulSoup
from typing import Dict, List, Optional
from datetime import datetime

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class DateMatchExtractor:
    def __init__(self):
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'zh-C<PERSON>,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1'
        })

    def extract_matches_from_date_page(self, date_url: str) -> Dict[str, List[Dict]]:
        """
        从日期页面提取比赛信息
        
        Args:
            date_url: 日期页面URL，如 https://m.titan007.com/Schedule.htm?date=2025-07-22
            
        Returns:
            Dict[str, List[Dict]]: 按联赛分组的比赛信息
            {
                "英超": [
                    {
                        "match_id": "2826834",
                        "home_team": "曼城",
                        "away_team": "利物浦",
                        "match_time": "2025-07-22 20:00:00",
                        "league": "英超"
                    },
                    ...
                ],
                "西甲": [...],
                ...
            }
        """
        try:
            logger.info(f"开始提取日期页面比赛: {date_url}")
            
            # 获取页面内容
            response = self.session.get(date_url, timeout=30)
            response.raise_for_status()
            response.encoding = 'utf-8'
            
            # 解析HTML
            soup = BeautifulSoup(response.text, 'html.parser')
            
            # 提取比赛信息
            matches_by_league = self._parse_matches_from_soup(soup, date_url)
            
            total_matches = sum(len(matches) for matches in matches_by_league.values())
            logger.info(f"成功提取 {len(matches_by_league)} 个联赛的 {total_matches} 场比赛")
            
            return matches_by_league
            
        except Exception as e:
            logger.error(f"提取日期页面比赛失败: {e}")
            return {}

    def _parse_matches_from_soup(self, soup: BeautifulSoup, date_url: str) -> Dict[str, List[Dict]]:
        """从BeautifulSoup对象中解析比赛信息"""
        matches_by_league = {}

        try:
            # 首先尝试从JavaScript数据中解析
            js_matches = self._parse_javascript_data(soup, date_url)
            if js_matches:
                logger.info(f"从JavaScript数据中找到 {sum(len(matches) for matches in js_matches.values())} 场比赛")
                return js_matches

            # 如果JavaScript解析失败，尝试HTML解析
            html_matches = self._parse_html_structure(soup, date_url)
            if html_matches:
                logger.info(f"从HTML结构中找到 {sum(len(matches) for matches in html_matches.values())} 场比赛")
                return html_matches

            return matches_by_league

        except Exception as e:
            logger.error(f"解析比赛信息失败: {e}")
            return {}

    def _parse_javascript_data(self, soup: BeautifulSoup, date_url: str) -> Dict[str, List[Dict]]:
        """从JavaScript数据中解析比赛信息"""
        matches_by_league = {}

        try:
            # 查找包含比赛数据的script标签
            scripts = soup.find_all('script')

            for script in scripts:
                script_text = script.get_text()
                if not script_text:
                    continue

                # 查找比赛数据模式
                # 格式类似: 2833447^2705^0^20250723093000^^黑龙江U15^沈阳大球中心U15^...
                match_pattern = r'(\d{7,})\^(\d+)\^(\d+)\^(\d{14})\^\^([^^\r\n]+)\^([^^\r\n]+)\^'
                matches = re.findall(match_pattern, script_text)

                if matches:
                    logger.info(f"在JavaScript中找到 {len(matches)} 场比赛")

                    # 同时查找联赛信息
                    league_data = self._extract_league_data_from_script(script_text)

                    for match_data in matches:
                        match_id, league_id, _, time_str, home_team, away_team = match_data

                        # 解析时间
                        match_time = self._parse_time_string(time_str, date_url)

                        # 获取联赛名称
                        league_name = league_data.get(league_id, f"联赛{league_id}")

                        match_info = {
                            'match_id': match_id,
                            'home_team': home_team.strip(),
                            'away_team': away_team.strip(),
                            'match_time': match_time,
                            'league': league_name
                        }

                        if league_name not in matches_by_league:
                            matches_by_league[league_name] = []
                        matches_by_league[league_name].append(match_info)

                    break  # 找到数据后退出循环

            return matches_by_league

        except Exception as e:
            logger.error(f"解析JavaScript数据失败: {e}")
            return {}

    def _extract_league_data_from_script(self, script_text: str) -> Dict[str, str]:
        """从脚本中提取联赛数据"""
        league_data = {}

        try:
            # 查找联赛数据字符串
            # 格式: var sclassDataStr = "欧冠杯^103^1^13^#f75000!欧会杯^2187^1^1^#D078D8!..."
            sclass_pattern = r'sclassDataStr\s*=\s*"([^"]+)"'
            sclass_match = re.search(sclass_pattern, script_text)

            if sclass_match:
                sclass_data = sclass_match.group(1)
                logger.info(f"找到联赛数据字符串，长度: {len(sclass_data)}")

                # 按感叹号分割不同联赛
                league_entries = sclass_data.split('!')

                for entry in league_entries:
                    if not entry.strip():
                        continue

                    # 解析每个联赛条目
                    # 格式: 联赛名称^联赛ID^其他字段^#颜色
                    parts = entry.split('^')
                    if len(parts) >= 2:
                        league_name = parts[0].strip()
                        league_id = parts[1].strip()

                        if league_name and league_id.isdigit():
                            league_data[league_id] = league_name
                            logger.debug(f"映射联赛: {league_id} -> {league_name}")

                logger.info(f"成功提取 {len(league_data)} 个联赛映射")

                # 显示前几个联赛映射用于调试
                if league_data:
                    sample_leagues = list(league_data.items())[:5]
                    logger.info(f"联赛映射示例: {sample_leagues}")

            else:
                logger.warning("未找到联赛数据字符串")

                # 备用方案：尝试其他模式
                logger.info("尝试备用联赛数据提取方案...")
                backup_pattern = r'([^!^$]+)\^(\d+)\^[^!]*(?:!|$)'
                backup_matches = re.findall(backup_pattern, script_text)

                for league_name, league_id in backup_matches:
                    if league_name and league_id and len(league_name) < 50:  # 过滤掉过长的无效数据
                        # 清理联赛名称
                        clean_name = re.sub(r'[^\u4e00-\u9fff\w\s]', '', league_name).strip()
                        if clean_name and len(clean_name) > 1:
                            league_data[league_id] = clean_name

                logger.info(f"备用方案提取到 {len(league_data)} 个联赛映射")

        except Exception as e:
            logger.error(f"提取联赛数据失败: {e}")

        return league_data

    def _parse_time_string(self, time_str: str, date_url: str) -> str:
        """解析时间字符串"""
        try:
            if len(time_str) == 14:  # YYYYMMDDHHMMSS
                year = time_str[:4]
                month = time_str[4:6]
                day = time_str[6:8]
                hour = time_str[8:10]
                minute = time_str[10:12]
                return f"{year}-{month}-{day} {hour}:{minute}:00"
        except Exception:
            pass

        # 如果解析失败，使用URL中的日期
        date_match = re.search(r'date=(\d{4}-\d{2}-\d{2})', date_url)
        if date_match:
            return f"{date_match.group(1)} 00:00:00"

        return f"{datetime.now().strftime('%Y-%m-%d')} 00:00:00"

    def _parse_html_structure(self, soup: BeautifulSoup, date_url: str) -> Dict[str, List[Dict]]:
        """从HTML结构中解析比赛信息（备用方法）"""
        matches_by_league = {}

        try:
            # 查找比赛表格或列表
            match_rows = soup.find_all('tr', class_='') or soup.find_all('div', class_='match-item')

            if not match_rows:
                # 尝试其他可能的选择器
                match_rows = soup.find_all('tr') or soup.find_all('div', class_='game')

            logger.info(f"找到 {len(match_rows)} 个可能的比赛行")

            current_league = "未知联赛"

            for row in match_rows:
                try:
                    # 检查是否是联赛标题行
                    league_name = self._extract_league_name(row)
                    if league_name:
                        current_league = league_name
                        continue

                    # 提取比赛信息
                    match_info = self._extract_match_info(row, current_league, date_url)
                    if match_info:
                        league = match_info['league']
                        if league not in matches_by_league:
                            matches_by_league[league] = []
                        matches_by_league[league].append(match_info)

                except Exception as e:
                    logger.debug(f"解析比赛行失败: {e}")
                    continue

            return matches_by_league

        except Exception as e:
            logger.error(f"解析HTML结构失败: {e}")
            return {}

    def _extract_league_name(self, row) -> Optional[str]:
        """提取联赛名称"""
        try:
            # 查找联赛名称的各种可能位置
            league_selectors = [
                'td.league',
                'div.league',
                'span.league',
                '.league-name',
                'td[colspan]',  # 通常联赛标题会跨列
                'div.title'
            ]
            
            for selector in league_selectors:
                league_elem = row.select_one(selector)
                if league_elem:
                    league_text = league_elem.get_text(strip=True)
                    if league_text and len(league_text) > 1:
                        return league_text
            
            # 检查是否整行都是联赛名称
            row_text = row.get_text(strip=True)
            if row_text and len(row_text.split()) <= 3 and not any(char.isdigit() for char in row_text):
                return row_text
            
            return None
            
        except Exception:
            return None

    def _extract_match_info(self, row, current_league: str, date_url: str) -> Optional[Dict]:
        """从行中提取比赛信息"""
        try:
            # 查找比赛ID链接
            match_id = self._extract_match_id(row)
            if not match_id:
                return None
            
            # 提取队伍名称
            teams = self._extract_teams(row)
            if not teams:
                return None
            
            home_team, away_team = teams
            
            # 提取比赛时间
            match_time = self._extract_match_time(row, date_url)
            
            return {
                'match_id': match_id,
                'home_team': home_team,
                'away_team': away_team,
                'match_time': match_time,
                'league': current_league
            }
            
        except Exception as e:
            logger.debug(f"提取比赛信息失败: {e}")
            return None

    def _extract_match_id(self, row) -> Optional[str]:
        """提取比赛ID"""
        try:
            # 查找包含比赛ID的链接
            links = row.find_all('a', href=True)
            
            for link in links:
                href = link['href']
                
                # 匹配各种可能的比赛ID模式
                patterns = [
                    r'/(\d{7,})',  # 7位以上数字
                    r'id=(\d{7,})',
                    r'match[_-]?id[=:](\d{7,})',
                    r'fixture[/_](\d{7,})',
                    r'game[/_](\d{7,})'
                ]
                
                for pattern in patterns:
                    match = re.search(pattern, href)
                    if match:
                        return match.group(1)
            
            return None
            
        except Exception:
            return None

    def _extract_teams(self, row) -> Optional[tuple]:
        """提取主客队名称"""
        try:
            # 查找队伍名称的各种可能位置
            team_selectors = [
                'td.team',
                'div.team',
                'span.team',
                '.home-team',
                '.away-team',
                'td:nth-child(3)',  # 通常第3列是主队
                'td:nth-child(5)'   # 通常第5列是客队
            ]
            
            teams = []
            
            # 尝试直接查找主客队
            home_elem = row.select_one('.home-team, .home, td:nth-child(3)')
            away_elem = row.select_one('.away-team, .away, td:nth-child(5)')
            
            if home_elem and away_elem:
                home_team = home_elem.get_text(strip=True)
                away_team = away_elem.get_text(strip=True)
                if home_team and away_team:
                    return (home_team, away_team)
            
            # 尝试从文本中解析 "主队 vs 客队" 格式
            row_text = row.get_text()
            vs_patterns = [
                r'(.+?)\s+vs\s+(.+)',
                r'(.+?)\s+-\s+(.+)',
                r'(.+?)\s+对\s+(.+)',
                r'(.+?)\s+VS\s+(.+)'
            ]
            
            for pattern in vs_patterns:
                match = re.search(pattern, row_text)
                if match:
                    home_team = match.group(1).strip()
                    away_team = match.group(2).strip()
                    if home_team and away_team:
                        return (home_team, away_team)
            
            return None
            
        except Exception:
            return None

    def _extract_match_time(self, row, date_url: str) -> str:
        """提取比赛时间（优先使用准确时间提取）"""
        try:
            # 首先尝试获取比赛ID
            match_id = self._extract_match_id(row)

            if match_id:
                # 尝试从分析页面获取准确时间
                accurate_time = self._extract_accurate_match_time(match_id)
                if accurate_time and accurate_time.get('success'):
                    logger.info(f"从分析页面获取到准确时间: {accurate_time['match_time']}")
                    return accurate_time['match_time']

            # 回退到原有方法：从URL中提取日期
            date_match = re.search(r'date=(\d{4}-\d{2}-\d{2})', date_url)
            if date_match:
                date_str = date_match.group(1)
            else:
                date_str = datetime.now().strftime('%Y-%m-%d')

            # 查找时间信息
            time_selectors = [
                'td.time',
                'div.time',
                'span.time',
                '.match-time',
                'td:nth-child(2)'  # 通常第2列是时间
            ]

            for selector in time_selectors:
                time_elem = row.select_one(selector)
                if time_elem:
                    time_text = time_elem.get_text(strip=True)
                    # 匹配时间格式 HH:MM
                    time_match = re.search(r'(\d{1,2}):(\d{2})', time_text)
                    if time_match:
                        hour = time_match.group(1).zfill(2)
                        minute = time_match.group(2)
                        logger.info(f"使用URL日期+页面时间: {date_str} {hour}:{minute}:00")
                        return f"{date_str} {hour}:{minute}:00"

            # 默认时间
            logger.warning(f"未找到时间信息，使用默认时间: {date_str} 00:00:00")
            return f"{date_str} 00:00:00"

        except Exception:
            return f"{datetime.now().strftime('%Y-%m-%d')} 00:00:00"

    def get_leagues_summary(self, matches_by_league: Dict[str, List[Dict]]) -> str:
        """获取联赛摘要信息"""
        if not matches_by_league:
            return "未找到任何比赛"
        
        summary_lines = []
        total_matches = 0
        
        for league, matches in matches_by_league.items():
            match_count = len(matches)
            total_matches += match_count
            summary_lines.append(f"{league}: {match_count} 场")
        
        summary_lines.insert(0, f"共 {len(matches_by_league)} 个联赛，{total_matches} 场比赛")
        
        return "\n".join(summary_lines)

    def _extract_accurate_match_time(self, match_id: str) -> Dict:
        """从分析页面提取准确的比赛时间"""
        if not match_id:
            return {'success': False, 'error': '比赛ID为空'}

        try:
            # 构建分析页面URL
            analysis_url = f"https://m.titan007.com/Analy/ShiJian/{match_id}.htm"

            # 获取页面内容
            response = self.session.get(analysis_url, timeout=15)
            response.raise_for_status()
            response.encoding = 'utf-8'

            content = response.text

            # 查找时间变量
            time_patterns = [
                r'var\s+headMatchTime\s*=\s*["\']([^"\']+)["\']',
                r'headMatchTime\s*=\s*["\']([^"\']+)["\']',
                r'matchTime\s*=\s*["\']([^"\']+)["\']',
                r'var\s+matchTime\s*=\s*["\']([^"\']+)["\']',
            ]

            for pattern in time_patterns:
                match = re.search(pattern, content, re.IGNORECASE)
                if match:
                    time_str = match.group(1)

                    # 解析时间
                    try:
                        if 'T' in time_str:
                            # ISO格式: 2025-07-27T19:00:00
                            dt = datetime.fromisoformat(time_str.replace('T', ' ').replace('Z', ''))
                        else:
                            # 其他格式
                            dt = datetime.strptime(time_str, "%Y-%m-%d %H:%M:%S")

                        return {
                            'success': True,
                            'match_time': dt.strftime('%Y-%m-%d %H:%M:%S'),
                            'match_date': dt.strftime('%Y-%m-%d'),
                            'match_time_only': dt.strftime('%H:%M:%S'),
                            'year': dt.year,
                            'month': dt.month,
                            'day': dt.day,
                            'hour': dt.hour,
                            'minute': dt.minute,
                            'source': 'analysis_page',
                            'raw_time_str': time_str
                        }
                    except Exception as e:
                        logger.warning(f"时间解析失败: {time_str}, 错误: {e}")
                        continue

            return {'success': False, 'error': '未找到时间信息'}

        except Exception as e:
            logger.warning(f"从分析页面提取时间失败: {e}")
            return {'success': False, 'error': str(e)}


if __name__ == "__main__":
    # 测试代码
    extractor = DateMatchExtractor()
    
    test_url = "https://m.titan007.com/Schedule.htm?date=2025-07-22"
    matches = extractor.extract_matches_from_date_page(test_url)
    
    print("提取结果:")
    print(extractor.get_leagues_summary(matches))
    
    # 显示前几场比赛的详细信息
    for league, match_list in list(matches.items())[:2]:
        print(f"\n{league} 的比赛:")
        for match in match_list[:3]:
            print(f"  {match['match_id']}: {match['home_team']} vs {match['away_team']} ({match['match_time']})")

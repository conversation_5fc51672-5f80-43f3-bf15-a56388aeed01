#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试删除功能修复
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from database import OddsDatabase
import logging

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_delete_with_cache_simulation():
    """模拟界面缓存情况下的删除测试"""
    print("🧪 测试删除功能修复 - 模拟界面缓存")
    print("=" * 60)
    
    # 连接数据库
    db = OddsDatabase()
    
    # 1. 获取一些比赛数据（模拟筛选缓存）
    print("1️⃣ 模拟筛选缓存")
    matches = db.get_all_matches()
    if not matches:
        print("   ❌ 无比赛数据")
        return
        
    # 模拟筛选缓存（取前10场比赛）
    filtered_matches = matches[:10]
    print(f"   📋 模拟筛选缓存: {len(filtered_matches)} 场比赛")
    
    for i, match in enumerate(filtered_matches[:3], 1):
        match_id = match.get('match_id')
        teams = f"{match.get('home_team', '')} vs {match.get('away_team', '')}"
        print(f"      {i}. {match_id} - {teams}")
    
    # 2. 选择要删除的比赛
    test_match = filtered_matches[0]
    test_match_id = test_match.get('match_id')
    test_teams = f"{test_match.get('home_team', '')} vs {test_match.get('away_team', '')}"
    
    print(f"\n2️⃣ 选择删除目标")
    print(f"   🎯 目标比赛: {test_match_id} - {test_teams}")
    
    # 3. 执行删除操作
    print(f"\n3️⃣ 执行删除操作")
    delete_result = db.delete_match(test_match_id)
    print(f"   删除结果: {delete_result}")
    
    # 4. 模拟界面缓存更新逻辑
    print(f"\n4️⃣ 模拟界面缓存更新")
    
    # 删除前缓存状态
    print(f"   删除前缓存中比赛数: {len(filtered_matches)}")
    cache_has_target = any(m.get('match_id') == test_match_id for m in filtered_matches)
    print(f"   缓存中是否包含目标比赛: {cache_has_target}")
    
    # 模拟修复后的缓存更新逻辑
    if delete_result:
        # 从缓存中移除已删除的比赛
        filtered_matches = [m for m in filtered_matches if m.get('match_id') != test_match_id]
        print(f"   ✅ 已从缓存中移除删除的比赛")
        print(f"   删除后缓存中比赛数: {len(filtered_matches)}")
        
        # 验证缓存中是否还有目标比赛
        cache_still_has_target = any(m.get('match_id') == test_match_id for m in filtered_matches)
        print(f"   缓存中是否仍包含目标比赛: {cache_still_has_target}")
        
        if not cache_still_has_target:
            print(f"   ✅ 缓存更新成功，已删除比赛不再显示")
        else:
            print(f"   ❌ 缓存更新失败，已删除比赛仍在缓存中")
    
    # 5. 验证数据库状态
    print(f"\n5️⃣ 验证数据库状态")
    match_info = db.get_match_info(test_match_id)
    if match_info:
        print(f"   ❌ 数据库中仍存在已删除比赛")
    else:
        print(f"   ✅ 数据库中已删除目标比赛")
    
    # 6. 模拟界面刷新
    print(f"\n6️⃣ 模拟界面刷新")
    
    # 如果有筛选缓存，使用缓存数据
    if filtered_matches:
        print(f"   使用筛选缓存刷新界面")
        display_matches = filtered_matches
    else:
        print(f"   重新查询数据库刷新界面")
        display_matches = db.get_all_matches()
    
    # 检查显示的比赛中是否包含已删除的比赛
    display_has_target = any(m.get('match_id') == test_match_id for m in display_matches)
    print(f"   界面显示中是否包含已删除比赛: {display_has_target}")
    
    if not display_has_target:
        print(f"   ✅ 界面刷新正确，已删除比赛不再显示")
    else:
        print(f"   ❌ 界面刷新有问题，已删除比赛仍在显示")

def test_edge_cases():
    """测试边缘情况"""
    print(f"\n🔍 测试边缘情况")
    print("=" * 40)
    
    db = OddsDatabase()
    
    # 测试删除不存在的比赛
    print("1️⃣ 测试删除不存在的比赛")
    fake_match_id = "999999999"
    delete_result = db.delete_match(fake_match_id)
    print(f"   删除不存在比赛的结果: {delete_result}")
    
    # 测试空字符串
    print("2️⃣ 测试删除空字符串ID")
    try:
        delete_result = db.delete_match("")
        print(f"   删除空ID的结果: {delete_result}")
    except Exception as e:
        print(f"   删除空ID异常: {e}")

if __name__ == "__main__":
    test_delete_with_cache_simulation()
    test_edge_cases()

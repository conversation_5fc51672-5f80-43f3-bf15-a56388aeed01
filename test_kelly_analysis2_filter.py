#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试凯利分析2筛选功能
验证基于博彩公司出现频率的凯利分析筛选逻辑
"""

import sqlite3
import sys
import os
from database import OddsDatabase

def test_company_data_availability():
    """测试博彩公司数据的可用性"""
    print("=== 测试博彩公司数据的可用性 ===")
    try:
        with sqlite3.connect("odds_data.db") as conn:
            conn.row_factory = sqlite3.Row
            cursor = conn.cursor()
            
            # 获取所有博彩公司
            cursor.execute('SELECT DISTINCT company_name FROM odds WHERE company_name IS NOT NULL ORDER BY company_name')
            companies = [row[0] for row in cursor.fetchall()]
            
            print(f"✅ 数据库中的博彩公司: {len(companies)} 家")
            print("博彩公司列表:")
            for i, company in enumerate(companies, 1):
                print(f"  {i:2d}. {company}")
            
            # 检查每家公司的数据量
            print("\n各公司数据量统计:")
            for company in companies[:10]:  # 只显示前10家
                cursor.execute('SELECT COUNT(*) FROM odds WHERE company_name = ?', (company,))
                count = cursor.fetchone()[0]
                print(f"  {company}: {count} 条记录")
            
            return len(companies) > 0
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def test_kelly_analysis2_logic():
    """测试凯利分析2逻辑"""
    print("\n=== 测试凯利分析2逻辑 ===")
    
    def analyze_kelly2_sample(odds_data, stats_count, kelly_threshold, selected_companies, meaningless_threshold):
        """模拟凯利分析2逻辑"""
        if len(odds_data) < stats_count:
            return False, "数据不足"
        
        # 按凯利值降序排序
        sorted_data = sorted(odds_data, key=lambda x: x[0], reverse=True)
        
        # 取前N家
        top_data = sorted_data[:stats_count]
        
        # 计算平均凯利值和获取公司列表
        avg_kelly = sum(item[0] for item in top_data) / len(top_data)
        company_list = [item[2] for item in top_data]  # 公司列表（包含重复项）
        
        # 统计选中公司的出现次数
        observation_count = 0
        for company in selected_companies:
            observation_count += company_list.count(company)
        
        # 判断条件
        kelly_qualified = avg_kelly > kelly_threshold
        observation_qualified = observation_count <= meaningless_threshold
        
        result = kelly_qualified and observation_qualified
        
        return result, {
            'avg_kelly': avg_kelly,
            'company_list': company_list,
            'observation_count': observation_count,
            'kelly_qualified': kelly_qualified,
            'observation_qualified': observation_qualified
        }
    
    # 测试用例（格式：凯利值, 返还率, 公司名）
    test_cases = [
        {
            'name': '符合条件的案例',
            'data': [(1.08, 96.5, '公司A'), (1.07, 96.3, '公司B'), (1.06, 96.1, '公司C'),
                    (1.05, 95.9, '公司D'), (1.04, 95.7, '公司E'), (1.03, 95.5, '公司F'),
                    (1.02, 95.3, '公司G'), (1.01, 95.1, '公司H'), (1.00, 94.9, '公司I'),
                    (0.99, 94.7, '公司J')],
            'stats_count': 10,
            'kelly_threshold': 1.03,  # 调整门槛，确保平均值1.035能通过
            'selected_companies': ['公司X', '公司Y'],  # 不在前10家中的公司
            'meaningless_threshold': 2,
            'expected': True
        },
        {
            'name': '凯利值不足的案例',
            'data': [(1.02, 96.5, '公司A'), (1.01, 96.3, '公司B'), (1.00, 96.1, '公司C'), 
                    (0.99, 95.9, '公司D'), (0.98, 95.7, '公司E'), (0.97, 95.5, '公司F'),
                    (0.96, 95.3, '公司G'), (0.95, 95.1, '公司H'), (0.94, 94.9, '公司I'), 
                    (0.93, 94.7, '公司J')],
            'stats_count': 10,
            'kelly_threshold': 1.05,
            'selected_companies': ['公司X', '公司Y'],
            'meaningless_threshold': 2,
            'expected': False
        },
        {
            'name': '观察次数过多的案例',
            'data': [(1.08, 96.5, '公司A'), (1.07, 96.3, '公司B'), (1.06, 96.1, '公司A'), 
                    (1.05, 95.9, '公司B'), (1.04, 95.7, '公司A'), (1.03, 95.5, '公司F'),
                    (1.02, 95.3, '公司G'), (1.01, 95.1, '公司H'), (1.00, 94.9, '公司I'), 
                    (0.99, 94.7, '公司J')],
            'stats_count': 10,
            'kelly_threshold': 1.05,
            'selected_companies': ['公司A', '公司B'],  # 公司A出现3次，公司B出现2次，总共5次
            'meaningless_threshold': 2,
            'expected': False
        },
        {
            'name': '边界条件的案例',
            'data': [(1.08, 96.5, '公司A'), (1.07, 96.3, '公司B'), (1.06, 96.1, '公司C'),
                    (1.05, 95.9, '公司D'), (1.04, 95.7, '公司E'), (1.03, 95.5, '公司F'),
                    (1.02, 95.3, '公司G'), (1.01, 95.1, '公司H'), (1.00, 94.9, '公司I'),
                    (0.99, 94.7, '公司A')],  # 公司A出现2次
            'stats_count': 10,
            'kelly_threshold': 1.03,  # 调整门槛，确保平均值1.035能通过
            'selected_companies': ['公司A'],  # 公司A出现2次，等于门槛
            'meaningless_threshold': 2,
            'expected': True
        }
    ]
    
    all_passed = True
    for case in test_cases:
        result, details = analyze_kelly2_sample(
            case['data'], case['stats_count'], 
            case['kelly_threshold'], case['selected_companies'], case['meaningless_threshold']
        )
        
        if result == case['expected']:
            print(f"✅ {case['name']}: {result}")
            if isinstance(details, dict):
                print(f"   平均凯利: {details['avg_kelly']:.3f}, 观察次数: {details['observation_count']}")
                print(f"   公司列表: {details['company_list']}")
        else:
            print(f"❌ {case['name']}: {result}, 期望: {case['expected']}")
            if isinstance(details, dict):
                print(f"   平均凯利: {details['avg_kelly']:.3f}, 观察次数: {details['observation_count']}")
            all_passed = False
    
    return all_passed

def test_real_kelly_analysis2():
    """测试真实数据的凯利分析2"""
    print("\n=== 测试真实数据的凯利分析2 ===")
    try:
        with sqlite3.connect("odds_data.db") as conn:
            conn.row_factory = sqlite3.Row
            cursor = conn.cursor()
            
            # 获取一个有足够凯利数据的比赛
            cursor.execute('''
                SELECT match_id, COUNT(*) as company_count
                FROM odds 
                WHERE kelly_home IS NOT NULL AND company_name IS NOT NULL
                GROUP BY match_id
                HAVING company_count >= 10
                ORDER BY company_count DESC
                LIMIT 1
            ''')
            
            match_row = cursor.fetchone()
            if not match_row:
                print("❌ 没有找到足够的凯利数据进行测试")
                return False
            
            match_id = match_row[0]
            company_count = match_row[1]
            print(f"测试比赛 {match_id}: {company_count} 家博彩公司")
            
            # 获取该比赛的凯利数据
            cursor.execute('''
                SELECT kelly_home, return_rate, company_name 
                FROM odds 
                WHERE match_id = ? AND kelly_home IS NOT NULL AND company_name IS NOT NULL
                ORDER BY kelly_home DESC
            ''', (match_id,))
            
            kelly_data = cursor.fetchall()
            
            # 获取前几家公司作为测试对象
            top_companies = [row[2] for row in kelly_data[:5]]
            print(f"选择观察的公司: {top_companies[:3]}")  # 只显示前3家
            
            # 测试不同的参数设置
            test_params = [
                {'stats_count': 10, 'kelly_threshold': 1.05, 'selected_companies': top_companies[:2], 'meaningless_threshold': 3},
                {'stats_count': 15, 'kelly_threshold': 1.02, 'selected_companies': top_companies[:1], 'meaningless_threshold': 1},
                {'stats_count': 5, 'kelly_threshold': 1.08, 'selected_companies': top_companies[:3], 'meaningless_threshold': 5}
            ]
            
            for i, params in enumerate(test_params, 1):
                stats_count = params['stats_count']
                kelly_threshold = params['kelly_threshold']
                selected_companies = params['selected_companies']
                meaningless_threshold = params['meaningless_threshold']
                
                if len(kelly_data) >= stats_count:
                    # 取前N家数据
                    top_data = kelly_data[:stats_count]
                    
                    # 计算平均凯利值和统计公司出现次数
                    kelly_values = [float(row[0]) for row in top_data]
                    company_list = [row[2] for row in top_data]
                    
                    avg_kelly = sum(kelly_values) / len(kelly_values)
                    
                    # 统计观察公司的出现次数
                    observation_count = 0
                    for company in selected_companies:
                        observation_count += company_list.count(company)
                    
                    # 判断条件
                    kelly_qualified = avg_kelly > kelly_threshold
                    observation_qualified = observation_count <= meaningless_threshold
                    result = kelly_qualified and observation_qualified
                    
                    status = "✅ 符合" if result else "❌ 不符合"
                    print(f"  参数组{i}: {status}")
                    print(f"    统计{stats_count}家, 凯利门槛{kelly_threshold}, 观察{len(selected_companies)}家公司, 门槛{meaningless_threshold}")
                    print(f"    平均凯利: {avg_kelly:.3f} ({'>' if kelly_qualified else '<='} {kelly_threshold})")
                    print(f"    观察次数: {observation_count} ({'<=' if observation_qualified else '>'} {meaningless_threshold})")
                    print(f"    前{stats_count}家公司: {company_list}")
                else:
                    print(f"  参数组{i}: ❌ 数据不足（需要{stats_count}家，实际{len(kelly_data)}家）")
            
            return True
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🧪 凯利分析2筛选功能测试")
    print("=" * 50)
    
    # 检查数据库文件是否存在
    if not os.path.exists("odds_data.db"):
        print("❌ 数据库文件 odds_data.db 不存在")
        return False
    
    tests = [
        test_company_data_availability,
        test_kelly_analysis2_logic,
        test_real_kelly_analysis2
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"❌ 测试异常: {e}")
    
    print("\n" + "=" * 50)
    print(f"📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！凯利分析2筛选功能应该可以正常工作。")
        return True
    else:
        print("⚠️  部分测试失败，请检查相关功能。")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)

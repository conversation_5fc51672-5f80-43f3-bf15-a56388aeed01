#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试观察清单功能的调试脚本
"""

import sys
import os
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

def test_watchlist_debug():
    """测试观察清单功能的调试"""
    print("🔍 测试观察清单功能调试")
    
    try:
        # 模拟分析结果数据结构
        mock_analysis_results = {
            'success': True,
            'results': [
                {
                    'combination_index': 0,
                    'combination': [
                        {'home_odds': 1.9, 'draw_odds': 3.4, 'away_odds': 4.1},
                        {'home_odds': 2.1, 'draw_odds': 3.2, 'away_odds': 3.8}
                    ],
                    'match_count': 5,
                    'result_stats': {'win': 3, 'draw': 1, 'loss': 1},
                    'matched_matches': []  # 这里可能是空的，导致问题
                },
                {
                    'combination_index': 1,
                    'combination': [
                        {'home_odds': 2.0, 'draw_odds': 3.3, 'away_odds': 4.0},
                        {'home_odds': 2.2, 'draw_odds': 3.1, 'away_odds': 3.7}
                    ],
                    'match_count': 3,
                    'result_stats': {'win': 2, 'draw': 0, 'loss': 1},
                    'matched_matches': [
                        {'match_id': '2701757'},
                        {'match_id': '2701758'}
                    ]
                }
            ],
            'total_combinations': 2
        }
        
        print(f"📊 模拟分析结果:")
        print(f"  总组合数: {len(mock_analysis_results['results'])}")
        
        for i, result in enumerate(mock_analysis_results['results']):
            print(f"\n  组合 {i+1}:")
            print(f"    匹配次数: {result['match_count']}")
            print(f"    胜平负: {result['result_stats']}")
            print(f"    匹配比赛数: {len(result.get('matched_matches', []))}")
            
            # 模拟尾部概率计算
            if i == 0:
                tail_prob = 0.023  # 符合条件
            else:
                tail_prob = 0.156  # 不符合条件
            
            print(f"    尾部概率: {tail_prob}")
        
        # 测试筛选逻辑
        threshold = 0.05
        print(f"\n🔍 测试筛选逻辑（阈值: {threshold}）:")
        
        filtered_count = 0
        for i, result in enumerate(mock_analysis_results['results']):
            # 模拟尾部概率
            if i == 0:
                tail_prob = 0.023
            else:
                tail_prob = 0.156
            
            print(f"\n  组合 {i+1}: 尾部概率 {tail_prob}")
            
            if tail_prob <= threshold:
                print(f"    ✅ 符合条件 ({tail_prob} <= {threshold})")
                matched_matches = result.get('matched_matches', [])
                print(f"    匹配比赛: {len(matched_matches)} 场")
                
                if not matched_matches:
                    print(f"    ⚠️ 没有matched_matches，需要使用当前比赛ID")
                    filtered_count += 1  # 假设使用当前比赛ID
                else:
                    for match_info in matched_matches:
                        if isinstance(match_info, dict):
                            match_id = match_info.get('match_id')
                        else:
                            match_id = str(match_info)
                        print(f"      比赛ID: {match_id}")
                        filtered_count += 1
            else:
                print(f"    ❌ 不符合条件 ({tail_prob} > {threshold})")
        
        print(f"\n📋 筛选结果:")
        print(f"  符合条件的比赛数: {filtered_count}")
        
        # 分析可能的问题
        print(f"\n🔧 可能的问题分析:")
        print(f"  1. matched_matches为空 - 需要使用当前分析的比赛ID")
        print(f"  2. 数据库连接问题 - get_match_detail_for_watchlist返回None")
        print(f"  3. 尾部概率计算问题 - calculate_probabilities返回异常值")
        print(f"  4. UI更新问题 - 表格填充失败")
        
        print(f"\n✅ 调试测试完成")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    test_watchlist_debug()

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
天启代理官方文档测试
基于您提供的官方API文档
"""

import requests
import hashlib
import hmac
import time
import json
from urllib.parse import urlencode

def generate_sign_methods(secret, params):
    """生成各种可能的签名方法"""
    signs = {}
    
    # 移除sign参数本身
    clean_params = {k: v for k, v in params.items() if k != 'sign'}
    
    # 1. 直接使用secret作为sign
    signs["direct_secret"] = secret
    
    # 2. MD5(secret)
    signs["md5_secret"] = hashlib.md5(secret.encode('utf-8')).hexdigest()
    
    # 3. 参数按字典序排列 + secret 的MD5
    sorted_params = sorted(clean_params.items())
    param_string = "&".join([f"{k}={v}" for k, v in sorted_params])
    signs["md5_params_secret"] = hashlib.md5(f"{param_string}&key={secret}".encode('utf-8')).hexdigest()
    
    # 4. 参数按字典序排列 + secret 的MD5 (不同格式)
    signs["md5_params_secret2"] = hashlib.md5(f"{param_string}{secret}".encode('utf-8')).hexdigest()
    
    # 5. 所有参数值拼接 + secret
    param_values = "".join([str(v) for k, v in sorted_params])
    signs["md5_values_secret"] = hashlib.md5(f"{param_values}{secret}".encode('utf-8')).hexdigest()
    
    # 6. HMAC-MD5
    signs["hmac_md5"] = hmac.new(secret.encode('utf-8'), param_string.encode('utf-8'), hashlib.md5).hexdigest()
    
    # 7. HMAC-SHA1
    signs["hmac_sha1"] = hmac.new(secret.encode('utf-8'), param_string.encode('utf-8'), hashlib.sha1).hexdigest()
    
    # 8. 时间戳相关
    timestamp = str(int(time.time()))
    signs["timestamp_md5"] = hashlib.md5(f"{secret}{timestamp}".encode('utf-8')).hexdigest()
    
    # 9. 常见的API签名格式 (参数=值&参数=值&key=secret)
    query_with_key = f"{param_string}&key={secret}"
    signs["standard_api_sign"] = hashlib.md5(query_with_key.encode('utf-8')).hexdigest()
    
    # 10. 微信支付风格签名
    signs["wechat_style"] = hashlib.md5(f"{param_string}&key={secret}".encode('utf-8')).hexdigest().upper()
    
    return signs

def test_tianqi_with_official_format():
    """使用官方文档格式测试天启代理"""
    print("🎯 天启代理官方文档测试")
    print("=" * 60)
    
    # 读取API密钥
    try:
        with open('ip_keys.txt', 'r') as f:
            secret_key = f.read().strip()
        print(f"📋 API密钥: {secret_key}")
    except Exception as e:
        print(f"❌ 读取密钥失败: {e}")
        return
    
    # 根据官方文档构建基础参数
    base_params = {
        "secret": secret_key,
        "num": 1,           # 提取1个IP
        "time": 3,          # 3分钟时效
        "port": 1,          # HTTP协议
        "type": "json",     # JSON格式
        "ts": 1,            # 显示过期时间
        "cs": 1,            # 显示城市
        "ys": 1,            # 显示运营商
        "mr": 1             # 去重
    }
    
    print(f"📋 基础参数: {base_params}")
    
    # 生成各种签名
    sign_methods = generate_sign_methods(secret_key, base_params)
    
    print(f"\n🔍 生成了 {len(sign_methods)} 种签名方法")
    
    # 测试每种签名方法
    for i, (method_name, sign_value) in enumerate(sign_methods.items()):
        print(f"\n{'='*50}")
        print(f"🧪 测试 {i+1}/{len(sign_methods)}: {method_name}")
        print(f"签名值: {sign_value}")
        print(f"{'='*50}")
        
        # 构建完整参数
        test_params = base_params.copy()
        test_params["sign"] = sign_value
        
        try:
            url = "http://api.tianqiip.com/getip"
            
            print(f"🔍 请求URL: {url}")
            print(f"📋 完整参数: {test_params}")
            
            response = requests.get(url, params=test_params, timeout=15)
            
            print(f"📊 HTTP状态码: {response.status_code}")
            print(f"🔗 完整请求URL: {response.url}")
            
            if response.status_code == 200:
                try:
                    data = response.json()
                    print(f"✅ JSON响应: {json.dumps(data, indent=2, ensure_ascii=False)}")
                    
                    if data.get("code") == 1000:
                        print("🎉 成功！找到正确的签名方法！")
                        print(f"✅ 正确的签名方法: {method_name}")
                        print(f"✅ 签名算法: {sign_value}")
                        
                        # 显示获取到的代理信息
                        if "data" in data and data["data"]:
                            proxy_list = data["data"]
                            print(f"\n📋 成功获取 {len(proxy_list)} 个代理IP:")
                            
                            for j, proxy in enumerate(proxy_list):
                                ip = proxy.get("ip", "N/A")
                                port = proxy.get("port", "N/A")
                                city = proxy.get("city", "N/A")
                                isp = proxy.get("isp", "N/A")
                                expire = proxy.get("expire", "N/A")
                                
                                print(f"  {j+1}. {ip}:{port}")
                                print(f"     城市: {city} | 运营商: {isp}")
                                print(f"     过期时间: {expire}")
                            
                            # 测试第一个代理的连接
                            if proxy_list:
                                test_proxy_connection(proxy_list[0])
                        
                        # 保存成功的配置
                        save_successful_config(method_name, sign_value, test_params)
                        return True
                        
                    else:
                        error_msg = data.get("msg", "未知错误")
                        print(f"❌ API返回错误: {error_msg}")
                        
                except json.JSONDecodeError:
                    print(f"❌ 响应不是有效的JSON: {response.text[:200]}...")
                    
            else:
                print(f"❌ HTTP请求失败: {response.status_code}")
                try:
                    error_data = response.json()
                    print(f"错误信息: {error_data}")
                except:
                    print(f"错误内容: {response.text[:200]}...")
                    
        except requests.exceptions.Timeout:
            print("❌ 请求超时")
        except requests.exceptions.RequestException as e:
            print(f"❌ 请求异常: {e}")
        except Exception as e:
            print(f"❌ 未知异常: {e}")
    
    print("\n❌ 所有签名方法都失败了")
    return False

def test_proxy_connection(proxy_info):
    """测试代理连接"""
    print(f"\n🧪 测试代理连接")
    
    try:
        ip = proxy_info.get("ip")
        port = proxy_info.get("port")
        
        if not ip or not port:
            print("❌ 代理信息不完整")
            return False
        
        # 构建代理配置
        proxies = {
            'http': f'http://{ip}:{port}',
            'https': f'http://{ip}:{port}'
        }
        
        print(f"🔍 测试代理: {ip}:{port}")
        
        # 测试1: 获取当前IP
        print("📡 测试1: 获取当前IP...")
        response = requests.get("http://httpbin.org/ip", proxies=proxies, timeout=10)
        
        if response.status_code == 200:
            result = response.json()
            current_ip = result.get('origin', 'Unknown')
            print(f"✅ 代理连接成功！当前IP: {current_ip}")
            
            # 测试2: 访问百度
            print("📡 测试2: 访问百度...")
            baidu_response = requests.get("https://www.baidu.com", proxies=proxies, timeout=10)
            if baidu_response.status_code == 200:
                print("✅ 百度访问成功！")
            else:
                print(f"❌ 百度访问失败: {baidu_response.status_code}")
            
            return True
        else:
            print(f"❌ 代理连接失败: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 代理测试异常: {e}")
        return False

def save_successful_config(method_name, sign_value, params):
    """保存成功的配置"""
    config = {
        "success_time": time.strftime("%Y-%m-%d %H:%M:%S"),
        "sign_method": method_name,
        "sign_value": sign_value,
        "test_params": params,
        "api_url": "http://api.tianqiip.com/getip"
    }
    
    try:
        with open('tianqi_success_config.json', 'w', encoding='utf-8') as f:
            json.dump(config, f, indent=2, ensure_ascii=False)
        print(f"\n💾 成功配置已保存到: tianqi_success_config.json")
    except Exception as e:
        print(f"❌ 保存配置失败: {e}")

def main():
    """主函数"""
    try:
        print("🚀 开始测试天启代理...")
        
        success = test_tianqi_with_official_format()
        
        if success:
            print("\n🎉 天启代理测试成功！")
            print("✅ 您现在可以使用代理服务了")
            print("📄 成功的配置已保存，可用于后续开发")
        else:
            print("\n❌ 天启代理测试失败")
            print("\n💡 建议:")
            print("1. 检查账户余额是否充足")
            print("2. 确认是否完成实名认证")
            print("3. 联系客服获取正确的签名算法")
            print("   - QQ: 3006006530")
            print("   - 微信: 17006536461")
        
    except KeyboardInterrupt:
        print("\n👋 用户取消测试")
    except Exception as e:
        print(f"❌ 程序执行失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()

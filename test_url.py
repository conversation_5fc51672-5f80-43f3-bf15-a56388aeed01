#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试具体的URL是否有效
"""

import requests

def test_url(url):
    """测试URL是否有效"""
    print(f"🔗 测试URL: {url}")
    
    try:
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
            'Accept-Language': 'zh-CN,zh;q=0.8,en-US;q=0.5,en;q=0.3',
            'Connection': 'keep-alive',
        }
        
        response = requests.get(url, headers=headers, timeout=15)
        
        print(f"📊 状态码: {response.status_code}")
        print(f"📏 内容长度: {len(response.text)}")
        
        if response.status_code == 200:
            # 检查是否包含赔率相关内容
            content = response.text.lower()
            
            keywords = ['odds', '赔率', 'kelly', '凯利', 'table', '表格', 'home', 'draw', 'away']
            found_keywords = [kw for kw in keywords if kw in content]
            
            print(f"🔍 找到关键词: {found_keywords}")
            
            # 显示前500个字符
            print(f"📄 内容预览:")
            print("-" * 50)
            print(response.text[:500])
            print("-" * 50)
            
            return True
        else:
            print(f"❌ 请求失败")
            return False
            
    except Exception as e:
        print(f"❌ 异常: {e}")
        return False

def main():
    """主函数"""
    print("🎯 测试传统抓取URL的有效性")
    print("=" * 60)
    
    # 测试几个不同的URL
    test_urls = [
        # bet365 - 比赛2804677
        "https://1x2.titan007.com/CompensateDetail/281/2804677.htm",
        
        # 威廉希尔 - 比赛2804677  
        "https://1x2.titan007.com/CompensateDetail/115/2804677.htm",
        
        # bet365 - 比赛2598314
        "https://1x2.titan007.com/CompensateDetail/281/2598314.htm",
        
        # 测试不同的URL格式
        "https://m.titan007.com/CompensateDetail/281/2804677.htm",
    ]
    
    for i, url in enumerate(test_urls, 1):
        print(f"\n{'='*60}")
        print(f"测试 {i}/{len(test_urls)}")
        print(f"{'='*60}")
        
        success = test_url(url)
        
        if success:
            print("✅ URL有效")
        else:
            print("❌ URL无效")

if __name__ == "__main__":
    main()

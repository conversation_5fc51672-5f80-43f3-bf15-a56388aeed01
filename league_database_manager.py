#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
按联赛分库的数据库管理器
根据不同联赛自动创建和管理不同的数据库文件
"""

import os
import re
import sqlite3
from datetime import datetime
from typing import Dict, List, Optional
import logging
from database import OddsDatabase

logger = logging.getLogger(__name__)

class LeagueDatabaseManager:
    def __init__(self, base_dir: str = "league_databases"):
        """初始化联赛数据库管理器
        
        Args:
            base_dir: 存储联赛数据库的基础目录
        """
        self.base_dir = base_dir
        self.databases = {}  # 缓存已打开的数据库连接
        self.league_mapping = {}  # 联赛名称映射
        
        # 创建基础目录
        if not os.path.exists(base_dir):
            os.makedirs(base_dir)
        
        # 初始化联赛映射
        self._init_league_mapping()
        
        logger.info(f"联赛数据库管理器初始化完成，存储目录: {base_dir}")
    
    def _init_league_mapping(self):
        """初始化联赛名称映射"""
        self.league_mapping = {
            # 欧洲五大联赛
            '英超': 'Premier_League',
            '西甲': 'La_Liga', 
            '德甲': 'Bundesliga',
            '意甲': 'Serie_A',
            '法甲': 'Ligue_1',
            
            # 欧洲其他联赛
            '荷甲': 'Eredivisie',
            '葡超': 'Primeira_Liga',
            '俄超': 'Russian_Premier_League',
            '土超': 'Super_Lig',
            '比甲': 'Belgian_Pro_League',
            
            # 美洲联赛
            '美职业': 'MLS',
            '美职联': 'MLS',
            '巴甲': 'Brasileirao',
            '阿甲': 'Primera_Division_Argentina',
            '墨超': 'Liga_MX',
            
            # 亚洲联赛
            '中超': 'Chinese_Super_League',
            '日职': 'J_League',
            '韩职': 'K_League',
            '澳超': 'A_League',
            
            # 欧洲杯赛
            '欧冠': 'Champions_League',
            '欧联': 'Europa_League',
            '欧会': 'Conference_League',
            
            # 国际比赛
            '世界杯': 'World_Cup',
            '欧洲杯': 'European_Championship',
            '美洲杯': 'Copa_America',
            '亚洲杯': 'Asian_Cup',
            
            # 友谊赛和其他
            '友谊赛': 'Friendlies',
            '国际友谊': 'International_Friendlies',
            '俱乐部友谊': 'Club_Friendlies',
        }
    
    def _normalize_league_name(self, league_name: str) -> str:
        """标准化联赛名称为文件名"""
        if not league_name:
            return "Unknown_League"
        
        # 移除特殊字符和空格
        league_name = league_name.strip()
        
        # 使用映射表
        if league_name in self.league_mapping:
            return self.league_mapping[league_name]
        
        # 如果没有映射，自动生成文件名
        # 移除特殊字符，保留字母数字和下划线
        normalized = re.sub(r'[^\w\u4e00-\u9fff]', '_', league_name)
        normalized = re.sub(r'_+', '_', normalized)  # 合并多个下划线
        normalized = normalized.strip('_')  # 移除首尾下划线
        
        if not normalized:
            normalized = "Unknown_League"
        
        return normalized
    
    def get_database_path(self, league_name: str) -> str:
        """获取联赛对应的数据库文件路径"""
        normalized_name = self._normalize_league_name(league_name)
        return os.path.join(self.base_dir, f"{normalized_name}.db")
    
    def get_database(self, league_name: str) -> OddsDatabase:
        """获取联赛对应的数据库连接"""
        db_path = self.get_database_path(league_name)
        
        # 如果已经缓存，直接返回
        if db_path in self.databases:
            return self.databases[db_path]
        
        # 创建新的数据库连接
        database = OddsDatabase(db_path)
        self.databases[db_path] = database
        
        logger.info(f"为联赛 '{league_name}' 创建数据库: {db_path}")
        return database
    
    def save_match_data(self, match_info: Dict, odds_data: List[Dict]) -> Dict:
        """保存比赛数据到对应联赛的数据库"""
        try:
            league_name = match_info.get('league', 'Unknown')
            match_id = match_info.get('match_id')
            
            if not match_id:
                return {'success': False, 'error': '缺少比赛ID'}
            
            # 获取对应的数据库
            database = self.get_database(league_name)
            db_path = self.get_database_path(league_name)
            
            # 保存比赛信息
            match_success = database.save_match_info(match_info)
            if not match_success:
                return {'success': False, 'error': '保存比赛信息失败'}
            
            # 保存赔率数据
            odds_success = database.save_odds_data(match_id, odds_data)
            if not odds_success:
                return {'success': False, 'error': '保存赔率数据失败'}
            
            logger.info(f"比赛 {match_id} 数据已保存到联赛数据库: {db_path}")
            
            return {
                'success': True,
                'league': league_name,
                'database_path': db_path,
                'match_id': match_id,
                'odds_count': len(odds_data)
            }
            
        except Exception as e:
            logger.error(f"保存比赛数据失败: {e}")
            return {'success': False, 'error': str(e)}
    
    def get_match_data(self, match_id: str, league_name: str = None) -> Dict:
        """获取比赛数据"""
        try:
            if league_name:
                # 从指定联赛数据库获取
                database = self.get_database(league_name)
                match_info = database.get_match_info(match_id)
                if match_info:
                    odds_data = database.get_odds_data(match_id)
                    return {
                        'match_info': match_info,
                        'odds_data': odds_data,
                        'database_path': self.get_database_path(league_name)
                    }
            else:
                # 从所有数据库中搜索
                for db_path in self._get_all_database_paths():
                    try:
                        database = OddsDatabase(db_path)
                        match_info = database.get_match_info(match_id)
                        if match_info:
                            odds_data = database.get_odds_data(match_id)
                            return {
                                'match_info': match_info,
                                'odds_data': odds_data,
                                'database_path': db_path
                            }
                    except Exception as e:
                        logger.warning(f"搜索数据库 {db_path} 时出错: {e}")
                        continue
            
            return {'match_info': None, 'odds_data': []}
            
        except Exception as e:
            logger.error(f"获取比赛数据失败: {e}")
            return {'match_info': None, 'odds_data': []}
    
    def _get_all_database_paths(self) -> List[str]:
        """获取所有数据库文件路径"""
        db_files = []
        if os.path.exists(self.base_dir):
            for file in os.listdir(self.base_dir):
                if file.endswith('.db'):
                    db_files.append(os.path.join(self.base_dir, file))
        return db_files
    
    def get_all_leagues(self) -> List[Dict]:
        """获取所有联赛的统计信息"""
        leagues = []
        
        for db_path in self._get_all_database_paths():
            try:
                database = OddsDatabase(db_path)
                stats = database.get_database_stats()
                
                # 从文件名提取联赛名称
                file_name = os.path.basename(db_path)
                league_file_name = file_name.replace('.db', '')
                
                # 尝试找到原始联赛名称
                original_name = None
                for orig, norm in self.league_mapping.items():
                    if norm == league_file_name:
                        original_name = orig
                        break
                
                if not original_name:
                    original_name = league_file_name.replace('_', ' ')
                
                leagues.append({
                    'league_name': original_name,
                    'file_name': league_file_name,
                    'database_path': db_path,
                    'match_count': stats.get('match_count', 0),
                    'odds_count': stats.get('odds_count', 0),
                    'company_count': stats.get('company_count', 0),
                    'db_size_mb': stats.get('db_size_mb', 0)
                })
                
            except Exception as e:
                logger.warning(f"获取数据库统计失败 {db_path}: {e}")
                continue
        
        # 按比赛数量排序
        leagues.sort(key=lambda x: x['match_count'], reverse=True)
        return leagues
    
    def get_total_stats(self) -> Dict:
        """获取所有联赛的总统计"""
        total_stats = {
            'total_leagues': 0,
            'total_matches': 0,
            'total_odds': 0,
            'total_size_mb': 0,
            'largest_league': None,
            'most_active_league': None
        }
        
        leagues = self.get_all_leagues()
        total_stats['total_leagues'] = len(leagues)
        
        if leagues:
            total_stats['total_matches'] = sum(l['match_count'] for l in leagues)
            total_stats['total_odds'] = sum(l['odds_count'] for l in leagues)
            total_stats['total_size_mb'] = sum(l['db_size_mb'] for l in leagues)
            
            # 找到最大的联赛（按文件大小）
            largest = max(leagues, key=lambda x: x['db_size_mb'])
            total_stats['largest_league'] = {
                'name': largest['league_name'],
                'size_mb': largest['db_size_mb']
            }
            
            # 找到最活跃的联赛（按比赛数量）
            most_active = max(leagues, key=lambda x: x['match_count'])
            total_stats['most_active_league'] = {
                'name': most_active['league_name'],
                'matches': most_active['match_count']
            }
        
        return total_stats
    
    def migrate_from_single_database(self, source_db_path: str) -> Dict:
        """从单一数据库迁移到分联赛数据库"""
        try:
            if not os.path.exists(source_db_path):
                return {'success': False, 'error': f'源数据库不存在: {source_db_path}'}
            
            source_db = OddsDatabase(source_db_path)
            matches = source_db.get_all_matches()
            
            migration_stats = {
                'total_matches': len(matches),
                'migrated_matches': 0,
                'leagues_created': set(),
                'errors': []
            }
            
            for match in matches:
                try:
                    match_id = match['match_id']
                    league_name = match.get('league', 'Unknown')
                    
                    # 获取完整的比赛数据
                    odds_data = source_db.get_odds_data(match_id)
                    
                    # 保存到对应联赛数据库
                    result = self.save_match_data(match, odds_data)
                    
                    if result['success']:
                        migration_stats['migrated_matches'] += 1
                        migration_stats['leagues_created'].add(league_name)
                    else:
                        migration_stats['errors'].append(f"比赛 {match_id}: {result['error']}")
                        
                except Exception as e:
                    migration_stats['errors'].append(f"比赛 {match.get('match_id', 'Unknown')}: {str(e)}")
                    continue
            
            migration_stats['leagues_created'] = list(migration_stats['leagues_created'])
            migration_stats['success'] = True
            
            logger.info(f"迁移完成: {migration_stats['migrated_matches']}/{migration_stats['total_matches']} 场比赛")
            return migration_stats
            
        except Exception as e:
            logger.error(f"数据库迁移失败: {e}")
            return {'success': False, 'error': str(e)}
    
    def cleanup_empty_databases(self) -> Dict:
        """清理空的数据库文件"""
        cleaned = []
        errors = []
        
        for db_path in self._get_all_database_paths():
            try:
                database = OddsDatabase(db_path)
                stats = database.get_database_stats()
                
                if stats.get('match_count', 0) == 0:
                    os.remove(db_path)
                    cleaned.append(db_path)
                    logger.info(f"删除空数据库: {db_path}")
                    
            except Exception as e:
                errors.append(f"{db_path}: {str(e)}")
                logger.error(f"清理数据库失败 {db_path}: {e}")
        
        return {
            'cleaned_files': cleaned,
            'errors': errors,
            'success': len(errors) == 0
        }

def main():
    """命令行工具主函数"""
    import sys
    
    if len(sys.argv) < 2:
        print("用法: python league_database_manager.py <command> [options]")
        print("命令:")
        print("  list - 列出所有联赛数据库")
        print("  stats - 显示总体统计")
        print("  migrate <source_db> - 从单一数据库迁移")
        print("  cleanup - 清理空数据库")
        return
    
    manager = LeagueDatabaseManager()
    command = sys.argv[1]
    
    if command == "list":
        leagues = manager.get_all_leagues()
        print("=== 联赛数据库列表 ===")
        for league in leagues:
            print(f"{league['league_name']:20} | {league['match_count']:>6} 场比赛 | {league['db_size_mb']:>8.2f} MB")
    
    elif command == "stats":
        stats = manager.get_total_stats()
        print("=== 总体统计 ===")
        print(f"联赛数量: {stats['total_leagues']}")
        print(f"总比赛数: {stats['total_matches']:,}")
        print(f"总赔率记录: {stats['total_odds']:,}")
        print(f"总大小: {stats['total_size_mb']:.2f} MB")
        if stats['largest_league']:
            print(f"最大联赛: {stats['largest_league']['name']} ({stats['largest_league']['size_mb']:.2f} MB)")
        if stats['most_active_league']:
            print(f"最活跃联赛: {stats['most_active_league']['name']} ({stats['most_active_league']['matches']} 场比赛)")
    
    elif command == "migrate" and len(sys.argv) > 2:
        source_db = sys.argv[2]
        result = manager.migrate_from_single_database(source_db)
        if result['success']:
            print(f"迁移完成: {result['migrated_matches']}/{result['total_matches']} 场比赛")
            print(f"创建联赛: {', '.join(result['leagues_created'])}")
            if result['errors']:
                print(f"错误数量: {len(result['errors'])}")
        else:
            print(f"迁移失败: {result['error']}")
    
    elif command == "cleanup":
        result = manager.cleanup_empty_databases()
        print(f"清理完成: 删除了 {len(result['cleaned_files'])} 个空数据库")
        if result['errors']:
            print(f"错误: {result['errors']}")

if __name__ == "__main__":
    main()

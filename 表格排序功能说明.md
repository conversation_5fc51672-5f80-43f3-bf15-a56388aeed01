# 表格排序功能说明

## 🎯 功能概述

为"比赛列表"面板和"比赛详情"面板中的所有表格添加了点击表头排序功能，用户可以通过点击表头来对表格内容进行排序。

## ✨ 新增功能

### 1. 比赛列表表格排序
**位置**: 主界面 → "比赛列表" 标签页

**可排序列**:
- **比赛ID**: 按数字大小排序
- **联赛**: 按字母顺序排序
- **赛季**: 按字母顺序排序
- **轮次**: 按字母顺序排序
- **对阵**: 按字母顺序排序
- **比赛时间**: 按时间排序（有✓标记的准确时间优先显示）
- **比分**: 按字母顺序排序
- **状态**: 按字母顺序排序
- **博彩公司**: 按数字大小排序

### 2. 比赛详情赔率表格排序
**位置**: 主界面 → "比赛详情" 标签页 → 赔率数据表格

**可排序列**:
- **公司**: 按字母顺序排序
- **日期**: 按日期排序
- **时间**: 按时间排序
- **主胜**: 按数值大小排序
- **平局**: 按数值大小排序
- **客胜**: 按数值大小排序
- **返还率**: 按百分比数值排序
- **凯利主**: 按数值大小排序
- **凯利平**: 按数值大小排序
- **凯利客**: 按数值大小排序

### 3. 联赛批量抓取结果表格排序
**位置**: 主界面 → "联赛批量抓取" 标签页 → 抓取结果表格

**可排序列**:
- **轮次**: 按数字大小排序
- **比赛ID**: 按数字大小排序
- **对阵**: 按字母顺序排序
- **比赛时间**: 按时间排序（有✓标记的准确时间优先显示）
- **比分**: 按字母顺序排序
- **状态**: 按字母顺序排序
- **赔率状态**: 按字母顺序排序

## 🖱️ 使用方法

### 基本操作
1. **点击表头**: 点击任意表头可按该列进行升序排序
2. **切换排序方向**: 再次点击同一表头可切换为降序排序
3. **排序指示器**: 表头会显示箭头指示当前排序状态
   - `↑` 表示升序排序
   - `↓` 表示降序排序

### 排序规则

#### 数字列排序
- **比赛ID、博彩公司数量、轮次**: 按数值大小排序
- **赔率数值**: 按浮点数大小排序，自动处理百分号

#### 时间列排序
- **比赛时间**: 有✓标记的准确时间优先显示在前面
- **日期时间**: 按时间顺序排序

#### 文本列排序
- **联赛、对阵、状态等**: 按字母顺序排序（不区分大小写）

## 🔧 技术实现

### 核心功能
- **智能排序**: 根据列的数据类型自动选择合适的排序方法
- **状态记忆**: 记住每个表格的当前排序列和排序方向
- **视觉反馈**: 表头显示排序箭头指示器
- **错误处理**: 包含完整的异常处理机制

### 排序算法
```python
# 数字列排序
if column in ["match_id", "companies"]:
    return int(value) if value else 0

# 时间列特殊排序（准确时间优先）
elif column == "match_time":
    if "✓" in str(value):
        return "0" + str(value)  # 准确时间排在前面
    else:
        return "1" + str(value)  # 估算时间排在后面

# 文本列排序
else:
    return str(value).lower()
```

## 🎨 用户体验改进

### 视觉提示
- **排序箭头**: 清晰显示当前排序状态
- **点击反馈**: 表头可点击，提供良好的交互体验
- **一致性**: 所有表格使用相同的排序交互模式

### 智能排序
- **准确时间优先**: 在比赛时间列中，有✓标记的准确时间会优先显示
- **数值识别**: 自动识别数字列并按数值大小排序
- **百分比处理**: 自动处理返还率等百分比数据的排序

## 📝 使用示例

### 示例1: 按比赛时间排序
1. 点击"比赛时间"表头
2. 有✓标记的准确时间会排在前面
3. 再次点击可切换为降序（最新时间在前）

### 示例2: 按赔率排序
1. 在比赛详情页面，点击"主胜"表头
2. 赔率按数值大小升序排列
3. 再次点击切换为降序（最高赔率在前）

### 示例3: 按联赛排序
1. 点击"联赛"表头
2. 联赛名称按字母顺序排列
3. 方便查找特定联赛的比赛

## 🔄 兼容性

- **向后兼容**: 不影响现有功能
- **数据完整性**: 排序不会修改原始数据
- **性能优化**: 高效的排序算法，适用于大量数据

## 🚀 未来扩展

可以考虑添加的功能：
- **多列排序**: 支持按多个列进行复合排序
- **排序记忆**: 记住用户的排序偏好
- **自定义排序**: 允许用户自定义排序规则
- **排序历史**: 提供排序操作的撤销功能

---

**注意**: 此功能已集成到主程序中，无需额外配置即可使用。所有表格的排序状态都是独立的，互不影响。

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据库模式选择器
提供图形界面来选择和切换数据库模式
"""

import sys
import os
from PyQt5.QtWidgets import (QApplication, QMainWindow, QVBoxLayout, QHBoxLayout, 
                             QWidget, QPushButton, QLabel, QTextEdit, QRadioButton,
                             QGroupBox, QGridLayout, QMessageBox, QProgressBar,
                             QTableWidget, QTableWidgetItem, QHeaderView,
                             QFileDialog, QButtonGroup, QSpinBox, QCheckBox)
from PyQt5.QtCore import QThread, pyqtSignal, QTimer
from PyQt5.QtGui import QFont, QPixmap, QIcon
from enhanced_database import EnhancedDatabase
from league_database_manager import LeagueDatabaseManager
from database import OddsDatabase
from config import DATABASE_CONFIG

class DatabaseModeWorker(QThread):
    """数据库模式操作工作线程"""
    finished = pyqtSignal(dict)
    progress = pyqtSignal(str)
    
    def __init__(self, operation, *args):
        super().__init__()
        self.operation = operation
        self.args = args
    
    def run(self):
        try:
            if self.operation == "switch_to_league":
                source_db_path, base_dir = self.args
                self.progress.emit("正在分析源数据库...")
                
                manager = LeagueDatabaseManager(base_dir)
                self.progress.emit("正在迁移数据到联赛数据库...")
                result = manager.migrate_from_single_database(source_db_path)
                
            elif self.operation == "switch_to_single":
                base_dir, target_db_path = self.args
                self.progress.emit("正在合并联赛数据库...")
                
                # 这里需要实现合并逻辑
                result = self._merge_league_databases(base_dir, target_db_path)
                
            elif self.operation == "analyze_current":
                db_path = self.args[0]
                self.progress.emit("正在分析当前数据库...")
                result = self._analyze_database(db_path)

            elif self.operation == "backup_and_clear":
                db_path = self.args[0]
                self.progress.emit("正在备份并清空数据库...")
                result = self._backup_and_clear_database(db_path)

            elif self.operation == "migrate_with_duplicate_check":
                source_db_path, base_dir = self.args
                self.progress.emit("正在迁移数据库（检测重复）...")
                result = self._migrate_with_duplicate_check(source_db_path, base_dir)

            elif self.operation == "merge_with_duplicate_check":
                base_dir, target_db_path = self.args
                self.progress.emit("正在合并分库（检测重复）...")
                result = self._merge_with_duplicate_check(base_dir, target_db_path)

            elif self.operation == "clean_duplicates_single":
                db_path = self.args[0]
                self.progress.emit("正在清理单一数据库重复数据...")
                result = self._clean_duplicates_single(db_path)

            elif self.operation == "clean_duplicates_league":
                base_dir = self.args[0]
                self.progress.emit("正在清理联赛分库重复数据...")
                result = self._clean_duplicates_league(base_dir)

            else:
                result = {"error": "未知操作"}
            
            self.finished.emit(result)
        except Exception as e:
            self.finished.emit({"error": str(e)})
    
    def _merge_league_databases(self, base_dir, target_db_path):
        """合并联赛数据库到单一数据库"""
        try:
            manager = LeagueDatabaseManager(base_dir)
            leagues = manager.get_all_leagues()
            
            target_db = OddsDatabase(target_db_path)
            total_matches = 0
            total_errors = 0
            
            for league in leagues:
                self.progress.emit(f"正在合并联赛: {league['league_name']}")
                
                source_db = OddsDatabase(league['database_path'])
                matches = source_db.get_all_matches()
                
                for match in matches:
                    match_id = match['match_id']
                    odds_data = source_db.get_odds_data(match_id)
                    
                    if target_db.save_match_info(match):
                        if target_db.save_odds_data(match_id, odds_data):
                            total_matches += 1
                        else:
                            total_errors += 1
                    else:
                        total_errors += 1
            
            return {
                'success': True,
                'merged_matches': total_matches,
                'errors': total_errors,
                'target_database': target_db_path
            }
            
        except Exception as e:
            return {'success': False, 'error': str(e)}

    def _backup_and_clear_database(self, db_path):
        """备份并清空数据库"""
        try:
            import shutil
            from datetime import datetime

            # 生成备份文件名
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_name = f"odds_data_backup_{timestamp}.db"

            self.progress.emit(f"正在备份到 {backup_name}...")

            # 备份文件
            shutil.copy2(db_path, backup_name)
            backup_size = os.path.getsize(backup_name) / (1024 * 1024)

            self.progress.emit("正在清空原数据库...")

            # 清空原数据库（保留结构）
            import sqlite3
            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()

            # 获取所有表名
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name NOT LIKE 'sqlite_%'")
            tables = [row[0] for row in cursor.fetchall()]

            # 删除所有数据但保留表结构
            tables_cleared = []
            for table in tables:
                try:
                    cursor.execute(f"DELETE FROM {table}")
                    tables_cleared.append(table)
                except Exception as e:
                    self.progress.emit(f"清空表 {table} 失败: {e}")

            conn.commit()
            conn.close()

            # 在事务外执行VACUUM回收空间
            conn = sqlite3.connect(db_path)
            conn.execute("VACUUM")
            conn.close()

            return {
                'success': True,
                'backup_file': backup_name,
                'backup_size_mb': backup_size,
                'tables_cleared': tables_cleared,
                'message': f'数据库已备份到 {backup_name} ({backup_size:.2f} MB)，原数据库已清空 {len(tables_cleared)} 个表'
            }

        except Exception as e:
            return {'success': False, 'error': str(e)}

    def _migrate_with_duplicate_check(self, source_db_path, base_dir):
        """迁移数据库到分库（检测重复）"""
        try:
            from league_database_manager import LeagueDatabaseManager

            self.progress.emit("正在初始化联赛管理器...")
            manager = LeagueDatabaseManager(base_dir)

            self.progress.emit("正在读取源数据库...")
            source_db = OddsDatabase(source_db_path)
            matches = source_db.get_all_matches()

            total_matches = len(matches)
            migrated_matches = 0
            skipped_matches = 0
            error_matches = 0
            leagues_created = set()

            self.progress.emit(f"开始迁移 {total_matches} 场比赛...")

            for i, match in enumerate(matches):
                if i % 10 == 0:  # 每10场比赛更新一次进度
                    self.progress.emit(f"正在处理 {i+1}/{total_matches} 场比赛...")

                match_id = match['match_id']
                league = match.get('league', 'Unknown')
                leagues_created.add(league)

                try:
                    # 检查是否已存在
                    target_db = manager.get_database(league)
                    existing_match = target_db.get_match_info(match_id)

                    if existing_match:
                        skipped_matches += 1
                        continue

                    # 获取赔率数据
                    odds_data = source_db.get_odds_data(match_id)

                    # 保存数据
                    result = manager.save_match_data(match, odds_data)
                    if result['success']:
                        migrated_matches += 1
                    else:
                        error_matches += 1

                except Exception as e:
                    error_matches += 1
                    continue

            return {
                'success': True,
                'total_matches': total_matches,
                'migrated_matches': migrated_matches,
                'skipped_matches': skipped_matches,
                'error_matches': error_matches,
                'leagues_created': list(leagues_created),
                'message': f'迁移完成：成功 {migrated_matches}，跳过 {skipped_matches}，错误 {error_matches}'
            }

        except Exception as e:
            return {'success': False, 'error': str(e)}

    def _merge_with_duplicate_check(self, base_dir, target_db_path):
        """合并分库到单一数据库（检测重复）"""
        try:
            from league_database_manager import LeagueDatabaseManager

            self.progress.emit("正在初始化联赛管理器...")
            manager = LeagueDatabaseManager(base_dir)
            leagues = manager.get_all_leagues()

            self.progress.emit("正在创建目标数据库...")
            target_db = OddsDatabase(target_db_path)

            total_matches = 0
            merged_matches = 0
            skipped_matches = 0
            error_matches = 0

            for league_info in leagues:
                league_name = league_info['league_name']
                self.progress.emit(f"正在合并联赛: {league_name}")

                source_db = OddsDatabase(league_info['database_path'])
                matches = source_db.get_all_matches()
                total_matches += len(matches)

                for match in matches:
                    match_id = match['match_id']

                    try:
                        # 检查是否已存在
                        existing_match = target_db.get_match_info(match_id)
                        if existing_match:
                            skipped_matches += 1
                            continue

                        # 获取赔率数据
                        odds_data = source_db.get_odds_data(match_id)

                        # 保存到目标数据库
                        if target_db.save_match_info(match):
                            if target_db.save_odds_data(match_id, odds_data):
                                merged_matches += 1
                            else:
                                error_matches += 1
                        else:
                            error_matches += 1

                    except Exception as e:
                        error_matches += 1
                        continue

            return {
                'success': True,
                'total_matches': total_matches,
                'merged_matches': merged_matches,
                'skipped_matches': skipped_matches,
                'error_matches': error_matches,
                'target_database': target_db_path,
                'message': f'合并完成：成功 {merged_matches}，跳过 {skipped_matches}，错误 {error_matches}'
            }

        except Exception as e:
            return {'success': False, 'error': str(e)}

    def _clean_duplicates_single(self, db_path):
        """清理单一数据库的重复数据（仅清理重复的比赛记录，不删除赔率数据）"""
        try:
            import sqlite3

            self.progress.emit("正在分析重复比赛数据...")

            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()

            # 只检查重复的比赛记录（相同match_id的比赛）
            cursor.execute("""
                SELECT match_id, COUNT(*) as count
                FROM matches
                GROUP BY match_id
                HAVING COUNT(*) > 1
            """)
            duplicate_matches = cursor.fetchall()

            cleaned_matches = 0

            if duplicate_matches:
                self.progress.emit(f"发现 {len(duplicate_matches)} 个重复比赛ID，正在清理...")
                for match_id, count in duplicate_matches:
                    # 保留最新的比赛记录，删除重复的
                    cursor.execute("""
                        DELETE FROM matches
                        WHERE match_id = ? AND rowid NOT IN (
                            SELECT MAX(rowid) FROM matches WHERE match_id = ?
                        )
                    """, (match_id, match_id))
                    cleaned_matches += count - 1

            # 清理孤立的数据记录（没有对应比赛的数据）
            self.progress.emit("正在清理孤立的数据记录...")

            # 获取所有表名（除了matches表）
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name NOT LIKE 'sqlite_%' AND name != 'matches'")
            tables_to_clean = [row[0] for row in cursor.fetchall()]

            total_cleaned_orphaned = 0

            for table in tables_to_clean:
                try:
                    # 检查表是否有match_id字段
                    cursor.execute(f"PRAGMA table_info({table})")
                    columns = [row[1] for row in cursor.fetchall()]

                    if 'match_id' in columns:
                        cursor.execute(f"""
                            DELETE FROM {table}
                            WHERE match_id NOT IN (SELECT DISTINCT match_id FROM matches)
                        """)
                        cleaned_count = cursor.rowcount
                        total_cleaned_orphaned += cleaned_count
                        if cleaned_count > 0:
                            self.progress.emit(f"从 {table} 表清理了 {cleaned_count} 条孤立记录")
                except Exception as e:
                    self.progress.emit(f"清理表 {table} 时出错: {e}")
                    continue

            cleaned_orphaned_odds = total_cleaned_orphaned  # 保持向后兼容

            conn.commit()
            conn.close()

            # 优化数据库（需要在事务外执行）
            self.progress.emit("正在优化数据库...")
            conn = sqlite3.connect(db_path)
            conn.execute("VACUUM")
            conn.close()

            message_parts = []
            if cleaned_matches > 0:
                message_parts.append(f"删除 {cleaned_matches} 个重复比赛记录")
            if total_cleaned_orphaned > 0:
                message_parts.append(f"删除 {total_cleaned_orphaned} 个孤立数据记录")

            if not message_parts:
                message = "未发现需要清理的重复数据"
            else:
                message = f"清理完成：{', '.join(message_parts)}"

            return {
                'success': True,
                'cleaned_matches': cleaned_matches,
                'cleaned_odds': total_cleaned_orphaned,
                'cleaned_tables': len(tables_to_clean),
                'message': message
            }

        except Exception as e:
            return {'success': False, 'error': str(e)}

    def _clean_duplicates_league(self, base_dir):
        """清理联赛分库的重复数据"""
        try:
            from league_database_manager import LeagueDatabaseManager

            self.progress.emit("正在初始化联赛管理器...")
            manager = LeagueDatabaseManager(base_dir)
            leagues = manager.get_all_leagues()

            total_cleaned_matches = 0
            total_cleaned_odds = 0

            for league_info in leagues:
                league_name = league_info['league_name']
                db_path = league_info['database_path']

                self.progress.emit(f"正在清理联赛: {league_name}")

                # 对每个联赛数据库执行清理
                result = self._clean_duplicates_single(db_path)

                if result['success']:
                    total_cleaned_matches += result['cleaned_matches']
                    total_cleaned_odds += result['cleaned_odds']
                else:
                    # 如果某个联赛清理失败，记录但继续处理其他联赛
                    self.progress.emit(f"清理联赛 {league_name} 失败: {result['error']}")

            return {
                'success': True,
                'total_cleaned_matches': total_cleaned_matches,
                'total_cleaned_odds': total_cleaned_odds,
                'leagues_processed': len(leagues),
                'message': f'清理完成：处理 {len(leagues)} 个联赛，删除 {total_cleaned_matches} 个重复比赛，{total_cleaned_odds} 个重复赔率记录'
            }

        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    def _analyze_database(self, db_path):
        """分析数据库"""
        try:
            if os.path.exists(db_path):
                db = OddsDatabase(db_path)
                stats = db.get_database_stats()

                self.progress.emit("正在分析数据库架构...")

                # 分析数据库架构
                import sqlite3
                conn = sqlite3.connect(db_path)
                cursor = conn.cursor()

                # 获取所有表
                cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name NOT LIKE 'sqlite_%'")
                tables = [row[0] for row in cursor.fetchall()]

                # 统计各表的记录数
                table_stats = {}
                for table in tables:
                    try:
                        cursor.execute(f"SELECT COUNT(*) FROM {table}")
                        count = cursor.fetchone()[0]
                        table_stats[table] = count
                    except Exception as e:
                        table_stats[table] = f"错误: {e}"

                # 检查扩展表是否存在
                extended_tables = ['technical_stats', 'lineups', 'player_stats', 'goal_probability', 'schema_versions']
                extended_table_status = {}
                for table in extended_tables:
                    extended_table_status[table] = table in tables

                # 获取架构版本
                schema_version = "1.0.0"  # 默认版本
                try:
                    cursor.execute("SELECT version FROM schema_versions ORDER BY applied_at DESC LIMIT 1")
                    version_row = cursor.fetchone()
                    if version_row:
                        schema_version = version_row[0]
                except:
                    pass

                conn.close()

                self.progress.emit("正在分析联赛分布...")

                # 分析联赛分布
                matches = db.get_all_matches()
                league_stats = {}

                self.progress.emit(f"正在处理 {len(matches)} 场比赛...")

                for i, match in enumerate(matches):
                    if i % 50 == 0:  # 每50场比赛更新一次进度
                        self.progress.emit(f"已处理 {i}/{len(matches)} 场比赛...")

                    league = match.get('league', 'Unknown')
                    if not league or league.strip() == '':
                        league = 'Unknown'

                    if league not in league_stats:
                        league_stats[league] = 0
                    league_stats[league] += 1

                # 排序联赛
                sorted_leagues = sorted(league_stats.items(), key=lambda x: x[1], reverse=True)

                # 验证数据一致性
                total_from_analysis = sum(count for _, count in sorted_leagues)
                db_match_count = stats.get('match_count', 0)

                self.progress.emit("分析完成，正在生成报告...")

                return {
                    'success': True,
                    'database_stats': stats,
                    'league_distribution': sorted_leagues,
                    'total_leagues': len(league_stats),
                    'analysis_match_count': len(matches),
                    'league_total_count': total_from_analysis,
                    'data_consistent': db_match_count == total_from_analysis,
                    'table_stats': table_stats,
                    'extended_table_status': extended_table_status,
                    'schema_version': schema_version,
                    'total_tables': len(tables)
                }
            else:
                return {'success': False, 'error': '数据库文件不存在'}

        except Exception as e:
            return {'success': False, 'error': str(e)}

class DatabaseModeSelectorUI(QMainWindow):
    def __init__(self):
        super().__init__()
        self.worker = None
        self.current_mode = DATABASE_CONFIG.get('mode', 'single')
        self.init_ui()
        self.load_current_status()
    
    def init_ui(self):
        self.setWindowTitle("数据库模式选择器")
        self.setGeometry(100, 100, 900, 700)
        
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        layout = QVBoxLayout(central_widget)
        
        # 标题
        title_label = QLabel("数据库模式管理")
        title_font = QFont()
        title_font.setPointSize(16)
        title_font.setBold(True)
        title_label.setFont(title_font)
        layout.addWidget(title_label)
        
        # 当前状态
        self.create_status_section(layout)
        
        # 模式选择
        self.create_mode_selection(layout)
        
        # 操作按钮
        self.create_action_buttons(layout)

        # 数据库管理按钮
        self.create_database_management_buttons(layout)
        
        # 详细信息
        self.create_details_section(layout)
        
        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        layout.addWidget(self.progress_bar)
        
        # 状态标签
        self.status_label = QLabel("就绪")
        layout.addWidget(self.status_label)
    
    def create_status_section(self, parent_layout):
        """创建当前状态部分"""
        status_group = QGroupBox("当前状态")
        layout = QGridLayout(status_group)
        
        self.current_mode_label = QLabel("当前模式: 检测中...")
        self.database_info_label = QLabel("数据库信息: 加载中...")
        self.size_info_label = QLabel("大小信息: 计算中...")
        self.recommendation_label = QLabel("建议: 分析中...")
        
        layout.addWidget(QLabel("模式:"), 0, 0)
        layout.addWidget(self.current_mode_label, 0, 1)
        layout.addWidget(QLabel("信息:"), 1, 0)
        layout.addWidget(self.database_info_label, 1, 1)
        layout.addWidget(QLabel("大小:"), 2, 0)
        layout.addWidget(self.size_info_label, 2, 1)
        layout.addWidget(QLabel("建议:"), 3, 0)
        layout.addWidget(self.recommendation_label, 3, 1)
        
        parent_layout.addWidget(status_group)
    
    def create_mode_selection(self, parent_layout):
        """创建模式选择部分"""
        mode_group = QGroupBox("选择数据库模式")
        layout = QVBoxLayout(mode_group)
        
        self.mode_button_group = QButtonGroup()
        
        # 单一数据库模式
        self.single_radio = QRadioButton("单一数据库模式")
        self.single_radio.setChecked(True)
        single_desc = QLabel("• 所有数据存储在一个数据库文件中\n• 简单易用，适合小到中等规模数据\n• 文件大小可能会增长较快")
        single_desc.setStyleSheet("color: gray; margin-left: 20px;")
        
        # 联赛分库模式
        self.league_radio = QRadioButton("联赛分库模式")
        league_desc = QLabel("• 按联赛分别存储到不同数据库文件\n• 单个文件大小可控，性能更好\n• 适合大规模数据和长期使用")
        league_desc.setStyleSheet("color: gray; margin-left: 20px;")
        
        self.mode_button_group.addButton(self.single_radio, 0)
        self.mode_button_group.addButton(self.league_radio, 1)
        
        layout.addWidget(self.single_radio)
        layout.addWidget(single_desc)
        layout.addWidget(self.league_radio)
        layout.addWidget(league_desc)
        
        parent_layout.addWidget(mode_group)
    
    def create_action_buttons(self, parent_layout):
        """创建操作按钮"""
        button_layout = QHBoxLayout()
        
        self.analyze_btn = QPushButton("分析当前数据库")
        self.analyze_btn.clicked.connect(self.analyze_current_database)
        
        self.switch_btn = QPushButton("切换模式")
        self.switch_btn.clicked.connect(self.switch_database_mode)
        
        self.refresh_btn = QPushButton("刷新状态")
        self.refresh_btn.clicked.connect(self.load_current_status)
        
        button_layout.addWidget(self.analyze_btn)
        button_layout.addWidget(self.switch_btn)
        button_layout.addWidget(self.refresh_btn)
        button_layout.addStretch()
        
        parent_layout.addLayout(button_layout)

    def create_database_management_buttons(self, parent_layout):
        """创建数据库管理按钮"""
        management_group = QGroupBox("数据库管理")
        layout = QVBoxLayout(management_group)

        # 第一行按钮
        button_row1 = QHBoxLayout()

        self.backup_clear_btn = QPushButton("备份并清空当前数据库")
        self.backup_clear_btn.clicked.connect(self.backup_and_clear_database)
        self.backup_clear_btn.setStyleSheet("QPushButton { background-color: #ff9800; color: white; font-weight: bold; }")

        self.select_migrate_btn = QPushButton("选择数据库文件迁移到分库")
        self.select_migrate_btn.clicked.connect(self.select_and_migrate_database)
        self.select_migrate_btn.setStyleSheet("QPushButton { background-color: #2196f3; color: white; font-weight: bold; }")

        button_row1.addWidget(self.backup_clear_btn)
        button_row1.addWidget(self.select_migrate_btn)

        # 第二行按钮
        button_row2 = QHBoxLayout()

        self.merge_to_single_btn = QPushButton("合并分库到单一数据库")
        self.merge_to_single_btn.clicked.connect(self.merge_leagues_to_single)
        self.merge_to_single_btn.setStyleSheet("QPushButton { background-color: #4caf50; color: white; font-weight: bold; }")

        self.clean_duplicates_btn = QPushButton("清理重复数据")
        self.clean_duplicates_btn.clicked.connect(self.clean_duplicate_data)
        self.clean_duplicates_btn.setStyleSheet("QPushButton { background-color: #9c27b0; color: white; font-weight: bold; }")

        button_row2.addWidget(self.merge_to_single_btn)
        button_row2.addWidget(self.clean_duplicates_btn)

        layout.addLayout(button_row1)
        layout.addLayout(button_row2)

        # 说明文字
        help_text = QLabel("""
• 备份并清空：将当前 odds_data.db 以日期命名备份，然后清空原文件
• 选择迁移：选择一个数据库文件，将其数据迁移到联赛分库（自动检测重复）
• 合并分库：将所有联赛分库合并回单一数据库
• 清理重复：检查并清理数据库中的重复数据
        """)
        help_text.setStyleSheet("color: gray; font-size: 10px;")
        layout.addWidget(help_text)

        parent_layout.addWidget(management_group)
    
    def create_details_section(self, parent_layout):
        """创建详细信息部分"""
        details_group = QGroupBox("详细信息")
        layout = QVBoxLayout(details_group)
        
        self.details_text = QTextEdit()
        self.details_text.setMaximumHeight(200)
        layout.addWidget(self.details_text)
        
        parent_layout.addWidget(details_group)
    
    def load_current_status(self):
        """加载当前状态"""
        try:
            # 检测当前模式
            single_db_path = DATABASE_CONFIG.get('default_db_name', 'odds_data.db')
            league_base_dir = DATABASE_CONFIG.get('league_base_dir', 'league_databases')
            
            if os.path.exists(single_db_path) and not os.path.exists(league_base_dir):
                self.current_mode = "single"
                self.current_mode_label.setText("单一数据库模式")
                self.single_radio.setChecked(True)
                
                # 获取数据库信息
                db = OddsDatabase(single_db_path)
                stats = db.get_database_stats()

                # 验证数据一致性
                matches = db.get_all_matches()
                actual_count = len(matches)

                if actual_count == stats.get('match_count', 0):
                    self.database_info_label.setText(f"{stats.get('match_count', 0)} 场比赛, {stats.get('odds_count', 0)} 条记录")
                else:
                    self.database_info_label.setText(f"{actual_count} 场比赛 (统计:{stats.get('match_count', 0)}), {stats.get('odds_count', 0)} 条记录")

                self.size_info_label.setText(f"{stats.get('db_size_mb', 0):.2f} MB")
                
                # 给出建议
                size_mb = stats.get('db_size_mb', 0)
                if size_mb < 50:
                    recommendation = "当前大小适中，建议继续使用单一数据库"
                elif size_mb < 200:
                    recommendation = "可以考虑切换到联赛分库模式以获得更好性能"
                else:
                    recommendation = "强烈建议切换到联赛分库模式"
                
                self.recommendation_label.setText(recommendation)
                
            elif os.path.exists(league_base_dir):
                self.current_mode = "league"
                self.current_mode_label.setText("联赛分库模式")
                self.league_radio.setChecked(True)
                
                # 获取联赛统计
                manager = LeagueDatabaseManager(league_base_dir)
                total_stats = manager.get_total_stats()
                
                self.database_info_label.setText(f"{total_stats.get('total_leagues', 0)} 个联赛, {total_stats.get('total_matches', 0)} 场比赛")
                self.size_info_label.setText(f"总计 {total_stats.get('total_size_mb', 0):.2f} MB")
                self.recommendation_label.setText("联赛分库模式运行良好")
                
            else:
                self.current_mode_label.setText("未检测到数据库")
                self.database_info_label.setText("无数据")
                self.size_info_label.setText("0 MB")
                self.recommendation_label.setText("请先创建数据库")
                
        except Exception as e:
            self.current_mode_label.setText(f"检测失败: {e}")
    
    def analyze_current_database(self):
        """分析当前数据库"""
        if self.current_mode == "single":
            db_path = DATABASE_CONFIG.get('default_db_name', 'odds_data.db')
            if os.path.exists(db_path):
                self.start_operation("analyze_current", db_path)
            else:
                QMessageBox.warning(self, "警告", "数据库文件不存在")
        else:
            # 分析联赛数据库
            base_dir = DATABASE_CONFIG.get('league_base_dir', 'league_databases')
            if os.path.exists(base_dir):
                self.show_league_analysis(base_dir)
            else:
                QMessageBox.warning(self, "警告", "联赛数据库目录不存在")
    
    def show_league_analysis(self, base_dir):
        """显示联赛分析"""
        try:
            manager = LeagueDatabaseManager(base_dir)
            leagues = manager.get_all_leagues()
            total_stats = manager.get_total_stats()
            
            analysis_text = f"""联赛分库分析报告
================
总联赛数: {total_stats.get('total_leagues', 0)}
总比赛数: {total_stats.get('total_matches', 0):,}
总记录数: {total_stats.get('total_odds', 0):,}
总大小: {total_stats.get('total_size_mb', 0):.2f} MB

各联赛详情:
"""
            
            for league in leagues[:10]:  # 显示前10个联赛
                analysis_text += f"• {league['league_name']}: {league['match_count']} 场比赛, {league['db_size_mb']:.2f} MB\n"
            
            if len(leagues) > 10:
                analysis_text += f"... 还有 {len(leagues) - 10} 个联赛\n"
            
            self.details_text.setText(analysis_text)
            
        except Exception as e:
            QMessageBox.warning(self, "错误", f"分析联赛数据库失败: {e}")
    
    def switch_database_mode(self):
        """切换数据库模式"""
        selected_mode = "single" if self.single_radio.isChecked() else "league"
        
        if selected_mode == self.current_mode:
            QMessageBox.information(self, "信息", f"已经是 {selected_mode} 模式")
            return
        
        # 确认切换
        if selected_mode == "league":
            reply = QMessageBox.question(self, "确认切换", 
                                       "确定要切换到联赛分库模式吗？\n"
                                       "这将按联赛分别创建数据库文件。",
                                       QMessageBox.Yes | QMessageBox.No)
        else:
            reply = QMessageBox.question(self, "确认切换", 
                                       "确定要切换到单一数据库模式吗？\n"
                                       "这将合并所有联赛数据到一个文件。",
                                       QMessageBox.Yes | QMessageBox.No)
        
        if reply == QMessageBox.Yes:
            if selected_mode == "league":
                self.switch_to_league_mode()
            else:
                self.switch_to_single_mode()
    
    def switch_to_league_mode(self):
        """切换到联赛分库模式"""
        source_db_path = DATABASE_CONFIG.get('default_db_name', 'odds_data.db')
        base_dir = DATABASE_CONFIG.get('league_base_dir', 'league_databases')
        
        if not os.path.exists(source_db_path):
            QMessageBox.warning(self, "错误", "源数据库文件不存在")
            return
        
        self.start_operation("switch_to_league", source_db_path, base_dir)
    
    def switch_to_single_mode(self):
        """切换到单一数据库模式"""
        base_dir = DATABASE_CONFIG.get('league_base_dir', 'league_databases')
        
        # 选择目标数据库文件
        target_db_path, _ = QFileDialog.getSaveFileName(
            self, "选择目标数据库文件", "odds_data_merged.db", "数据库文件 (*.db)")
        
        if target_db_path:
            self.start_operation("switch_to_single", base_dir, target_db_path)
    
    def start_operation(self, operation, *args):
        """开始数据库操作"""
        if self.worker and self.worker.isRunning():
            QMessageBox.warning(self, "警告", "有操作正在进行中，请稍候...")
            return
        
        self.progress_bar.setVisible(True)
        self.progress_bar.setRange(0, 0)  # 不确定进度
        
        self.worker = DatabaseModeWorker(operation, *args)
        self.worker.progress.connect(self.status_label.setText)
        self.worker.finished.connect(self.on_operation_finished)
        self.worker.start()
    
    def on_operation_finished(self, result):
        """操作完成"""
        self.progress_bar.setVisible(False)
        self.status_label.setText("就绪")
        
        if "error" in result:
            QMessageBox.warning(self, "错误", f"操作失败: {result['error']}")
        else:
            if 'backup_file' in result:
                msg = f"备份并清空操作成功!\n{result['message']}"
            elif 'migrated_matches' in result and 'skipped_matches' in result:
                msg = f"数据库迁移成功!\n{result['message']}\n创建了 {len(result.get('leagues_created', []))} 个联赛数据库"
            elif 'merged_matches' in result and 'skipped_matches' in result:
                msg = f"分库合并成功!\n{result['message']}"
            elif 'cleaned_matches' in result:
                msg = f"重复数据清理成功!\n{result['message']}"
            elif 'total_cleaned_matches' in result:
                msg = f"联赛分库清理成功!\n{result['message']}"
            elif 'migrated_matches' in result:
                msg = f"切换到联赛分库模式成功!\n迁移了 {result['migrated_matches']} 场比赛\n创建了 {len(result.get('leagues_created', []))} 个联赛数据库"
            elif 'merged_matches' in result:
                msg = f"切换到单一数据库模式成功!\n合并了 {result['merged_matches']} 场比赛"
            elif 'database_stats' in result:
                stats = result['database_stats']
                leagues = result['league_distribution']
                table_stats = result.get('table_stats', {})
                extended_status = result.get('extended_table_status', {})
                schema_version = result.get('schema_version', '1.0.0')

                # 验证数据一致性
                total_from_leagues = sum(count for _, count in leagues)
                db_match_count = stats.get('match_count', 0)

                analysis_text = f"""数据库分析结果
================
架构版本: {schema_version}
数据库统计比赛数: {db_match_count:,}
联赛分析比赛数: {total_from_leagues:,}
总记录数: {stats.get('odds_count', 0):,}
数据库大小: {stats.get('db_size_mb', 0):.2f} MB
联赛数量: {result['total_leagues']}
数据表数量: {result.get('total_tables', 0)}

数据表统计:
"""

                # 显示各表的记录数
                for table, count in table_stats.items():
                    analysis_text += f"• {table}: {count} 条记录\n"

                analysis_text += "\n扩展功能支持:\n"

                # 显示扩展表状态
                feature_mapping = {
                    'technical_stats': '技术统计',
                    'lineups': '阵容信息',
                    'player_stats': '球员统计',
                    'goal_probability': '进失球概率',
                    'schema_versions': '架构版本管理'
                }

                for table, feature_name in feature_mapping.items():
                    status = "✅ 支持" if extended_status.get(table, False) else "❌ 不支持"
                    analysis_text += f"• {feature_name}: {status}\n"

                # 如果数据不一致，显示警告
                if db_match_count != total_from_leagues:
                    analysis_text += f"\n⚠️ 数据不一致警告:\n"
                    analysis_text += f"数据库统计显示 {db_match_count} 场比赛\n"
                    analysis_text += f"但联赛分析只找到 {total_from_leagues} 场比赛\n"
                    analysis_text += f"可能存在数据完整性问题\n"

                analysis_text += "\n联赛分布 (前10):\n"
                for league, count in leagues[:10]:
                    analysis_text += f"• {league}: {count} 场比赛\n"

                if len(leagues) > 10:
                    remaining_count = sum(count for _, count in leagues[10:])
                    analysis_text += f"• ... 其他 {len(leagues) - 10} 个联赛: {remaining_count} 场比赛\n"

                self.details_text.setText(analysis_text)
                msg = "数据库分析完成，详细信息已显示在下方"
            else:
                msg = "操作完成!"
            
            QMessageBox.information(self, "成功", msg)
        
        # 刷新状态
        self.load_current_status()

    def backup_and_clear_database(self):
        """备份并清空当前数据库"""
        try:
            db_path = DATABASE_CONFIG.get('default_db_name', 'odds_data.db')

            if not os.path.exists(db_path):
                QMessageBox.warning(self, "警告", "数据库文件不存在")
                return

            # 确认操作
            reply = QMessageBox.question(self, "确认操作",
                                       f"确定要备份并清空数据库 {db_path} 吗？\n\n"
                                       "操作步骤：\n"
                                       "1. 将当前数据库以日期命名备份\n"
                                       "2. 清空原数据库文件\n\n"
                                       "此操作不可逆，请确认！",
                                       QMessageBox.Yes | QMessageBox.No)

            if reply == QMessageBox.Yes:
                self.start_operation("backup_and_clear", db_path)

        except Exception as e:
            QMessageBox.critical(self, "错误", f"操作失败: {e}")

    def select_and_migrate_database(self):
        """选择数据库文件并迁移到分库"""
        try:
            # 选择数据库文件
            file_path, _ = QFileDialog.getOpenFileName(
                self, "选择要迁移的数据库文件", "", "数据库文件 (*.db)")

            if not file_path:
                return

            base_dir = DATABASE_CONFIG.get('league_base_dir', 'league_databases')

            # 确认操作
            reply = QMessageBox.question(self, "确认迁移",
                                       f"确定要将数据库文件迁移到联赛分库吗？\n\n"
                                       f"源文件: {file_path}\n"
                                       f"目标目录: {base_dir}\n\n"
                                       "系统将自动检测并跳过重复数据",
                                       QMessageBox.Yes | QMessageBox.No)

            if reply == QMessageBox.Yes:
                self.start_operation("migrate_with_duplicate_check", file_path, base_dir)

        except Exception as e:
            QMessageBox.critical(self, "错误", f"操作失败: {e}")

    def merge_leagues_to_single(self):
        """合并联赛分库到单一数据库"""
        try:
            base_dir = DATABASE_CONFIG.get('league_base_dir', 'league_databases')

            if not os.path.exists(base_dir):
                QMessageBox.warning(self, "警告", "联赛分库目录不存在")
                return

            # 选择目标数据库文件
            target_db_path, _ = QFileDialog.getSaveFileName(
                self, "选择目标数据库文件", "odds_data_merged.db", "数据库文件 (*.db)")

            if target_db_path:
                # 确认操作
                reply = QMessageBox.question(self, "确认合并",
                                           f"确定要合并所有联赛分库到单一数据库吗？\n\n"
                                           f"目标文件: {target_db_path}\n\n"
                                           "系统将自动检测并跳过重复数据",
                                           QMessageBox.Yes | QMessageBox.No)

                if reply == QMessageBox.Yes:
                    self.start_operation("merge_with_duplicate_check", base_dir, target_db_path)

        except Exception as e:
            QMessageBox.critical(self, "错误", f"操作失败: {e}")

    def clean_duplicate_data(self):
        """清理重复数据"""
        try:
            # 让用户选择要清理的数据库类型
            reply = QMessageBox.question(self, "选择清理目标",
                                       "请选择要清理重复数据的目标：\n\n"
                                       "Yes - 清理单一数据库 (odds_data.db)\n"
                                       "No - 清理联赛分库\n"
                                       "Cancel - 取消操作",
                                       QMessageBox.Yes | QMessageBox.No | QMessageBox.Cancel)

            if reply == QMessageBox.Yes:
                # 清理单一数据库
                db_path = DATABASE_CONFIG.get('default_db_name', 'odds_data.db')
                if os.path.exists(db_path):
                    self.start_operation("clean_duplicates_single", db_path)
                else:
                    QMessageBox.warning(self, "警告", "单一数据库文件不存在")

            elif reply == QMessageBox.No:
                # 清理联赛分库
                base_dir = DATABASE_CONFIG.get('league_base_dir', 'league_databases')
                if os.path.exists(base_dir):
                    self.start_operation("clean_duplicates_league", base_dir)
                else:
                    QMessageBox.warning(self, "警告", "联赛分库目录不存在")

        except Exception as e:
            QMessageBox.critical(self, "错误", f"操作失败: {e}")

def main():
    app = QApplication(sys.argv)
    window = DatabaseModeSelectorUI()
    window.show()
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试去重分析功能
"""

import sys
import os
import logging

# 添加当前目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

def test_dedup_feature():
    """测试去重分析功能"""
    print("🔍 测试去重分析功能")
    
    try:
        # 模拟组合去重逻辑
        print("\n📋 模拟去重处理逻辑:")
        
        # 模拟原始分析结果
        mock_results = {
            'success': True,
            'results': [
                {
                    'combination_index': 0,
                    'combination': [
                        {'home_odds': 1.9, 'draw_odds': 3.4, 'away_odds': 4.1},
                        {'home_odds': 2.1, 'draw_odds': 3.2, 'away_odds': 3.8}
                    ],
                    'match_count': 5,
                    'result_stats': {'win': 3, 'draw': 1, 'loss': 1},
                    'matched_matches': ['match1', 'match2', 'match3', 'match4', 'match5']
                },
                {
                    'combination_index': 1,
                    'combination': [
                        {'home_odds': 2.0, 'draw_odds': 3.3, 'away_odds': 4.0},
                        {'home_odds': 2.2, 'draw_odds': 3.1, 'away_odds': 3.7}
                    ],
                    'match_count': 3,
                    'result_stats': {'win': 2, 'draw': 0, 'loss': 1},
                    'matched_matches': ['match6', 'match7', 'match8']
                },
                {
                    'combination_index': 2,
                    'combination': [
                        {'home_odds': 1.9, 'draw_odds': 3.4, 'away_odds': 4.1},
                        {'home_odds': 2.1, 'draw_odds': 3.2, 'away_odds': 3.8}
                    ],
                    'match_count': 2,
                    'result_stats': {'win': 1, 'draw': 1, 'loss': 0},
                    'matched_matches': ['match9', 'match10']
                }
            ],
            'total_combinations': 3,
            'analysis_time': 1.5
        }
        
        print(f"原始组合数量: {len(mock_results['results'])}")
        
        # 模拟去重处理
        def format_combination_text(combination):
            """格式化组合文本显示"""
            parts = []
            for i, odds in enumerate(combination):
                part = f"{i+1}:({odds['home_odds']},{odds['draw_odds']},{odds['away_odds']})"
                parts.append(part)
            return " | ".join(parts)
        
        deduped_combinations = {}
        
        for result in mock_results['results']:
            combination = result['combination']
            combination_text = format_combination_text(combination)
            
            print(f"\n处理组合: {combination_text}")
            
            if combination_text in deduped_combinations:
                print(f"  -> 发现重复，合并统计")
                existing = deduped_combinations[combination_text]
                
                # 累加统计数据
                existing['match_count'] += result['match_count']
                existing['result_stats']['win'] += result['result_stats']['win']
                existing['result_stats']['draw'] += result['result_stats']['draw']
                existing['result_stats']['loss'] += result['result_stats']['loss']
                existing['matched_matches'].extend(result['matched_matches'])
                existing['duplicate_count'] += 1
                
                print(f"  -> 累计匹配次数: {existing['match_count']}")
                print(f"  -> 重复次数: {existing['duplicate_count']}")
                
            else:
                print(f"  -> 首次出现，添加到结果")
                new_result = result.copy()
                new_result['duplicate_count'] = 1
                deduped_combinations[combination_text] = new_result
        
        # 转换为列表
        deduped_results = list(deduped_combinations.values())
        
        print(f"\n📊 去重结果:")
        print(f"去重后组合数量: {len(deduped_results)}")
        
        for i, result in enumerate(deduped_results):
            combination = result['combination']
            combination_text = format_combination_text(combination)
            stats = result['result_stats']
            
            print(f"\n组合 {i+1}:")
            print(f"  内容: {combination_text}")
            print(f"  匹配次数: {result['match_count']}")
            print(f"  胜平负: {stats['win']}-{stats['draw']}-{stats['loss']}")
            print(f"  重复次数: {result['duplicate_count']}")
        
        print(f"\n✅ 去重功能测试完成")
        print(f"💡 新的去重分析功能已实现")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    # 配置日志
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    test_dedup_feature()

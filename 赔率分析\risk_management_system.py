#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
风险管理系统
实现资金管理、止损止盈、仓位控制等风险管理机制
"""

import pandas as pd
import numpy as np
import json
import os
from datetime import datetime
from abc import ABC, abstractmethod

import warnings
warnings.filterwarnings('ignore')

class RiskManager(ABC):
    """风险管理器基类"""
    
    def __init__(self, name, description):
        self.name = name
        self.description = description
        self.parameters = {}
    
    @abstractmethod
    def check_risk(self, current_bankroll, proposed_stake, trade_history, market_conditions):
        """检查风险，返回调整后的投注金额"""
        pass

class PositionSizeManager(RiskManager):
    """仓位管理器"""
    
    def __init__(self, max_position_ratio=0.1, max_daily_risk=0.2):
        super().__init__(
            "Position Size Manager",
            "控制单次投注和每日总风险敞口"
        )
        self.parameters = {
            'max_position_ratio': max_position_ratio,  # 单次最大投注比例
            'max_daily_risk': max_daily_risk           # 每日最大风险比例
        }
    
    def check_risk(self, current_bankroll, proposed_stake, trade_history, market_conditions):
        """检查仓位风险"""
        max_single_bet = current_bankroll * self.parameters['max_position_ratio']
        
        # 限制单次投注金额
        adjusted_stake = min(proposed_stake, max_single_bet)
        
        # 检查每日风险
        today_trades = self._get_today_trades(trade_history)
        today_risk = sum(trade.get('stake', 0) for trade in today_trades)
        max_daily_risk_amount = current_bankroll * self.parameters['max_daily_risk']
        
        if today_risk + adjusted_stake > max_daily_risk_amount:
            adjusted_stake = max(0, max_daily_risk_amount - today_risk)
        
        return {
            'adjusted_stake': adjusted_stake,
            'risk_level': 'high' if adjusted_stake < proposed_stake * 0.5 else 'normal',
            'reason': 'position_size_limit' if adjusted_stake < proposed_stake else 'approved'
        }
    
    def _get_today_trades(self, trade_history):
        """获取今日交易（简化实现）"""
        # 这里简化处理，实际应该根据日期筛选
        return trade_history[-10:] if len(trade_history) > 10 else trade_history

class DrawdownManager(RiskManager):
    """回撤管理器"""
    
    def __init__(self, max_drawdown=0.2, stop_loss_threshold=0.15):
        super().__init__(
            "Drawdown Manager",
            "监控回撤水平，实施止损机制"
        )
        self.parameters = {
            'max_drawdown': max_drawdown,
            'stop_loss_threshold': stop_loss_threshold
        }
        self.peak_bankroll = 0
    
    def check_risk(self, current_bankroll, proposed_stake, trade_history, market_conditions):
        """检查回撤风险"""
        # 更新峰值资金
        if current_bankroll > self.peak_bankroll:
            self.peak_bankroll = current_bankroll
        
        # 计算当前回撤
        if self.peak_bankroll > 0:
            current_drawdown = (self.peak_bankroll - current_bankroll) / self.peak_bankroll
        else:
            current_drawdown = 0
        
        # 检查是否触发止损
        if current_drawdown >= self.parameters['stop_loss_threshold']:
            return {
                'adjusted_stake': 0,
                'risk_level': 'critical',
                'reason': 'stop_loss_triggered',
                'current_drawdown': current_drawdown
            }
        
        # 根据回撤水平调整仓位
        if current_drawdown >= self.parameters['max_drawdown'] * 0.5:
            # 回撤超过一半限制时，减少投注
            reduction_factor = 1 - (current_drawdown / self.parameters['max_drawdown'])
            adjusted_stake = proposed_stake * reduction_factor
        else:
            adjusted_stake = proposed_stake
        
        return {
            'adjusted_stake': adjusted_stake,
            'risk_level': 'high' if current_drawdown > self.parameters['max_drawdown'] * 0.3 else 'normal',
            'reason': 'drawdown_adjustment' if adjusted_stake < proposed_stake else 'approved',
            'current_drawdown': current_drawdown
        }

class VolatilityManager(RiskManager):
    """波动率管理器"""
    
    def __init__(self, lookback_period=20, volatility_threshold=0.3):
        super().__init__(
            "Volatility Manager",
            "根据资金波动率调整投注规模"
        )
        self.parameters = {
            'lookback_period': lookback_period,
            'volatility_threshold': volatility_threshold
        }
    
    def check_risk(self, current_bankroll, proposed_stake, trade_history, market_conditions):
        """检查波动率风险"""
        if len(trade_history) < self.parameters['lookback_period']:
            return {
                'adjusted_stake': proposed_stake,
                'risk_level': 'normal',
                'reason': 'insufficient_history'
            }
        
        # 计算最近的收益波动率
        recent_trades = trade_history[-self.parameters['lookback_period']:]
        returns = []
        
        for i in range(1, len(recent_trades)):
            prev_bankroll = recent_trades[i-1].get('bankroll_after', current_bankroll)
            curr_bankroll = recent_trades[i].get('bankroll_after', current_bankroll)
            if prev_bankroll > 0:
                ret = (curr_bankroll - prev_bankroll) / prev_bankroll
                returns.append(ret)
        
        if len(returns) < 5:
            volatility = 0
        else:
            volatility = np.std(returns)
        
        # 根据波动率调整投注
        if volatility > self.parameters['volatility_threshold']:
            # 高波动时减少投注
            reduction_factor = self.parameters['volatility_threshold'] / volatility
            adjusted_stake = proposed_stake * reduction_factor
        else:
            adjusted_stake = proposed_stake
        
        return {
            'adjusted_stake': adjusted_stake,
            'risk_level': 'high' if volatility > self.parameters['volatility_threshold'] else 'normal',
            'reason': 'volatility_adjustment' if adjusted_stake < proposed_stake else 'approved',
            'volatility': volatility
        }

class StreakManager(RiskManager):
    """连败管理器"""
    
    def __init__(self, max_losing_streak=5, streak_reduction_factor=0.5):
        super().__init__(
            "Streak Manager",
            "监控连败情况，防止情绪化投注"
        )
        self.parameters = {
            'max_losing_streak': max_losing_streak,
            'streak_reduction_factor': streak_reduction_factor
        }
    
    def check_risk(self, current_bankroll, proposed_stake, trade_history, market_conditions):
        """检查连败风险"""
        if not trade_history:
            return {
                'adjusted_stake': proposed_stake,
                'risk_level': 'normal',
                'reason': 'no_history'
            }
        
        # 计算当前连败次数
        losing_streak = 0
        for trade in reversed(trade_history):
            if trade.get('profit', 0) < 0:
                losing_streak += 1
            else:
                break
        
        # 根据连败次数调整投注
        if losing_streak >= self.parameters['max_losing_streak']:
            # 连败过多时大幅减少投注
            adjusted_stake = proposed_stake * self.parameters['streak_reduction_factor']
        elif losing_streak >= self.parameters['max_losing_streak'] * 0.6:
            # 连败较多时适度减少投注
            reduction = 1 - (losing_streak / self.parameters['max_losing_streak']) * 0.5
            adjusted_stake = proposed_stake * reduction
        else:
            adjusted_stake = proposed_stake
        
        return {
            'adjusted_stake': adjusted_stake,
            'risk_level': 'high' if losing_streak >= self.parameters['max_losing_streak'] * 0.6 else 'normal',
            'reason': 'streak_adjustment' if adjusted_stake < proposed_stake else 'approved',
            'losing_streak': losing_streak
        }

class ConfidenceManager(RiskManager):
    """置信度管理器"""
    
    def __init__(self, min_confidence=0.6, confidence_scaling=True):
        super().__init__(
            "Confidence Manager",
            "根据预测置信度调整投注规模"
        )
        self.parameters = {
            'min_confidence': min_confidence,
            'confidence_scaling': confidence_scaling
        }
    
    def check_risk(self, current_bankroll, proposed_stake, trade_history, market_conditions):
        """检查置信度风险"""
        confidence = market_conditions.get('confidence', 0.5)
        
        # 置信度过低时拒绝投注
        if confidence < self.parameters['min_confidence']:
            return {
                'adjusted_stake': 0,
                'risk_level': 'high',
                'reason': 'low_confidence',
                'confidence': confidence
            }
        
        # 根据置信度缩放投注金额
        if self.parameters['confidence_scaling']:
            # 线性缩放：置信度越高，投注越多
            scaling_factor = (confidence - self.parameters['min_confidence']) / (1 - self.parameters['min_confidence'])
            adjusted_stake = proposed_stake * (0.5 + 0.5 * scaling_factor)  # 0.5-1.0倍缩放
        else:
            adjusted_stake = proposed_stake
        
        return {
            'adjusted_stake': adjusted_stake,
            'risk_level': 'normal',
            'reason': 'confidence_scaling' if adjusted_stake < proposed_stake else 'approved',
            'confidence': confidence
        }

class RiskManagementSystem:
    """综合风险管理系统"""
    
    def __init__(self):
        self.risk_managers = []
        self.risk_history = []
        self.initialize_managers()
    
    def initialize_managers(self):
        """初始化风险管理器"""
        print("=== 初始化风险管理系统 ===")
        
        self.risk_managers = [
            PositionSizeManager(max_position_ratio=0.1, max_daily_risk=0.2),
            DrawdownManager(max_drawdown=0.2, stop_loss_threshold=0.15),
            VolatilityManager(lookback_period=20, volatility_threshold=0.3),
            StreakManager(max_losing_streak=5, streak_reduction_factor=0.5),
            ConfidenceManager(min_confidence=0.6, confidence_scaling=True)
        ]
        
        print(f"初始化了 {len(self.risk_managers)} 个风险管理器:")
        for manager in self.risk_managers:
            print(f"  - {manager.name}: {manager.description}")
    
    def assess_risk(self, current_bankroll, proposed_stake, trade_history, market_conditions):
        """综合风险评估"""
        risk_assessments = []
        final_stake = proposed_stake
        overall_risk_level = 'normal'
        
        for manager in self.risk_managers:
            try:
                assessment = manager.check_risk(
                    current_bankroll, final_stake, trade_history, market_conditions
                )
                assessment['manager'] = manager.name
                risk_assessments.append(assessment)
                
                # 使用最保守的投注金额
                final_stake = min(final_stake, assessment['adjusted_stake'])
                
                # 更新风险等级
                if assessment['risk_level'] == 'critical':
                    overall_risk_level = 'critical'
                elif assessment['risk_level'] == 'high' and overall_risk_level != 'critical':
                    overall_risk_level = 'high'
                    
            except Exception as e:
                print(f"风险管理器 {manager.name} 评估失败: {e}")
                continue
        
        # 综合风险评估结果
        risk_result = {
            'original_stake': proposed_stake,
            'final_stake': final_stake,
            'risk_level': overall_risk_level,
            'reduction_ratio': final_stake / proposed_stake if proposed_stake > 0 else 0,
            'individual_assessments': risk_assessments,
            'approved': final_stake > 0 and overall_risk_level != 'critical'
        }
        
        # 记录风险评估历史
        self.risk_history.append({
            'timestamp': datetime.now().isoformat(),
            'assessment': risk_result
        })
        
        return risk_result
    
    def get_risk_summary(self):
        """获取风险管理总结"""
        if not self.risk_history:
            return {'message': '暂无风险评估历史'}
        
        recent_assessments = self.risk_history[-20:]  # 最近20次评估
        
        # 统计风险等级分布
        risk_levels = [a['assessment']['risk_level'] for a in recent_assessments]
        risk_distribution = {
            'normal': risk_levels.count('normal'),
            'high': risk_levels.count('high'),
            'critical': risk_levels.count('critical')
        }
        
        # 统计平均削减比例
        reductions = [a['assessment']['reduction_ratio'] for a in recent_assessments]
        avg_reduction = np.mean(reductions) if reductions else 1.0
        
        # 统计批准率
        approvals = [a['assessment']['approved'] for a in recent_assessments]
        approval_rate = sum(approvals) / len(approvals) if approvals else 0
        
        summary = {
            'total_assessments': len(self.risk_history),
            'recent_assessments': len(recent_assessments),
            'risk_distribution': risk_distribution,
            'avg_stake_reduction': 1 - avg_reduction,
            'approval_rate': approval_rate,
            'most_active_manager': self._get_most_active_manager(recent_assessments)
        }
        
        return summary
    
    def _get_most_active_manager(self, assessments):
        """获取最活跃的风险管理器"""
        manager_activity = {}
        
        for assessment in assessments:
            for individual in assessment['assessment']['individual_assessments']:
                manager = individual['manager']
                if individual['reason'] != 'approved':
                    manager_activity[manager] = manager_activity.get(manager, 0) + 1
        
        if manager_activity:
            return max(manager_activity.items(), key=lambda x: x[1])
        else:
            return None
    
    def update_manager_parameters(self, manager_name, new_parameters):
        """更新风险管理器参数"""
        for manager in self.risk_managers:
            if manager.name.lower().replace(' ', '_') == manager_name.lower():
                manager.parameters.update(new_parameters)
                print(f"已更新 {manager.name} 的参数: {new_parameters}")
                return True
        
        print(f"未找到风险管理器: {manager_name}")
        return False
    
    def save_risk_config(self, filename_prefix="risk_config"):
        """保存风险管理配置"""
        config = {
            'managers': [],
            'risk_summary': self.get_risk_summary()
        }
        
        for manager in self.risk_managers:
            config['managers'].append({
                'name': manager.name,
                'description': manager.description,
                'parameters': manager.parameters,
                'type': type(manager).__name__
            })
        
        output_file = f"{filename_prefix}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(config, f, ensure_ascii=False, indent=2)
        
        print(f"\n风险管理配置已保存到: {output_file}")
        return output_file

def main():
    # 创建风险管理系统
    risk_system = RiskManagementSystem()
    
    # 模拟风险评估
    print("\n=== 模拟风险评估 ===")
    
    # 模拟交易历史
    trade_history = [
        {'profit': -50, 'stake': 100, 'bankroll_after': 950},
        {'profit': 80, 'stake': 120, 'bankroll_after': 1030},
        {'profit': -30, 'stake': 80, 'bankroll_after': 1000},
        {'profit': -40, 'stake': 90, 'bankroll_after': 960},
        {'profit': -35, 'stake': 85, 'bankroll_after': 925}
    ]
    
    # 模拟市场条件
    market_conditions = {
        'confidence': 0.75,
        'volatility': 0.25,
        'market_sentiment': 'neutral'
    }
    
    # 进行风险评估
    risk_result = risk_system.assess_risk(
        current_bankroll=1000,
        proposed_stake=150,
        trade_history=trade_history,
        market_conditions=market_conditions
    )
    
    print(f"原始投注金额: {risk_result['original_stake']}")
    print(f"调整后金额: {risk_result['final_stake']:.2f}")
    print(f"风险等级: {risk_result['risk_level']}")
    print(f"削减比例: {(1-risk_result['reduction_ratio'])*100:.1f}%")
    print(f"是否批准: {'是' if risk_result['approved'] else '否'}")
    
    print(f"\n各管理器评估:")
    for assessment in risk_result['individual_assessments']:
        print(f"  {assessment['manager']}: {assessment['reason']} "
              f"(调整: {assessment['adjusted_stake']:.2f})")
    
    # 获取风险总结
    summary = risk_system.get_risk_summary()
    print(f"\n=== 风险管理总结 ===")
    print(f"总评估次数: {summary['total_assessments']}")
    print(f"批准率: {summary['approval_rate']:.3f}")
    print(f"平均削减: {summary['avg_stake_reduction']*100:.1f}%")
    
    # 保存配置
    risk_system.save_risk_config()

if __name__ == "__main__":
    main()

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试正则表达式匹配
"""

import re

def test_regex():
    # 从实际文件中读取内容
    with open('debug_js_2804677.txt', 'r', encoding='utf-8') as f:
        content = f.read()
    
    print(f"文件长度: {len(content)}")
    
    # 测试不同的正则表达式
    patterns = [
        r'var game=Array\("([^"]+)"\);',
        r'var game=Array\(([^)]+)\);',
        r'var game=Array\((.+?)\);',
        r'var game=(.+?);',
        r'var game=Array\((.*?)\);'
    ]
    
    for i, pattern in enumerate(patterns):
        print(f"\n测试模式 {i+1}: {pattern}")
        
        match = re.search(pattern, content, re.DOTALL)
        if match:
            matched_text = match.group(1)
            print(f"✅ 匹配成功，长度: {len(matched_text)}")
            print(f"前100字符: {matched_text[:100]}...")
            
            # 如果匹配到引号包围的内容，提取引号内的部分
            if matched_text.startswith('"') and matched_text.endswith('"'):
                inner_content = matched_text[1:-1]
                print(f"引号内容长度: {len(inner_content)}")
                print(f"引号内容前100字符: {inner_content[:100]}...")
        else:
            print("❌ 未匹配")
    
    # 手动查找game变量的位置
    print(f"\n手动查找 'var game=' 的位置:")
    pos = content.find('var game=')
    if pos >= 0:
        print(f"找到位置: {pos}")
        # 显示周围的内容
        start = max(0, pos - 50)
        end = min(len(content), pos + 200)
        print(f"周围内容: {content[start:end]}")
    else:
        print("未找到 'var game='")

if __name__ == "__main__":
    test_regex()

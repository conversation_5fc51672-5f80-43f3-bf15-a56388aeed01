#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
独立比赛筛选分析工具 - 修正版
实现博彩态度2和凯利分析2的筛选功能
每次都重新抓取赔率数据，无数据库依赖
"""

import logging
from datetime import datetime
from typing import List, Dict, Optional
from enhanced_odds_scraper import EnhancedOddsScraper

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class MatchAnalyzer:
    """独立比赛分析器"""

    def __init__(self):
        """初始化分析器"""
        # 使用现有的赔率抓取器
        self.scraper = EnhancedOddsScraper()
        
        self.target_companies = [
            'bet365', 'betathome', 'betfair', 'bwin', 'coral',
            'Interwetten', 'pinnacle', '伟德', '利记', '威廉希尔',
            '明升', '易胜博', '澳门', '立博', '金宝博', '香港马会', '竞猜官方'
        ]

        print("🎯 独立比赛筛选分析工具已初始化")
        print("📡 每次分析都将重新抓取最新赔率数据")
        print(f"🏢 支持的博彩公司: {len(self.target_companies)} 家")
    
    def scrape_match_odds(self, match_id: str) -> Optional[Dict]:
        """重新抓取比赛的赔率数据"""
        print(f"\n📡 正在抓取比赛 {match_id} 的赔率数据...")
        
        try:
            # 使用现有的抓取器获取完整数据
            complete_data = self.scraper.scrape_complete_match_data(
                match_id=match_id,
                max_companies=16,  # 抓取所有16家公司
                delay=1.0  # 1秒延迟
            )
            
            if not complete_data or not complete_data.get('odds_data'):
                print("❌ 未能获取到赔率数据")
                return None
            
            # 转换数据格式
            companies_data = {}
            odds_records = complete_data['odds_data']
            
            print(f"📊 原始数据包含 {len(odds_records)} 条记录")
            
            # 按公司分组，取最新的赔率
            company_latest_odds = {}
            for record in odds_records:
                company_name = record['company_name']
                
                # 检查是否是目标公司
                if company_name not in self.target_companies:
                    continue
                
                # 构建时间键用于排序
                time_key = f"{record['date']} {record['time']}"
                
                if company_name not in company_latest_odds:
                    company_latest_odds[company_name] = record
                else:
                    # 比较时间，保留最新的
                    current_time = f"{company_latest_odds[company_name]['date']} {company_latest_odds[company_name]['time']}"
                    if time_key > current_time:
                        company_latest_odds[company_name] = record
            
            # 转换为标准格式
            for company_name, record in company_latest_odds.items():
                companies_data[company_name] = {
                    'home_odds': record['home_odds'],
                    'draw_odds': record['draw_odds'],
                    'away_odds': record['away_odds'],
                    'scrape_time': f"{record['date']} {record['time']}"
                }
            
            print(f"✅ 成功处理 {len(companies_data)} 家公司的最新赔率")
            for company, odds in companies_data.items():
                print(f"  📊 {company}: {odds['home_odds']:.2f} {odds['draw_odds']:.2f} {odds['away_odds']:.2f}")
            
            return {
                'match_id': match_id,
                'companies': companies_data,
                'scrape_time': datetime.now().isoformat()
            }
            
        except Exception as e:
            print(f"❌ 抓取赔率数据失败: {e}")
            return None

    def analyze_betting_attitude2(self, match_id: str, odds_type: str, threshold: int, target_company: str):
        """分析博彩态度2（基于实时抓取数据）"""
        print(f"\n🔍 开始分析比赛 {match_id} 的博彩态度2")
        print(f"📋 目标公司: {target_company}, 投注类型: {odds_type}, 阈值: {threshold}")
        
        try:
            # 重新抓取赔率数据
            odds_data = self.scrape_match_odds(match_id)
            if not odds_data or not odds_data['companies']:
                return False, f"无法获取比赛 {match_id} 的赔率数据"
            
            companies = odds_data['companies']
            print(f"📊 成功获取 {len(companies)} 家公司的赔率数据")
            
            # 检查目标公司是否存在
            if target_company not in companies:
                available_companies = list(companies.keys())
                return False, f"目标公司 {target_company} 不在可用公司列表中。可用公司: {available_companies}"
            
            # 获取目标公司的赔率
            target_data = companies[target_company]
            if odds_type == 'home':
                target_odds = target_data['home_odds']
            elif odds_type == 'draw':
                target_odds = target_data['draw_odds']
            elif odds_type == 'away':
                target_odds = target_data['away_odds']
            else:
                return False, f"无效的投注类型: {odds_type}"
            
            print(f"🎯 目标公司 {target_company} 的 {odds_type} 赔率: {target_odds:.2f}")
            
            # 统计有多少其他公司的赔率高于目标公司
            higher_count = 0
            other_companies_data = []
            
            for company_name, company_data in companies.items():
                if company_name == target_company:
                    continue
                
                if odds_type == 'home':
                    company_odds = company_data['home_odds']
                elif odds_type == 'draw':
                    company_odds = company_data['draw_odds']
                else:  # away
                    company_odds = company_data['away_odds']
                
                other_companies_data.append({
                    'company': company_name,
                    'odds': company_odds,
                    'higher': company_odds > target_odds
                })
                
                if company_odds > target_odds:
                    higher_count += 1
                    print(f"  📈 {company_name}: {company_odds:.2f} (高于目标)")
                else:
                    print(f"  📉 {company_name}: {company_odds:.2f} (低于或等于目标)")
            
            # 判断是否符合条件：高于目标公司赔率的公司数量小于阈值
            is_qualified = higher_count < threshold
            
            result_msg = f"目标公司 {target_company} 赔率: {target_odds:.2f}, 高于此赔率的公司数量: {higher_count}/{len(other_companies_data)}, 阈值: {threshold}, 结果: {'✅符合条件' if is_qualified else '❌不符合条件'}"
            
            print(f"📊 分析结果: {result_msg}")
            return is_qualified, result_msg

        except Exception as e:
            error_msg = f"分析失败: {e}"
            print(f"❌ {error_msg}")
            return False, error_msg

def main():
    """主函数"""
    print("🎯 独立比赛筛选分析工具")
    print("=" * 60)
    
    analyzer = MatchAnalyzer()
    
    while True:
        print(f"\n请输入比赛ID（输入 'quit' 退出）:")
        match_id = input().strip()
        
        if match_id.lower() == 'quit':
            break
        
        if not match_id:
            print("❌ 请输入有效的比赛ID")
            continue
        
        print(f"\n🧪 开始分析比赛 {match_id}...")
        
        # 测试筛选条件1：竞猜官方，阈值2，主胜
        print(f"\n{'='*50}")
        print("🔍 筛选条件1：博彩态度2（竞猜官方，阈值2，主胜）")
        print(f"{'='*50}")
        
        result1, msg1 = analyzer.analyze_betting_attitude2(match_id, 'home', 2, '竞猜官方')
        print(f"📊 条件1结果: {msg1}")
        
        # 测试筛选条件2：香港马会，阈值2，主胜
        print(f"\n{'='*50}")
        print("🔍 筛选条件2：博彩态度2（香港马会，阈值2，主胜）")
        print(f"{'='*50}")
        
        result2, msg2 = analyzer.analyze_betting_attitude2(match_id, 'home', 2, '香港马会')
        print(f"📊 条件2结果: {msg2}")
        
        # 综合结果
        print(f"\n{'='*50}")
        print("📋 综合分析结果")
        print(f"{'='*50}")
        print(f"条件1（竞猜官方）: {'✅通过' if result1 else '❌未通过'}")
        print(f"条件2（香港马会）: {'✅通过' if result2 else '❌未通过'}")
        
        if result1 and result2:
            print("🎉 比赛通过所有筛选条件！")
        elif result1 or result2:
            print("⚠️  比赛部分通过筛选条件")
        else:
            print("❌ 比赛未通过任何筛选条件")

if __name__ == "__main__":
    main()

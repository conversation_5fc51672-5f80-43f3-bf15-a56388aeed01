#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
博彩态度筛选功能演示
展示新增的博彩态度筛选功能的完整使用流程
"""

import sqlite3
import sys
import os

def demo_single_company_filter():
    """演示单个公司的博彩态度筛选"""
    print("=== 演示：单个公司博彩态度筛选 ===")
    
    # 模拟用户选择
    selected_company = "pinnacle"
    odds_type = "home_odds"  # 主胜
    threshold = 0.95  # 降低阈值以便找到更多符合条件的比赛
    
    print(f"筛选条件:")
    print(f"  选择公司: {selected_company}")
    print(f"  赔率类型: 主胜")
    print(f"  阈值: {threshold}")
    print()
    
    try:
        with sqlite3.connect("odds_data.db") as conn:
            conn.row_factory = sqlite3.Row
            cursor = conn.cursor()
            
            # 模拟筛选逻辑
            qualified_matches = []
            
            # 获取有该公司数据的比赛
            cursor.execute(f'''
                SELECT DISTINCT match_id 
                FROM odds 
                WHERE company_name = ? AND {odds_type} IS NOT NULL
                LIMIT 10
            ''', (selected_company,))
            
            match_ids = [row[0] for row in cursor.fetchall()]
            print(f"开始分析 {len(match_ids)} 场比赛...")
            
            for match_id in match_ids:
                # 获取该公司的开盘赔率
                cursor.execute(f'''
                    SELECT date, time, {odds_type}
                    FROM odds
                    WHERE match_id = ? AND company_name = ? AND {odds_type} IS NOT NULL
                    ORDER BY date ASC, time ASC
                    LIMIT 1
                ''', (match_id, selected_company))
                
                target_record = cursor.fetchone()
                if not target_record:
                    continue
                
                target_date = target_record['date']
                target_time = target_record['time']
                target_odds = float(target_record[odds_type])
                
                # 获取同时间点其他公司的赔率
                cursor.execute(f'''
                    SELECT {odds_type}
                    FROM odds
                    WHERE match_id = ? AND company_name != ? AND {odds_type} IS NOT NULL
                    AND date = ? AND time = ?
                ''', (match_id, selected_company, target_date, target_time))

                other_odds = cursor.fetchall()

                if len(other_odds) == 0:
                    # 如果同时间点没有其他公司数据，尝试找最接近的时间
                    cursor.execute(f'''
                        SELECT {odds_type}
                        FROM odds
                        WHERE match_id = ? AND company_name != ? AND {odds_type} IS NOT NULL
                        ORDER BY ABS(julianday(date || ' ' || time) - julianday(? || ' ' || ?))
                        LIMIT 5
                    ''', (match_id, selected_company, target_date, target_time))

                    other_odds = cursor.fetchall()

                if len(other_odds) == 0:
                    print(f"    比赛 {match_id}: 没有找到其他公司的赔率数据")
                    continue
                
                # 计算平均赔率和比率
                other_odds_values = [float(record[odds_type]) for record in other_odds]
                avg_other_odds = sum(other_odds_values) / len(other_odds)
                ratio = target_odds / avg_other_odds
                
                print(f"    比赛 {match_id}: {selected_company}={target_odds:.3f}, 其他平均={avg_other_odds:.3f}, 比率={ratio:.3f}")

                if ratio >= threshold:
                    qualified_matches.append({
                        'match_id': match_id,
                        'target_odds': target_odds,
                        'avg_other_odds': avg_other_odds,
                        'ratio': ratio,
                        'other_companies_count': len(other_odds)
                    })
            
            print(f"筛选结果: {len(qualified_matches)} 场比赛符合条件")
            
            # 显示前3场符合条件的比赛详情
            for i, match in enumerate(qualified_matches[:3], 1):
                print(f"\n{i}. 比赛 {match['match_id']}:")
                print(f"   {selected_company} 主胜赔率: {match['target_odds']:.3f}")
                print(f"   其他 {match['other_companies_count']} 家公司平均: {match['avg_other_odds']:.3f}")
                print(f"   比率: {match['ratio']:.3f} (>= {threshold})")
                print(f"   结果: ✅ 符合条件")
            
            return len(qualified_matches) > 0
            
    except Exception as e:
        print(f"❌ 演示失败: {e}")
        return False

def demo_multiple_companies_filter():
    """演示多个公司的博彩态度筛选"""
    print("\n=== 演示：多个公司博彩态度筛选 ===")
    
    # 模拟用户选择
    selected_companies = ["pinnacle", "bet365"]
    odds_type = "away_odds"  # 客胜
    threshold = 1.08
    
    print(f"筛选条件:")
    print(f"  选择公司: {', '.join(selected_companies)}")
    print(f"  赔率类型: 客胜")
    print(f"  阈值: {threshold}")
    print(f"  要求: 所有选中公司都必须满足条件")
    print()
    
    try:
        with sqlite3.connect("odds_data.db") as conn:
            conn.row_factory = sqlite3.Row
            cursor = conn.cursor()
            
            # 找到同时有这些公司数据的比赛
            placeholders = ','.join(['?' for _ in selected_companies])
            cursor.execute(f'''
                SELECT match_id, COUNT(DISTINCT company_name) as company_count
                FROM odds 
                WHERE company_name IN ({placeholders}) AND {odds_type} IS NOT NULL
                GROUP BY match_id
                HAVING company_count = ?
                LIMIT 5
            ''', selected_companies + [len(selected_companies)])
            
            candidate_matches = cursor.fetchall()
            print(f"找到 {len(candidate_matches)} 场有所有选中公司数据的比赛")
            
            qualified_matches = []
            
            for match_record in candidate_matches:
                match_id = match_record['match_id']
                all_companies_qualified = True
                match_details = []
                
                # 检查每个选中的公司
                for company in selected_companies:
                    # 获取该公司的开盘赔率
                    cursor.execute(f'''
                        SELECT date, time, {odds_type}
                        FROM odds
                        WHERE match_id = ? AND company_name = ? AND {odds_type} IS NOT NULL
                        ORDER BY date ASC, time ASC
                        LIMIT 1
                    ''', (match_id, company))
                    
                    target_record = cursor.fetchone()
                    if not target_record:
                        all_companies_qualified = False
                        break
                    
                    target_date = target_record['date']
                    target_time = target_record['time']
                    target_odds = float(target_record[odds_type])
                    
                    # 获取同时间点其他公司的赔率
                    cursor.execute(f'''
                        SELECT {odds_type}
                        FROM odds
                        WHERE match_id = ? AND company_name != ? AND {odds_type} IS NOT NULL
                        AND date = ? AND time = ?
                    ''', (match_id, company, target_date, target_time))
                    
                    other_odds = cursor.fetchall()
                    
                    if len(other_odds) == 0:
                        all_companies_qualified = False
                        break
                    
                    # 计算比率
                    other_odds_values = [float(record[odds_type]) for record in other_odds]
                    avg_other_odds = sum(other_odds_values) / len(other_odds)
                    ratio = target_odds / avg_other_odds
                    
                    company_qualified = ratio >= threshold
                    match_details.append({
                        'company': company,
                        'target_odds': target_odds,
                        'avg_other_odds': avg_other_odds,
                        'ratio': ratio,
                        'qualified': company_qualified
                    })
                    
                    if not company_qualified:
                        all_companies_qualified = False
                
                if all_companies_qualified:
                    qualified_matches.append({
                        'match_id': match_id,
                        'details': match_details
                    })
            
            print(f"筛选结果: {len(qualified_matches)} 场比赛符合条件")
            
            # 显示符合条件的比赛详情
            for i, match in enumerate(qualified_matches[:2], 1):
                print(f"\n{i}. 比赛 {match['match_id']}:")
                for detail in match['details']:
                    print(f"   {detail['company']:10s}: 客胜{detail['target_odds']:.3f} / 平均{detail['avg_other_odds']:.3f} = {detail['ratio']:.3f} ({'✅' if detail['qualified'] else '❌'})")
                print(f"   结果: ✅ 所有公司都符合条件")
            
            return len(qualified_matches) > 0
            
    except Exception as e:
        print(f"❌ 演示失败: {e}")
        return False

def demo_different_odds_types():
    """演示不同赔率类型的筛选"""
    print("\n=== 演示：不同赔率类型筛选 ===")
    
    odds_types = [
        ("home_odds", "主胜"),
        ("draw_odds", "平局"),
        ("away_odds", "客胜")
    ]
    
    selected_company = "明升"
    threshold = 1.03
    
    print(f"测试公司: {selected_company}")
    print(f"阈值: {threshold}")
    print()
    
    try:
        with sqlite3.connect("odds_data.db") as conn:
            conn.row_factory = sqlite3.Row
            cursor = conn.cursor()
            
            for odds_type, type_name in odds_types:
                print(f"--- {type_name}赔率筛选 ---")
                
                # 获取有该公司数据的比赛（限制数量以加快演示）
                cursor.execute(f'''
                    SELECT DISTINCT match_id 
                    FROM odds 
                    WHERE company_name = ? AND {odds_type} IS NOT NULL
                    LIMIT 3
                ''', (selected_company,))
                
                match_ids = [row[0] for row in cursor.fetchall()]
                qualified_count = 0
                
                for match_id in match_ids:
                    # 获取该公司的开盘赔率
                    cursor.execute(f'''
                        SELECT date, time, {odds_type}
                        FROM odds
                        WHERE match_id = ? AND company_name = ? AND {odds_type} IS NOT NULL
                        ORDER BY date ASC, time ASC
                        LIMIT 1
                    ''', (match_id, selected_company))
                    
                    target_record = cursor.fetchone()
                    if not target_record:
                        continue
                    
                    target_date = target_record['date']
                    target_time = target_record['time']
                    target_odds = float(target_record[odds_type])
                    
                    # 获取同时间点其他公司的赔率
                    cursor.execute(f'''
                        SELECT {odds_type}
                        FROM odds
                        WHERE match_id = ? AND company_name != ? AND {odds_type} IS NOT NULL
                        AND date = ? AND time = ?
                    ''', (match_id, selected_company, target_date, target_time))
                    
                    other_odds = cursor.fetchall()
                    
                    if len(other_odds) == 0:
                        continue
                    
                    # 计算比率
                    other_odds_values = [float(record[odds_type]) for record in other_odds]
                    avg_other_odds = sum(other_odds_values) / len(other_odds)
                    ratio = target_odds / avg_other_odds
                    
                    qualified = ratio >= threshold
                    if qualified:
                        qualified_count += 1
                    
                    print(f"  比赛 {match_id}: {target_odds:.3f}/{avg_other_odds:.3f} = {ratio:.3f} ({'✅' if qualified else '❌'})")
                
                print(f"  {type_name}赔率筛选: {qualified_count}/{len(match_ids)} 场符合条件")
                print()
            
            return True
            
    except Exception as e:
        print(f"❌ 演示失败: {e}")
        return False

def main():
    """主演示函数"""
    print("🎯 博彩态度筛选功能完整演示")
    print("=" * 60)
    
    # 检查数据库文件是否存在
    if not os.path.exists("odds_data.db"):
        print("❌ 数据库文件 odds_data.db 不存在")
        return False
    
    demos = [
        demo_single_company_filter,
        demo_multiple_companies_filter,
        demo_different_odds_types
    ]
    
    passed = 0
    total = len(demos)
    
    for demo in demos:
        try:
            if demo():
                passed += 1
        except Exception as e:
            print(f"❌ 演示异常: {e}")
    
    print("=" * 60)
    print(f"📊 演示结果: {passed}/{total} 成功")
    
    if passed == total:
        print("🎉 所有演示成功！")
        print("\n🚀 博彩态度筛选功能已完成，主要特点:")
        print("✅ 支持单个或多个博彩公司选择")
        print("✅ 支持主胜、平局、客胜三种赔率类型")
        print("✅ 可自定义阈值（默认1.05）")
        print("✅ 基于开盘时间点的精确比较")
        print("✅ 多公司时要求所有公司都满足条件")
        print("✅ 集成到现有筛选框架中")
        return True
    else:
        print("⚠️  部分演示失败，请检查相关功能。")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)

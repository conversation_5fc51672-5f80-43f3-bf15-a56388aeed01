# 赔率组合观察工具 - 回测功能说明

## 🎯 新增功能概述

在原有的"ID区间数据分析"功能基础上，新增了**回测功能**，可以基于已获得的区间分析数据进行投资回测，评估投资策略的历史表现。

## 📊 功能特点

### 🔢 新增控件
- **投资阈值输入框**：输入小数值（如：1.05）
- **场次阈值输入框**：输入整数值（如：5）
- **回测按钮**：橙色按钮，执行回测分析

### 📋 回测逻辑

#### 投资条件判断
1. **场次条件**：总主胜 >= 场次阈值
2. **投资价值条件**：(总主胜/总匹配次数) × 主胜赔率 >= 投资阈值

#### 收益计算
- **命中**：按照主胜赔率计算收益
- **未命中**：收益为0
- **净利润**：总回报 - 总投资

### 🎨 界面布局
```
第七行：回测功能控件
┌─────────────┬──────────────┬──────────────┬──────────────┬────────┐
│ 回测功能:   │ 投资阈值:    │ [1.05]       │ 场次阈值:    │ [5]    │
│             │              │              │              │ [回测] │
└─────────────┴──────────────┴──────────────┴──────────────┴────────┘
```

## 🔧 技术实现

### 修改的文件
- **odds_combination_ui.py**：主要修改文件

### 核心修改点

#### 1. UI控件添加
```python
# 第七行：回测功能控件
backtest_layout = QHBoxLayout()
backtest_layout.addWidget(QLabel("回测功能:"))

# 投资阈值输入
self.investment_threshold_input = QLineEdit()
self.investment_threshold_input.setText("1.05")  # 默认值

# 场次阈值输入
self.match_threshold_input = QLineEdit()
self.match_threshold_input.setText("5")  # 默认值

# 回测按钮
self.backtest_button = QPushButton("回测")
self.backtest_button.setEnabled(False)  # 初始禁用
```

#### 2. 数据存储和状态管理
```python
# 初始化时添加数据存储
self.range_analysis_data = None  # 存储ID区间分析的数据

# ID区间分析完成后启用回测
def on_range_analysis_completed(self, report_data: list):
    self.range_analysis_data = report_data
    if report_data:
        self.backtest_button.setEnabled(True)
```

#### 3. 比赛结果重新索引
```python
def get_match_result_for_backtest(self, match_id: str) -> str:
    """获取比赛结果用于回测（重新索引）"""
    cursor = self.database.cursor()
    cursor.execute("""
        SELECT home_score, away_score 
        FROM matches 
        WHERE match_id = ?
    """, (match_id,))
    
    result = cursor.fetchone()
    if result:
        home_score, away_score = result
        if home_score > away_score:
            return "主胜"
        elif home_score < away_score:
            return "客胜"
        else:
            return "平局"
```

#### 4. 赔率解析
```python
def extract_home_odds_from_combination(self, combination_content: str) -> float:
    """从组合内容中提取第二组赔率的主胜赔率"""
    # 组合内容格式：1:(1.9,3.4,4.1)|2:(2.1,3.2,3.8)|...
    # 提取第二组的主胜赔率（第一个数字）
    parts = combination_content.split('|')
    if len(parts) >= 2:
        second_part = parts[1]  # 第二组
        odds_str = second_part.split(':(')[1].split(')')[0]
        odds_values = odds_str.split(',')
        return float(odds_values[0])  # 主胜赔率
```

#### 5. 回测核心算法
```python
def perform_backtest(self, investment_threshold: float, match_threshold: int):
    """执行回测分析"""
    for report_item in self.range_analysis_data:
        total_wins_all = report_item.get('total_wins_all', 0)
        total_matches_all = report_item.get('total_matches_all', 0)
        
        # 检查场次阈值
        if total_wins_all >= match_threshold and total_matches_all > 0:
            # 获取主胜赔率
            home_odds = self.extract_home_odds_from_combination(combination_content)
            
            # 计算投资价值
            win_rate = total_wins_all / total_matches_all
            investment_value = win_rate * home_odds
            
            # 检查投资阈值
            if investment_value >= investment_threshold:
                # 执行投资并计算收益
                if actual_result == "主胜":
                    return_value = home_odds
                else:
                    return_value = 0
```

## 📋 使用方法

### 操作步骤
1. **完成ID区间分析**：
   - 选择数据库
   - 输入起始ID和结束ID
   - 选择博彩公司和组合数
   - 点击"ID区间数据分析"并等待完成

2. **设置回测参数**：
   - 投资阈值：如1.05（表示期望投资价值）
   - 场次阈值：如5（表示最少主胜场次）

3. **执行回测**：
   - 点击"回测"按钮
   - 等待分析完成
   - 查看回测结果

### 参数说明
- **投资阈值**：投资价值的最低要求
  - 计算公式：(总主胜/总匹配次数) × 主胜赔率
  - 建议值：1.05-1.20
  
- **场次阈值**：总主胜的最少场次要求
  - 用于过滤数据不足的情况
  - 建议值：3-10

## 📊 回测结果

### 统计信息
```
回测结果统计：

总投资次数：15
获胜次数：10
胜率：66.67%

总投资金额：15.00
总回报金额：18.50
净利润：3.50
投资回报率：23.33%
```

### 详细记录
每次投资都会记录：
- 比赛ID
- 实际结果
- 总主胜/总匹配次数
- 主胜赔率
- 投资价值
- 投资结果
- 收益情况

## ⚠️ 注意事项

### 使用前提
- **必须先完成ID区间分析**：回测基于区间分析的数据
- **数据完整性**：确保数据库包含比赛结果信息
- **参数合理性**：投资阈值和场次阈值需要合理设置

### 功能限制
- **单次投资**：每个满足条件的情况投资1单位
- **主胜投资**：目前只支持主胜投资策略
- **历史数据**：基于历史数据的回测，不代表未来表现

### 兼容性
- **完全向后兼容**：不影响任何现有功能
- **状态管理**：自动管理回测按钮的启用/禁用状态
- **数据隔离**：回测数据独立存储，不影响原有分析

## 🎯 实际应用

### 策略验证
```
示例场景：
- 投资阈值：1.10
- 场次阈值：5

结果：
- 找到8个满足条件的投资机会
- 6次命中，2次失败
- 胜率75%，投资回报率25%
```

### 参数优化
- **调整投资阈值**：观察不同阈值下的表现
- **调整场次阈值**：平衡数据可靠性和投资机会
- **历史验证**：基于历史数据验证策略有效性

## 🔄 与现有功能的关系

### 依赖关系
- **基于ID区间分析**：必须先完成区间分析
- **使用总统计数据**：利用新增的4列总统计信息
- **重新索引结果**：独立获取比赛结果，解决显示问题

### 功能增强
- **投资决策支持**：提供量化的投资策略验证
- **风险评估**：通过历史回测评估策略风险
- **参数优化**：帮助找到最优的投资参数

## 🎉 总结

回测功能为赔率组合观察工具增加了重要的投资策略验证能力：

✅ **策略验证**：基于历史数据验证投资策略
✅ **风险评估**：量化投资风险和收益预期
✅ **参数优化**：帮助找到最优投资参数
✅ **决策支持**：为实际投资提供数据支持
✅ **完全兼容**：不影响任何现有功能

这个功能让用户能够在实际投资前，通过历史数据验证策略的有效性，大大提高了投资决策的科学性和可靠性！

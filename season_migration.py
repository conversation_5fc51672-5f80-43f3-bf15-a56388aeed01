#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
赛季字段迁移脚本
为比赛列表添加赛季字段，并对已有数据进行一次性修正

功能：
1. 在matches表中添加season字段
2. 根据比赛ID规则更新已有数据的赛季信息
3. 从URL中提取赛季信息的工具函数

使用方法：
    python season_migration.py
"""

import sqlite3
import logging
import os
import re
from typing import Dict, Optional
from datetime import datetime

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class SeasonMigration:
    def __init__(self, db_path: str = "odds_data.db"):
        self.db_path = db_path
        
    def backup_database(self) -> str:
        """备份数据库"""
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_path = f"odds_data_backup_season_{timestamp}.db"
            
            with sqlite3.connect(self.db_path) as source:
                with sqlite3.connect(backup_path) as backup:
                    source.backup(backup)
            
            logger.info(f"数据库备份完成: {backup_path}")
            return backup_path
            
        except Exception as e:
            logger.error(f"数据库备份失败: {e}")
            raise
    
    def add_season_column(self) -> bool:
        """添加赛季字段到matches表"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # 检查season字段是否已存在
                cursor.execute("PRAGMA table_info(matches)")
                columns = [column[1] for column in cursor.fetchall()]
                
                if 'season' in columns:
                    logger.info("season字段已存在，跳过添加")
                    return True
                
                # 添加season字段
                cursor.execute("ALTER TABLE matches ADD COLUMN season TEXT")
                conn.commit()
                
                logger.info("成功添加season字段到matches表")
                return True
                
        except sqlite3.Error as e:
            logger.error(f"添加season字段失败: {e}")
            return False
    
    def update_existing_seasons(self) -> Dict:
        """根据ID规则更新已有数据的赛季信息"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # 获取所有没有赛季信息的比赛
                cursor.execute("""
                    SELECT match_id FROM matches 
                    WHERE season IS NULL OR season = ''
                """)
                matches = cursor.fetchall()
                
                updated_2025 = 0
                updated_2024 = 0
                failed_updates = 0
                
                for (match_id,) in matches:
                    try:
                        # 根据ID规则判断赛季
                        match_id_int = int(match_id)
                        if match_id_int > 2700000:
                            season = "2025"
                            updated_2025 += 1
                        else:
                            season = "2024"
                            updated_2024 += 1
                        
                        # 更新数据库
                        cursor.execute("""
                            UPDATE matches SET season = ? WHERE match_id = ?
                        """, (season, match_id))
                        
                    except (ValueError, TypeError):
                        logger.warning(f"无法解析比赛ID: {match_id}")
                        failed_updates += 1
                        continue
                
                conn.commit()
                
                result = {
                    'total_processed': len(matches),
                    'updated_2025': updated_2025,
                    'updated_2024': updated_2024,
                    'failed_updates': failed_updates,
                    'success': True
                }
                
                logger.info(f"赛季信息更新完成: {result}")
                return result
                
        except sqlite3.Error as e:
            logger.error(f"更新赛季信息失败: {e}")
            return {
                'total_processed': 0,
                'updated_2025': 0,
                'updated_2024': 0,
                'failed_updates': 0,
                'success': False,
                'error': str(e)
            }
    
    def extract_season_from_url(self, url: str) -> Optional[str]:
        """从URL中提取赛季信息"""
        try:
            # 处理类似 https://m.titan007.com/info/fixture/2025/21_165_4.htm 的URL
            pattern1 = r'/info/fixture/(\d{4})/\d+_\d+_\d+\.htm'
            match1 = re.search(pattern1, url)
            if match1:
                return match1.group(1)
            
            # 处理类似 https://m.titan007.com/info/Fixture/2024-2025/36_0_0.htm 的URL
            pattern2 = r'/info/Fixture/(\d{4}-\d{4})/\d+_\d+_\d+\.htm'
            match2 = re.search(pattern2, url)
            if match2:
                return match2.group(1)
            
            logger.warning(f"无法从URL中提取赛季信息: {url}")
            return None
            
        except Exception as e:
            logger.error(f"提取赛季信息失败: {e}")
            return None
    
    def verify_migration(self) -> Dict:
        """验证迁移结果"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # 检查season字段是否存在
                cursor.execute("PRAGMA table_info(matches)")
                columns = [column[1] for column in cursor.fetchall()]
                has_season_column = 'season' in columns
                
                # 统计赛季分布
                cursor.execute("SELECT season, COUNT(*) FROM matches GROUP BY season")
                season_distribution = dict(cursor.fetchall())
                
                # 统计没有赛季信息的比赛
                cursor.execute("SELECT COUNT(*) FROM matches WHERE season IS NULL OR season = ''")
                missing_season_count = cursor.fetchone()[0]
                
                # 总比赛数
                cursor.execute("SELECT COUNT(*) FROM matches")
                total_matches = cursor.fetchone()[0]
                
                result = {
                    'has_season_column': has_season_column,
                    'season_distribution': season_distribution,
                    'missing_season_count': missing_season_count,
                    'total_matches': total_matches,
                    'migration_complete': has_season_column and missing_season_count == 0
                }
                
                logger.info(f"迁移验证结果: {result}")
                return result
                
        except sqlite3.Error as e:
            logger.error(f"验证迁移失败: {e}")
            return {'error': str(e)}
    
    def run_migration(self) -> Dict:
        """执行完整的迁移流程"""
        try:
            logger.info("开始赛季字段迁移...")
            
            # 1. 备份数据库
            backup_path = self.backup_database()
            
            # 2. 添加season字段
            if not self.add_season_column():
                return {'success': False, 'error': '添加season字段失败'}
            
            # 3. 更新已有数据的赛季信息
            update_result = self.update_existing_seasons()
            if not update_result['success']:
                return {'success': False, 'error': '更新赛季信息失败', 'details': update_result}
            
            # 4. 验证迁移结果
            verification = self.verify_migration()
            
            result = {
                'success': True,
                'backup_path': backup_path,
                'update_result': update_result,
                'verification': verification
            }
            
            logger.info("赛季字段迁移完成！")
            return result
            
        except Exception as e:
            logger.error(f"迁移过程失败: {e}")
            return {'success': False, 'error': str(e)}

def main():
    """主函数"""
    # 检查数据库文件是否存在
    db_path = "odds_data.db"
    if not os.path.exists(db_path):
        logger.error(f"数据库文件不存在: {db_path}")
        return
    
    # 执行迁移
    migration = SeasonMigration(db_path)
    result = migration.run_migration()
    
    if result['success']:
        print("\n✅ 赛季字段迁移成功完成！")
        print(f"📁 备份文件: {result['backup_path']}")
        
        update_result = result['update_result']
        print(f"📊 更新统计:")
        print(f"   - 总处理数量: {update_result['total_processed']}")
        print(f"   - 2025赛季: {update_result['updated_2025']}")
        print(f"   - 2024赛季: {update_result['updated_2024']}")
        print(f"   - 失败数量: {update_result['failed_updates']}")
        
        verification = result['verification']
        print(f"🔍 验证结果:")
        print(f"   - 赛季字段已添加: {verification['has_season_column']}")
        print(f"   - 赛季分布: {verification['season_distribution']}")
        print(f"   - 缺失赛季信息: {verification['missing_season_count']}")
        print(f"   - 迁移完整: {verification['migration_complete']}")
        
    else:
        print(f"\n❌ 迁移失败: {result['error']}")

if __name__ == "__main__":
    main()

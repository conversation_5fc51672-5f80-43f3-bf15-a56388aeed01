#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试回测功能（带调试输出）
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from PyQt5.QtWidgets import QApplication
from odds_combination_ui import OddsCombinationMainWindow

def main():
    """启动UI进行回测调试测试"""
    print("🔍 启动回测功能调试测试")
    print("=" * 80)
    print("📝 测试步骤:")
    print("1. 选择数据库")
    print("2. 设置ID区间（包含2596914）")
    print("3. 选择博彩公司")
    print("4. 点击'ID区间数据分析'")
    print("5. 等待分析完成")
    print("6. 设置回测参数：")
    print("   - 投资阈值：1.05")
    print("   - 场次阈值：5")
    print("7. 点击'回测'")
    print("8. 查看控制台的DEBUG输出")
    print("")
    print("🎯 重点关注:")
    print("- 比赛2596914的详细信息")
    print("- 组合内容的实际格式")
    print("- 主胜赔率的提取结果")
    print("- 投资价值的计算")
    print("- 最终的净利润")
    print("")
    print("⚠️ 注意:")
    print("- 所有DEBUG信息会在控制台显示")
    print("- 如果净利润为负，检查赔率提取是否正确")
    print("- 如果没有投资记录，检查投资条件是否满足")
    print("")
    print("🚀 正在启动UI...")
    
    app = QApplication(sys.argv)
    
    # 创建主窗口
    window = OddsCombinationMainWindow()
    window.show()
    
    print("✅ UI已启动，请按照上述步骤进行测试")
    print("📊 DEBUG输出将在此控制台显示")
    
    # 运行应用
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()

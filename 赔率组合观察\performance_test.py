#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
性能测试 - 比较优化前后的分析速度
"""

import sys
import os
import time
import logging

# 添加父目录到Python路径
parent_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, parent_dir)

from odds_combination_database import OddsCombinationDatabase
from odds_combination_analyzer import OddsCombinationAnalyzer
from optimized_analyzer import OptimizedOddsCombinationAnalyzer

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_performance():
    """性能测试"""
    print("赔率组合分析器性能测试")
    print("=" * 60)
    
    # 测试参数
    test_match_id = "2617497"  # 比萨 vs 弗洛西诺尼，有108条bet365数据
    test_company = "bet365"
    test_combination_size = 3
    
    print(f"测试参数:")
    print(f"  比赛ID: {test_match_id}")
    print(f"  博彩公司: {test_company}")
    print(f"  组合大小: {test_combination_size}")
    print()
    
    try:
        # 初始化数据库
        db = OddsCombinationDatabase()
        
        # 检查数据
        print("数据检查:")
        target_odds = db.get_match_odds_by_company(test_match_id, test_company)
        print(f"  目标比赛赔率数据: {len(target_odds)} 条")
        
        other_matches = db.get_all_other_matches(test_match_id)
        print(f"  其他比赛数量: {len(other_matches)} 场")
        
        if len(target_odds) < test_combination_size:
            print(f"❌ 赔率数据不足，需要至少 {test_combination_size} 条")
            return
        
        if len(other_matches) == 0:
            print("❌ 没有其他比赛数据")
            return
        
        print()
        
        # 测试标准分析器
        print("🔄 测试标准分析器...")
        start_time = time.time()
        
        standard_analyzer = OddsCombinationAnalyzer()
        standard_results = standard_analyzer.analyze_odds_combinations(
            test_match_id, test_company, test_combination_size
        )
        
        standard_time = time.time() - start_time
        
        if standard_results['success']:
            print(f"✅ 标准分析器完成")
            print(f"   耗时: {standard_time:.2f} 秒")
            print(f"   组合数: {standard_results['total_combinations']}")
            print(f"   搜索比赛数: {standard_results['total_other_matches']}")
        else:
            print(f"❌ 标准分析器失败: {standard_results['error']}")
            return
        
        print()
        
        # 清除缓存以确保公平测试
        db.clear_cache()
        
        # 测试优化分析器
        print("🚀 测试优化分析器...")
        start_time = time.time()
        
        optimized_analyzer = OptimizedOddsCombinationAnalyzer(
            max_workers=4,
            batch_size=100
        )
        optimized_results = optimized_analyzer.analyze_odds_combinations_optimized(
            test_match_id, test_company, test_combination_size
        )
        
        optimized_time = time.time() - start_time
        
        if optimized_results['success']:
            print(f"✅ 优化分析器完成")
            print(f"   耗时: {optimized_time:.2f} 秒")
            print(f"   组合数: {optimized_results['total_combinations']}")
            print(f"   搜索比赛数: {optimized_results['total_other_matches']}")
        else:
            print(f"❌ 优化分析器失败: {optimized_results['error']}")
            return
        
        print()
        
        # 性能对比
        print("📊 性能对比:")
        print(f"   标准分析器: {standard_time:.2f} 秒")
        print(f"   优化分析器: {optimized_time:.2f} 秒")
        
        if optimized_time > 0:
            speedup = standard_time / optimized_time
            print(f"   性能提升: {speedup:.2f}x")
            
            if speedup > 1:
                print(f"   🎉 优化分析器快 {speedup:.1f} 倍！")
            else:
                print(f"   ⚠️  优化分析器较慢，可能需要调整参数")
        
        print()
        
        # 结果一致性检查
        print("🔍 结果一致性检查:")
        
        if (standard_results['total_combinations'] == optimized_results['total_combinations'] and
            standard_results['total_other_matches'] == optimized_results['total_other_matches']):
            print("✅ 基本统计一致")
        else:
            print("❌ 基本统计不一致")
            print(f"   标准: 组合{standard_results['total_combinations']}, 比赛{standard_results['total_other_matches']}")
            print(f"   优化: 组合{optimized_results['total_combinations']}, 比赛{optimized_results['total_other_matches']}")
        
        # 检查前几个组合的匹配数量
        standard_matches = [r['match_count'] for r in standard_results['results'][:5]]
        optimized_matches = [r['match_count'] for r in optimized_results['results'][:5]]
        
        if standard_matches == optimized_matches:
            print("✅ 前5个组合匹配数量一致")
        else:
            print("❌ 前5个组合匹配数量不一致")
            print(f"   标准: {standard_matches}")
            print(f"   优化: {optimized_matches}")
        
        print()
        
        # 内存使用建议
        print("💡 优化建议:")
        if len(other_matches) > 1000:
            print("   - 数据量较大，建议使用优化分析器")
            print("   - 可以适当增加batch_size以减少数据库查询次数")
            print("   - 可以适当增加max_workers以提高并发度")
        else:
            print("   - 数据量较小，两种分析器性能差异可能不明显")
        
        if optimized_time < 10:
            print("   - 分析速度已经很快，无需进一步优化")
        elif optimized_time < 60:
            print("   - 分析速度可接受，可考虑进一步优化")
        else:
            print("   - 分析速度较慢，建议检查数据库索引和系统性能")
        
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()

def test_different_configurations():
    """测试不同配置的性能"""
    print("\n" + "=" * 60)
    print("不同配置性能测试")
    print("=" * 60)
    
    test_match_id = "2617497"  # 比萨 vs 弗洛西诺尼，有108条bet365数据
    test_company = "bet365"
    test_combination_size = 3
    
    configurations = [
        {"max_workers": 1, "batch_size": 50, "name": "单线程小批量"},
        {"max_workers": 2, "batch_size": 100, "name": "双线程中批量"},
        {"max_workers": 4, "batch_size": 100, "name": "四线程中批量"},
        {"max_workers": 4, "batch_size": 200, "name": "四线程大批量"},
        {"max_workers": 8, "batch_size": 100, "name": "八线程中批量"},
    ]
    
    results = []
    
    for config in configurations:
        print(f"\n🔄 测试配置: {config['name']}")
        print(f"   线程数: {config['max_workers']}, 批量大小: {config['batch_size']}")
        
        try:
            # 清除缓存
            db = OddsCombinationDatabase()
            db.clear_cache()
            
            start_time = time.time()
            
            analyzer = OptimizedOddsCombinationAnalyzer(
                max_workers=config['max_workers'],
                batch_size=config['batch_size']
            )
            
            result = analyzer.analyze_odds_combinations_optimized(
                test_match_id, test_company, test_combination_size
            )
            
            elapsed_time = time.time() - start_time
            
            if result['success']:
                print(f"   ✅ 完成，耗时: {elapsed_time:.2f} 秒")
                results.append({
                    'config': config['name'],
                    'time': elapsed_time,
                    'max_workers': config['max_workers'],
                    'batch_size': config['batch_size']
                })
            else:
                print(f"   ❌ 失败: {result['error']}")
                
        except Exception as e:
            print(f"   ❌ 错误: {e}")
    
    # 显示最佳配置
    if results:
        print(f"\n📊 配置性能排名:")
        sorted_results = sorted(results, key=lambda x: x['time'])
        
        for i, result in enumerate(sorted_results):
            print(f"   {i+1}. {result['config']}: {result['time']:.2f}秒 "
                  f"(线程{result['max_workers']}, 批量{result['batch_size']})")
        
        best_config = sorted_results[0]
        print(f"\n🏆 最佳配置: {best_config['config']} ({best_config['time']:.2f}秒)")

def main():
    """主函数"""
    print("赔率组合分析器性能测试工具")
    print("=" * 60)
    
    # 基本性能测试
    test_performance()
    
    # 不同配置测试
    test_different_configurations()
    
    print(f"\n" + "=" * 60)
    print("测试完成！")

if __name__ == '__main__':
    main()

# 赔率组合观察分析工具 - 使用指南

## 快速开始

### 1. 启动程序

**方法一：双击批处理文件**
```
双击 start_ui.bat 文件
```

**方法二：命令行启动**
```bash
python start_odds_combination_analyzer.py
```

### 2. 界面操作

1. **选择数据库**：在顶部下拉框中选择要使用的数据库
2. **输入比赛ID**：在文本框中输入要观察的比赛ID
3. **选择博彩公司**：从下拉框中选择博彩公司
4. **设置组合数**：使用数字选择器设置组合大小
5. **开始分析**：点击"开始分析"按钮
6. **查看结果**：在两个表格中查看分析结果

## 示例操作

### 使用示例数据
- **比赛ID**: 2701757
- **博彩公司**: bet365  
- **组合数**: 3

### 预期结果
- 该比赛有15条bet365赔率数据
- 将生成13个3组连续赔率组合
- 在数据库的其他5000+比赛中查找匹配
- 显示每个组合的匹配次数和胜平负分布

## 结果解读

### 组合统计表（上方表格）
| 列名 | 说明 |
|------|------|
| 组合序号 | 组合的编号（1开始） |
| 组合内容 | 显示组合中每个赔率 |
| 匹配次数 | 在其他比赛中的匹配数量 |
| 主胜 | 匹配比赛中主队获胜的数量 |
| 平局 | 匹配比赛中平局的数量 |
| 客胜 | 匹配比赛中客队获胜的数量 |

### 详细匹配表（下方表格）
点击组合统计表中的任意行，下方表格会显示该组合的详细匹配信息：

| 列名 | 说明 |
|------|------|
| 比赛ID | 匹配的比赛ID |
| 比赛结果 | 比赛结果类型（主胜/平局/客胜） |
| 匹配位置 | 在该比赛赔率序列中的位置 |
| 比分 | 比赛的具体比分 |

## 功能测试

### 快速测试
```bash
python fast_test.py
```
测试核心功能是否正常。

### 演示分析
```bash
python demo_analysis.py
```
查看完整的分析过程演示。

### 完整测试
```bash
python test_functionality.py
```
执行所有功能的完整测试。

## 注意事项

1. **数据要求**：确保比赛ID在数据库中存在
2. **赔率数据**：选择的博彩公司必须有该比赛的赔率数据
3. **数据量**：赔率数据数量必须≥组合数
4. **分析时间**：大数据库分析可能需要1-5分钟
5. **界面响应**：分析过程中界面可能暂时无响应，请耐心等待

## 常见问题

**Q: 分析按钮点击后没有反应？**
A: 检查输入的比赛ID是否存在，博彩公司是否有数据。

**Q: 分析时间很长？**
A: 这是正常的，数据库越大分析时间越长，请耐心等待。

**Q: 没有找到匹配？**
A: 这说明该赔率组合在其他比赛中没有出现过，这是正常现象。

**Q: 界面卡死？**
A: 分析过程中界面可能暂时无响应，这是正常的，请等待分析完成。

## 技术支持

如果遇到问题，请：
1. 查看状态显示区域的错误信息
2. 运行测试脚本检查功能
3. 检查数据库文件是否完整
4. 确认PyQt5已正确安装

## 更新日志

**v1.0 (2025-07-16)**
- 初始版本发布
- 实现所有核心功能
- 提供完整的UI界面
- 包含测试和演示脚本

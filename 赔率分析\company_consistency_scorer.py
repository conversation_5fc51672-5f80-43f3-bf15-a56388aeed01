#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
公司一致性评分系统
构建方向一致性、幅度一致性、时间一致性的综合评分体系
"""

import pandas as pd
import numpy as np
import json
import os
from datetime import datetime
import matplotlib.pyplot as plt
import seaborn as sns
from collections import defaultdict, Counter
from itertools import combinations
from scipy.stats import pearsonr
import warnings
warnings.filterwarnings('ignore')

class CompanyConsistencyScorer:
    def __init__(self, normalized_data_file=None):
        self.normalized_data_file = normalized_data_file
        self.normalized_data = None
        self.consistency_results = {}
        
        # 评分参数
        self.min_change_threshold = 0.01  # 最小变化阈值（1%）
        self.time_window = 1.0           # 时间窗口（小时）
        
    def load_normalized_data(self, filename=None):
        """加载标准化的赔率数据"""
        if filename:
            self.normalized_data_file = filename
        
        if not self.normalized_data_file:
            # 查找最新的标准化数据文件
            files = [f for f in os.listdir('.') if f.startswith('normalized_odds_') and f.endswith('.json')]
            if files:
                self.normalized_data_file = sorted(files)[-1]
                print(f"自动选择最新文件: {self.normalized_data_file}")
            else:
                print("未找到标准化数据文件")
                return None
        
        with open(self.normalized_data_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        self.normalized_data = data
        print(f"加载标准化数据: {self.normalized_data_file}")
        return data
    
    def calculate_direction_consistency(self, match_data):
        """计算方向一致性"""
        companies_data = match_data['companies_data']
        
        if len(companies_data) < 2:
            return {}
        
        direction_consistency = {}
        
        for odds_type in ['home', 'draw', 'away']:
            odds_key = f'{odds_type}_odds'
            
            # 收集所有公司的变化方向
            company_directions = {}
            
            for company, company_data in companies_data.items():
                if odds_key not in company_data:
                    continue
                
                odds_series = company_data[odds_key]
                
                # 计算整体方向
                if len(odds_series) >= 2:
                    total_change = odds_series[-1] - odds_series[0]
                    if abs(total_change / odds_series[0]) >= self.min_change_threshold:
                        company_directions[company] = 1 if total_change > 0 else -1
                    else:
                        company_directions[company] = 0  # 无明显变化
            
            if len(company_directions) >= 2:
                # 计算方向一致性
                directions = list(company_directions.values())
                
                # 统计各方向的公司数
                up_count = sum(1 for d in directions if d == 1)
                down_count = sum(1 for d in directions if d == -1)
                stable_count = sum(1 for d in directions if d == 0)
                
                total_companies = len(directions)
                
                # 一致性 = 最大方向占比
                max_direction_count = max(up_count, down_count, stable_count)
                direction_consistency[odds_type] = {
                    'consistency_score': max_direction_count / total_companies,
                    'up_count': up_count,
                    'down_count': down_count,
                    'stable_count': stable_count,
                    'total_companies': total_companies,
                    'dominant_direction': 'up' if up_count == max_direction_count else 
                                        'down' if down_count == max_direction_count else 'stable'
                }
        
        return direction_consistency
    
    def calculate_magnitude_consistency(self, match_data):
        """计算幅度一致性"""
        companies_data = match_data['companies_data']
        
        if len(companies_data) < 2:
            return {}
        
        magnitude_consistency = {}
        
        for odds_type in ['home', 'draw', 'away']:
            odds_key = f'{odds_type}_odds'
            
            # 收集所有公司的变化幅度
            company_magnitudes = {}
            
            for company, company_data in companies_data.items():
                if odds_key not in company_data:
                    continue
                
                odds_series = company_data[odds_key]
                
                if len(odds_series) >= 2:
                    # 计算总变化幅度（百分比）
                    total_change_pct = abs((odds_series[-1] - odds_series[0]) / odds_series[0] * 100)
                    company_magnitudes[company] = total_change_pct
            
            if len(company_magnitudes) >= 2:
                magnitudes = list(company_magnitudes.values())
                
                # 计算变异系数（标准差/均值）
                mean_magnitude = np.mean(magnitudes)
                std_magnitude = np.std(magnitudes)
                cv = std_magnitude / mean_magnitude if mean_magnitude > 0 else 0
                
                # 一致性 = 1 - 变异系数（变异系数越小，一致性越高）
                consistency_score = max(0, 1 - cv)
                
                magnitude_consistency[odds_type] = {
                    'consistency_score': consistency_score,
                    'mean_magnitude': mean_magnitude,
                    'std_magnitude': std_magnitude,
                    'cv': cv,
                    'companies_count': len(company_magnitudes)
                }
        
        return magnitude_consistency
    
    def calculate_timing_consistency(self, match_data):
        """计算时间一致性"""
        companies_data = match_data['companies_data']
        
        if len(companies_data) < 2:
            return {}
        
        timing_consistency = {}
        
        for odds_type in ['home', 'draw', 'away']:
            odds_key = f'{odds_type}_odds'
            
            # 收集所有公司的主要变化时间点
            company_change_times = {}
            
            for company, company_data in companies_data.items():
                if odds_key not in company_data:
                    continue
                
                time_points = company_data['time_points']
                odds_series = company_data[odds_key]
                
                # 找到最大变化的时间点
                max_change = 0
                max_change_time = None
                
                for i in range(1, len(odds_series)):
                    change_pct = abs((odds_series[i] - odds_series[i-1]) / odds_series[i-1] * 100)
                    if change_pct > max_change:
                        max_change = change_pct
                        max_change_time = time_points[i]
                
                if max_change >= self.min_change_threshold * 100 and max_change_time is not None:
                    company_change_times[company] = max_change_time
            
            if len(company_change_times) >= 2:
                change_times = list(company_change_times.values())
                
                # 计算时间分散度
                time_std = np.std(change_times)
                time_range = max(change_times) - min(change_times)
                
                # 一致性 = 1 / (1 + 时间标准差)
                consistency_score = 1 / (1 + time_std)
                
                timing_consistency[odds_type] = {
                    'consistency_score': consistency_score,
                    'time_std': time_std,
                    'time_range': time_range,
                    'mean_time': np.mean(change_times),
                    'companies_count': len(company_change_times)
                }
        
        return timing_consistency
    
    def calculate_correlation_consistency(self, match_data):
        """计算相关性一致性（公司间赔率变化的相关性）"""
        companies_data = match_data['companies_data']
        
        if len(companies_data) < 2:
            return {}
        
        correlation_consistency = {}
        
        for odds_type in ['home', 'draw', 'away']:
            odds_key = f'{odds_type}_odds'
            
            # 收集所有公司的赔率序列
            company_series = {}
            
            for company, company_data in companies_data.items():
                if odds_key in company_data:
                    company_series[company] = company_data[odds_key]
            
            if len(company_series) >= 2:
                companies = list(company_series.keys())
                correlations = []
                
                # 计算所有公司对之间的相关性
                for i, j in combinations(range(len(companies)), 2):
                    company1, company2 = companies[i], companies[j]
                    series1 = company_series[company1]
                    series2 = company_series[company2]
                    
                    # 确保序列长度相同
                    min_len = min(len(series1), len(series2))
                    if min_len >= 3:  # 至少需要3个点计算相关性
                        try:
                            corr, _ = pearsonr(series1[:min_len], series2[:min_len])
                            if not np.isnan(corr):
                                correlations.append(abs(corr))  # 使用绝对值
                        except:
                            pass
                
                if correlations:
                    avg_correlation = np.mean(correlations)
                    correlation_consistency[odds_type] = {
                        'consistency_score': avg_correlation,
                        'avg_correlation': avg_correlation,
                        'correlations_count': len(correlations),
                        'companies_count': len(company_series)
                    }
        
        return correlation_consistency
    
    def calculate_comprehensive_consistency(self, match_data):
        """计算综合一致性评分"""
        direction_cons = self.calculate_direction_consistency(match_data)
        magnitude_cons = self.calculate_magnitude_consistency(match_data)
        timing_cons = self.calculate_timing_consistency(match_data)
        correlation_cons = self.calculate_correlation_consistency(match_data)
        
        comprehensive_scores = {}
        
        for odds_type in ['home', 'draw', 'away']:
            scores = []
            weights = []
            
            # 方向一致性（权重：0.3）
            if odds_type in direction_cons:
                scores.append(direction_cons[odds_type]['consistency_score'])
                weights.append(0.3)
            
            # 幅度一致性（权重：0.25）
            if odds_type in magnitude_cons:
                scores.append(magnitude_cons[odds_type]['consistency_score'])
                weights.append(0.25)
            
            # 时间一致性（权重：0.2）
            if odds_type in timing_cons:
                scores.append(timing_cons[odds_type]['consistency_score'])
                weights.append(0.2)
            
            # 相关性一致性（权重：0.25）
            if odds_type in correlation_cons:
                scores.append(correlation_cons[odds_type]['consistency_score'])
                weights.append(0.25)
            
            if scores:
                # 加权平均
                weighted_score = sum(s * w for s, w in zip(scores, weights)) / sum(weights)
                comprehensive_scores[odds_type] = {
                    'comprehensive_score': weighted_score,
                    'component_scores': {
                        'direction': direction_cons.get(odds_type, {}).get('consistency_score', 0),
                        'magnitude': magnitude_cons.get(odds_type, {}).get('consistency_score', 0),
                        'timing': timing_cons.get(odds_type, {}).get('consistency_score', 0),
                        'correlation': correlation_cons.get(odds_type, {}).get('consistency_score', 0)
                    }
                }
        
        return {
            'direction_consistency': direction_cons,
            'magnitude_consistency': magnitude_cons,
            'timing_consistency': timing_cons,
            'correlation_consistency': correlation_cons,
            'comprehensive_consistency': comprehensive_scores
        }
    
    def analyze_all_matches(self):
        """分析所有比赛的一致性"""
        if self.normalized_data is None:
            print("请先加载标准化数据")
            return None
        
        print("\n=== 开始公司一致性评分分析 ===")
        
        all_consistency_results = {}
        
        for db_name, db_data in self.normalized_data.items():
            print(f"\n处理数据库: {db_name}")
            db_consistency_results = {}
            
            for match_id, match_data in db_data.items():
                print(f"  分析比赛: {match_id}")
                
                consistency_result = self.calculate_comprehensive_consistency(match_data)
                consistency_result['match_id'] = match_id
                consistency_result['companies_count'] = len(match_data['companies_data'])
                
                db_consistency_results[match_id] = consistency_result
                
                # 输出综合得分
                comp_scores = consistency_result.get('comprehensive_consistency', {})
                if comp_scores:
                    avg_score = np.mean([score['comprehensive_score'] for score in comp_scores.values()])
                    print(f"    综合一致性得分: {avg_score:.3f}")
            
            all_consistency_results[db_name] = db_consistency_results
        
        self.consistency_results = all_consistency_results
        return all_consistency_results
    
    def generate_consistency_summary(self):
        """生成一致性总结报告"""
        if not self.consistency_results:
            print("请先进行一致性分析")
            return None
        
        print("\n=== 一致性分析总结 ===")
        
        # 收集所有得分
        all_scores = {
            'direction': [],
            'magnitude': [],
            'timing': [],
            'correlation': [],
            'comprehensive': []
        }
        
        match_scores = []
        
        for db_name, db_results in self.consistency_results.items():
            for match_id, result in db_results.items():
                comp_consistency = result.get('comprehensive_consistency', {})
                
                match_comprehensive_scores = []
                
                for odds_type, scores in comp_consistency.items():
                    comp_score = scores['comprehensive_score']
                    component_scores = scores['component_scores']
                    
                    match_comprehensive_scores.append(comp_score)
                    all_scores['direction'].append(component_scores['direction'])
                    all_scores['magnitude'].append(component_scores['magnitude'])
                    all_scores['timing'].append(component_scores['timing'])
                    all_scores['correlation'].append(component_scores['correlation'])
                    all_scores['comprehensive'].append(comp_score)
                
                if match_comprehensive_scores:
                    match_avg_score = np.mean(match_comprehensive_scores)
                    match_scores.append({
                        'match_id': match_id,
                        'db_name': db_name,
                        'avg_score': match_avg_score,
                        'companies_count': result['companies_count']
                    })
        
        # 统计分析
        print(f"各维度一致性得分统计:")
        for dimension, scores in all_scores.items():
            if scores:
                print(f"  {dimension}: 均值{np.mean(scores):.3f}, "
                      f"标准差{np.std(scores):.3f}, "
                      f"范围[{np.min(scores):.3f}, {np.max(scores):.3f}]")
        
        # 比赛排名
        print(f"\n比赛一致性排名（前5名）:")
        sorted_matches = sorted(match_scores, key=lambda x: x['avg_score'], reverse=True)
        for i, match in enumerate(sorted_matches[:5], 1):
            print(f"  {i}. 比赛{match['match_id']}: {match['avg_score']:.3f} "
                  f"({match['companies_count']}家公司)")
        
        # 一致性等级分布
        high_consistency = sum(1 for m in match_scores if m['avg_score'] >= 0.8)
        medium_consistency = sum(1 for m in match_scores if 0.6 <= m['avg_score'] < 0.8)
        low_consistency = sum(1 for m in match_scores if m['avg_score'] < 0.6)
        
        total_matches = len(match_scores)
        print(f"\n一致性等级分布:")
        print(f"  高一致性(≥0.8): {high_consistency}场 ({high_consistency/total_matches*100:.1f}%)")
        print(f"  中等一致性(0.6-0.8): {medium_consistency}场 ({medium_consistency/total_matches*100:.1f}%)")
        print(f"  低一致性(<0.6): {low_consistency}场 ({low_consistency/total_matches*100:.1f}%)")
        
        return {
            'dimension_stats': all_scores,
            'match_scores': match_scores,
            'consistency_distribution': {
                'high': high_consistency,
                'medium': medium_consistency,
                'low': low_consistency
            }
        }
    
    def save_consistency_results(self, filename_prefix="consistency_analysis"):
        """保存一致性分析结果"""
        if not self.consistency_results:
            print("没有一致性分析结果可保存")
            return None
        
        output_file = f"{filename_prefix}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        
        # 转换为可序列化的格式
        serializable_results = self._make_serializable(self.consistency_results)
        
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(serializable_results, f, ensure_ascii=False, indent=2)
        
        print(f"\n一致性分析结果已保存到: {output_file}")
        return output_file
    
    def _make_serializable(self, obj):
        """将对象转换为可序列化的格式"""
        if isinstance(obj, dict):
            return {k: self._make_serializable(v) for k, v in obj.items()}
        elif isinstance(obj, (list, tuple)):
            return [self._make_serializable(item) for item in obj]
        elif isinstance(obj, (np.integer, np.int64)):
            return int(obj)
        elif isinstance(obj, (np.floating, np.float64)):
            return float(obj)
        elif isinstance(obj, (np.bool_, bool)):
            return bool(obj)
        elif isinstance(obj, np.ndarray):
            return obj.tolist()
        elif pd.isna(obj):
            return None
        else:
            return obj

def main():
    scorer = CompanyConsistencyScorer()
    
    # 加载标准化数据
    normalized_data = scorer.load_normalized_data()
    if not normalized_data:
        return
    
    # 分析一致性
    consistency_results = scorer.analyze_all_matches()
    
    # 生成总结报告
    summary = scorer.generate_consistency_summary()
    
    # 保存结果
    scorer.save_consistency_results()

if __name__ == "__main__":
    main()

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试比赛信息提取修复
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from enhanced_odds_scraper import EnhancedOddsScraper
import logging

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_match_info_fix():
    """测试比赛信息提取修复"""
    print("🚀 测试比赛信息提取修复")
    print("=" * 60)
    
    scraper = EnhancedOddsScraper()
    match_id = "2213559"
    
    print(f"\n🎯 测试比赛: {match_id}")
    print("阿斯顿维拉 vs 埃弗顿 (应该是2022-08-13)")
    print("-" * 40)
    
    # 测试比赛信息提取
    print("\n📋 测试比赛信息提取")
    print("-" * 40)
    
    try:
        match_info = scraper.extract_match_info(match_id)
        
        if match_info:
            print("✅ 比赛信息提取成功")
            print(f"  比赛时间: {match_info.get('match_time', 'N/A')}")
            print(f"  比赛日期: {match_info.get('match_date', 'N/A')}")
            print(f"  推测年份: {match_info.get('inferred_year', 'N/A')}")
            print(f"  原始日期: {match_info.get('raw_date', 'N/A')}")
            print(f"  原始时间: {match_info.get('raw_time', 'N/A')}")
            print(f"  主队: {match_info.get('home_team', 'N/A')}")
            print(f"  客队: {match_info.get('away_team', 'N/A')}")
            print(f"  比分: {match_info.get('score', 'N/A')}")
            print(f"  联赛: {match_info.get('league', 'N/A')}")
            
            # 检查年份是否正确
            if match_info.get('match_time', '').startswith('2022'):
                print("  ✅ 年份正确：2022年")
            elif match_info.get('match_time', '').startswith('2025'):
                print("  ❌ 年份错误：仍然是2025年")
            else:
                year = match_info.get('match_time', '')[:4] if match_info.get('match_time') else 'Unknown'
                print(f"  ⚠️ 年份: {year}")
        else:
            print("❌ 比赛信息提取失败：返回None")
            
    except Exception as e:
        print(f"❌ 比赛信息提取失败：{e}")
    
    # 测试年份推测功能
    print(f"\n📋 测试年份推测功能")
    print("-" * 40)
    
    test_cases = [
        ("08-13", "2213559", "应该是2022年"),
        ("12-25", "3000000", "应该是2024年"),
        ("01-15", "2500000", "应该是2023年"),
        ("06-20", "2213559", "应该是2022年"),  # 测试06月份是否也会推测为2022年
    ]
    
    for date_str, test_match_id, expected in test_cases:
        try:
            inferred_year = scraper._infer_match_year(date_str, test_match_id)
            print(f"  日期: {date_str}, 比赛ID: {test_match_id}")
            print(f"    推测年份: {inferred_year} ({expected})")
        except Exception as e:
            print(f"  日期: {date_str}, 比赛ID: {test_match_id}")
            print(f"    推测失败: {e}")
    
    # 测试完整数据抓取（只抓取前3家公司以节省时间）
    print(f"\n📋 测试完整数据抓取（前3家公司）")
    print("-" * 40)
    
    try:
        complete_data = scraper.scrape_complete_match_data(match_id, max_companies=3)
        
        if complete_data:
            print("✅ 完整数据抓取成功")
            
            # 检查比赛信息
            match_info = complete_data.get('match_info', {})
            if match_info and match_info.get('match_time'):
                print(f"  📊 比赛信息: ✅")
                print(f"    时间: {match_info.get('match_time', 'N/A')}")
                print(f"    主队: {match_info.get('home_team', 'N/A')}")
                print(f"    客队: {match_info.get('away_team', 'N/A')}")
                
                # 检查年份
                if match_info.get('match_time', '').startswith('2022'):
                    print(f"    ✅ 年份正确：2022年")
                else:
                    year = match_info.get('match_time', '')[:4] if match_info.get('match_time') else 'Unknown'
                    print(f"    ⚠️ 年份: {year}")
            else:
                print(f"  📊 比赛信息: ❌ 无数据或时间为空")
            
            # 检查赔率数据
            odds_data = complete_data.get('odds_data', [])
            if odds_data:
                print(f"  💰 赔率数据: ✅ {len(odds_data)} 条记录")
                
                # 检查时间数据
                time_samples = []
                for record in odds_data[:5]:
                    change_time = record.get('change_time', '')
                    if change_time and change_time not in time_samples:
                        time_samples.append(change_time)
                
                if time_samples:
                    print(f"    时间样本: {time_samples}")
                    # 检查是否包含08月份的数据
                    august_records = [r for r in odds_data if '08-' in r.get('change_time', '')]
                    if august_records:
                        print(f"    ✅ 包含 {len(august_records)} 条08月份记录")
                    else:
                        print(f"    ❌ 未找到08月份记录")
                else:
                    print(f"    ⚠️ 无时间数据")
            else:
                print(f"  💰 赔率数据: ❌ 无数据")
                
        else:
            print("❌ 完整数据抓取失败")
            
    except Exception as e:
        print(f"❌ 完整数据抓取失败：{e}")
    
    print("\n" + "=" * 60)
    print("📋 测试总结")
    print("=" * 60)
    print(f"🎉 比赛信息提取修复测试完成！")

if __name__ == "__main__":
    test_match_info_fix()

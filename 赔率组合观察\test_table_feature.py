#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试表格显示功能
"""

import sys
import os
import logging

# 添加当前目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

from odds_combination_database import OddsCombinationDatabase

def test_table_feature():
    """测试表格显示功能"""
    print("🔍 测试表格显示功能")
    
    try:
        # 初始化数据库（使用父目录的数据库）
        parent_dir = os.path.dirname(current_dir)
        db_path = os.path.join(parent_dir, "odds_data.db")
        db = OddsCombinationDatabase(db_path)
        print(f"✅ 数据库连接成功: {db.db_path}")
        
        # 测试一个较大的时间范围
        days = 1095  # 3年
        print(f"\n📅 测试最近 {days} 天的比赛:")
        
        recent_matches = db.get_recent_matches(days)
        print(f"  找到 {len(recent_matches)} 场比赛")
        
        if len(recent_matches) > 0:
            print(f"\n📋 表格显示格式预览（前10场）:")
            print(f"{'比赛ID':<10} {'开赛时间':<20} {'联赛':<15} {'主队':<15} {'客队':<15} {'比赛结果':<20}")
            print("-" * 100)
            
            for i, match in enumerate(recent_matches[:10]):
                match_id = match['match_id']
                home_team = match.get('home_team', '未知')[:12]  # 限制长度
                away_team = match.get('away_team', '未知')[:12]
                league = match.get('league', '未知联赛')[:12]
                display_time = match.get('display_time', '未知时间')[:18]
                
                # 模拟获取比赛结果的逻辑
                home_score = match.get('home_score')
                away_score = match.get('away_score')
                match_state = match.get('match_state')
                
                if home_score is not None and away_score is not None:
                    try:
                        home_score_int = int(home_score)
                        away_score_int = int(away_score)
                        
                        if home_score_int > away_score_int:
                            result_text = "主胜"
                        elif home_score_int < away_score_int:
                            result_text = "客胜"
                        else:
                            result_text = "平局"
                        
                        match_result = f"{home_score}:{away_score} ({result_text})"
                        
                    except (ValueError, TypeError):
                        match_result = f"{home_score}:{away_score}"
                else:
                    match_result = match_state or "未知"
                
                print(f"{match_id:<10} {display_time:<20} {league:<15} {home_team:<15} {away_team:<15} {match_result:<20}")
        
        print(f"\n✅ 测试完成")
        print(f"💡 新的表格显示功能已实现，包含比赛结果列")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    # 配置日志
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    test_table_feature()

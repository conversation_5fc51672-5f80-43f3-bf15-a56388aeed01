#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单测试博彩态度2功能
"""

from betting_attitude_2_filter import BettingAttitude2Filter

def test_betting_attitude2():
    """测试博彩态度2功能"""
    print("🎯 测试博彩态度2功能")
    print("=" * 60)
    
    # 创建筛选器
    filter_obj = BettingAttitude2Filter()
    
    # 模拟赔率数据（基于实际抓取的数据）
    odds_data = [
        {'company_name': 'bet365', 'home_odds': 6.50, 'draw_odds': 5.00, 'away_odds': 1.30},
        {'company_name': '立博', 'home_odds': 7.00, 'draw_odds': 5.75, 'away_odds': 1.25},
        {'company_name': '易胜博', 'home_odds': 7.00, 'draw_odds': 5.00, 'away_odds': 1.29},
        {'company_name': 'bwin', 'home_odds': 7.00, 'draw_odds': 6.00, 'away_odds': 1.26},
        {'company_name': '利记', 'home_odds': 6.30, 'draw_odds': 5.60, 'away_odds': 1.27},
        {'company_name': '明升', 'home_odds': 7.60, 'draw_odds': 5.70, 'away_odds': 1.26},
    ]
    
    print("📊 测试数据:")
    for data in odds_data:
        print(f"  {data['company_name']}: {data['home_odds']:.2f} {data['draw_odds']:.2f} {data['away_odds']:.2f}")
    
    # 测试不同的目标公司和阈值
    test_cases = [
        ('bet365', 'home', 2),  # bet365主胜，阈值2
        ('利记', 'home', 2),    # 利记主胜，阈值2
        ('明升', 'home', 1),    # 明升主胜，阈值1
        ('立博', 'home', 3),    # 立博主胜，阈值3
    ]
    
    for target_company, bet_type, threshold in test_cases:
        print(f"\n{'='*40}")
        print(f"测试: {target_company} {bet_type} 阈值{threshold}")
        print(f"{'='*40}")
        
        # 执行筛选
        result = filter_obj.filter_matches([{
            'match_id': 'test',
            'odds_data': odds_data
        }], target_company, bet_type, threshold)
        
        if result:
            print(f"✅ 通过筛选")
        else:
            print(f"❌ 未通过筛选")
            
        # 手动验证逻辑
        target_odds = None
        for data in odds_data:
            if data['company_name'] == target_company:
                target_odds = data[f'{bet_type}_odds']
                break
        
        if target_odds:
            higher_count = 0
            for data in odds_data:
                if data['company_name'] != target_company:
                    other_odds = data[f'{bet_type}_odds']
                    if other_odds > target_odds:
                        higher_count += 1
                        print(f"  {data['company_name']}: {other_odds:.2f} > {target_odds:.2f} ✓")
                    else:
                        print(f"  {data['company_name']}: {other_odds:.2f} ≤ {target_odds:.2f}")
            
            print(f"📊 {target_company}赔率: {target_odds:.2f}")
            print(f"📊 高于目标的公司数: {higher_count}")
            print(f"📊 阈值: {threshold}")
            print(f"📊 条件: {higher_count} < {threshold} = {higher_count < threshold}")

if __name__ == "__main__":
    test_betting_attitude2()

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查页面内容
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from enhanced_odds_scraper import EnhancedOddsScraper
import logging

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def check_page_content():
    """检查页面内容"""
    print("🔍 检查页面内容 - 比赛2399071")
    print("=" * 60)
    
    scraper = EnhancedOddsScraper()
    match_id = "2399071"
    
    url = f"https://op1.titan007.com/oddslist/{match_id}.htm"
    print(f"📋 检查URL: {url}")
    
    content = scraper.get_page_content(url)
    
    if not content:
        print(f"❌ 无法获取页面内容")
        return
        
    print(f"✅ 页面可访问，内容长度: {len(content)}")
    
    # 保存页面内容到文件
    with open(f"page_content_{match_id}.html", "w", encoding="utf-8") as f:
        f.write(content)
    print(f"📄 页面内容已保存到: page_content_{match_id}.html")
    
    # 检查关键词
    keywords = [
        'OddsHistory.aspx',
        'bet365',
        'cid=281',
        'CompensateDetail',
        'odds',
        'history',
        '赔率',
        '历史'
    ]
    
    print(f"\n🔍 关键词检查:")
    for keyword in keywords:
        count = content.count(keyword)
        print(f"  {keyword}: {count} 次")
    
    # 检查是否包含JavaScript重定向或其他内容
    if 'javascript' in content.lower():
        print(f"\n⚠️ 页面包含JavaScript")
        
    if 'redirect' in content.lower():
        print(f"⚠️ 页面包含重定向")
        
    if 'error' in content.lower():
        print(f"⚠️ 页面包含错误信息")
        
    # 显示页面的前1000个字符
    print(f"\n📄 页面前1000个字符:")
    print("-" * 40)
    print(content[:1000])
    print("-" * 40)
    
    # 显示页面的最后500个字符
    print(f"\n📄 页面最后500个字符:")
    print("-" * 40)
    print(content[-500:])
    print("-" * 40)

if __name__ == "__main__":
    check_page_content()

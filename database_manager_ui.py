#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据库管理UI界面
提供图形化的数据库管理功能
"""

import sys
import os
from datetime import datetime, timedelta
from PyQt5.QtWidgets import (QApplication, QMainWindow, QVBoxLayout, QHBoxLayout, 
                             QWidget, QPushButton, QLabel, QTextEdit, QSpinBox,
                             QProgressBar, QGroupBox, QGridLayout, QMessageBox,
                             QFileDialog, QTabWidget, QTableWidget, QTableWidgetItem,
                             QHeaderView, QComboBox)
from PyQt5.QtCore import QThread, pyqtSignal, QTimer
from PyQt5.QtGui import QFont
from database_manager import DatabaseManager
from database import OddsDatabase

class DatabaseWorker(QThread):
    """数据库操作工作线程"""
    finished = pyqtSignal(dict)
    progress = pyqtSignal(str)
    
    def __init__(self, operation, *args):
        super().__init__()
        self.operation = operation
        self.args = args
        self.manager = DatabaseManager()
    
    def run(self):
        try:
            if self.operation == "analyze":
                self.progress.emit("正在分析数据库...")
                result = self.manager.get_size_analysis()
            elif self.operation == "cleanup":
                days = self.args[0]
                self.progress.emit(f"正在清理{days}天前的数据...")
                result = self.manager.db.cleanup_old_data(days)
            elif self.operation == "optimize":
                self.progress.emit("正在优化数据库...")
                result = self.manager.db.optimize_database()
            elif self.operation == "backup":
                backup_path = self.args[0] if self.args else None
                self.progress.emit("正在备份数据库...")
                result = self.manager.db.backup_database(backup_path)
            elif self.operation == "auto_cleanup":
                max_size, days = self.args
                self.progress.emit("正在执行自动清理...")
                result = self.manager.auto_cleanup(max_size, days)
            else:
                result = {"error": "未知操作"}
            
            self.finished.emit(result)
        except Exception as e:
            self.finished.emit({"error": str(e)})

class DatabaseManagerUI(QMainWindow):
    def __init__(self):
        super().__init__()
        self.manager = DatabaseManager()
        self.worker = None
        self.init_ui()
        self.load_initial_data()
        
        # 设置定时器更新统计信息
        self.timer = QTimer()
        self.timer.timeout.connect(self.update_stats)
        self.timer.start(30000)  # 30秒更新一次
    
    def init_ui(self):
        self.setWindowTitle("数据库管理器 - 足球赔率数据")
        self.setGeometry(100, 100, 800, 600)
        
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 创建标签页
        tab_widget = QTabWidget()
        central_widget.setLayout(QVBoxLayout())
        central_widget.layout().addWidget(tab_widget)
        
        # 统计信息标签页
        self.create_stats_tab(tab_widget)
        
        # 清理管理标签页
        self.create_cleanup_tab(tab_widget)
        
        # 备份管理标签页
        self.create_backup_tab(tab_widget)
        
        # 高级功能标签页
        self.create_advanced_tab(tab_widget)
    
    def create_stats_tab(self, parent):
        """创建统计信息标签页"""
        stats_widget = QWidget()
        layout = QVBoxLayout(stats_widget)
        
        # 当前统计信息
        stats_group = QGroupBox("当前数据库统计")
        stats_layout = QGridLayout(stats_group)
        
        self.size_label = QLabel("数据库大小: 计算中...")
        self.matches_label = QLabel("比赛数量: 计算中...")
        self.odds_label = QLabel("赔率记录: 计算中...")
        self.companies_label = QLabel("博彩公司: 计算中...")
        self.avg_label = QLabel("平均每场比赛记录: 计算中...")
        
        stats_layout.addWidget(self.size_label, 0, 0)
        stats_layout.addWidget(self.matches_label, 0, 1)
        stats_layout.addWidget(self.odds_label, 1, 0)
        stats_layout.addWidget(self.companies_label, 1, 1)
        stats_layout.addWidget(self.avg_label, 2, 0, 1, 2)
        
        layout.addWidget(stats_group)
        
        # 增长预测
        prediction_group = QGroupBox("增长预测")
        self.prediction_table = QTableWidget(4, 3)
        self.prediction_table.setHorizontalHeaderLabels(["比赛数量", "预计大小(MB)", "预计大小(GB)"])
        self.prediction_table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        
        prediction_layout = QVBoxLayout(prediction_group)
        prediction_layout.addWidget(self.prediction_table)
        layout.addWidget(prediction_group)
        
        # 优化建议
        recommendations_group = QGroupBox("优化建议")
        self.recommendations_text = QTextEdit()
        self.recommendations_text.setMaximumHeight(150)
        recommendations_layout = QVBoxLayout(recommendations_group)
        recommendations_layout.addWidget(self.recommendations_text)
        layout.addWidget(recommendations_group)
        
        # 刷新按钮
        refresh_btn = QPushButton("刷新统计信息")
        refresh_btn.clicked.connect(self.update_stats)
        layout.addWidget(refresh_btn)
        
        parent.addTab(stats_widget, "统计信息")
    
    def create_cleanup_tab(self, parent):
        """创建清理管理标签页"""
        cleanup_widget = QWidget()
        layout = QVBoxLayout(cleanup_widget)
        
        # 手动清理
        manual_group = QGroupBox("手动清理")
        manual_layout = QGridLayout(manual_group)
        
        manual_layout.addWidget(QLabel("保留天数:"), 0, 0)
        self.cleanup_days_spin = QSpinBox()
        self.cleanup_days_spin.setRange(1, 365)
        self.cleanup_days_spin.setValue(30)
        manual_layout.addWidget(self.cleanup_days_spin, 0, 1)
        
        cleanup_btn = QPushButton("清理旧数据")
        cleanup_btn.clicked.connect(self.cleanup_old_data)
        manual_layout.addWidget(cleanup_btn, 0, 2)
        
        layout.addWidget(manual_group)
        
        # 自动清理
        auto_group = QGroupBox("自动清理")
        auto_layout = QGridLayout(auto_group)
        
        auto_layout.addWidget(QLabel("最大大小(MB):"), 0, 0)
        self.max_size_spin = QSpinBox()
        self.max_size_spin.setRange(50, 10000)
        self.max_size_spin.setValue(500)
        auto_layout.addWidget(self.max_size_spin, 0, 1)
        
        auto_layout.addWidget(QLabel("保留天数:"), 1, 0)
        self.auto_days_spin = QSpinBox()
        self.auto_days_spin.setRange(1, 365)
        self.auto_days_spin.setValue(30)
        auto_layout.addWidget(self.auto_days_spin, 1, 1)
        
        auto_cleanup_btn = QPushButton("执行自动清理")
        auto_cleanup_btn.clicked.connect(self.auto_cleanup)
        auto_layout.addWidget(auto_cleanup_btn, 0, 2, 2, 1)
        
        layout.addWidget(auto_group)
        
        # 数据库优化
        optimize_group = QGroupBox("数据库优化")
        optimize_layout = QVBoxLayout(optimize_group)
        
        optimize_btn = QPushButton("优化数据库 (VACUUM)")
        optimize_btn.clicked.connect(self.optimize_database)
        optimize_layout.addWidget(optimize_btn)
        
        layout.addWidget(optimize_group)
        
        # 操作日志
        log_group = QGroupBox("操作日志")
        self.cleanup_log = QTextEdit()
        self.cleanup_log.setMaximumHeight(200)
        log_layout = QVBoxLayout(log_group)
        log_layout.addWidget(self.cleanup_log)
        layout.addWidget(log_group)
        
        parent.addTab(cleanup_widget, "清理管理")
    
    def create_backup_tab(self, parent):
        """创建备份管理标签页"""
        backup_widget = QWidget()
        layout = QVBoxLayout(backup_widget)
        
        # 备份操作
        backup_group = QGroupBox("数据库备份")
        backup_layout = QGridLayout(backup_group)
        
        backup_btn = QPushButton("创建备份")
        backup_btn.clicked.connect(self.backup_database)
        backup_layout.addWidget(backup_btn, 0, 0)
        
        choose_backup_btn = QPushButton("选择备份位置")
        choose_backup_btn.clicked.connect(self.choose_backup_location)
        backup_layout.addWidget(choose_backup_btn, 0, 1)
        
        self.backup_path_label = QLabel("备份路径: 自动生成")
        backup_layout.addWidget(self.backup_path_label, 1, 0, 1, 2)
        
        layout.addWidget(backup_group)
        
        # 备份历史
        history_group = QGroupBox("备份历史")
        self.backup_table = QTableWidget(0, 4)
        self.backup_table.setHorizontalHeaderLabels(["文件名", "大小", "创建时间", "操作"])
        self.backup_table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        
        history_layout = QVBoxLayout(history_group)
        history_layout.addWidget(self.backup_table)
        
        refresh_backup_btn = QPushButton("刷新备份列表")
        refresh_backup_btn.clicked.connect(self.refresh_backup_list)
        history_layout.addWidget(refresh_backup_btn)
        
        layout.addWidget(history_group)
        
        parent.addTab(backup_widget, "备份管理")
    
    def create_advanced_tab(self, parent):
        """创建高级功能标签页"""
        advanced_widget = QWidget()
        layout = QVBoxLayout(advanced_widget)
        
        # 数据归档
        archive_group = QGroupBox("数据归档")
        archive_layout = QGridLayout(archive_group)
        
        archive_layout.addWidget(QLabel("归档天数:"), 0, 0)
        self.archive_days_spin = QSpinBox()
        self.archive_days_spin.setRange(30, 365)
        self.archive_days_spin.setValue(90)
        archive_layout.addWidget(self.archive_days_spin, 0, 1)
        
        archive_btn = QPushButton("创建归档")
        archive_btn.clicked.connect(self.create_archive)
        archive_layout.addWidget(archive_btn, 0, 2)
        
        layout.addWidget(archive_group)
        
        # 数据库分割
        split_group = QGroupBox("数据库分割")
        split_layout = QGridLayout(split_group)
        
        split_layout.addWidget(QLabel("分割日期:"), 0, 0)
        self.split_date_combo = QComboBox()
        self.split_date_combo.setEditable(True)
        split_layout.addWidget(self.split_date_combo, 0, 1)
        
        split_btn = QPushButton("分割数据库")
        split_btn.clicked.connect(self.split_database)
        split_layout.addWidget(split_btn, 0, 2)
        
        layout.addWidget(split_group)
        
        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        layout.addWidget(self.progress_bar)
        
        # 状态标签
        self.status_label = QLabel("就绪")
        layout.addWidget(self.status_label)
        
        parent.addTab(advanced_widget, "高级功能")
    
    def load_initial_data(self):
        """加载初始数据"""
        self.update_stats()
        self.refresh_backup_list()
    
    def update_stats(self):
        """更新统计信息"""
        if self.worker and self.worker.isRunning():
            return
        
        self.worker = DatabaseWorker("analyze")
        self.worker.finished.connect(self.on_stats_updated)
        self.worker.start()
    
    def on_stats_updated(self, result):
        """统计信息更新完成"""
        if "error" in result:
            QMessageBox.warning(self, "错误", f"获取统计信息失败: {result['error']}")
            return
        
        stats = result.get('current_stats', {})
        
        # 更新统计标签
        self.size_label.setText(f"数据库大小: {stats.get('db_size_mb', 0):.2f} MB")
        self.matches_label.setText(f"比赛数量: {stats.get('match_count', 0):,}")
        self.odds_label.setText(f"赔率记录: {stats.get('odds_count', 0):,}")
        self.companies_label.setText(f"博彩公司: {stats.get('company_count', 0)}")
        
        if stats.get('match_count', 0) > 0:
            avg_records = stats.get('odds_count', 0) / stats['match_count']
            self.avg_label.setText(f"平均每场比赛记录: {avg_records:.1f}")
        
        # 更新预测表格
        predictions = result.get('predictions', {})
        self.prediction_table.setRowCount(len(predictions))
        
        row = 0
        for key, pred in predictions.items():
            matches = key.replace('_matches', '')
            self.prediction_table.setItem(row, 0, QTableWidgetItem(f"{matches:,}"))
            self.prediction_table.setItem(row, 1, QTableWidgetItem(f"{pred['size_mb']:.2f}"))
            self.prediction_table.setItem(row, 2, QTableWidgetItem(f"{pred['size_gb']:.2f}"))
            row += 1
        
        # 更新建议
        recommendations = result.get('recommendations', [])
        self.recommendations_text.setText('\n'.join(f"• {rec}" for rec in recommendations))
    
    def cleanup_old_data(self):
        """清理旧数据"""
        days = self.cleanup_days_spin.value()
        
        reply = QMessageBox.question(self, "确认清理", 
                                   f"确定要删除{days}天前的数据吗？此操作不可撤销！",
                                   QMessageBox.Yes | QMessageBox.No)
        
        if reply == QMessageBox.Yes:
            self.start_operation("cleanup", days)
    
    def auto_cleanup(self):
        """自动清理"""
        max_size = self.max_size_spin.value()
        days = self.auto_days_spin.value()
        
        self.start_operation("auto_cleanup", max_size, days)
    
    def optimize_database(self):
        """优化数据库"""
        reply = QMessageBox.question(self, "确认优化", 
                                   "确定要优化数据库吗？这可能需要一些时间。",
                                   QMessageBox.Yes | QMessageBox.No)
        
        if reply == QMessageBox.Yes:
            self.start_operation("optimize")
    
    def backup_database(self):
        """备份数据库"""
        backup_path = getattr(self, 'selected_backup_path', None)
        self.start_operation("backup", backup_path)
    
    def choose_backup_location(self):
        """选择备份位置"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        default_name = f"odds_data_backup_{timestamp}.db"
        
        file_path, _ = QFileDialog.getSaveFileName(
            self, "选择备份位置", default_name, "数据库文件 (*.db)")
        
        if file_path:
            self.selected_backup_path = file_path
            self.backup_path_label.setText(f"备份路径: {file_path}")
    
    def refresh_backup_list(self):
        """刷新备份列表"""
        # 这里可以扫描备份文件并显示在表格中
        # 简化实现，只显示当前目录的备份文件
        import glob
        backup_files = glob.glob("odds_data_backup_*.db")
        
        self.backup_table.setRowCount(len(backup_files))
        
        for i, file_path in enumerate(backup_files):
            if os.path.exists(file_path):
                size = os.path.getsize(file_path)
                size_mb = size / 1024 / 1024
                mtime = datetime.fromtimestamp(os.path.getmtime(file_path))
                
                self.backup_table.setItem(i, 0, QTableWidgetItem(file_path))
                self.backup_table.setItem(i, 1, QTableWidgetItem(f"{size_mb:.2f} MB"))
                self.backup_table.setItem(i, 2, QTableWidgetItem(mtime.strftime("%Y-%m-%d %H:%M:%S")))
                
                delete_btn = QPushButton("删除")
                delete_btn.clicked.connect(lambda checked, f=file_path: self.delete_backup(f))
                self.backup_table.setCellWidget(i, 3, delete_btn)
    
    def delete_backup(self, file_path):
        """删除备份文件"""
        reply = QMessageBox.question(self, "确认删除", 
                                   f"确定要删除备份文件 {file_path} 吗？",
                                   QMessageBox.Yes | QMessageBox.No)
        
        if reply == QMessageBox.Yes:
            try:
                os.remove(file_path)
                self.refresh_backup_list()
                QMessageBox.information(self, "成功", "备份文件已删除")
            except Exception as e:
                QMessageBox.warning(self, "错误", f"删除失败: {e}")
    
    def create_archive(self):
        """创建归档"""
        days = self.archive_days_spin.value()
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        archive_path = f"odds_data_archive_{timestamp}.db"
        
        try:
            result = self.manager.create_archive(archive_path, days)
            if result.get('success'):
                QMessageBox.information(self, "成功", 
                                      f"归档创建成功!\n"
                                      f"归档文件: {result['archive_path']}\n"
                                      f"归档大小: {result['archive_size_mb']} MB\n"
                                      f"归档比赛: {result['matches_archived']} 场")
                self.update_stats()
            else:
                QMessageBox.warning(self, "错误", f"归档创建失败: {result.get('error')}")
        except Exception as e:
            QMessageBox.warning(self, "错误", f"归档创建失败: {e}")
    
    def split_database(self):
        """分割数据库"""
        split_date = self.split_date_combo.currentText()
        if not split_date:
            QMessageBox.warning(self, "错误", "请输入分割日期")
            return
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        new_db_path = f"odds_data_new_{timestamp}.db"
        
        try:
            result = self.manager.split_database_by_date(split_date, new_db_path)
            if result.get('success'):
                QMessageBox.information(self, "成功", 
                                      f"数据库分割成功!\n"
                                      f"新数据库: {result['new_db_path']} ({result['new_db_size_mb']} MB)\n"
                                      f"原数据库: {result['old_db_size_mb']} MB\n"
                                      f"移动比赛: {result['matches_moved']} 场")
                self.update_stats()
            else:
                QMessageBox.warning(self, "错误", f"数据库分割失败: {result.get('error')}")
        except Exception as e:
            QMessageBox.warning(self, "错误", f"数据库分割失败: {e}")
    
    def start_operation(self, operation, *args):
        """开始数据库操作"""
        if self.worker and self.worker.isRunning():
            QMessageBox.warning(self, "警告", "有操作正在进行中，请稍候...")
            return
        
        self.progress_bar.setVisible(True)
        self.progress_bar.setRange(0, 0)  # 不确定进度
        
        self.worker = DatabaseWorker(operation, *args)
        self.worker.progress.connect(self.status_label.setText)
        self.worker.finished.connect(self.on_operation_finished)
        self.worker.start()
    
    def on_operation_finished(self, result):
        """操作完成"""
        self.progress_bar.setVisible(False)
        self.status_label.setText("就绪")
        
        if "error" in result:
            QMessageBox.warning(self, "错误", f"操作失败: {result['error']}")
        else:
            # 根据不同操作显示不同的成功消息
            if 'matches_deleted' in result:
                msg = f"清理完成!\n删除了 {result['matches_deleted']} 场比赛和 {result['odds_deleted']} 条赔率记录"
            elif 'saved_space_mb' in result:
                msg = f"优化完成!\n节省了 {result['saved_space_mb']} MB 空间"
            elif 'backup_path' in result:
                msg = f"备份完成!\n备份文件: {result['backup_path']}\n大小: {result['backup_size_mb']} MB"
            elif 'actions_taken' in result:
                msg = f"自动清理完成!\n执行的操作: {', '.join(result['actions_taken'])}\n节省空间: {result['space_saved_mb']:.2f} MB"
            else:
                msg = "操作完成!"
            
            QMessageBox.information(self, "成功", msg)
            
            # 记录到日志
            timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            self.cleanup_log.append(f"[{timestamp}] {msg}")
        
        # 刷新统计信息
        self.update_stats()
        if hasattr(self, 'backup_table'):
            self.refresh_backup_list()

def main():
    app = QApplication(sys.argv)
    window = DatabaseManagerUI()
    window.show()
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()

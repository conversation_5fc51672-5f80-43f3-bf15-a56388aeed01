#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
反常行为检测
识别'对着干'的公司和异常变盘行为，分析其特殊预测价值
"""

import pandas as pd
import numpy as np
import json
import os
from datetime import datetime
import matplotlib.pyplot as plt
import seaborn as sns
from collections import defaultdict, Counter
from scipy import stats
from sklearn.ensemble import IsolationForest
from sklearn.preprocessing import StandardScaler
import warnings
warnings.filterwarnings('ignore')

class AnomalyBehaviorDetector:
    def __init__(self, normalized_data_file=None, labeled_data_file=None):
        self.normalized_data_file = normalized_data_file
        self.labeled_data_file = labeled_data_file
        self.normalized_data = None
        self.labeled_data = None
        self.anomaly_results = {}
        
        # 异常检测参数
        self.min_change_threshold = 0.02  # 最小变化阈值（2%）
        self.anomaly_threshold = 0.3      # 异常度阈值
        self.min_sample_size = 3          # 最小样本数
        
    def load_data(self, normalized_filename=None, labeled_filename=None):
        """加载标准化数据和标记数据"""
        # 加载标准化数据
        if normalized_filename:
            self.normalized_data_file = normalized_filename
        
        if not self.normalized_data_file:
            files = [f for f in os.listdir('.') if f.startswith('normalized_odds_') and f.endswith('.json')]
            if files:
                self.normalized_data_file = sorted(files)[-1]
                print(f"自动选择标准化数据文件: {self.normalized_data_file}")
        
        if self.normalized_data_file and os.path.exists(self.normalized_data_file):
            with open(self.normalized_data_file, 'r', encoding='utf-8') as f:
                self.normalized_data = json.load(f)
        
        # 加载标记数据
        if labeled_filename:
            self.labeled_data_file = labeled_filename
        
        if not self.labeled_data_file:
            files = [f for f in os.listdir('.') if f.startswith('labeled_patterns_') and f.endswith('.csv')]
            if files:
                self.labeled_data_file = sorted(files)[-1]
                print(f"自动选择标记数据文件: {self.labeled_data_file}")
        
        if self.labeled_data_file and os.path.exists(self.labeled_data_file):
            self.labeled_data = pd.read_csv(self.labeled_data_file)
        
        print(f"数据加载完成")
        return self.normalized_data is not None and self.labeled_data is not None
    
    def detect_contrarian_companies(self):
        """检测对着干的公司"""
        if self.labeled_data is None:
            print("需要标记数据进行对着干分析")
            return None
        
        print("\n=== 对着干公司检测 ===")
        
        contrarian_stats = defaultdict(lambda: {
            'total_matches': 0,
            'contrarian_count': 0,
            'contrarian_rate': 0,
            'contrarian_details': []
        })
        
        # 按比赛分组分析
        for match_id in self.labeled_data['match_id'].unique():
            match_data = self.labeled_data[self.labeled_data['match_id'] == match_id]
            
            if len(match_data) < 3:  # 至少需要3家公司
                continue
            
            for odds_type in ['home', 'draw', 'away']:
                pattern_col = f'primary_pattern_{odds_type}'
                
                # 统计各模式的公司数
                pattern_counts = match_data[pattern_col].value_counts()
                
                if len(pattern_counts) < 2:  # 需要至少2种不同模式
                    continue
                
                # 找到主流模式（最多公司采用的模式）
                mainstream_pattern = pattern_counts.index[0]
                mainstream_count = pattern_counts.iloc[0]
                
                # 识别对着干的公司（采用非主流模式的公司）
                contrarian_companies = match_data[
                    match_data[pattern_col] != mainstream_pattern
                ]['company'].tolist()
                
                # 更新统计
                for company in match_data['company'].unique():
                    contrarian_stats[company]['total_matches'] += 1
                    
                    if company in contrarian_companies:
                        contrarian_stats[company]['contrarian_count'] += 1
                        contrarian_stats[company]['contrarian_details'].append({
                            'match_id': match_id,
                            'odds_type': odds_type,
                            'company_pattern': match_data[match_data['company'] == company][pattern_col].iloc[0],
                            'mainstream_pattern': mainstream_pattern,
                            'mainstream_count': mainstream_count,
                            'total_companies': len(match_data)
                        })
        
        # 计算对着干率
        for company, stats in contrarian_stats.items():
            if stats['total_matches'] > 0:
                stats['contrarian_rate'] = stats['contrarian_count'] / stats['total_matches']
        
        # 排序并输出结果
        sorted_contrarians = sorted(contrarian_stats.items(), 
                                  key=lambda x: x[1]['contrarian_rate'], reverse=True)
        
        print(f"对着干公司排名（前10名）:")
        for company, stats in sorted_contrarians[:10]:
            if stats['total_matches'] >= self.min_sample_size:
                print(f"  {company}: {stats['contrarian_rate']:.3f} "
                      f"({stats['contrarian_count']}/{stats['total_matches']})")
        
        return dict(contrarian_stats)
    
    def detect_statistical_anomalies(self):
        """使用统计方法检测异常行为"""
        if self.labeled_data is None:
            print("需要标记数据进行统计异常检测")
            return None
        
        print("\n=== 统计异常行为检测 ===")
        
        # 选择数值特征进行异常检测
        numeric_features = [
            'total_change_pct_home', 'total_change_pct_draw', 'total_change_pct_away',
            'volatility_home', 'volatility_draw', 'volatility_away',
            'max_change_home', 'max_change_draw', 'max_change_away',
            'direction_changes_home', 'direction_changes_draw', 'direction_changes_away'
        ]
        
        # 过滤存在的特征
        available_features = [f for f in numeric_features if f in self.labeled_data.columns]
        
        if len(available_features) < 3:
            print("可用特征不足，无法进行异常检测")
            return None
        
        # 准备数据
        feature_data = self.labeled_data[available_features].fillna(0)
        
        # 标准化
        scaler = StandardScaler()
        scaled_data = scaler.fit_transform(feature_data)
        
        # 使用Isolation Forest检测异常
        iso_forest = IsolationForest(contamination=0.1, random_state=42)
        anomaly_labels = iso_forest.fit_predict(scaled_data)
        anomaly_scores = iso_forest.score_samples(scaled_data)
        
        # 添加异常标记到数据
        self.labeled_data['is_anomaly'] = anomaly_labels == -1
        self.labeled_data['anomaly_score'] = anomaly_scores
        
        # 分析异常行为
        anomaly_data = self.labeled_data[self.labeled_data['is_anomaly']]
        
        print(f"检测到 {len(anomaly_data)} 个异常行为记录 ({len(anomaly_data)/len(self.labeled_data)*100:.1f}%)")
        
        # 按公司统计异常行为
        company_anomaly_stats = anomaly_data['company'].value_counts()
        print(f"\n异常行为最多的公司:")
        for company, count in company_anomaly_stats.head(10).items():
            total_records = len(self.labeled_data[self.labeled_data['company'] == company])
            anomaly_rate = count / total_records
            print(f"  {company}: {count}次异常 ({anomaly_rate:.3f})")
        
        # 按比赛结果分析异常行为的预测价值
        if 'match_result' in self.labeled_data.columns:
            print(f"\n异常行为与比赛结果关联:")
            
            normal_results = self.labeled_data[~self.labeled_data['is_anomaly']]['match_result'].value_counts(normalize=True)
            anomaly_results = anomaly_data['match_result'].value_counts(normalize=True)
            
            print(f"正常行为结果分布: {dict(normal_results.round(3))}")
            print(f"异常行为结果分布: {dict(anomaly_results.round(3))}")
        
        return {
            'anomaly_data': anomaly_data,
            'company_anomaly_stats': company_anomaly_stats,
            'total_anomalies': len(anomaly_data),
            'anomaly_rate': len(anomaly_data) / len(self.labeled_data)
        }
    
    def detect_timing_anomalies(self):
        """检测时间异常（过早或过晚的变盘）"""
        if self.normalized_data is None:
            print("需要标准化数据进行时间异常检测")
            return None
        
        print("\n=== 时间异常检测 ===")
        
        timing_anomalies = []
        
        for db_name, db_data in self.normalized_data.items():
            for match_id, match_data in db_data.items():
                companies_data = match_data['companies_data']
                
                for company, company_data in companies_data.items():
                    time_points = company_data['time_points']
                    
                    for odds_type in ['home', 'draw', 'away']:
                        odds_key = f'{odds_type}_odds'
                        if odds_key not in company_data:
                            continue
                        
                        odds_series = company_data[odds_key]
                        
                        # 检测异常早期变盘（-48小时之前的大幅变化）
                        for i, time_point in enumerate(time_points):
                            if time_point < -48 and i > 0:  # 48小时前
                                change_pct = abs((odds_series[i] - odds_series[i-1]) / odds_series[i-1] * 100)
                                if change_pct > 5:  # 5%以上变化
                                    timing_anomalies.append({
                                        'type': 'early_major_change',
                                        'match_id': match_id,
                                        'company': company,
                                        'odds_type': odds_type,
                                        'time': time_point,
                                        'change_pct': change_pct,
                                        'severity': 'high' if change_pct > 10 else 'medium'
                                    })
                        
                        # 检测异常晚期变盘（-1小时之后的变化）
                        for i, time_point in enumerate(time_points):
                            if time_point > -1 and i > 0:  # 1小时内
                                change_pct = abs((odds_series[i] - odds_series[i-1]) / odds_series[i-1] * 100)
                                if change_pct > 2:  # 2%以上变化
                                    timing_anomalies.append({
                                        'type': 'late_change',
                                        'match_id': match_id,
                                        'company': company,
                                        'odds_type': odds_type,
                                        'time': time_point,
                                        'change_pct': change_pct,
                                        'severity': 'high' if change_pct > 5 else 'medium'
                                    })
        
        print(f"检测到 {len(timing_anomalies)} 个时间异常事件")
        
        # 按类型统计
        anomaly_types = Counter([a['type'] for a in timing_anomalies])
        print(f"异常类型分布:")
        for atype, count in anomaly_types.items():
            print(f"  {atype}: {count}次")
        
        # 按公司统计
        company_timing_anomalies = Counter([a['company'] for a in timing_anomalies])
        print(f"\n时间异常最多的公司:")
        for company, count in company_timing_anomalies.most_common(10):
            print(f"  {company}: {count}次")
        
        return timing_anomalies
    
    def analyze_anomaly_predictive_value(self):
        """分析异常行为的预测价值"""
        if self.labeled_data is None or 'is_anomaly' not in self.labeled_data.columns:
            print("需要先进行异常检测")
            return None
        
        print("\n=== 异常行为预测价值分析 ===")
        
        # 基准准确率
        baseline_accuracy = self.labeled_data['match_result'].value_counts(normalize=True)
        print(f"基准准确率分布: {dict(baseline_accuracy.round(3))}")
        
        # 异常行为的预测表现
        anomaly_data = self.labeled_data[self.labeled_data['is_anomaly']]
        normal_data = self.labeled_data[~self.labeled_data['is_anomaly']]
        
        if len(anomaly_data) > 0:
            anomaly_accuracy = anomaly_data['match_result'].value_counts(normalize=True)
            print(f"异常行为结果分布: {dict(anomaly_accuracy.round(3))}")
            
            # 计算信息增益
            def calculate_entropy(series):
                value_counts = series.value_counts(normalize=True)
                return -sum(p * np.log2(p) for p in value_counts.values if p > 0)
            
            total_entropy = calculate_entropy(self.labeled_data['match_result'])
            anomaly_entropy = calculate_entropy(anomaly_data['match_result'])
            normal_entropy = calculate_entropy(normal_data['match_result'])
            
            # 加权平均熵
            anomaly_weight = len(anomaly_data) / len(self.labeled_data)
            normal_weight = len(normal_data) / len(self.labeled_data)
            weighted_entropy = anomaly_weight * anomaly_entropy + normal_weight * normal_entropy
            
            information_gain = total_entropy - weighted_entropy
            
            print(f"\n信息增益分析:")
            print(f"  总体熵: {total_entropy:.3f}")
            print(f"  异常行为熵: {anomaly_entropy:.3f}")
            print(f"  正常行为熵: {normal_entropy:.3f}")
            print(f"  信息增益: {information_gain:.3f}")
            
            # 按异常程度分析
            anomaly_data_sorted = anomaly_data.sort_values('anomaly_score')
            top_anomalies = anomaly_data_sorted.head(len(anomaly_data)//3)  # 最异常的1/3
            
            if len(top_anomalies) > 0:
                top_anomaly_accuracy = top_anomalies['match_result'].value_counts(normalize=True)
                print(f"\n最异常行为结果分布: {dict(top_anomaly_accuracy.round(3))}")
        
        return {
            'baseline_accuracy': baseline_accuracy,
            'anomaly_accuracy': anomaly_accuracy if len(anomaly_data) > 0 else None,
            'information_gain': information_gain if len(anomaly_data) > 0 else 0
        }
    
    def save_anomaly_results(self, filename_prefix="anomaly_analysis"):
        """保存异常分析结果"""
        if not self.anomaly_results:
            print("没有异常分析结果可保存")
            return None
        
        output_file = f"{filename_prefix}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        
        # 转换为可序列化的格式
        serializable_results = self._make_serializable(self.anomaly_results)
        
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(serializable_results, f, ensure_ascii=False, indent=2)
        
        print(f"\n异常分析结果已保存到: {output_file}")
        
        # 保存带异常标记的数据
        if self.labeled_data is not None and 'is_anomaly' in self.labeled_data.columns:
            csv_file = output_file.replace('.json', '_with_anomalies.csv')
            self.labeled_data.to_csv(csv_file, index=False, encoding='utf-8')
            print(f"带异常标记的数据已保存到: {csv_file}")
        
        return output_file
    
    def _make_serializable(self, obj):
        """将对象转换为可序列化的格式"""
        if isinstance(obj, dict):
            return {k: self._make_serializable(v) for k, v in obj.items()}
        elif isinstance(obj, (list, tuple)):
            return [self._make_serializable(item) for item in obj]
        elif isinstance(obj, pd.DataFrame):
            return obj.to_dict('records')
        elif isinstance(obj, pd.Series):
            return obj.to_dict()
        elif isinstance(obj, (np.integer, np.int64)):
            return int(obj)
        elif isinstance(obj, (np.floating, np.float64)):
            return float(obj)
        elif isinstance(obj, (np.bool_, bool)):
            return bool(obj)
        elif isinstance(obj, np.ndarray):
            return obj.tolist()
        elif obj is None:
            return None
        elif hasattr(obj, '__dict__'):
            return str(obj)
        else:
            return obj

def main():
    detector = AnomalyBehaviorDetector()
    
    # 加载数据
    if not detector.load_data():
        print("数据加载失败")
        return
    
    # 检测对着干公司
    contrarian_stats = detector.detect_contrarian_companies()
    
    # 检测统计异常
    statistical_anomalies = detector.detect_statistical_anomalies()
    
    # 检测时间异常
    timing_anomalies = detector.detect_timing_anomalies()
    
    # 分析预测价值
    predictive_value = detector.analyze_anomaly_predictive_value()
    
    # 保存结果
    detector.anomaly_results = {
        'contrarian_stats': contrarian_stats,
        'statistical_anomalies': statistical_anomalies,
        'timing_anomalies': timing_anomalies,
        'predictive_value': predictive_value
    }
    
    detector.save_anomaly_results()

if __name__ == "__main__":
    main()

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增强版数据库管理器
支持单一数据库和按联赛分库两种模式
"""

import os
from typing import Dict, List, Optional
import logging
from database import OddsDatabase
from league_database_manager import LeagueDatabaseManager

logger = logging.getLogger(__name__)

class EnhancedDatabase:
    def __init__(self, mode: str = "single", **kwargs):
        """初始化增强版数据库
        
        Args:
            mode: 数据库模式 ("single" 或 "league")
            **kwargs: 其他参数
                - db_path: 单一数据库模式的数据库路径
                - base_dir: 联赛分库模式的基础目录
        """
        self.mode = mode
        
        if mode == "single":
            db_path = kwargs.get('db_path', 'odds_data.db')
            self.single_db = OddsDatabase(db_path)
            self.league_manager = None
            logger.info(f"使用单一数据库模式: {db_path}")
            
        elif mode == "league":
            base_dir = kwargs.get('base_dir', 'league_databases')
            self.league_manager = LeagueDatabaseManager(base_dir)
            self.single_db = None
            logger.info(f"使用联赛分库模式: {base_dir}")
            
        else:
            raise ValueError(f"不支持的数据库模式: {mode}")
    
    def save_match_info(self, match_info: Dict) -> bool:
        """保存比赛信息"""
        if self.mode == "single":
            return self.single_db.save_match_info(match_info)
        else:
            # 联赛分库模式需要同时保存比赛信息和赔率数据
            # 这里只保存比赛信息，赔率数据通过 save_complete_match_data 保存
            league_name = match_info.get('league', 'Unknown')
            database = self.league_manager.get_database(league_name)
            return database.save_match_info(match_info)
    
    def save_odds_data(self, match_id: str, odds_data: List[Dict]) -> bool:
        """保存赔率数据"""
        if self.mode == "single":
            return self.single_db.save_odds_data(match_id, odds_data)
        else:
            # 联赛分库模式需要先找到比赛所在的数据库
            match_data = self.get_match_info(match_id)
            if match_data:
                league_name = match_data.get('league', 'Unknown')
                database = self.league_manager.get_database(league_name)
                return database.save_odds_data(match_id, odds_data)
            else:
                logger.error(f"无法找到比赛 {match_id} 的信息，无法保存赔率数据")
                return False
    
    def save_complete_match_data(self, match_info: Dict, odds_data: List[Dict]) -> Dict:
        """保存完整的比赛数据（推荐使用）"""
        if self.mode == "single":
            match_success = self.single_db.save_match_info(match_info)
            odds_success = self.single_db.save_odds_data(match_info.get('match_id'), odds_data)
            return {
                'success': match_success and odds_success,
                'mode': 'single',
                'database_path': self.single_db.db_path
            }
        else:
            result = self.league_manager.save_match_data(match_info, odds_data)
            result['mode'] = 'league'
            return result
    
    def get_match_info(self, match_id: str) -> Optional[Dict]:
        """获取比赛信息"""
        if self.mode == "single":
            return self.single_db.get_match_info(match_id)
        else:
            result = self.league_manager.get_match_data(match_id)
            return result.get('match_info')
    
    def get_odds_data(self, match_id: str) -> List[Dict]:
        """获取赔率数据"""
        if self.mode == "single":
            return self.single_db.get_odds_data(match_id)
        else:
            result = self.league_manager.get_match_data(match_id)
            return result.get('odds_data', [])
    
    def get_complete_match_data(self, match_id: str) -> Dict:
        """获取完整的比赛数据"""
        if self.mode == "single":
            match_info = self.single_db.get_match_info(match_id)
            odds_data = self.single_db.get_odds_data(match_id)
            return {
                'match_info': match_info,
                'odds_data': odds_data,
                'database_path': self.single_db.db_path
            }
        else:
            return self.league_manager.get_match_data(match_id)
    
    def get_all_matches(self) -> List[Dict]:
        """获取所有比赛列表"""
        if self.mode == "single":
            return self.single_db.get_all_matches()
        else:
            all_matches = []
            leagues = self.league_manager.get_all_leagues()
            
            for league in leagues:
                try:
                    database = OddsDatabase(league['database_path'])
                    matches = database.get_all_matches()
                    # 添加数据库路径信息
                    for match in matches:
                        match['database_path'] = league['database_path']
                        match['league_file'] = league['file_name']
                    all_matches.extend(matches)
                except Exception as e:
                    logger.warning(f"获取联赛 {league['league_name']} 的比赛列表失败: {e}")
                    continue
            
            # 按时间排序
            all_matches.sort(key=lambda x: x.get('match_time', ''), reverse=True)
            return all_matches
    
    def get_database_stats(self) -> Dict:
        """获取数据库统计信息"""
        if self.mode == "single":
            stats = self.single_db.get_database_stats()
            stats['mode'] = 'single'
            return stats
        else:
            total_stats = self.league_manager.get_total_stats()
            total_stats['mode'] = 'league'
            return total_stats
    
    def get_league_stats(self) -> List[Dict]:
        """获取联赛统计信息（仅联赛分库模式）"""
        if self.mode == "league":
            return self.league_manager.get_all_leagues()
        else:
            return []
    
    def match_exists(self, match_id: str) -> bool:
        """检查比赛是否已存在"""
        if self.mode == "single":
            return self.single_db.match_exists(match_id)
        else:
            match_info = self.get_match_info(match_id)
            return match_info is not None
    
    def odds_exist(self, match_id: str) -> bool:
        """检查比赛的赔率数据是否已存在"""
        if self.mode == "single":
            return self.single_db.odds_exist(match_id)
        else:
            odds_data = self.get_odds_data(match_id)
            return len(odds_data) > 0
    
    def get_match_data_summary(self, match_id: str) -> Dict:
        """获取比赛数据摘要"""
        if self.mode == "single":
            return self.single_db.get_match_data_summary(match_id)
        else:
            match_info = self.get_match_info(match_id)
            odds_data = self.get_odds_data(match_id)
            
            if match_info:
                # 计算博彩公司数量
                companies = set()
                for odds in odds_data:
                    if odds.get('company_name'):
                        companies.add(odds['company_name'])
                
                # 获取最后更新时间
                last_update = None
                if odds_data:
                    last_update = max(odds.get('extraction_time', '') for odds in odds_data)
                
                return {
                    'match_exists': True,
                    'odds_count': len(odds_data),
                    'company_count': len(companies),
                    'last_update': last_update,
                    'has_complete_data': True
                }
            else:
                return {
                    'match_exists': False,
                    'odds_count': 0,
                    'company_count': 0,
                    'last_update': None,
                    'has_complete_data': False
                }
    
    def switch_mode(self, new_mode: str, **kwargs) -> Dict:
        """切换数据库模式"""
        if new_mode == self.mode:
            return {'success': True, 'message': f'已经是 {new_mode} 模式'}
        
        if new_mode == "single" and self.mode == "league":
            # 从联赛分库切换到单一数据库
            target_db_path = kwargs.get('db_path', 'odds_data_merged.db')
            return self._merge_to_single_database(target_db_path)
            
        elif new_mode == "league" and self.mode == "single":
            # 从单一数据库切换到联赛分库
            base_dir = kwargs.get('base_dir', 'league_databases')
            return self._split_to_league_databases(base_dir)
            
        else:
            return {'success': False, 'error': f'不支持从 {self.mode} 切换到 {new_mode}'}
    
    def _merge_to_single_database(self, target_db_path: str) -> Dict:
        """合并联赛数据库到单一数据库"""
        try:
            target_db = OddsDatabase(target_db_path)
            leagues = self.league_manager.get_all_leagues()
            
            total_matches = 0
            total_errors = 0
            
            for league in leagues:
                try:
                    source_db = OddsDatabase(league['database_path'])
                    matches = source_db.get_all_matches()
                    
                    for match in matches:
                        match_id = match['match_id']
                        odds_data = source_db.get_odds_data(match_id)
                        
                        # 保存到目标数据库
                        if target_db.save_match_info(match):
                            if target_db.save_odds_data(match_id, odds_data):
                                total_matches += 1
                            else:
                                total_errors += 1
                        else:
                            total_errors += 1
                            
                except Exception as e:
                    logger.error(f"合并联赛 {league['league_name']} 失败: {e}")
                    total_errors += 1
                    continue
            
            # 切换到单一数据库模式
            self.mode = "single"
            self.single_db = target_db
            self.league_manager = None
            
            return {
                'success': True,
                'merged_matches': total_matches,
                'errors': total_errors,
                'target_database': target_db_path
            }
            
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    def _split_to_league_databases(self, base_dir: str) -> Dict:
        """分割单一数据库到联赛数据库"""
        try:
            league_manager = LeagueDatabaseManager(base_dir)
            result = league_manager.migrate_from_single_database(self.single_db.db_path)
            
            if result['success']:
                # 切换到联赛分库模式
                self.mode = "league"
                self.league_manager = league_manager
                self.single_db = None
                
                return {
                    'success': True,
                    'migrated_matches': result['migrated_matches'],
                    'leagues_created': result['leagues_created'],
                    'base_directory': base_dir
                }
            else:
                return result
                
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    def export_to_csv(self, match_id: str, output_dir: str = ".") -> Dict[str, str]:
        """导出数据到CSV文件"""
        if self.mode == "single":
            return self.single_db.export_to_csv(match_id, output_dir)
        else:
            # 找到比赛所在的数据库
            result = self.league_manager.get_match_data(match_id)
            if result['match_info']:
                # 使用临时数据库对象导出
                db_path = result['database_path']
                temp_db = OddsDatabase(db_path)
                return temp_db.export_to_csv(match_id, output_dir)
            else:
                return {}

# 工厂函数
def create_database(mode: str = "single", **kwargs) -> EnhancedDatabase:
    """创建数据库实例的工厂函数"""
    return EnhancedDatabase(mode, **kwargs)

def create_single_database(db_path: str = "odds_data.db") -> EnhancedDatabase:
    """创建单一数据库实例"""
    return EnhancedDatabase("single", db_path=db_path)

def create_league_database(base_dir: str = "league_databases") -> EnhancedDatabase:
    """创建联赛分库实例"""
    return EnhancedDatabase("league", base_dir=base_dir)

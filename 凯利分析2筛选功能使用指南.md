# 凯利分析2筛选功能使用指南

## 🎯 功能概述

**凯利分析2筛选**是一个更加精细的高级筛选工具，它不仅基于凯利指数进行分析，还通过监控特定博彩公司在高凯利指数排名中的出现频率来识别潜在的市场操控或异常情况。该功能可以帮助您避开可能被特定博彩公司影响的比赛，找到更加可靠的投注机会。

## 📍 功能位置

在"比赛筛选"标签页中，您会看到一个新的筛选条件：**"凯利分析2"**，位于"凯利分析"之后。

## 🔧 参数设置

### 凯利类型选择
- **凯利主**：分析主胜的凯利指数
- **凯利平**：分析平局的凯利指数  
- **凯利客**：分析客胜的凯利指数

### 分析参数
1. **统计数量**（默认：10）
   - 选取排名前N家博彩公司的数据进行分析
   - 建议设置为5-20之间

2. **凯利门槛**（默认：1.05）
   - 平均凯利指数的最低要求
   - 建议设置为1.02-1.08之间

3. **博彩公司**（复选列表）
   - 显示数据库中所有可用的博彩公司
   - 可以选择多家需要监控的公司
   - 支持滚动查看所有公司

4. **无意义公司门槛**（默认：2）
   - 选中的博彩公司在前N家中允许出现的最大次数
   - 超过此门槛的比赛将被排除
   - 建议设置为1-5之间

## 📊 筛选逻辑

### 分析流程
1. **数据收集**：获取每场比赛所有博彩公司的凯利指数数据
2. **排序筛选**：按选定的凯利类型（主/平/客）从大到小排序
3. **样本选择**：选取排名前"统计数量"家的数据
4. **统计计算**：计算平均凯利指数，并获取这些公司的名单
5. **公司监控**：统计选中的博彩公司在前N家中出现的总次数
6. **条件判断**：同时满足以下条件的比赛被筛选出来：
   - 平均凯利指数 > 凯利门槛
   - 观察次数 ≤ 无意义公司门槛

### 筛选条件
```
符合条件 = (平均凯利指数 > 凯利门槛) AND (观察次数 ≤ 无意义公司门槛)
```

### 观察次数计算
- 统计选中的博彩公司在前N家排名中出现的总次数
- 同一家公司出现多次会累计计算
- 例如：选择监控"pinnacle"和"bet365"，如果前10家中pinnacle出现3次，bet365出现1次，则观察次数=4

## 🎮 使用步骤

### 步骤1：启用凯利分析2
1. 进入"比赛筛选"标签页
2. 勾选"启用凯利分析2筛选"
3. 筛选参数面板将变为可编辑状态

### 步骤2：选择凯利类型
- 选择要分析的凯利类型：凯利主、凯利平或凯利客

### 步骤3：设置分析参数
1. **统计数量**：设置要统计的博彩公司数量（建议10-15家）
2. **凯利门槛**：设置凯利指数的最低要求（建议1.05-1.08）

### 步骤4：选择监控公司
1. 在博彩公司列表中勾选需要监控的公司
2. 通常选择1-3家您认为可能影响市场的大型博彩公司
3. 可以滚动查看所有18家可用的博彩公司

### 步骤5：设置门槛
- **无意义公司门槛**：设置监控公司允许出现的最大次数（建议2-3）

### 步骤6：执行筛选
1. 点击"应用筛选"按钮
2. 系统会分析所有比赛的凯利数据和公司分布
3. 符合条件的比赛将在"比赛列表"中显示

## 💡 参数设置建议

### 保守型设置（避免大公司影响）
```
统计数量：15
凯利门槛：1.05
监控公司：pinnacle, bet365
无意义公司门槛：1
```
- 适合：希望避开大型博彩公司影响的投资者
- 特点：严格排除被大公司主导的比赛

### 平衡型设置（一般监控）
```
统计数量：10
凯利门槛：1.05
监控公司：pinnacle, bet365, 威廉希尔
无意义公司门槛：2
```
- 适合：一般投资者
- 特点：平衡机会数量和质量

### 宽松型设置（轻度监控）
```
统计数量：10
凯利门槛：1.03
监控公司：pinnacle
无意义公司门槛：3
```
- 适合：希望更多机会的投资者
- 特点：只排除极端情况

## 📈 实际应用示例

### 示例1：避免pinnacle主导的主胜机会
```
凯利类型：凯利主
统计数量：10
凯利门槛：1.05
监控公司：pinnacle
无意义公司门槛：2

结果：筛选出平均凯利主>1.05且pinnacle在前10家中出现≤2次的比赛
```

### 示例2：监控多家大公司的平局机会
```
凯利类型：凯利平
统计数量：15
凯利门槛：1.08
监控公司：pinnacle, bet365, 威廉希尔
无意义公司门槛：3

结果：筛选出高凯利平局且不被三大公司过度影响的比赛
```

### 示例3：组合筛选
```
1. 先用联赛筛选选择"英超"
2. 再用时间筛选选择"本周"
3. 最后用凯利分析2筛选高质量机会

结果：英超本周的高质量、低操控风险的投注机会
```

## 🔍 结果解读

### 筛选状态显示
筛选完成后，在"比赛筛选"页面底部会显示：
```
已筛选 (X 场): 凯利分析2: 凯利主(统计10家,观察3家公司) | 其他条件...
```

### 数据质量指标
- **符合条件的比赛数量**：通常比凯利分析1的结果更少
- **平均凯利指数**：关注具体数值
- **公司分布均匀性**：避免被单一公司主导的比赛

## ⚠️ 注意事项

### 监控公司选择
1. **选择原则**：通常选择市场影响力大的博彩公司
2. **数量建议**：建议选择1-3家，不要选择过多
3. **常见选择**：pinnacle、bet365、威廉希尔等知名公司

### 门槛设置
1. **无意义公司门槛**：设置过低可能过滤掉太多机会
2. **凯利门槛**：应该比凯利分析1稍高，因为增加了公司分布要求
3. **统计数量**：影响分析的样本大小

### 使用场景
1. **适用情况**：怀疑市场被操控或希望更高质量的机会
2. **不适用情况**：数据量较少或对机会数量要求较高的情况
3. **组合使用**：建议与其他筛选条件组合使用

## 🔄 与其他功能的集成

### 与凯利分析的区别
- **凯利分析**：只关注凯利指数和返还率
- **凯利分析2**：增加了博彩公司分布的监控
- **使用建议**：通常不同时使用两个凯利分析

### 与投资回测的结合
- 筛选出的比赛可以直接用于投资回测
- 验证避开特定公司影响后的实际效果

### 数据库兼容性
- 支持单一数据库和联赛分库模式
- 自动适应当前选择的数据库
- 博彩公司列表会根据当前数据库动态更新

## 📚 理论背景

### 市场操控检测
- **原理**：如果某家大型博彩公司频繁出现在高凯利指数排名中，可能存在市场操控
- **目的**：通过监控公司分布，识别和避开可能被操控的比赛
- **效果**：提高投注机会的可靠性和独立性

### 公司影响力分析
- **大公司效应**：大型博彩公司的赔率变化可能影响整个市场
- **独立性要求**：寻找不被单一公司过度影响的投注机会
- **分散化原则**：偏好凯利指数来源分散的比赛

### 统计意义
通过监控公司分布，可以：
- 识别市场异常情况
- 提高分析的独立性
- 减少单一公司的影响
- 发现更加可靠的价值机会

---

**版本**: v1.0  
**更新时间**: 2025年6月28日  
**兼容性**: 与现有所有功能完全兼容

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试ID区间分析的总统计功能
验证新增的4列总统计是否正确显示
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from PyQt5.QtWidgets import QApplication
from odds_combination_ui import OddsCombinationAnalyzerUI

def main():
    """启动UI进行ID区间分析总统计功能测试"""
    print("🎯 启动赔率组合观察UI - ID区间分析总统计功能测试")
    print("=" * 80)
    print("📝 测试说明:")
    print("1. 选择一个数据库")
    print("2. 输入起始ID和结束ID（如：2598140 到 2598150）")
    print("3. 选择一个博彩公司（如：bet365）")
    print("4. 设置组合数为3")
    print("5. 点击'ID区间数据分析'")
    print("6. 观察结果表格是否有新增的4列总统计")
    print("")
    print("✅ 预期结果:")
    print("- 表格有12列（原8列 + 新增4列）")
    print("- 新增列标题：总匹配次数、总主胜、总平局、总客胜")
    print("- 每行显示该比赛所有匹配组合的总统计")
    print("- 总统计只计算匹配次数 > 0 的组合")
    print("")
    print("📊 表格列结构:")
    print("1. 比赛ID")
    print("2. 比赛结果")
    print("3. 组合内容")
    print("4. 匹配次数")
    print("5. 主胜")
    print("6. 平局")
    print("7. 客胜")
    print("8. 尾部概率")
    print("9. 总匹配次数 ← 新增")
    print("10. 总主胜 ← 新增")
    print("11. 总平局 ← 新增")
    print("12. 总客胜 ← 新增")
    print("")
    print("🔍 验证要点:")
    print("- 第9-12列数据应该 >= 第4-7列数据")
    print("- 总主胜+总平局+总客胜 = 总匹配次数")
    print("- 切换回普通分析时，表格恢复8列")
    print("")
    print("🚀 正在启动UI...")
    
    app = QApplication(sys.argv)
    
    # 创建主窗口
    window = OddsCombinationAnalyzerUI()
    window.show()
    
    # 运行应用
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()

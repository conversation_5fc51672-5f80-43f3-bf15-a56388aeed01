#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
联赛比赛网址获取工具 (增强版)
用于获取某个联赛当前赛季已进行比赛的网址

新增功能:
1. 多轮次自动抓取 - 自动发现并抓取所有可用轮次的比赛数据
2. 指定轮次范围抓取 - 可以指定抓取特定轮次范围的比赛
3. 轮次发现功能 - 自动检测联赛有多少轮比赛
4. 增强的数据导出 - 支持按轮次分组显示和统计

解决的问题:
- 原版只能抓取单个轮次的比赛，现在可以抓取整个赛季的所有比赛
- 当赛季进行了很多轮比赛时，不需要手动调整URL中的轮次数字
- 提供了多种抓取模式，适应不同的使用场景

使用方法:
1. 直接运行: python league_match_extractor.py
2. 选择抓取模式 (1-5)
3. 根据提示输入参数
4. 查看结果并导出到文件

示例URL格式: https://m.titan007.com/info/fixture/2025/21_165_4.htm
其中最后的数字 4 代表第4轮
"""

import requests
from bs4 import BeautifulSoup
import re
from datetime import datetime, timedelta
from typing import List, Dict, Optional
import logging
import time

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class LeagueMatchURLExtractor:
    def __init__(self):
        """初始化联赛比赛网址提取器"""
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 14_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.0 Mobile/15E148 Safari/604.1',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1'
        })

    def parse_league_url(self, league_url: str) -> Dict[str, str]:
        """解析联赛URL，提取关键参数"""
        try:
            # 示例URL: https://m.titan007.com/info/fixture/2025/21_165_4.htm
            # 格式: /info/fixture/{year}/{league_id}_{season_id}_{stage}.htm

            pattern = r'/info/fixture/(\d{4})/(\d+)_(\d+)_(\d+)\.htm'
            match = re.search(pattern, league_url)

            if match:
                year, league_id, season_id, stage = match.groups()
                return {
                    'year': year,
                    'league_id': league_id,
                    'season_id': season_id,
                    'stage': stage,
                    'base_url': 'https://m.titan007.com'
                }
            else:
                logger.error(f"无法解析联赛URL: {league_url}")
                return {}

        except Exception as e:
            logger.error(f"解析联赛URL失败: {e}")
            return {}

    def get_league_info(self, league_url: str) -> Dict[str, str]:
        """获取联赛基本信息"""
        try:
            response = self.session.get(league_url, timeout=10)
            response.raise_for_status()
            response.encoding = 'utf-8'

            soup = BeautifulSoup(response.text, 'html.parser')

            # 提取联赛名称
            league_name = "未知联赛"
            title_elem = soup.find('title')
            if title_elem:
                title_text = title_elem.get_text().strip()
                # 从标题中提取联赛名称
                if '赛程' in title_text:
                    league_name = title_text.split('赛程')[0].strip()
                elif '积分榜' in title_text:
                    league_name = title_text.split('积分榜')[0].strip()
                else:
                    league_name = title_text

            # 提取赛季信息
            season_info = "当前赛季"
            season_elem = soup.find('div', class_='season_select') or soup.find('select', {'name': 'season'})
            if season_elem:
                selected_option = season_elem.find('option', selected=True)
                if selected_option:
                    season_info = selected_option.get_text().strip()

            return {
                'league_name': league_name,
                'season_info': season_info,
                'original_url': league_url
            }

        except Exception as e:
            logger.error(f"获取联赛信息失败: {e}")
            return {
                'league_name': "未知联赛",
                'season_info': "当前赛季",
                'original_url': league_url
            }

    def extract_match_id_from_row(self, row, row_idx: int = 0) -> Optional[str]:
        """从表格行中提取真实的比赛ID"""
        try:
            # 1. 首先查找分析页面链接（优先级最高）
            analysis_link = row.find('a', href=re.compile(r'/analy/Analysis/\d+\.htm'))
            if analysis_link:
                href = analysis_link.get('href', '')
                match = re.search(r'/analy/Analysis/(\d+)\.htm', href)
                if match:
                    match_id = match.group(1)
                    logger.info(f"从分析页面链接提取到比赛ID: {match_id}")
                    return match_id

            # 2. 查找所有链接并详细分析
            links = row.find_all('a')
            for link in links:
                href = link.get('href', '')
                if not href:
                    continue

                # 常见的比赛ID URL模式（按优先级排序）
                patterns = [
                    r'/analy/Analysis/(\d+)\.htm',     # 分析页面: /analy/Analysis/2703990.htm
                    r'/match/(\d+)',                    # 比赛页面: /match/2703990
                    r'/odds/(\d+)',                     # 赔率页面: /odds/2703990
                    r'/game/(\d+)',                     # 游戏页面: /game/2703990
                    r'/fixture/(\d+)',                  # 赛程页面: /fixture/2703990
                    r'id=(\d+)',                        # 参数形式: ?id=2703990
                    r'matchid=(\d+)',                   # 参数形式: ?matchid=2703990
                    r'gameid=(\d+)',                    # 参数形式: ?gameid=2703990
                    r'/(\d{6,8})(?:\.htm)?$',          # 6-8位数字: /2703990 或 /2703990.htm
                ]

                for pattern in patterns:
                    match = re.search(pattern, href)
                    if match:
                        match_id = match.group(1)
                        # 验证ID长度合理（通常6-8位）
                        if 6 <= len(match_id) <= 8:
                            logger.info(f"从链接 {href} 提取到比赛ID: {match_id}")
                            return match_id

            # 3. 查找onclick事件中的ID（重点修复这里）
            # 首先检查行本身的onclick属性
            row_onclick = row.get('onclick', '')
            if row_onclick:
                # 查找ToAnaly函数中的完整ID
                toAnaly_match = re.search(r'ToAnaly\s*\(\s*(\d+)', row_onclick)
                if toAnaly_match:
                    match_id = toAnaly_match.group(1)
                    logger.info(f"从onclick事件提取到比赛ID: {match_id}")
                    return match_id

                # 备用方案：查找onclick中的任何7位数字
                id_match = re.search(r'(\d{7})', row_onclick)
                if id_match:
                    match_id = id_match.group(1)
                    logger.info(f"从onclick事件提取到比赛ID: {match_id}")
                    return match_id

            # 检查子元素的onclick事件
            for element in row.find_all(['td', 'a', 'span', 'div']):
                onclick = element.get('onclick', '')
                if onclick:
                    # 查找ToAnaly函数中的完整ID
                    toAnaly_match = re.search(r'ToAnaly\s*\(\s*(\d+)', onclick)
                    if toAnaly_match:
                        match_id = toAnaly_match.group(1)
                        logger.info(f"从onclick事件提取到比赛ID: {match_id}")
                        return match_id

                    # 备用方案：查找onclick中的任何7位数字
                    id_match = re.search(r'(\d{7})', onclick)
                    if id_match:
                        match_id = id_match.group(1)
                        logger.info(f"从onclick事件提取到比赛ID: {match_id}")
                        return match_id

            # 4. 查找data属性中的ID
            for element in row.find_all(['td', 'a', 'span', 'div']):
                for attr_name, attr_value in element.attrs.items():
                    if isinstance(attr_value, str) and ('id' in attr_name.lower() or 'match' in attr_name.lower()):
                        id_match = re.search(r'(\d{6,8})', attr_value)
                        if id_match:
                            match_id = id_match.group(1)
                            logger.info(f"从属性 {attr_name} 提取到比赛ID: {match_id}")
                            return match_id

            # 5. 在行文本中查找可能的ID
            row_text = row.get_text()
            id_match = re.search(r'\b(\d{6,8})\b', row_text)
            if id_match:
                match_id = id_match.group(1)
                logger.info(f"从行文本中提取到可能的比赛ID: {match_id}")
                return match_id

            # 6. 查找所有可能的数字ID（包括class、id等属性）
            for element in row.find_all():
                for attr_name, attr_value in element.attrs.items():
                    if isinstance(attr_value, str):
                        id_matches = re.findall(r'(\d{6,8})', attr_value)
                        if id_matches:
                            for potential_id in id_matches:
                                if 6 <= len(potential_id) <= 8:
                                    return potential_id

            return None

        except Exception as e:
            logger.warning(f"从行 {row_idx} 提取比赛ID失败: {e}")
            return None

    def scrape_goal_probability_data(self, match_id: str) -> Dict[str, any]:
        """抓取进失球概率数据"""
        try:
            # 构造分析页面URL
            analysis_url = f"https://m.titan007.com/analy/shijian/{match_id}.htm"
            logger.info(f"正在抓取比赛 {match_id} 的进失球概率数据: {analysis_url}")

            response = self.session.get(analysis_url, timeout=15)
            response.raise_for_status()
            response.encoding = 'utf-8'

            soup = BeautifulSoup(response.text, 'html.parser')

            # 存储所有概率数据
            probability_data = {
                'match_id': match_id,
                'analysis_url': analysis_url,
                'goal_probabilities': {},
                'time_segments': [],
                'raw_data': {}
            }

            # 1. 查找进失球概率相关的表格和数据
            logger.info("正在查找进失球概率数据...")

            # 查找所有表格
            tables = soup.find_all('table')
            logger.info(f"找到 {len(tables)} 个表格")

            for i, table in enumerate(tables):
                table_text = table.get_text().strip()

                # 检查是否包含概率相关关键词
                if any(keyword in table_text for keyword in ['概率', '进球', '失球', '30场', '50场', '%']):
                    logger.info(f"表格 {i+1} 可能包含概率数据")
                    self._parse_probability_table(table, probability_data, f"table_{i+1}")

            # 2. 查找JavaScript中的数据
            logger.info("正在查找JavaScript中的概率数据...")
            script_tags = soup.find_all('script')

            for script in script_tags:
                if script.string:
                    script_content = script.string

                    # 查找可能的概率数据变量
                    if any(keyword in script_content for keyword in ['概率', 'probability', 'goal', 'Count_30', 'Count_50']):
                        self._parse_probability_script(script_content, probability_data)

            # 3. 查找特定的div或section
            logger.info("正在查找特定的概率数据容器...")

            # 查找可能包含概率数据的div
            prob_divs = soup.find_all('div', class_=re.compile(r'prob|goal|stat', re.I))
            for div in prob_divs:
                div_text = div.get_text().strip()
                if any(keyword in div_text for keyword in ['概率', '进球', '失球', '%']):
                    self._parse_probability_div(div, probability_data)

            # 4. 查找所有包含百分比的文本
            logger.info("正在查找所有百分比数据...")
            page_text = soup.get_text()
            percentage_matches = re.findall(r'(\d+(?:\.\d+)?%)', page_text)
            if percentage_matches:
                probability_data['raw_percentages'] = list(set(percentage_matches))
                logger.info(f"找到 {len(percentage_matches)} 个百分比数据")

            # 5. 查找时间段相关数据
            time_patterns = [
                r'(\d+-\d+)分钟',
                r'(\d+)-(\d+)\'',
                r'第(\d+)分钟',
                r'(\d+)分钟内'
            ]

            for pattern in time_patterns:
                matches = re.findall(pattern, page_text)
                if matches:
                    probability_data['time_segments'].extend(matches)

            # 去重时间段
            probability_data['time_segments'] = list(set(probability_data['time_segments']))

            logger.info(f"抓取完成，找到 {len(probability_data['goal_probabilities'])} 组概率数据")
            return probability_data

        except Exception as e:
            logger.error(f"抓取进失球概率数据失败: {e}")
            return {
                'match_id': match_id,
                'error': str(e),
                'goal_probabilities': {},
                'time_segments': []
            }

    def _parse_probability_table(self, table, probability_data: Dict, table_id: str):
        """解析概率表格数据"""
        try:
            rows = table.find_all('tr')
            logger.info(f"解析表格 {table_id}，共 {len(rows)} 行")

            for i, row in enumerate(rows):
                cells = row.find_all(['td', 'th'])
                if len(cells) < 2:
                    continue

                row_text = ' '.join([cell.get_text().strip() for cell in cells])

                # 查找包含概率数据的行
                if any(keyword in row_text for keyword in ['30场', '50场', '概率', '%']):
                    logger.info(f"找到概率数据行: {row_text}")

                    # 提取百分比数据
                    percentages = re.findall(r'(\d+(?:\.\d+)?%)', row_text)
                    if percentages:
                        # 尝试识别数据类型
                        if '30场' in row_text:
                            data_type = 'Count_30'
                        elif '50场' in row_text:
                            data_type = 'Count_50'
                        else:
                            data_type = f'{table_id}_row_{i}'

                        if data_type not in probability_data['goal_probabilities']:
                            probability_data['goal_probabilities'][data_type] = {}

                        # 存储百分比数据
                        probability_data['goal_probabilities'][data_type]['percentages'] = percentages
                        probability_data['goal_probabilities'][data_type]['raw_text'] = row_text

                        logger.info(f"提取到 {data_type} 数据: {percentages}")

        except Exception as e:
            logger.warning(f"解析表格 {table_id} 失败: {e}")

    def _parse_probability_script(self, script_content: str, probability_data: Dict):
        """解析JavaScript中的概率数据"""
        try:
            # 查找常见的数据变量模式
            patterns = [
                r'Count_30\s*[=:]\s*([^;]+)',
                r'Count_50\s*[=:]\s*([^;]+)',
                r'goalProb\s*[=:]\s*([^;]+)',
                r'probability\s*[=:]\s*([^;]+)',
                r'var\s+(\w*[Pp]rob\w*)\s*=\s*([^;]+)',
                r'var\s+(\w*[Gg]oal\w*)\s*=\s*([^;]+)'
            ]

            for pattern in patterns:
                matches = re.findall(pattern, script_content, re.IGNORECASE)
                if matches:
                    for match in matches:
                        if isinstance(match, tuple):
                            var_name, var_value = match
                            logger.info(f"找到JavaScript变量: {var_name} = {var_value[:100]}...")
                            probability_data['raw_data'][var_name] = var_value
                        else:
                            logger.info(f"找到JavaScript数据: {match[:100]}...")
                            probability_data['raw_data']['script_data'] = match

            # 查找数组数据
            array_patterns = [
                r'\[([^\]]*(?:\d+(?:\.\d+)?%[^\]]*)+)\]',  # 包含百分比的数组
                r'\[([^\]]*(?:\d+(?:\.\d+)?[^\]]*){3,})\]'  # 包含多个数字的数组
            ]

            for pattern in array_patterns:
                matches = re.findall(pattern, script_content)
                if matches:
                    for match in matches:
                        if '%' in match or len(re.findall(r'\d+', match)) >= 3:
                            logger.info(f"找到可能的概率数组: {match}")
                            probability_data['raw_data']['arrays'] = probability_data['raw_data'].get('arrays', [])
                            probability_data['raw_data']['arrays'].append(match)

        except Exception as e:
            logger.warning(f"解析JavaScript失败: {e}")

    def _parse_probability_div(self, div, probability_data: Dict):
        """解析div中的概率数据"""
        try:
            div_text = div.get_text().strip()
            div_class = div.get('class', [])
            div_id = div.get('id', '')

            logger.info(f"解析div: class={div_class}, id={div_id}")

            # 查找百分比数据
            percentages = re.findall(r'(\d+(?:\.\d+)?%)', div_text)
            if percentages:
                div_key = f"div_{div_id}" if div_id else f"div_{'_'.join(div_class)}" if div_class else "div_unknown"

                probability_data['goal_probabilities'][div_key] = {
                    'percentages': percentages,
                    'text': div_text,
                    'class': div_class,
                    'id': div_id
                }

                logger.info(f"从div提取到概率数据: {percentages}")

        except Exception as e:
            logger.warning(f"解析div失败: {e}")

    def discover_available_rounds(self, league_url: str, max_rounds: int = 20) -> List[int]:
        """发现联赛可用的轮次"""
        try:
            logger.info(f"开始发现联赛轮次: {league_url}, 最大轮次: {max_rounds}")

            # 解析联赛URL参数
            url_params = self.parse_league_url(league_url)
            if not url_params:
                logger.error("无法解析联赛URL参数")
                return []

            available_rounds = []

            # 尝试访问不同轮次的页面
            for round_num in range(1, max_rounds + 1):
                try:
                    # 构造轮次URL
                    # 示例: https://m.titan007.com/info/fixture/2025/21_165_4.htm?round=1
                    round_url = f"{league_url}?round={round_num}"

                    logger.info(f"检查第 {round_num} 轮: {round_url}")

                    response = self.session.get(round_url, timeout=10)
                    response.raise_for_status()
                    response.encoding = 'utf-8'

                    soup = BeautifulSoup(response.text, 'html.parser')

                    # 查找比赛表格
                    tables = soup.find_all('table')
                    has_matches = False

                    for table in tables:
                        # 查找包含比赛数据的行
                        rows = table.find_all('tr')
                        for row in rows:
                            # 检查是否包含比赛相关信息
                            row_text = row.get_text().strip()
                            if any(keyword in row_text for keyword in ['vs', 'VS', '-', ':', '比分', '时间']):
                                # 进一步验证是否为比赛行
                                cells = row.find_all(['td', 'th'])
                                if len(cells) >= 3:  # 至少有3列才可能是比赛数据
                                    has_matches = True
                                    break

                        if has_matches:
                            break

                    if has_matches:
                        available_rounds.append(round_num)
                        logger.info(f"✅ 第 {round_num} 轮有比赛数据")
                    else:
                        logger.info(f"❌ 第 {round_num} 轮无比赛数据")

                    # 添加延迟避免请求过快
                    import time
                    time.sleep(0.5)

                except Exception as e:
                    logger.warning(f"检查第 {round_num} 轮失败: {e}")
                    continue

            logger.info(f"发现可用轮次: {available_rounds}")
            return available_rounds

        except Exception as e:
            logger.error(f"发现联赛轮次失败: {e}")
            return []

    def get_completed_matches_from_multiple_rounds(self, league_url: str, rounds: List[int]) -> List[Dict]:
        """从多个轮次获取已完成的比赛"""
        try:
            logger.info(f"开始获取轮次 {rounds} 的已完成比赛")

            all_matches = []

            for round_num in rounds:
                try:
                    logger.info(f"正在处理第 {round_num} 轮")

                    # 获取该轮次的比赛
                    round_matches = self.get_completed_matches_from_round(league_url, round_num)

                    if round_matches:
                        logger.info(f"第 {round_num} 轮找到 {len(round_matches)} 场已完成比赛")
                        all_matches.extend(round_matches)
                    else:
                        logger.info(f"第 {round_num} 轮未找到已完成比赛")

                    # 添加延迟
                    import time
                    time.sleep(1)

                except Exception as e:
                    logger.error(f"处理第 {round_num} 轮失败: {e}")
                    continue

            logger.info(f"总共找到 {len(all_matches)} 场已完成比赛")
            return all_matches

        except Exception as e:
            logger.error(f"获取多轮次比赛失败: {e}")
            return []

    def get_completed_matches_from_round(self, league_url: str, round_num: int) -> List[Dict]:
        """从指定轮次获取已完成的比赛"""
        try:
            # 构造轮次URL - 修改URL中的轮次数字
            # 例如：https://m.titan007.com/info/fixture/2025/21_165_4.htm -> https://m.titan007.com/info/fixture/2025/21_165_1.htm
            import re

            # 查找URL中的轮次数字模式 (例如 _4.htm)
            round_pattern = r'_(\d+)\.htm$'
            match = re.search(round_pattern, league_url)

            if match:
                # 替换轮次数字
                round_url = re.sub(round_pattern, f'_{round_num}.htm', league_url)
            else:
                # 如果没有找到轮次模式，尝试添加轮次参数
                round_url = f"{league_url}?round={round_num}"

            logger.info(f"获取第 {round_num} 轮比赛: {round_url}")

            response = self.session.get(round_url, timeout=15)
            response.raise_for_status()
            response.encoding = 'utf-8'

            soup = BeautifulSoup(response.text, 'html.parser')

            matches = []

            # 查找比赛表格
            tables = soup.find_all('table')

            for table in tables:
                rows = table.find_all('tr')

                for i, row in enumerate(rows):
                    try:
                        # 提取比赛ID
                        match_id = self.extract_match_id_from_row(row, i)
                        if not match_id:
                            continue

                        # 提取比赛信息
                        cells = row.find_all(['td', 'th'])
                        if len(cells) < 3:
                            continue

                        # 解析比赛信息
                        match_info = self._parse_match_row(row, cells, match_id, round_num)

                        if match_info and self._is_match_completed(match_info):
                            match_info['data_source'] = 'table_extraction_real_id'
                            matches.append(match_info)
                            logger.info(f"找到已完成比赛: {match_id} - {match_info.get('home_team', '')} vs {match_info.get('away_team', '')}")

                    except Exception as e:
                        logger.warning(f"解析比赛行失败: {e}")
                        continue

            return matches

        except Exception as e:
            logger.error(f"获取第 {round_num} 轮比赛失败: {e}")
            return []

    def _parse_match_row(self, row, cells, match_id: str, round_num: int) -> Dict:
        """解析比赛行数据"""
        try:
            match_info = {
                'match_id': match_id,
                'round': f"第{round_num}轮"
            }

            # 提取文本内容
            cell_texts = [cell.get_text().strip() for cell in cells]
            row_text = ' '.join(cell_texts)

            # 查找队伍名称（通常包含 vs 或 - ）
            team_patterns = [
                r'(.+?)\s+vs\s+(.+?)(?:\s|$)',
                r'(.+?)\s+-\s+(.+?)(?:\s|$)',
                r'(.+?)\s+:\s+(.+?)(?:\s|$)'
            ]

            teams_found = False
            for pattern in team_patterns:
                match = re.search(pattern, row_text, re.IGNORECASE)
                if match:
                    match_info['home_team'] = match.group(1).strip()
                    match_info['away_team'] = match.group(2).strip()
                    teams_found = True
                    break

            if not teams_found:
                # 尝试从单元格中分别提取队伍名称
                if len(cell_texts) >= 2:
                    for i in range(len(cell_texts) - 1):
                        if cell_texts[i] and cell_texts[i+1]:
                            # 检查是否像队伍名称
                            if len(cell_texts[i]) > 1 and len(cell_texts[i+1]) > 1:
                                match_info['home_team'] = cell_texts[i]
                                match_info['away_team'] = cell_texts[i+1]
                                teams_found = True
                                break

            # 查找比分
            score_pattern = r'(\d+)\s*[-:]\s*(\d+)'
            score_match = re.search(score_pattern, row_text)
            if score_match:
                match_info['home_score'] = score_match.group(1)
                match_info['away_score'] = score_match.group(2)
                match_info['score'] = f"{score_match.group(1)}-{score_match.group(2)}"

            # 查找时间信息
            time_patterns = [
                r'(\d{4}-\d{2}-\d{2})',  # 日期
                r'(\d{2}:\d{2})',        # 时间
                r'(\d{2}/\d{2})',        # 月/日
            ]

            for pattern in time_patterns:
                time_match = re.search(pattern, row_text)
                if time_match:
                    if 'match_time' not in match_info:
                        match_info['match_time'] = time_match.group(1)
                    else:
                        match_info['match_time'] += ' ' + time_match.group(1)

            # 设置状态
            if 'score' in match_info:
                match_info['status'] = '已完成'
                match_info['match_state'] = 'finished'
            else:
                match_info['status'] = '未知'
                match_info['match_state'] = 'unknown'

            # 如果没有找到队伍名称，使用默认值
            if not teams_found:
                match_info['home_team'] = f"主队_{match_id}"
                match_info['away_team'] = f"客队_{match_id}"

            return match_info

        except Exception as e:
            logger.warning(f"解析比赛行数据失败: {e}")
            return {}

    def _is_match_completed(self, match_info: Dict) -> bool:
        """判断比赛是否已完成"""
        try:
            # 检查是否有比分
            if 'score' in match_info and match_info['score']:
                return True

            # 检查状态
            status = match_info.get('status', '').lower()
            if any(keyword in status for keyword in ['完成', 'finished', '结束']):
                return True

            # 检查是否有主客队比分
            if 'home_score' in match_info and 'away_score' in match_info:
                return True

            return False

        except Exception as e:
            logger.warning(f"判断比赛完成状态失败: {e}")
            return False

def test_goal_probability_scraping():
    """测试进失球概率抓取功能"""
    print("🧪 测试进失球概率抓取功能")
    print("=" * 50)

    # 创建提取器实例
    extractor = LeagueMatchURLExtractor()

    # 测试比赛ID
    test_match_id = "2511566"

    print(f"正在测试比赛ID: {test_match_id}")
    print(f"分析页面: https://m.titan007.com/analy/shijian/{test_match_id}.htm")
    print()

    # 抓取概率数据
    result = extractor.scrape_goal_probability_data(test_match_id)

    # 显示结果
    print("📊 抓取结果:")
    print("-" * 30)

    if 'error' in result:
        print(f"❌ 抓取失败: {result['error']}")
        return

    print(f"✅ 比赛ID: {result['match_id']}")
    print(f"📄 分析页面: {result['analysis_url']}")
    print()

    # 显示概率数据
    goal_probs = result.get('goal_probabilities', {})
    if goal_probs:
        print(f"📈 找到 {len(goal_probs)} 组概率数据:")
        for data_type, data in goal_probs.items():
            print(f"  🔸 {data_type}:")
            if isinstance(data, dict):
                if 'percentages' in data:
                    print(f"     百分比: {data['percentages']}")
                if 'raw_text' in data:
                    print(f"     原始文本: {data['raw_text'][:100]}...")
                if 'text' in data:
                    print(f"     文本内容: {data['text'][:100]}...")
            else:
                print(f"     数据: {str(data)[:100]}...")
            print()
    else:
        print("❌ 未找到概率数据")

    # 显示时间段
    time_segments = result.get('time_segments', [])
    if time_segments:
        print(f"⏰ 找到 {len(time_segments)} 个时间段:")
        for segment in time_segments[:10]:  # 显示前10个
            print(f"  🕐 {segment}")
        if len(time_segments) > 10:
            print(f"  ... 还有 {len(time_segments) - 10} 个")
        print()

    # 显示原始百分比数据
    raw_percentages = result.get('raw_percentages', [])
    if raw_percentages:
        print(f"📊 页面中的所有百分比 (共{len(raw_percentages)}个):")
        for i, pct in enumerate(raw_percentages[:20]):  # 显示前20个
            print(f"  {pct}", end="  ")
            if (i + 1) % 10 == 0:
                print()  # 每10个换行
        if len(raw_percentages) > 20:
            print(f"\n  ... 还有 {len(raw_percentages) - 20} 个")
        print()

    # 显示原始数据
    raw_data = result.get('raw_data', {})
    if raw_data:
        print(f"🔍 原始数据 (共{len(raw_data)}项):")
        for key, value in raw_data.items():
            print(f"  🔸 {key}: {str(value)[:100]}...")
        print()

    print("🎯 测试完成！")
    print()
    print("💡 使用建议:")
    print("1. 如果找到了概率数据，说明抓取成功")
    print("2. 如果只找到百分比但没有结构化数据，需要进一步分析页面结构")
    print("3. 可以根据实际数据结构调整解析逻辑")

if __name__ == '__main__':
    # 运行测试
    test_goal_probability_scraping()

# 凯利分析筛选功能使用指南

## 🎯 功能概述

新增的**凯利分析筛选**功能是一个高级筛选工具，基于凯利指数和返还率的统计分析来筛选比赛。该功能通过分析每场比赛中多家博彩公司的凯利指数，找出具有统计优势的投注机会。

## 📍 功能位置

在"比赛筛选"标签页中，您会看到一个新的筛选条件：**"凯利分析"**，位于所有筛选条件的最后。

## 🔧 参数设置

### 凯利类型选择
- **凯利主**：分析主胜的凯利指数
- **凯利平**：分析平局的凯利指数  
- **凯利客**：分析客胜的凯利指数

### 统计参数
1. **统计数量**（默认：10）
   - 选取排名前N家博彩公司的数据进行分析
   - 建议设置为5-20之间，确保有足够的样本

2. **凯利门槛**（默认：1.01）
   - 平均凯利指数的最低要求
   - 凯利指数>1表示有正期望值
   - 建议设置为1.01-1.05之间

3. **返还率门槛**（默认：97）
   - 平均返还率的最高限制（百分比）
   - 返还率越低，博彩公司抽水越少，对投注者越有利
   - 建议设置为95-98之间

## 📊 筛选逻辑

### 分析流程
1. **数据收集**：获取每场比赛所有博彩公司的凯利指数和返还率数据
2. **排序筛选**：按选定的凯利类型（主/平/客）从大到小排序
3. **样本选择**：选取排名前"统计数量"家的数据
4. **统计计算**：计算这些数据的平均凯利指数和平均返还率
5. **条件判断**：同时满足以下条件的比赛被筛选出来：
   - 平均凯利指数 > 凯利门槛
   - 平均返还率 < 返还率门槛

### 筛选条件
```
符合条件 = (平均凯利指数 > 凯利门槛) AND (平均返还率 < 返还率门槛)
```

## 🎮 使用步骤

### 步骤1：启用凯利分析
1. 进入"比赛筛选"标签页
2. 勾选"启用凯利分析筛选"
3. 筛选参数面板将变为可编辑状态

### 步骤2：选择凯利类型
- 选择要分析的凯利类型：凯利主、凯利平或凯利客
- 不同类型分析不同的投注方向

### 步骤3：设置分析参数
1. **统计数量**：设置要统计的博彩公司数量（建议10-15家）
2. **凯利门槛**：设置凯利指数的最低要求（建议1.01-1.02）
3. **返还率门槛**：设置返还率的最高限制（建议96-97）

### 步骤4：执行筛选
1. 点击"应用筛选"按钮
2. 系统会分析所有比赛的凯利数据
3. 符合条件的比赛将在"比赛列表"中显示

## 💡 参数设置建议

### 保守型设置
```
统计数量：15
凯利门槛：1.015
返还率门槛：96.5
```
- 适合：稳健投资者
- 特点：筛选条件较严格，机会较少但质量较高

### 平衡型设置
```
统计数量：10
凯利门槛：1.01
返还率门槛：97
```
- 适合：一般投资者
- 特点：平衡机会数量和质量

### 激进型设置
```
统计数量：5
凯利门槛：1.005
返还率门槛：97.5
```
- 适合：积极投资者
- 特点：更多机会，但需要更仔细的分析

## 📈 实际应用示例

### 示例1：寻找主胜机会
```
凯利类型：凯利主
统计数量：10
凯利门槛：1.01
返还率门槛：97

结果：筛选出平均凯利主>1.01且平均返还率<97%的比赛
```

### 示例2：寻找平局机会
```
凯利类型：凯利平
统计数量：15
凯利门槛：1.02
返还率门槛：96

结果：筛选出平均凯利平>1.02且平均返还率<96%的比赛
```

### 示例3：组合筛选
```
1. 先用联赛筛选选择"英超"
2. 再用时间筛选选择"最近一周"
3. 最后用凯利分析筛选高价值机会

结果：英超最近一周的高价值投注机会
```

## 🔍 结果解读

### 筛选状态显示
筛选完成后，在"比赛筛选"页面底部会显示：
```
已筛选 (X 场): 凯利分析: 凯利主(统计10家) | 其他条件...
```

### 数据质量指标
- **符合条件的比赛数量**：数量适中（不要太多也不要太少）
- **凯利指数分布**：关注平均凯利指数的具体数值
- **返还率水平**：返还率越低越好

## ⚠️ 注意事项

### 数据要求
1. **完整性**：需要比赛有足够的博彩公司凯利数据
2. **时效性**：凯利指数基于开盘时的数据
3. **样本量**：统计数量不应超过实际可用的博彩公司数量

### 分析局限性
1. **历史数据**：凯利指数基于历史概率模型
2. **市场变化**：实际投注时赔率可能已经变化
3. **其他因素**：需要结合其他分析方法

### 使用建议
1. **参数调试**：根据历史数据调整最优参数
2. **组合使用**：与其他筛选条件组合使用
3. **验证分析**：对筛选结果进行进一步分析验证

## 🔄 与其他功能的集成

### 与比赛筛选的结合
- 可以与联赛、时间、队伍等筛选条件组合使用
- 先用基础条件缩小范围，再用凯利分析找出价值机会

### 与投资回测的结合
- 筛选出的比赛可以直接用于投资回测
- 验证凯利分析筛选的实际效果

### 数据库兼容性
- 支持单一数据库和联赛分库模式
- 自动适应当前选择的数据库

## 📚 理论背景

### 凯利指数
- **定义**：衡量投注价值的指标，反映真实概率与赔率隐含概率的差异
- **计算**：基于历史数据和概率模型
- **意义**：>1表示有正期望值，越大越好

### 返还率
- **定义**：博彩公司的返还比例，100%-抽水比例
- **范围**：通常在90%-98%之间
- **意义**：越高对投注者越有利

### 统计意义
通过分析多家博彩公司的平均值，可以：
- 减少单一公司的偏差
- 提高分析的可靠性
- 发现市场共识中的价值机会

---

**版本**: v1.0  
**更新时间**: 2025年6月28日  
**兼容性**: 与现有所有功能完全兼容

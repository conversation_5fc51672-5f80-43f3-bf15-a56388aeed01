#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
比赛时间字段迁移脚本
为比赛时间添加详细字段，并从分析页面抓取准确的时间信息

功能：
1. 在matches表中添加详细时间字段
2. 从比赛分析页面抓取准确的时间信息
3. 更新已有数据的时间信息

使用方法：
    python match_time_migration.py
"""

import sqlite3
import logging
import os
import time
from typing import Dict, List
from datetime import datetime
from match_time_scraper import MatchTimeScraper

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class MatchTimeMigration:
    def __init__(self, db_path: str = "odds_data.db"):
        self.db_path = db_path
        self.scraper = MatchTimeScraper()
        
    def backup_database(self) -> str:
        """备份数据库"""
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_path = f"odds_data_backup_time_{timestamp}.db"
            
            with sqlite3.connect(self.db_path) as source:
                with sqlite3.connect(backup_path) as backup:
                    source.backup(backup)
            
            logger.info(f"数据库备份完成: {backup_path}")
            return backup_path
            
        except Exception as e:
            logger.error(f"数据库备份失败: {e}")
            raise
    
    def add_time_columns(self) -> bool:
        """添加详细时间字段到matches表"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # 检查现有字段
                cursor.execute("PRAGMA table_info(matches)")
                columns = [column[1] for column in cursor.fetchall()]
                
                # 需要添加的新字段
                new_columns = [
                    ('accurate_datetime', 'TEXT'),      # 准确的完整日期时间
                    ('accurate_date', 'TEXT'),          # 准确的日期
                    ('accurate_time', 'TEXT'),          # 准确的时间
                    ('weekday', 'TEXT'),                # 星期
                    ('match_year', 'TEXT'),             # 年
                    ('match_month', 'TEXT'),            # 月
                    ('match_day', 'TEXT'),              # 日
                    ('match_hour', 'TEXT'),             # 时
                    ('match_minute', 'TEXT'),           # 分
                    ('time_source', 'TEXT')             # 时间来源（analysis/estimated）
                ]
                
                added_columns = []
                for column_name, column_type in new_columns:
                    if column_name not in columns:
                        try:
                            cursor.execute(f"ALTER TABLE matches ADD COLUMN {column_name} {column_type}")
                            added_columns.append(column_name)
                            logger.info(f"成功添加字段: {column_name}")
                        except sqlite3.Error as e:
                            logger.warning(f"添加字段 {column_name} 失败: {e}")
                    else:
                        logger.info(f"字段 {column_name} 已存在，跳过")
                
                conn.commit()
                
                if added_columns:
                    logger.info(f"成功添加 {len(added_columns)} 个新字段: {added_columns}")
                else:
                    logger.info("所有时间字段已存在，无需添加")
                
                return True
                
        except sqlite3.Error as e:
            logger.error(f"添加时间字段失败: {e}")
            return False
    
    def get_matches_without_accurate_time(self) -> List[Dict]:
        """获取没有准确时间信息的比赛"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                conn.row_factory = sqlite3.Row
                cursor = conn.cursor()
                
                cursor.execute("""
                    SELECT match_id, league, home_team, away_team, match_time
                    FROM matches 
                    WHERE accurate_datetime IS NULL OR accurate_datetime = ''
                    ORDER BY match_id DESC
                """)
                
                matches = [dict(row) for row in cursor.fetchall()]
                logger.info(f"找到 {len(matches)} 场比赛需要更新时间信息")
                return matches
                
        except sqlite3.Error as e:
            logger.error(f"获取比赛列表失败: {e}")
            return []
    
    def update_match_time(self, match_id: str, time_info: Dict) -> bool:
        """更新比赛的时间信息"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                if time_info.get('error'):
                    # 如果抓取失败，标记为估算时间
                    cursor.execute("""
                        UPDATE matches 
                        SET time_source = 'estimated_failed'
                        WHERE match_id = ?
                    """, (match_id,))
                    logger.warning(f"比赛 {match_id} 时间抓取失败，标记为估算时间")
                    return False
                
                # 更新准确的时间信息
                cursor.execute("""
                    UPDATE matches 
                    SET accurate_datetime = ?,
                        accurate_date = ?,
                        accurate_time = ?,
                        weekday = ?,
                        match_year = ?,
                        match_month = ?,
                        match_day = ?,
                        match_hour = ?,
                        match_minute = ?,
                        time_source = 'analysis'
                    WHERE match_id = ?
                """, (
                    time_info.get('full_datetime'),
                    time_info.get('match_date'),
                    time_info.get('match_time'),
                    time_info.get('weekday'),
                    time_info.get('year'),
                    time_info.get('month'),
                    time_info.get('day'),
                    time_info.get('hour'),
                    time_info.get('minute'),
                    match_id
                ))
                
                conn.commit()
                logger.info(f"成功更新比赛 {match_id} 的时间信息")
                return True
                
        except sqlite3.Error as e:
            logger.error(f"更新比赛 {match_id} 时间信息失败: {e}")
            return False
    
    def batch_update_match_times(self, max_matches: int = 50, delay_seconds: float = 1.0) -> Dict:
        """批量更新比赛时间信息"""
        try:
            logger.info(f"开始批量更新比赛时间信息，最大处理数量: {max_matches}")
            
            # 获取需要更新的比赛
            matches = self.get_matches_without_accurate_time()
            
            if not matches:
                logger.info("所有比赛都已有准确时间信息")
                return {
                    'total_processed': 0,
                    'successful_updates': 0,
                    'failed_updates': 0,
                    'success': True
                }
            
            # 限制处理数量
            matches_to_process = matches[:max_matches]
            logger.info(f"将处理 {len(matches_to_process)} 场比赛")
            
            successful_updates = 0
            failed_updates = 0
            
            for i, match in enumerate(matches_to_process, 1):
                match_id = match['match_id']
                teams = f"{match['home_team']} vs {match['away_team']}"
                
                logger.info(f"处理第 {i}/{len(matches_to_process)} 场比赛: {match_id} ({teams})")
                
                try:
                    # 抓取时间信息
                    time_info = self.scraper.scrape_match_time(match_id)
                    
                    # 更新数据库
                    if self.update_match_time(match_id, time_info):
                        successful_updates += 1
                    else:
                        failed_updates += 1
                    
                    # 添加延迟避免请求过快
                    if i < len(matches_to_process):
                        time.sleep(delay_seconds)
                        
                except Exception as e:
                    logger.error(f"处理比赛 {match_id} 失败: {e}")
                    failed_updates += 1
                    continue
            
            result = {
                'total_processed': len(matches_to_process),
                'successful_updates': successful_updates,
                'failed_updates': failed_updates,
                'success': True
            }
            
            logger.info(f"批量更新完成: {result}")
            return result
            
        except Exception as e:
            logger.error(f"批量更新失败: {e}")
            return {
                'total_processed': 0,
                'successful_updates': 0,
                'failed_updates': 0,
                'success': False,
                'error': str(e)
            }
    
    def verify_migration(self) -> Dict:
        """验证迁移结果"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # 检查新字段是否存在
                cursor.execute("PRAGMA table_info(matches)")
                columns = [column[1] for column in cursor.fetchall()]
                
                required_columns = [
                    'accurate_datetime', 'accurate_date', 'accurate_time', 'weekday',
                    'match_year', 'match_month', 'match_day', 'match_hour', 'match_minute', 'time_source'
                ]
                
                missing_columns = [col for col in required_columns if col not in columns]
                
                # 统计时间数据
                cursor.execute("SELECT COUNT(*) FROM matches")
                total_matches = cursor.fetchone()[0]
                
                cursor.execute("SELECT COUNT(*) FROM matches WHERE accurate_datetime IS NOT NULL AND accurate_datetime != ''")
                accurate_time_count = cursor.fetchone()[0]
                
                cursor.execute("SELECT time_source, COUNT(*) FROM matches GROUP BY time_source")
                time_source_distribution = dict(cursor.fetchall())
                
                result = {
                    'missing_columns': missing_columns,
                    'total_matches': total_matches,
                    'accurate_time_count': accurate_time_count,
                    'time_source_distribution': time_source_distribution,
                    'migration_complete': len(missing_columns) == 0
                }
                
                logger.info(f"迁移验证结果: {result}")
                return result
                
        except sqlite3.Error as e:
            logger.error(f"验证迁移失败: {e}")
            return {'error': str(e)}
    
    def run_migration(self, max_matches: int = 50) -> Dict:
        """执行完整的迁移流程"""
        try:
            logger.info("开始比赛时间字段迁移...")
            
            # 1. 备份数据库
            backup_path = self.backup_database()
            
            # 2. 添加时间字段
            if not self.add_time_columns():
                return {'success': False, 'error': '添加时间字段失败'}
            
            # 3. 批量更新时间信息
            update_result = self.batch_update_match_times(max_matches=max_matches)
            if not update_result['success']:
                return {'success': False, 'error': '批量更新时间信息失败', 'details': update_result}
            
            # 4. 验证迁移结果
            verification = self.verify_migration()
            
            result = {
                'success': True,
                'backup_path': backup_path,
                'update_result': update_result,
                'verification': verification
            }
            
            logger.info("比赛时间字段迁移完成！")
            return result
            
        except Exception as e:
            logger.error(f"迁移过程失败: {e}")
            return {'success': False, 'error': str(e)}

def main():
    """主函数"""
    # 检查数据库文件是否存在
    db_path = "odds_data.db"
    if not os.path.exists(db_path):
        logger.error(f"数据库文件不存在: {db_path}")
        return
    
    # 执行迁移
    migration = MatchTimeMigration(db_path)
    
    print("⚠️  注意：这个操作会从网络抓取大量数据，可能需要较长时间")
    print("建议先测试少量数据，确认无误后再处理全部数据")
    
    # 询问用户要处理多少场比赛
    try:
        max_matches = input("\n请输入要处理的最大比赛数量 (默认50，输入0处理全部): ").strip()
        if max_matches == "":
            max_matches = 50
        elif max_matches == "0":
            max_matches = 999999  # 处理全部
        else:
            max_matches = int(max_matches)
    except ValueError:
        max_matches = 50
    
    result = migration.run_migration(max_matches=max_matches)
    
    if result['success']:
        print("\n✅ 比赛时间字段迁移成功完成！")
        print(f"📁 备份文件: {result['backup_path']}")
        
        update_result = result['update_result']
        print(f"📊 更新统计:")
        print(f"   - 总处理数量: {update_result['total_processed']}")
        print(f"   - 成功更新: {update_result['successful_updates']}")
        print(f"   - 失败数量: {update_result['failed_updates']}")
        
        verification = result['verification']
        print(f"🔍 验证结果:")
        print(f"   - 缺失字段: {verification.get('missing_columns', [])}")
        print(f"   - 总比赛数: {verification.get('total_matches', 0)}")
        print(f"   - 准确时间数: {verification.get('accurate_time_count', 0)}")
        print(f"   - 时间来源分布: {verification.get('time_source_distribution', {})}")
        print(f"   - 迁移完整: {verification.get('migration_complete', False)}")
        
    else:
        print(f"\n❌ 迁移失败: {result['error']}")

if __name__ == "__main__":
    main()

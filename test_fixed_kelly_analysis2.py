#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试修复后的凯利分析2筛选功能
验证大小写忽略的匹配逻辑
"""

import sqlite3
import sys
import os

def test_case_insensitive_matching():
    """测试忽略大小写的匹配逻辑"""
    print("=== 测试忽略大小写的匹配逻辑 ===")
    
    # 模拟修复后的逻辑
    def count_companies_case_insensitive(selected_companies, company_list):
        observation_count = 0
        for company in selected_companies:
            # 忽略大小写进行匹配
            for list_company in company_list:
                if company.lower() == list_company.lower():
                    observation_count += 1
        return observation_count
    
    # 测试用例
    test_cases = [
        {
            'name': '大小写混合的情况',
            'selected': ['Betfair', 'bet365', 'pinnacle'],
            'company_list': ['betfair', 'BET365', 'Pinnacle', 'betfair', 'PINNACLE'],
            'expected': 5  # betfair(1) + BET365(1) + Pinnacle(1) + betfair(1) + PINNACLE(1)
        },
        {
            'name': '完全匹配的情况',
            'selected': ['betfair', 'bet365'],
            'company_list': ['betfair', 'bet365', 'pinnacle', 'betfair'],
            'expected': 3  # betfair(2) + bet365(1)
        },
        {
            'name': '无匹配的情况',
            'selected': ['不存在的公司'],
            'company_list': ['betfair', 'bet365', 'pinnacle'],
            'expected': 0
        }
    ]
    
    all_passed = True
    for case in test_cases:
        result = count_companies_case_insensitive(case['selected'], case['company_list'])
        if result == case['expected']:
            print(f"✅ {case['name']}: {result}")
        else:
            print(f"❌ {case['name']}: {result}, 期望: {case['expected']}")
            all_passed = False
    
    return all_passed

def test_real_match_with_fixed_logic():
    """使用修复后的逻辑测试真实比赛"""
    print("\n=== 使用修复后的逻辑测试真实比赛 ===")
    
    # 用户的筛选条件
    kelly_type = "kelly_away"  # 凯利客
    stats_count = 10
    kelly_threshold = 1.05
    selected_companies = ["Betfair", "bet365", "betfair", "betathome", "pinnacle"]
    meaningless_threshold = 2
    
    print(f"筛选条件:")
    print(f"  凯利类型: {kelly_type}")
    print(f"  统计数量: {stats_count}")
    print(f"  凯利门槛: {kelly_threshold}")
    print(f"  监控公司: {selected_companies}")
    print(f"  无意义公司门槛: {meaningless_threshold}")
    print()
    
    try:
        with sqlite3.connect("odds_data.db") as conn:
            conn.row_factory = sqlite3.Row
            cursor = conn.cursor()
            
            # 找到一场有足够数据的比赛
            cursor.execute(f'''
                SELECT match_id, COUNT(*) as company_count
                FROM odds 
                WHERE {kelly_type} IS NOT NULL AND company_name IS NOT NULL
                GROUP BY match_id
                HAVING company_count >= {stats_count}
                ORDER BY company_count DESC
                LIMIT 1
            ''')
            
            match = cursor.fetchone()
            if not match:
                print("没有找到合适的测试比赛")
                return False
            
            match_id = match[0]
            company_count = match[1]
            
            print(f"测试比赛 {match_id} (共{company_count}家公司)")
            
            # 获取该比赛的赔率数据，按凯利客降序排列
            cursor.execute(f'''
                SELECT {kelly_type}, return_rate, company_name
                FROM odds
                WHERE match_id = ? AND {kelly_type} IS NOT NULL AND return_rate IS NOT NULL AND company_name IS NOT NULL
                ORDER BY {kelly_type} DESC
            ''', (match_id,))
            
            odds_data = cursor.fetchall()
            
            # 取前N家的数据
            top_odds = odds_data[:stats_count]
            
            print(f"\n前{stats_count}家凯利客数据:")
            kelly_values = []
            company_list = []
            
            for i, row in enumerate(top_odds):
                kelly_val = float(row[0])
                company = row[2]
                
                kelly_values.append(kelly_val)
                company_list.append(company)
                
                mark = "★" if any(company.lower() == sc.lower() for sc in selected_companies) else " "
                print(f"  {i+1:2d}. {mark} {company:15s} - 凯利客:{kelly_val:.3f}")
            
            # 计算平均凯利值
            avg_kelly = sum(kelly_values) / len(kelly_values)
            
            # 使用修复后的逻辑统计观察次数
            print(f"\n观察次数统计（修复后的逻辑）:")
            observation_count = 0
            for company in selected_companies:
                count = 0
                # 忽略大小写进行匹配
                for list_company in company_list:
                    if company.lower() == list_company.lower():
                        count += 1
                observation_count += count
                if count > 0:
                    print(f"  {company}: 出现 {count} 次")
            
            print(f"  总观察次数: {observation_count}")
            
            # 判断是否符合筛选条件
            kelly_qualified = avg_kelly > kelly_threshold
            observation_qualified = observation_count <= meaningless_threshold
            
            print(f"\n筛选条件判断:")
            print(f"  凯利条件: {avg_kelly:.3f} > {kelly_threshold} = {kelly_qualified}")
            print(f"  观察条件: {observation_count} <= {meaningless_threshold} = {observation_qualified}")
            
            result = kelly_qualified and observation_qualified
            status = "✅ 应该通过筛选" if result else "❌ 应该被排除"
            print(f"  最终结果: {result} ({status})")
            
            return True
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🔧 修复后的凯利分析2筛选功能测试")
    print("=" * 60)
    
    # 检查数据库文件是否存在
    if not os.path.exists("odds_data.db"):
        print("❌ 数据库文件 odds_data.db 不存在")
        return False
    
    tests = [
        test_case_insensitive_matching,
        test_real_match_with_fixed_logic
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"❌ 测试异常: {e}")
    
    print("\n" + "=" * 60)
    print(f"📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！修复应该生效了。")
        print("\n💡 修复内容:")
        print("- 修复了博彩公司名称大小写匹配问题")
        print("- 现在 'Betfair' 和 'betfair' 会被视为同一家公司")
        return True
    else:
        print("⚠️  部分测试失败，请检查相关功能。")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)

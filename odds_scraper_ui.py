#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
足球赔率数据抓取器 - UI界面版本
提供图形界面来抓取和管理足球比赛的赔率数据
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import threading
import queue
import logging
from datetime import datetime
from typing import List, Dict, Optional
import os
import sys
import webbrowser
import sqlite3
import requests
import random

# 导入我们之前创建的模块
from enhanced_odds_scraper import EnhancedOddsScraper
from database import OddsDatabase
from config import UI_CONFIG, VERSION_INFO
from timeline_chart import TimelineChart
from dynamic_timeline_chart import DynamicTimelineChart
from league_match_extractor import LeagueMatchURLExtractor
from enhanced_match_data_system import EnhancedMatchDataSystem
from enhanced_stats_display import EnhancedStatsDisplay
from enhanced_lineup_display import EnhancedLineupDisplay
from enhanced_probability_display import EnhancedProbabilityDisplay
from league_database_manager import LeagueDatabaseManager
from season_utils import get_season_for_match, extract_season_from_url
from match_time_scraper import MatchTimeScraper

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class OddsScraperGUI:
    def __init__(self, root):
        self.root = root
        self.root.title(UI_CONFIG['window_title'])
        self.root.geometry(UI_CONFIG['window_size'])

        # 初始化组件
        self.scraper = EnhancedOddsScraper()
        self.database = OddsDatabase()  # 默认数据库
        self.league_extractor = LeagueMatchURLExtractor()
        self.time_scraper = MatchTimeScraper()

        # 初始化联赛数据库管理器
        self.league_db_manager = LeagueDatabaseManager()

        # 当前选择的数据库（None表示使用默认数据库）
        self.current_database = None
        self.current_database_path = None

        # 初始化增强比赛数据系统
        try:
            self.enhanced_system = EnhancedMatchDataSystem()
            # 初始化系统（首次使用时会升级数据库架构）
            init_result = self.enhanced_system.initialize_system()
            if init_result['success']:
                logger.info("增强比赛数据系统初始化成功")
            else:
                logger.warning(f"增强比赛数据系统初始化失败: {init_result.get('error', '未知错误')}")
                self.enhanced_system = None
        except Exception as e:
            logger.warning(f"增强比赛数据系统初始化异常: {e}")
            self.enhanced_system = None

        # 线程通信队列
        self.message_queue = queue.Queue()

        # 排序状态跟踪
        self.matches_sort_column = None
        self.matches_sort_reverse = False
        self.odds_sort_column = None
        self.odds_sort_reverse = False
        self.batch_results_sort_column = None
        self.batch_results_sort_reverse = False

        # 初始化筛选相关变量
        self.filter_conditions = {}
        self.filtered_matches = []
        self.filter_widgets = {}  # 存储筛选条件的控件

        # 初始化回测相关变量
        self.backtest_strategies = {}
        self.backtest_widgets = {}  # 存储回测策略的控件
        self.backtest_results = []

        # 创建界面
        self.create_widgets()

        # 启动消息处理
        self.process_queue()

        # 刷新数据
        self.refresh_data()

    def create_widgets(self):
        """创建界面组件"""
        # 创建主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        # 配置网格权重
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        main_frame.rowconfigure(2, weight=1)

        # 1. 抓取控制区域
        self.create_scraping_section(main_frame)

        # 2. 数据显示区域
        self.create_data_section(main_frame)

        # 3. 状态栏
        self.create_status_section(main_frame)

    def create_scraping_section(self, parent):
        """创建抓取控制区域"""
        # 抓取控制框架
        scrape_frame = ttk.LabelFrame(parent, text="数据抓取", padding="10")
        scrape_frame.grid(row=0, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        scrape_frame.columnconfigure(1, weight=1)

        # 第一行：数据库选择
        ttk.Label(scrape_frame, text="数据库:").grid(row=0, column=0, sticky=tk.W, padx=(0, 5))
        self.database_var = tk.StringVar(value="默认数据库")
        self.database_combo = ttk.Combobox(scrape_frame, textvariable=self.database_var, width=25, state="readonly")
        self.database_combo.grid(row=0, column=1, sticky=tk.W, padx=(0, 10))
        self.database_combo.bind("<<ComboboxSelected>>", self.on_database_selected)

        # 刷新数据库列表按钮
        ttk.Button(scrape_frame, text="刷新数据库", command=self.refresh_database_list).grid(row=0, column=2, padx=(0, 10))

        # 第二行：比赛ID和抓取参数
        ttk.Label(scrape_frame, text="比赛ID:").grid(row=1, column=0, sticky=tk.W, padx=(0, 5), pady=(10, 0))
        self.match_id_var = tk.StringVar(value=UI_CONFIG['default_match_id'])
        match_id_entry = ttk.Entry(scrape_frame, textvariable=self.match_id_var, width=15)
        match_id_entry.grid(row=1, column=1, sticky=tk.W, padx=(0, 10), pady=(10, 0))

        # 最大公司数量
        ttk.Label(scrape_frame, text="最大公司数:").grid(row=1, column=2, sticky=tk.W, padx=(0, 5), pady=(10, 0))
        self.max_companies_var = tk.StringVar(value=str(UI_CONFIG['default_max_companies']))
        companies_entry = ttk.Entry(scrape_frame, textvariable=self.max_companies_var, width=10)
        companies_entry.grid(row=1, column=3, sticky=tk.W, padx=(0, 10), pady=(10, 0))

        # 延迟时间
        ttk.Label(scrape_frame, text="延迟(秒):").grid(row=1, column=4, sticky=tk.W, padx=(0, 5), pady=(10, 0))
        self.delay_var = tk.StringVar(value=str(UI_CONFIG['default_delay']))
        delay_entry = ttk.Entry(scrape_frame, textvariable=self.delay_var, width=8)
        delay_entry.grid(row=1, column=5, sticky=tk.W, padx=(0, 10), pady=(10, 0))

        # 抓取按钮
        self.scrape_button = ttk.Button(scrape_frame, text="开始抓取", command=self.start_scraping)
        self.scrape_button.grid(row=1, column=6, padx=(10, 0), pady=(10, 0))

        # 完赛后更新按钮
        self.update_finished_button = ttk.Button(scrape_frame, text="完赛后更新", command=self.start_update_finished_matches)
        self.update_finished_button.grid(row=1, column=7, padx=(10, 0), pady=(10, 0))

        # 第三行：状态和进度
        self.progress_var = tk.StringVar(value="就绪")
        ttk.Label(scrape_frame, text="状态:").grid(row=2, column=0, sticky=tk.W, pady=(10, 0))
        self.progress_label = ttk.Label(scrape_frame, textvariable=self.progress_var)
        self.progress_label.grid(row=2, column=1, columnspan=5, sticky=tk.W, pady=(10, 0))

        self.progress_bar = ttk.Progressbar(scrape_frame, mode='indeterminate')
        self.progress_bar.grid(row=3, column=0, columnspan=7, sticky=(tk.W, tk.E), pady=(5, 0))

        # 初始化数据库列表
        self.refresh_database_list()

    def create_data_section(self, parent):
        """创建数据显示区域"""
        # 创建Notebook来分页显示
        self.notebook = ttk.Notebook(parent)
        self.notebook.grid(row=2, column=0, columnspan=2, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 10))

        # 比赛列表页面
        self.create_matches_tab(self.notebook)

        # 比赛筛选页面
        self.create_match_filter_tab(self.notebook)

        # 投资回测页面
        self.create_backtest_tab(self.notebook)

        # 比赛详情页面
        self.create_details_tab(self.notebook)

        # 详细数据页面
        self.create_enhanced_details_tab(self.notebook)

        # 时间线图表页面
        self.create_timeline_tab(self.notebook)

        # 联赛批量抓取页面
        self.create_league_batch_tab(self.notebook)

        # 数据库统计页面
        self.create_stats_tab(self.notebook)

    def create_matches_tab(self, notebook):
        """创建比赛列表页面"""
        matches_frame = ttk.Frame(notebook)
        notebook.add(matches_frame, text="比赛列表")

        # 工具栏
        toolbar_frame = ttk.Frame(matches_frame)
        toolbar_frame.pack(fill=tk.X, pady=(0, 10))

        ttk.Button(toolbar_frame, text="刷新", command=self.refresh_data).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(toolbar_frame, text="删除选中", command=self.delete_selected_match).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(toolbar_frame, text="导出CSV", command=self.export_selected_match).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(toolbar_frame, text="打开网址", command=self.open_selected_match_url).pack(side=tk.LEFT, padx=(0, 5))

        # 比赛列表
        columns = ("match_id", "league", "season", "round", "teams", "match_time", "score", "state", "companies")
        self.matches_tree = ttk.Treeview(matches_frame, columns=columns, show="headings", height=15)

        # 设置列标题和宽度，添加排序功能
        self.matches_tree.heading("match_id", text="比赛ID", command=lambda: self.sort_matches_column("match_id"))
        self.matches_tree.heading("league", text="联赛", command=lambda: self.sort_matches_column("league"))
        self.matches_tree.heading("season", text="赛季", command=lambda: self.sort_matches_column("season"))
        self.matches_tree.heading("round", text="轮次", command=lambda: self.sort_matches_column("round"))
        self.matches_tree.heading("teams", text="对阵", command=lambda: self.sort_matches_column("teams"))
        self.matches_tree.heading("match_time", text="比赛时间", command=lambda: self.sort_matches_column("match_time"))
        self.matches_tree.heading("score", text="比分", command=lambda: self.sort_matches_column("score"))
        self.matches_tree.heading("state", text="状态", command=lambda: self.sort_matches_column("state"))
        self.matches_tree.heading("companies", text="博彩公司", command=lambda: self.sort_matches_column("companies"))

        self.matches_tree.column("match_id", width=80)
        self.matches_tree.column("league", width=100)
        self.matches_tree.column("season", width=80)
        self.matches_tree.column("round", width=80)
        self.matches_tree.column("teams", width=200)
        self.matches_tree.column("match_time", width=150)
        self.matches_tree.column("score", width=80)
        self.matches_tree.column("state", width=80)
        self.matches_tree.column("companies", width=80)

        # 滚动条
        matches_scrollbar = ttk.Scrollbar(matches_frame, orient=tk.VERTICAL, command=self.matches_tree.yview)
        self.matches_tree.configure(yscrollcommand=matches_scrollbar.set)

        # 布局
        self.matches_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        matches_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # 绑定双击事件
        self.matches_tree.bind("<Double-1>", self.on_match_double_click)

    def create_match_filter_tab(self, notebook):
        """创建比赛筛选页面"""
        filter_frame = ttk.Frame(notebook)
        notebook.add(filter_frame, text="比赛筛选")

        # 筛选条件面板
        filter_conditions_frame = ttk.LabelFrame(filter_frame, text="筛选条件", padding="10")
        filter_conditions_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # 筛选条件滚动区域
        filter_canvas = tk.Canvas(filter_conditions_frame)
        filter_scrollbar = ttk.Scrollbar(filter_conditions_frame, orient="vertical", command=filter_canvas.yview)
        self.filter_scrollable_frame = ttk.Frame(filter_canvas)

        self.filter_scrollable_frame.bind(
            "<Configure>",
            lambda e: filter_canvas.configure(scrollregion=filter_canvas.bbox("all"))
        )

        filter_canvas.create_window((0, 0), window=self.filter_scrollable_frame, anchor="nw")
        filter_canvas.configure(yscrollcommand=filter_scrollbar.set)

        filter_canvas.pack(side="left", fill="both", expand=True)
        filter_scrollbar.pack(side="right", fill="y")

        # 创建筛选条件
        self.create_filter_conditions()

        # 筛选操作按钮
        filter_buttons_frame = ttk.Frame(filter_conditions_frame)
        filter_buttons_frame.pack(fill=tk.X, pady=(10, 0))

        ttk.Button(filter_buttons_frame, text="应用筛选", command=self.apply_filters).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(filter_buttons_frame, text="清除筛选", command=self.clear_filters).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(filter_buttons_frame, text="重置条件", command=self.reset_filter_conditions).pack(side=tk.LEFT)

        # 筛选状态显示
        self.filter_status_var = tk.StringVar(value="当前显示: 所有比赛")
        status_frame = ttk.Frame(filter_conditions_frame)
        status_frame.pack(fill=tk.X, pady=(10, 0))
        ttk.Label(status_frame, textvariable=self.filter_status_var, font=("Arial", 9, "italic")).pack(anchor=tk.W)

    def create_backtest_tab(self, notebook):
        """创建投资回测页面"""
        backtest_frame = ttk.Frame(notebook)
        notebook.add(backtest_frame, text="投资回测")

        # 主要布局：左侧策略设置，右侧回测结果
        main_paned = ttk.PanedWindow(backtest_frame, orient=tk.HORIZONTAL)
        main_paned.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # 左侧策略设置面板
        strategy_frame = ttk.LabelFrame(main_paned, text="投注策略", padding="10")
        main_paned.add(strategy_frame, weight=1)

        # 策略滚动区域
        strategy_canvas = tk.Canvas(strategy_frame)
        strategy_scrollbar = ttk.Scrollbar(strategy_frame, orient="vertical", command=strategy_canvas.yview)
        self.strategy_scrollable_frame = ttk.Frame(strategy_canvas)

        self.strategy_scrollable_frame.bind(
            "<Configure>",
            lambda e: strategy_canvas.configure(scrollregion=strategy_canvas.bbox("all"))
        )

        strategy_canvas.create_window((0, 0), window=self.strategy_scrollable_frame, anchor="nw")
        strategy_canvas.configure(yscrollcommand=strategy_scrollbar.set)

        strategy_canvas.pack(side="left", fill="both", expand=True)
        strategy_scrollbar.pack(side="right", fill="y")

        # 创建投注策略
        self.create_betting_strategies()

        # 回测参数设置
        params_frame = ttk.LabelFrame(strategy_frame, text="回测参数", padding="5")
        params_frame.pack(fill=tk.X, pady=(10, 0))

        # 初始资金
        ttk.Label(params_frame, text="初始资金:").pack(anchor=tk.W)
        self.initial_capital_var = tk.StringVar(value="10000")
        ttk.Entry(params_frame, textvariable=self.initial_capital_var, width=15).pack(anchor=tk.W, pady=(2, 5))

        # 单注金额策略
        ttk.Label(params_frame, text="单注金额策略:").pack(anchor=tk.W)
        self.bet_size_strategy_var = tk.StringVar(value="fixed")
        bet_size_frame = ttk.Frame(params_frame)
        bet_size_frame.pack(fill=tk.X, pady=(2, 5))

        ttk.Radiobutton(bet_size_frame, text="固定金额", variable=self.bet_size_strategy_var, value="fixed").pack(side=tk.LEFT)
        ttk.Radiobutton(bet_size_frame, text="固定比例", variable=self.bet_size_strategy_var, value="percentage").pack(side=tk.LEFT, padx=(10, 0))

        # 单注金额/比例
        ttk.Label(params_frame, text="单注金额/比例(%):").pack(anchor=tk.W)
        self.bet_amount_var = tk.StringVar(value="100")
        ttk.Entry(params_frame, textvariable=self.bet_amount_var, width=15).pack(anchor=tk.W, pady=(2, 0))

        # 回测操作按钮
        backtest_buttons_frame = ttk.Frame(strategy_frame)
        backtest_buttons_frame.pack(fill=tk.X, pady=(10, 0))

        ttk.Button(backtest_buttons_frame, text="开始回测", command=self.start_backtest).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(backtest_buttons_frame, text="清除结果", command=self.clear_backtest_results).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(backtest_buttons_frame, text="导出报告", command=self.export_backtest_report).pack(side=tk.LEFT)

        # 右侧回测结果面板
        results_frame = ttk.LabelFrame(main_paned, text="回测结果", padding="10")
        main_paned.add(results_frame, weight=2)

        # 结果概览
        overview_frame = ttk.LabelFrame(results_frame, text="回测概览", padding="5")
        overview_frame.pack(fill=tk.X, pady=(0, 5))

        # 概览信息显示
        self.backtest_overview_text = tk.Text(overview_frame, height=6, wrap=tk.WORD, state=tk.DISABLED)
        overview_scrollbar = ttk.Scrollbar(overview_frame, orient=tk.VERTICAL, command=self.backtest_overview_text.yview)
        self.backtest_overview_text.configure(yscrollcommand=overview_scrollbar.set)

        self.backtest_overview_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        overview_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # 详细结果表格
        details_frame = ttk.LabelFrame(results_frame, text="详细结果", padding="5")
        details_frame.pack(fill=tk.BOTH, expand=True)

        # 回测结果表格
        result_columns = ("match_id", "teams", "match_time", "strategy", "bet_type", "bet_amount", "odds", "result", "profit")
        self.backtest_results_tree = ttk.Treeview(details_frame, columns=result_columns, show="headings", height=15)

        # 设置列标题
        result_headers = ["比赛ID", "对阵", "比赛时间", "策略", "投注类型", "投注金额", "赔率", "结果", "盈亏"]
        for col, header in zip(result_columns, result_headers):
            self.backtest_results_tree.heading(col, text=header)
            self.backtest_results_tree.column(col, width=100)

        # 滚动条
        results_scrollbar = ttk.Scrollbar(details_frame, orient=tk.VERTICAL, command=self.backtest_results_tree.yview)
        self.backtest_results_tree.configure(yscrollcommand=results_scrollbar.set)

        self.backtest_results_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        results_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

    def create_details_tab(self, notebook):
        """创建比赛详情页面"""
        details_frame = ttk.Frame(notebook)
        notebook.add(details_frame, text="比赛详情")

        # 比赛信息区域
        info_frame = ttk.LabelFrame(details_frame, text="比赛信息", padding="10")
        info_frame.pack(fill=tk.X, pady=(0, 10))

        self.match_info_text = tk.Text(info_frame, height=6, wrap=tk.WORD)
        info_scrollbar = ttk.Scrollbar(info_frame, orient=tk.VERTICAL, command=self.match_info_text.yview)
        self.match_info_text.configure(yscrollcommand=info_scrollbar.set)

        self.match_info_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        info_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # 赔率数据区域
        odds_frame = ttk.LabelFrame(details_frame, text="赔率数据", padding="10")
        odds_frame.pack(fill=tk.BOTH, expand=True)

        # 赔率数据表格
        odds_columns = ("company", "date", "time", "home", "draw", "away", "return_rate", "kelly_home", "kelly_draw", "kelly_away")
        self.odds_tree = ttk.Treeview(odds_frame, columns=odds_columns, show="headings", height=12)

        # 设置列标题，添加排序功能
        headers = ["公司", "日期", "时间", "主胜", "平局", "客胜", "返还率", "凯利主", "凯利平", "凯利客"]
        for col, header in zip(odds_columns, headers):
            self.odds_tree.heading(col, text=header, command=lambda c=col: self.sort_odds_column(c))
            self.odds_tree.column(col, width=80)

        # 滚动条
        odds_scrollbar = ttk.Scrollbar(odds_frame, orient=tk.VERTICAL, command=self.odds_tree.yview)
        self.odds_tree.configure(yscrollcommand=odds_scrollbar.set)

        self.odds_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        odds_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

    def create_enhanced_details_tab(self, notebook):
        """创建详细数据页面"""
        enhanced_frame = ttk.Frame(notebook)
        notebook.add(enhanced_frame, text="详细数据")

        # 工具栏
        toolbar_frame = ttk.Frame(enhanced_frame)
        toolbar_frame.pack(fill=tk.X, pady=(0, 10))

        ttk.Button(toolbar_frame, text="刷新详细数据", command=self.refresh_enhanced_details).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(toolbar_frame, text="抓取详细数据", command=self.scrape_enhanced_details).pack(side=tk.LEFT, padx=(0, 5))

        # 创建Notebook来分页显示详细数据
        self.enhanced_notebook = ttk.Notebook(enhanced_frame)
        self.enhanced_notebook.pack(fill=tk.BOTH, expand=True)

        # 技术统计页面
        self.create_tech_stats_tab(self.enhanced_notebook)

        # 阵容信息页面
        self.create_lineup_tab(self.enhanced_notebook)

        # 球员统计页面
        self.create_player_stats_tab(self.enhanced_notebook)

        # 进失球概率页面
        self.create_goal_probability_tab(self.enhanced_notebook)

    def create_tech_stats_tab(self, notebook):
        """创建技术统计页面"""
        tech_frame = ttk.Frame(notebook)
        notebook.add(tech_frame, text="技术统计")

        # 使用新的增强统计显示组件
        self.enhanced_stats_display = EnhancedStatsDisplay(tech_frame)

        # 连接刷新功能
        self.enhanced_stats_display.refresh_display = self.refresh_enhanced_stats_display

        # 保留原来的文本组件作为备用（隐藏）
        self.tech_stats_text = tk.Text(tech_frame, wrap=tk.WORD, font=("Consolas", 10))
        self.tech_stats_text.pack_forget()  # 隐藏原来的文本组件

    def create_lineup_tab(self, notebook):
        """创建阵容信息页面"""
        lineup_frame = ttk.Frame(notebook)
        notebook.add(lineup_frame, text="阵容信息")

        # 使用新的增强阵容显示组件
        self.enhanced_lineup_display = EnhancedLineupDisplay(lineup_frame)

        # 连接刷新功能
        self.enhanced_lineup_display.refresh_display = self.refresh_enhanced_lineup_display

        # 保留原来的文本组件作为备用（隐藏）
        self.lineup_text = tk.Text(lineup_frame, wrap=tk.WORD, font=("Consolas", 10))
        self.lineup_text.pack_forget()  # 隐藏原来的文本组件

    def create_player_stats_tab(self, notebook):
        """创建球员统计页面"""
        player_frame = ttk.Frame(notebook)
        notebook.add(player_frame, text="球员统计")

        self.player_stats_text = tk.Text(player_frame, wrap=tk.WORD, font=("Consolas", 10))
        player_scrollbar = ttk.Scrollbar(player_frame, orient=tk.VERTICAL, command=self.player_stats_text.yview)
        self.player_stats_text.configure(yscrollcommand=player_scrollbar.set)

        self.player_stats_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        player_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

    def create_goal_probability_tab(self, notebook):
        """创建进失球概率页面"""
        prob_frame = ttk.Frame(notebook)
        notebook.add(prob_frame, text="进失球概率")

        # 使用新的增强概率显示组件
        self.enhanced_probability_display = EnhancedProbabilityDisplay(prob_frame)

        # 连接刷新功能
        self.enhanced_probability_display.refresh_display = self.refresh_enhanced_probability_display

        # 保留原来的文本组件作为备用（隐藏）
        self.goal_prob_text = tk.Text(prob_frame, wrap=tk.WORD, font=("Consolas", 10))
        self.goal_prob_text.pack_forget()  # 隐藏原来的文本组件

    def create_timeline_tab(self, notebook):
        """创建时间线图表页面"""
        timeline_frame = ttk.Frame(notebook)
        notebook.add(timeline_frame, text="开盘时间线")

        # 工具栏
        toolbar_frame = ttk.Frame(timeline_frame)
        toolbar_frame.pack(fill=tk.X, pady=(0, 10))

        ttk.Button(toolbar_frame, text="生成时间线", command=self.generate_timeline).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(toolbar_frame, text="动态时间线", command=self.generate_dynamic_timeline).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(toolbar_frame, text="保存图表", command=self.save_timeline_chart).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(toolbar_frame, text="清除图表", command=self.clear_timeline_chart).pack(side=tk.LEFT, padx=(0, 5))

        # 状态标签
        self.timeline_status_var = tk.StringVar(value="请先选择比赛，然后点击'生成时间线'")
        status_label = ttk.Label(toolbar_frame, textvariable=self.timeline_status_var)
        status_label.pack(side=tk.RIGHT)

        # 图表容器
        self.timeline_chart_frame = ttk.Frame(timeline_frame)
        self.timeline_chart_frame.pack(fill=tk.BOTH, expand=True)

        # 初始化时间线图表对象
        self.timeline_chart = TimelineChart(self.timeline_chart_frame)
        self.dynamic_timeline_chart = DynamicTimelineChart(self.timeline_chart_frame)

        # 当前选中的比赛ID和图表模式
        self.current_timeline_match_id = None
        self.current_chart_mode = "static"  # "static" 或 "dynamic"

    def create_league_batch_tab(self, notebook):
        """创建联赛批量抓取页面"""
        league_frame = ttk.Frame(notebook)
        notebook.add(league_frame, text="联赛批量抓取")

        # 控制区域
        control_frame = ttk.LabelFrame(league_frame, text="抓取控制", padding="10")
        control_frame.pack(fill=tk.X, pady=(0, 10))

        # 联赛URL输入
        ttk.Label(control_frame, text="联赛URL:").grid(row=0, column=0, sticky=tk.W, padx=(0, 5))
        self.league_url_var = tk.StringVar(value="https://m.titan007.com/info/fixture/2025/21_165_4.htm")
        league_url_entry = ttk.Entry(control_frame, textvariable=self.league_url_var, width=60)
        league_url_entry.grid(row=0, column=1, sticky=(tk.W, tk.E), padx=(0, 10))
        control_frame.columnconfigure(1, weight=1)

        # 日期抓取功能
        ttk.Label(control_frame, text="日期URL:").grid(row=1, column=0, sticky=tk.W, pady=(10, 0), padx=(0, 5))
        self.date_url_var = tk.StringVar(value="https://m.titan007.com/Schedule.htm?date=2025-07-22")
        date_url_entry = ttk.Entry(control_frame, textvariable=self.date_url_var, width=50)
        date_url_entry.grid(row=1, column=1, sticky=tk.W, pady=(10, 0), padx=(0, 10))

        # 按日期抓取按钮
        self.date_scrape_button = ttk.Button(control_frame, text="按日期抓取", command=self.start_date_scraping)
        self.date_scrape_button.grid(row=1, column=2, pady=(10, 0), padx=(10, 0))

        # 网络模式选择
        ttk.Label(control_frame, text="网络模式:").grid(row=2, column=0, sticky=tk.W, pady=(10, 0), padx=(0, 5))
        self.network_mode_var = tk.StringVar(value="normal")
        network_mode_frame = ttk.Frame(control_frame)
        network_mode_frame.grid(row=2, column=1, sticky=tk.W, pady=(10, 0))

        ttk.Radiobutton(network_mode_frame, text="普通模式", variable=self.network_mode_var, value="normal", command=self.on_network_mode_change).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Radiobutton(network_mode_frame, text="并发模式", variable=self.network_mode_var, value="concurrent", command=self.on_network_mode_change).pack(side=tk.LEFT)

        # 代理配置区域
        self.proxy_config_frame = ttk.LabelFrame(control_frame, text="代理配置", padding="5")
        self.proxy_config_frame.grid(row=3, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(10, 0))
        self.proxy_config_frame.columnconfigure(1, weight=1)

        ttk.Label(self.proxy_config_frame, text="代理API:").grid(row=0, column=0, sticky=tk.W, padx=(0, 5))
        self.proxy_api_var = tk.StringVar(value="http://api.tianqiip.com/getip?secret=njiz0mls9uu36pau&num=10&type=json&port=1&time=15&mr=1&sign=daff68445da273a1af5904e239658f7d")
        self.proxy_api_entry = ttk.Entry(self.proxy_config_frame, textvariable=self.proxy_api_var, width=80)
        self.proxy_api_entry.grid(row=0, column=1, sticky=(tk.W, tk.E), padx=(0, 10))

        # 代理状态显示
        proxy_status_frame = ttk.Frame(self.proxy_config_frame)
        proxy_status_frame.grid(row=1, column=0, columnspan=2, sticky=tk.W, pady=(5, 0))

        ttk.Label(proxy_status_frame, text="代理状态:").pack(side=tk.LEFT, padx=(0, 5))
        self.proxy_status_var = tk.StringVar(value="未获取")
        ttk.Label(proxy_status_frame, textvariable=self.proxy_status_var, foreground="blue").pack(side=tk.LEFT, padx=(0, 10))

        ttk.Button(proxy_status_frame, text="获取代理", command=self.fetch_proxy_list).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(proxy_status_frame, text="测试代理", command=self.test_proxy_list).pack(side=tk.LEFT)

        # 抓取模式选择
        ttk.Label(control_frame, text="抓取模式:").grid(row=4, column=0, sticky=tk.W, pady=(10, 0), padx=(0, 5))
        self.batch_mode_var = tk.StringVar(value="auto")
        mode_frame = ttk.Frame(control_frame)
        mode_frame.grid(row=4, column=1, sticky=tk.W, pady=(10, 0))

        ttk.Radiobutton(mode_frame, text="自动发现所有轮次", variable=self.batch_mode_var, value="auto").pack(side=tk.LEFT, padx=(0, 10))
        ttk.Radiobutton(mode_frame, text="指定轮次范围", variable=self.batch_mode_var, value="range").pack(side=tk.LEFT)

        # 轮次范围设置
        range_frame = ttk.Frame(control_frame)
        range_frame.grid(row=5, column=1, sticky=tk.W, pady=(5, 0))

        ttk.Label(range_frame, text="起始轮次:").pack(side=tk.LEFT, padx=(0, 5))
        self.start_round_var = tk.StringVar(value="1")
        start_entry = ttk.Entry(range_frame, textvariable=self.start_round_var, width=8)
        start_entry.pack(side=tk.LEFT, padx=(0, 10))

        ttk.Label(range_frame, text="结束轮次:").pack(side=tk.LEFT, padx=(0, 5))
        self.end_round_var = tk.StringVar(value="10")
        end_entry = ttk.Entry(range_frame, textvariable=self.end_round_var, width=8)
        end_entry.pack(side=tk.LEFT, padx=(0, 10))

        ttk.Label(range_frame, text="最大轮次:").pack(side=tk.LEFT, padx=(0, 5))
        self.max_rounds_var = tk.StringVar(value="20")
        max_entry = ttk.Entry(range_frame, textvariable=self.max_rounds_var, width=8)
        max_entry.pack(side=tk.LEFT)

        # 批量抓取选项
        options_frame = ttk.Frame(control_frame)
        options_frame.grid(row=6, column=1, sticky=tk.W, pady=(10, 0))

        self.auto_scrape_odds_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(options_frame, text="自动抓取赔率数据", variable=self.auto_scrape_odds_var).pack(side=tk.LEFT, padx=(0, 10))

        self.auto_scrape_enhanced_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(options_frame, text="自动抓取详细数据", variable=self.auto_scrape_enhanced_var).pack(side=tk.LEFT, padx=(0, 10))

        # 第二行选项
        options_frame2 = ttk.Frame(control_frame)
        options_frame2.grid(row=7, column=1, sticky=tk.W, pady=(5, 0))

        ttk.Label(options_frame2, text="每场比赛最大公司数:").pack(side=tk.LEFT, padx=(0, 5))
        self.batch_max_companies_var = tk.StringVar(value="5")
        companies_entry = ttk.Entry(options_frame2, textvariable=self.batch_max_companies_var, width=8)
        companies_entry.pack(side=tk.LEFT, padx=(0, 10))

        ttk.Label(options_frame2, text="延迟(秒):").pack(side=tk.LEFT, padx=(0, 5))
        self.batch_delay_var = tk.StringVar(value="2.0")
        delay_entry = ttk.Entry(options_frame2, textvariable=self.batch_delay_var, width=8)
        delay_entry.pack(side=tk.LEFT, padx=(0, 10))

        # 并发模式下的线程数设置
        ttk.Label(options_frame2, text="并发线程数:").pack(side=tk.LEFT, padx=(10, 5))
        self.concurrent_threads_var = tk.StringVar(value="3")
        threads_entry = ttk.Entry(options_frame2, textvariable=self.concurrent_threads_var, width=8)
        threads_entry.pack(side=tk.LEFT)

        # 按钮区域
        button_frame = ttk.Frame(control_frame)
        button_frame.grid(row=8, column=0, columnspan=3, pady=(15, 0))

        self.discover_button = ttk.Button(button_frame, text="发现轮次", command=self.discover_league_rounds)
        self.discover_button.pack(side=tk.LEFT, padx=(0, 10))

        self.batch_scrape_button = ttk.Button(button_frame, text="开始批量抓取", command=self.start_batch_scraping)
        self.batch_scrape_button.pack(side=tk.LEFT, padx=(0, 10))

        ttk.Button(button_frame, text="停止抓取", command=self.stop_batch_scraping).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(button_frame, text="清空结果", command=self.clear_batch_results).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(button_frame, text="打开选中网址", command=self.open_batch_selected_match_url).pack(side=tk.LEFT)

        # 进度显示
        progress_frame = ttk.LabelFrame(league_frame, text="抓取进度", padding="10")
        progress_frame.pack(fill=tk.X, pady=(0, 10))

        self.batch_progress_var = tk.StringVar(value="就绪")
        ttk.Label(progress_frame, textvariable=self.batch_progress_var).pack(anchor=tk.W)

        self.batch_progress_bar = ttk.Progressbar(progress_frame, mode='determinate')
        self.batch_progress_bar.pack(fill=tk.X, pady=(5, 0))

        # 结果显示
        results_frame = ttk.LabelFrame(league_frame, text="抓取结果", padding="10")
        results_frame.pack(fill=tk.BOTH, expand=True)

        # 结果表格
        columns = ("round", "match_id", "teams", "match_time", "score", "status", "odds_status")
        self.batch_results_tree = ttk.Treeview(results_frame, columns=columns, show="headings", height=12)

        # 设置列标题，添加排序功能
        headers = ["轮次", "比赛ID", "对阵", "比赛时间", "比分", "状态", "赔率状态"]
        for col, header in zip(columns, headers):
            self.batch_results_tree.heading(col, text=header, command=lambda c=col: self.sort_batch_results_column(c))
            self.batch_results_tree.column(col, width=100)

        # 滚动条
        batch_scrollbar = ttk.Scrollbar(results_frame, orient=tk.VERTICAL, command=self.batch_results_tree.yview)
        self.batch_results_tree.configure(yscrollcommand=batch_scrollbar.set)

        self.batch_results_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        batch_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # 初始化批量抓取相关变量
        self.batch_stop_flag = False
        self.discovered_rounds = []

        # 初始化代理相关变量
        self.proxy_list = []  # 代理IP列表
        self.working_proxies = []  # 可用代理列表
        self.proxy_failure_count = {}  # 代理失败次数统计
        self.proxy_pool_lock = threading.Lock()  # 代理池锁

        # 初始化时隐藏代理配置
        self.on_network_mode_change()

    def create_stats_tab(self, notebook):
        """创建数据库统计页面"""
        stats_frame = ttk.Frame(notebook)
        notebook.add(stats_frame, text="数据库统计")

        self.stats_text = tk.Text(stats_frame, wrap=tk.WORD, font=("Consolas", 10))
        stats_scrollbar = ttk.Scrollbar(stats_frame, orient=tk.VERTICAL, command=self.stats_text.yview)
        self.stats_text.configure(yscrollcommand=stats_scrollbar.set)

        self.stats_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        stats_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

    def create_status_section(self, parent):
        """创建状态栏"""
        status_frame = ttk.Frame(parent)
        status_frame.grid(row=3, column=0, columnspan=2, sticky=(tk.W, tk.E))
        status_frame.columnconfigure(0, weight=1)

        self.status_var = tk.StringVar(value="就绪")
        status_label = ttk.Label(status_frame, textvariable=self.status_var, relief=tk.SUNKEN)
        status_label.grid(row=0, column=0, sticky=(tk.W, tk.E))

        # 数据库信息
        self.db_info_var = tk.StringVar()
        db_info_label = ttk.Label(status_frame, textvariable=self.db_info_var)
        db_info_label.grid(row=0, column=1, sticky=tk.E)

        # 更新状态栏数据库信息
        self.update_database_info()

    def start_scraping(self):
        """开始抓取数据"""
        try:
            match_id = self.match_id_var.get().strip()
            max_companies = int(self.max_companies_var.get())
            delay = float(self.delay_var.get())

            if not match_id:
                messagebox.showerror("错误", "请输入比赛ID")
                return

            # 禁用抓取按钮
            self.scrape_button.config(state='disabled')
            self.progress_bar.start()
            self.progress_var.set("正在抓取数据...")

            # 在新线程中执行抓取
            thread = threading.Thread(
                target=self.scrape_worker,
                args=(match_id, max_companies, delay),
                daemon=True
            )
            thread.start()

        except ValueError:
            messagebox.showerror("错误", "请输入有效的数字")

    def scrape_worker(self, match_id, max_companies, delay):
        """抓取工作线程"""
        try:
            # 获取当前选择的数据库
            current_db = self.get_current_database()

            # 检查数据是否已存在
            self.message_queue.put(("status", "正在检查数据是否已存在..."))
            data_summary = current_db.get_match_data_summary(match_id)

            if data_summary['has_complete_data']:
                # 数据已存在，询问是否重新抓取
                message = f"比赛 {match_id} 的数据已存在 ({data_summary['odds_count']} 条赔率记录)。\n\n是否重新抓取？"

                # 由于在工作线程中，需要通过消息队列处理用户选择
                self.message_queue.put(("ask_redownload", {
                    'match_id': match_id,
                    'message': message,
                    'data_summary': data_summary,
                    'max_companies': max_companies,
                    'delay': delay
                }))
                return

            # 发送状态更新
            self.message_queue.put(("status", "正在抓取比赛信息..."))

            # 抓取完整数据
            complete_data = self.scraper.scrape_complete_match_data(
                match_id=match_id,
                max_companies=max_companies,
                delay=delay
            )

            if complete_data['match_info'] or complete_data['odds_data']:
                # 保存到数据库
                self.message_queue.put(("status", "正在保存到数据库..."))

                # 保存比赛信息
                if complete_data['match_info']:
                    # 添加赛季信息
                    match_info = complete_data['match_info'].copy()
                    if 'season' not in match_info or not match_info['season']:
                        match_info['season'] = get_season_for_match(match_id=match_id)

                    # 添加准确的时间信息
                    self.message_queue.put(("info", f"正在抓取比赛 {match_id} 的准确时间信息..."))
                    try:
                        time_info = self.time_scraper.scrape_match_time(match_id)
                        if not time_info.get('error'):
                            match_info.update({
                                'accurate_datetime': time_info.get('full_datetime'),
                                'accurate_date': time_info.get('match_date'),
                                'accurate_time': time_info.get('match_time'),
                                'weekday': time_info.get('weekday'),
                                'match_year': time_info.get('year'),
                                'match_month': time_info.get('month'),
                                'match_day': time_info.get('day'),
                                'match_hour': time_info.get('hour'),
                                'match_minute': time_info.get('minute'),
                                'time_source': 'analysis'
                            })
                            self.message_queue.put(("info", f"成功获取准确时间: {time_info.get('full_datetime')}"))
                        else:
                            match_info['time_source'] = 'estimated'
                            self.message_queue.put(("warning", f"时间抓取失败，使用估算时间: {time_info.get('error')}"))
                    except Exception as e:
                        match_info['time_source'] = 'estimated'
                        self.message_queue.put(("warning", f"时间抓取异常，使用估算时间: {e}"))

                    success = current_db.save_match_info(match_info)
                    if not success:
                        self.message_queue.put(("error", "保存比赛信息失败"))
                        return

                # 保存赔率数据
                if complete_data['odds_data']:
                    success = current_db.save_odds_data(match_id, complete_data['odds_data'])
                    if not success:
                        self.message_queue.put(("error", "保存赔率数据失败"))
                        return

                # 抓取并保存详细比赛数据
                if self.enhanced_system:
                    try:
                        self.message_queue.put(("status", "正在抓取详细比赛数据..."))
                        enhanced_result = self.enhanced_system.process_match_data(match_id, save_data=True)

                        if enhanced_result['success']:
                            data_summary = enhanced_result.get('data_summary', {})
                            completeness = data_summary.get('completeness_score', 0)
                            logger.info(f"详细数据抓取成功，完整度: {completeness:.1f}%")
                        else:
                            logger.warning(f"详细数据抓取失败: {enhanced_result.get('error', '未知错误')}")
                    except Exception as e:
                        logger.warning(f"详细数据抓取异常: {e}")

                # 发送成功消息
                summary = complete_data['summary']
                message = f"抓取完成！比赛信息已保存，获得 {summary['total_odds_records']} 条赔率记录"
                if self.enhanced_system:
                    message += "\n详细比赛数据也已抓取保存"
                self.message_queue.put(("success", message))
                self.message_queue.put(("refresh", None))

            else:
                self.message_queue.put(("error", "未能抓取到任何数据"))

        except Exception as e:
            self.message_queue.put(("error", f"抓取失败: {str(e)}"))

        finally:
            self.message_queue.put(("complete", None))

    def process_queue(self):
        """处理消息队列"""
        try:
            while True:
                message_type, data = self.message_queue.get_nowait()

                if message_type == "status":
                    self.progress_var.set(data)
                    self.status_var.set(data)
                elif message_type == "error":
                    self.progress_bar.stop()
                    self.scrape_button.config(state='normal')
                    self.progress_var.set("就绪")
                    self.status_var.set("就绪")
                    messagebox.showerror("错误", data)
                elif message_type == "success":
                    self.progress_bar.stop()
                    self.scrape_button.config(state='normal')
                    self.progress_var.set("就绪")
                    self.status_var.set(data)
                    messagebox.showinfo("成功", data)
                elif message_type == "complete":
                    self.progress_bar.stop()
                    self.scrape_button.config(state='normal')
                    self.progress_var.set("就绪")
                elif message_type == "refresh":
                    self.refresh_data()
                # 批量抓取相关消息
                elif message_type == "batch_status":
                    self.batch_progress_var.set(data)
                    self.status_var.set(data)
                elif message_type == "batch_error":
                    self.discover_button.config(state='normal')
                    self.batch_scrape_button.config(state='normal')
                    self.batch_progress_var.set("错误")
                    messagebox.showerror("批量抓取错误", data)
                elif message_type == "batch_complete":
                    self.discover_button.config(state='normal')
                    self.batch_scrape_button.config(state='normal')
                    self.batch_progress_var.set("完成")
                    self.batch_progress_bar['value'] = self.batch_progress_bar['maximum']
                    messagebox.showinfo("批量抓取完成", data)
                elif message_type == "discover_complete":
                    self.discover_button.config(state='normal')
                elif message_type == "rounds_discovered":
                    # 更新发现的轮次信息
                    pass
                elif message_type == "batch_progress":
                    self.batch_progress_bar['value'] = data
                elif message_type == "batch_result":
                    # 添加批量抓取结果到表格
                    # 处理不同数据结构的兼容性
                    if 'round' in data:
                        # 普通批量抓取的数据结构
                        self.batch_results_tree.insert("", "end", values=(
                            data['round'],
                            data['match_id'],
                            data['teams'],
                            data['match_time'],
                            data['score'],
                            data['status'],
                            data['odds_status']
                        ))
                    else:
                        # 按日期抓取的数据结构
                        teams = f"{data.get('home_team', '')} vs {data.get('away_team', '')}"
                        if 'league' in data and 'home_team' in data:
                            # 新的按日期抓取数据结构
                            self.batch_results_tree.insert("", "end", values=(
                                data.get('league', ''),  # 使用联赛名称作为轮次
                                data['match_id'],
                                teams,
                                data.get('match_time', ''),
                                '',  # 按日期抓取没有比分信息
                                data['status'],
                                f"赔率: {data.get('odds_count', 0)} 条" if 'odds_count' in data else "待抓取"
                            ))
                        else:
                            # 兼容其他可能的数据结构
                            self.batch_results_tree.insert("", "end", values=(
                                data.get('round', ''),
                                data.get('match_id', ''),
                                data.get('teams', teams),
                                data.get('match_time', ''),
                                data.get('score', ''),
                                data.get('status', ''),
                                data.get('odds_status', '')
                            ))
                elif message_type == "update_odds_status":
                    # 更新赔率状态
                    self.update_batch_result_odds_status(data['match_id'], data['status'])
                elif message_type == "ask_redownload":
                    # 询问是否重新下载
                    self.handle_redownload_question(data)
                elif message_type == "proxy_status":
                    # 更新代理状态显示
                    if hasattr(self, 'proxy_status_var'):
                        self.proxy_status_var.set(data)
                # 完赛后更新相关消息
                elif message_type == "update_finished_complete":
                    self.progress_bar.stop()
                    self.update_finished_button.config(state='normal')
                    self.scrape_button.config(state='normal')
                    self.progress_var.set("就绪")
                    messagebox.showinfo("完赛后更新完成", data)
                elif message_type == "update_finished_error":
                    self.progress_bar.stop()
                    self.update_finished_button.config(state='normal')
                    self.scrape_button.config(state='normal')
                    self.progress_var.set("就绪")
                    messagebox.showerror("完赛后更新错误", data)
                # 日期抓取相关消息
                elif message_type == "show_league_selection":
                    self.handle_league_selection(data)
                elif message_type == "date_scraping_complete":
                    self.date_scrape_button.config(state='normal')
                    self.batch_scrape_button.config(state='normal')
                    self.discover_button.config(state='normal')
                    self.batch_progress_var.set("就绪")
                    messagebox.showinfo("按日期抓取完成", data)
                elif message_type == "date_scraping_error":
                    self.date_scrape_button.config(state='normal')
                    self.batch_scrape_button.config(state='normal')
                    self.discover_button.config(state='normal')
                    self.batch_progress_var.set("就绪")
                    messagebox.showerror("按日期抓取错误", data)

        except queue.Empty:
            pass

        # 继续处理队列
        self.root.after(UI_CONFIG['refresh_interval'], self.process_queue)

    def update_batch_result_odds_status(self, match_id, status):
        """更新批量抓取结果中的赔率状态"""
        for item in self.batch_results_tree.get_children():
            values = self.batch_results_tree.item(item)['values']
            if values[1] == match_id:  # match_id在第2列
                # 更新赔率状态（第7列）
                new_values = list(values)
                new_values[6] = status
                self.batch_results_tree.item(item, values=new_values)
                break

    def handle_redownload_question(self, data):
        """处理重新下载询问"""
        match_id = data['match_id']
        message = data['message']
        data_summary = data['data_summary']
        max_companies = data['max_companies']
        delay = data['delay']

        # 停止进度条并恢复按钮状态
        self.progress_bar.stop()
        self.scrape_button.config(state='normal')
        self.progress_var.set("就绪")

        # 询问用户是否重新抓取
        result = messagebox.askyesno("数据已存在", message)

        if result:
            # 用户选择重新抓取
            self.scrape_button.config(state='disabled')
            self.progress_bar.start()
            self.progress_var.set("正在重新抓取数据...")

            # 在新线程中重新抓取
            thread = threading.Thread(
                target=self.force_scrape_worker,
                args=(match_id, max_companies, delay),
                daemon=True
            )
            thread.start()
        else:
            # 用户选择不重新抓取，显示现有数据
            message = f"使用现有数据：{data_summary['odds_count']} 条赔率记录"
            self.status_var.set(message)
            messagebox.showinfo("使用现有数据", message)
            self.refresh_data()

    def force_scrape_worker(self, match_id, max_companies, delay):
        """强制重新抓取工作线程（跳过重复检查）"""
        try:
            # 获取当前选择的数据库
            current_db = self.get_current_database()

            # 发送状态更新
            self.message_queue.put(("status", "正在重新抓取比赛信息..."))

            # 抓取完整数据
            complete_data = self.scraper.scrape_complete_match_data(
                match_id=match_id,
                max_companies=max_companies,
                delay=delay
            )

            if complete_data['match_info'] or complete_data['odds_data']:
                # 保存到数据库
                self.message_queue.put(("status", "正在保存到数据库..."))

                # 保存比赛信息
                if complete_data['match_info']:
                    # 添加赛季信息
                    match_info = complete_data['match_info'].copy()
                    if 'season' not in match_info or not match_info['season']:
                        match_info['season'] = get_season_for_match(match_id=match_id)

                    # 添加准确的时间信息
                    self.message_queue.put(("info", f"正在抓取比赛 {match_id} 的准确时间信息..."))
                    try:
                        time_info = self.time_scraper.scrape_match_time(match_id)
                        if not time_info.get('error'):
                            match_info.update({
                                'accurate_datetime': time_info.get('full_datetime'),
                                'accurate_date': time_info.get('match_date'),
                                'accurate_time': time_info.get('match_time'),
                                'weekday': time_info.get('weekday'),
                                'match_year': time_info.get('year'),
                                'match_month': time_info.get('month'),
                                'match_day': time_info.get('day'),
                                'match_hour': time_info.get('hour'),
                                'match_minute': time_info.get('minute'),
                                'time_source': 'analysis'
                            })
                            self.message_queue.put(("info", f"成功获取准确时间: {time_info.get('full_datetime')}"))
                        else:
                            match_info['time_source'] = 'estimated'
                            self.message_queue.put(("warning", f"时间抓取失败，使用估算时间: {time_info.get('error')}"))
                    except Exception as e:
                        match_info['time_source'] = 'estimated'
                        self.message_queue.put(("warning", f"时间抓取异常，使用估算时间: {e}"))

                    success = current_db.save_match_info(match_info)
                    if not success:
                        self.message_queue.put(("error", "保存比赛信息失败"))
                        return

                # 保存赔率数据
                if complete_data['odds_data']:
                    success = current_db.save_odds_data(match_id, complete_data['odds_data'])
                    if not success:
                        self.message_queue.put(("error", "保存赔率数据失败"))
                        return

                # 抓取并保存详细比赛数据
                if self.enhanced_system:
                    try:
                        self.message_queue.put(("status", "正在重新抓取详细比赛数据..."))
                        enhanced_result = self.enhanced_system.process_match_data(match_id, save_data=True)

                        if enhanced_result['success']:
                            data_summary = enhanced_result.get('data_summary', {})
                            completeness = data_summary.get('completeness_score', 0)
                            logger.info(f"详细数据重新抓取成功，完整度: {completeness:.1f}%")
                        else:
                            logger.warning(f"详细数据重新抓取失败: {enhanced_result.get('error', '未知错误')}")
                    except Exception as e:
                        logger.warning(f"详细数据重新抓取异常: {e}")

                # 发送成功消息
                summary = complete_data['summary']
                message = f"重新抓取完成！比赛信息已更新，获得 {summary['total_odds_records']} 条赔率记录"
                if self.enhanced_system:
                    message += "\n详细比赛数据也已重新抓取保存"
                self.message_queue.put(("success", message))
                self.message_queue.put(("refresh", None))

            else:
                self.message_queue.put(("error", "未能抓取到任何数据"))

        except Exception as e:
            self.message_queue.put(("error", f"重新抓取失败: {str(e)}"))

        finally:
            self.message_queue.put(("complete", None))

    def refresh_data(self):
        """刷新数据显示"""
        try:
            # 刷新比赛列表
            self.refresh_matches_list()

            # 刷新数据库统计
            self.refresh_database_stats()

            # 更新状态栏数据库信息
            self.update_database_info()

        except Exception as e:
            logger.error(f"刷新数据失败: {e}")

    def refresh_matches_list(self):
        """刷新比赛列表"""
        # 如果有筛选结果，使用筛选结果刷新
        if self.filtered_matches:
            self.refresh_matches_list_with_filter()
            return

        # 清空现有数据
        for item in self.matches_tree.get_children():
            self.matches_tree.delete(item)

        # 获取当前选择的数据库
        current_db = self.get_current_database()

        # 获取比赛数据
        matches = current_db.get_all_matches()

        for match in matches:
            teams = f"{match.get('home_team', '')} vs {match.get('away_team', '')}"
            score = f"{match.get('home_score', '')}-{match.get('away_score', '')}" if match.get('home_score') else ""

            # 获取该比赛的博彩公司数量
            match_id = match.get('match_id', '')
            data_summary = current_db.get_match_data_summary(match_id)
            company_count = data_summary.get('company_count', 0)

            # 格式化博彩公司数量显示
            if company_count > 0:
                companies_display = f"{company_count}家"
            else:
                companies_display = "无数据"

            # 格式化轮次信息
            round_info = match.get('round_info', '')
            if round_info:
                # 提取轮次数字，例如从"第5轮&nbsp05-25"中提取"第5轮"
                import re
                round_match = re.search(r'第(\d+)轮', round_info)
                if round_match:
                    round_display = f"第{round_match.group(1)}轮"
                else:
                    round_display = round_info[:10] if len(round_info) > 10 else round_info
            else:
                round_display = "未知"

            # 获取赛季信息
            season_info = match.get('season', '')
            if not season_info:
                # 如果没有赛季信息，根据ID规则推断
                try:
                    match_id_int = int(match_id)
                    if match_id_int > 2700000:
                        season_info = "2025"
                    else:
                        season_info = "2024"
                except (ValueError, TypeError):
                    season_info = "未知"

            # 获取准确的时间信息
            display_time = match.get('match_time', '')
            accurate_datetime = match.get('accurate_datetime', '')
            accurate_date = match.get('accurate_date', '')
            accurate_time = match.get('accurate_time', '')
            weekday = match.get('weekday', '')
            time_source = match.get('time_source', '')

            # 优先显示准确时间
            if accurate_datetime:
                if weekday:
                    display_time = f"{accurate_date} {accurate_time} {weekday}"
                else:
                    display_time = f"{accurate_date} {accurate_time}"
                # 如果是从分析页面获取的时间，添加标记
                if time_source == 'analysis':
                    display_time += " ✓"
            elif accurate_date and accurate_time:
                display_time = f"{accurate_date} {accurate_time}"
                if time_source == 'analysis':
                    display_time += " ✓"

            self.matches_tree.insert("", "end", values=(
                match_id,
                match.get('league', ''),
                season_info,
                round_display,
                teams,
                display_time,
                score,
                match.get('match_state', ''),
                companies_display
            ))

    def refresh_database_stats(self):
        """刷新数据库统计"""
        current_db = self.get_current_database()
        stats = current_db.get_database_stats()

        if stats:
            db_path = self.current_database_path if self.current_database_path else "odds_data.db"
            stats_text = f"""
数据库统计信息
================

当前数据库: {os.path.basename(db_path)}
比赛数量: {stats['match_count']}
赔率记录数: {stats['odds_count']}
博彩公司数: {stats['company_count']}
数据库大小: {stats['db_size_mb']} MB

数据库文件: {db_path}
最后更新: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
"""
        else:
            stats_text = "无法获取数据库统计信息"

        self.stats_text.delete(1.0, tk.END)
        self.stats_text.insert(1.0, stats_text)

    def on_match_double_click(self, event):
        """处理比赛列表双击事件"""
        selection = self.matches_tree.selection()
        if selection:
            item = self.matches_tree.item(selection[0])
            match_id = item['values'][0]
            self.show_match_details(match_id)

    def show_match_details(self, match_id):
        """显示比赛详情"""
        try:
            # 获取当前选择的数据库
            current_db = self.get_current_database()

            # 获取比赛信息
            match_info = current_db.get_match_info(match_id)
            odds_data = current_db.get_odds_data(match_id)

            # 显示比赛信息
            if match_info:
                info_text = f"""比赛ID: {match_info.get('match_id', '')}
联赛: {match_info.get('league', '')} {match_info.get('round_info', '')}
对阵: {match_info.get('home_team', '')} vs {match_info.get('away_team', '')}
时间: {match_info.get('match_time', '')}
比分: {match_info.get('home_score', '')}-{match_info.get('away_score', '')} (半场: {match_info.get('half_score', '')})
状态: {match_info.get('match_state', '')}
天气: {match_info.get('weather', '')} {match_info.get('temperature', '')}
原始信息: {match_info.get('raw_league_text', '')}"""
            else:
                info_text = "未找到比赛信息"

            self.match_info_text.delete(1.0, tk.END)
            self.match_info_text.insert(1.0, info_text)

            # 显示赔率数据
            for item in self.odds_tree.get_children():
                self.odds_tree.delete(item)

            for odds in odds_data:
                self.odds_tree.insert("", "end", values=(
                    odds.get('company_name', ''),
                    odds.get('date', ''),
                    odds.get('time', ''),
                    f"{odds.get('home_odds', ''):.2f}" if odds.get('home_odds') else '',
                    f"{odds.get('draw_odds', ''):.2f}" if odds.get('draw_odds') else '',
                    f"{odds.get('away_odds', ''):.2f}" if odds.get('away_odds') else '',
                    f"{odds.get('return_rate', ''):.2f}%" if odds.get('return_rate') else '',
                    f"{odds.get('kelly_home', ''):.2f}" if odds.get('kelly_home') else '',
                    f"{odds.get('kelly_draw', ''):.2f}" if odds.get('kelly_draw') else '',
                    f"{odds.get('kelly_away', ''):.2f}" if odds.get('kelly_away') else ''
                ))

            # 切换到详情页面
            self.switch_to_tab("比赛详情")

            # 更新当前选中的比赛ID用于时间线图表
            self.current_timeline_match_id = match_id

            # 显示详细数据
            self.show_enhanced_details(match_id)

        except Exception as e:
            messagebox.showerror("错误", f"显示比赛详情失败: {e}")

    def delete_selected_match(self):
        """删除选中的比赛"""
        selection = self.matches_tree.selection()
        if not selection:
            messagebox.showwarning("警告", "请先选择要删除的比赛")
            return

        # 获取所有选中的比赛信息
        selected_matches = []
        for item_id in selection:
            item = self.matches_tree.item(item_id)
            match_id = item['values'][0]
            teams = item['values'][3]
            selected_matches.append({'id': match_id, 'teams': teams})

        # 构建确认消息
        if len(selected_matches) == 1:
            match = selected_matches[0]
            confirm_msg = f"确定要删除比赛 {match['id']} ({match['teams']}) 及其所有数据吗？"
        else:
            match_list = "\n".join([f"  - {match['id']} ({match['teams']})" for match in selected_matches])
            confirm_msg = f"确定要删除以下 {len(selected_matches)} 场比赛及其所有数据吗？\n\n{match_list}"

        # 确认删除
        if messagebox.askyesno("确认删除", confirm_msg):
            deleted_count = 0
            failed_matches = []

            # 获取当前选择的数据库
            current_db = self.get_current_database()

            # 逐个删除比赛
            for match in selected_matches:
                try:
                    if current_db.delete_match(match['id']):
                        deleted_count += 1
                    else:
                        failed_matches.append(match)
                except Exception as e:
                    logger.error(f"删除比赛 {match['id']} 失败: {e}")
                    failed_matches.append(match)

            # 显示结果
            if deleted_count > 0:
                # 清除筛选缓存，确保刷新时重新查询数据库
                if hasattr(self, 'filtered_matches') and self.filtered_matches:
                    # 从筛选缓存中移除已删除的比赛
                    deleted_match_ids = [match['id'] for match in selected_matches if match['id'] not in [fm['id'] for fm in failed_matches]]
                    self.filtered_matches = [m for m in self.filtered_matches if m.get('match_id') not in deleted_match_ids]

                    # 如果筛选缓存为空，清除筛选状态
                    if not self.filtered_matches:
                        self.filtered_matches = None
                        # 重置筛选UI状态
                        if hasattr(self, 'clear_filter_button'):
                            self.clear_filter_button.config(state='disabled')

                if failed_matches:
                    failed_list = "\n".join([f"  - {match['id']} ({match['teams']})" for match in failed_matches])
                    messagebox.showwarning("部分删除成功",
                        f"成功删除 {deleted_count} 场比赛\n\n删除失败的比赛：\n{failed_list}")
                else:
                    messagebox.showinfo("删除成功", f"成功删除 {deleted_count} 场比赛")

                # 刷新数据
                self.refresh_data()
            else:
                messagebox.showerror("删除失败", "所有比赛删除失败")

    def export_selected_match(self):
        """导出选中比赛的数据"""
        selection = self.matches_tree.selection()
        if not selection:
            messagebox.showwarning("警告", "请先选择要导出的比赛")
            return

        item = self.matches_tree.item(selection[0])
        match_id = item['values'][0]

        # 选择导出目录
        output_dir = filedialog.askdirectory(title="选择导出目录")
        if output_dir:
            # 获取当前选择的数据库
            current_db = self.get_current_database()
            files = current_db.export_to_csv(match_id, output_dir)
            if files:
                message = "导出成功！\n" + "\n".join([f"{k}: {v}" for k, v in files.items()])
                messagebox.showinfo("导出成功", message)
            else:
                messagebox.showerror("错误", "导出失败")

    def open_selected_match_url(self):
        """打开选中比赛的网址"""
        selection = self.matches_tree.selection()
        if not selection:
            messagebox.showwarning("警告", "请先选择要打开的比赛")
            return

        item = self.matches_tree.item(selection[0])
        match_id = item['values'][0]
        teams = item['values'][3]

        # 构造比赛分析页面URL
        match_url = f"https://m.titan007.com/analy/Analysis/{match_id}.htm"

        try:
            # 使用系统默认浏览器打开URL
            webbrowser.open(match_url)

            # 更新状态栏
            self.status_var.set(f"已打开比赛 {match_id} ({teams}) 的网址")

        except Exception as e:
            logger.error(f"打开比赛网址失败: {e}")
            messagebox.showerror("错误", f"打开网址失败: {e}\n\n网址：{match_url}")

    def get_match_url(self, match_id: str) -> str:
        """根据比赛ID生成比赛分析页面URL"""
        return f"https://m.titan007.com/analy/Analysis/{match_id}.htm"

    def get_match_odds_url(self, match_id: str) -> str:
        """根据比赛ID生成赔率页面URL"""
        return f"https://m.titan007.com/odds/{match_id}"

    def refresh_enhanced_details(self):
        """刷新详细数据显示"""
        selection = self.matches_tree.selection()
        if not selection:
            messagebox.showwarning("警告", "请先选择一场比赛")
            return

        item = self.matches_tree.item(selection[0])
        match_id = item['values'][0]
        self.show_enhanced_details(match_id)

    def scrape_enhanced_details(self):
        """抓取选中比赛的详细数据"""
        selection = self.matches_tree.selection()
        if not selection:
            messagebox.showwarning("警告", "请先选择一场比赛")
            return

        if not self.enhanced_system:
            messagebox.showerror("错误", "增强数据系统未初始化")
            return

        item = self.matches_tree.item(selection[0])
        match_id = item['values'][0]
        teams = item['values'][3]

        # 在新线程中抓取详细数据
        def scrape_worker():
            try:
                self.message_queue.put(("status", f"正在抓取比赛 {match_id} 的详细数据..."))
                result = self.enhanced_system.process_match_data(match_id, save_data=True)

                if result['success']:
                    data_summary = result.get('data_summary', {})
                    completeness = data_summary.get('completeness_score', 0)
                    message = f"详细数据抓取完成！\n比赛: {teams}\n完整度: {completeness:.1f}%"
                    self.message_queue.put(("success", message))
                    # 刷新详细数据显示
                    self.show_enhanced_details(match_id)
                else:
                    error_msg = result.get('error', '未知错误')
                    self.message_queue.put(("error", f"详细数据抓取失败: {error_msg}"))

            except Exception as e:
                self.message_queue.put(("error", f"详细数据抓取异常: {e}"))

        thread = threading.Thread(target=scrape_worker, daemon=True)
        thread.start()

    def show_enhanced_details(self, match_id):
        """显示详细数据"""
        if not self.enhanced_system:
            self.tech_stats_text.delete(1.0, tk.END)
            self.tech_stats_text.insert(1.0, "增强数据系统未初始化")
            return

        try:
            # 获取详细数据
            enhanced_data = self.enhanced_system.data_saver.get_extended_match_data(match_id)

            if 'error' in enhanced_data:
                error_text = f"获取详细数据失败: {enhanced_data['error']}"
                self.tech_stats_text.delete(1.0, tk.END)
                self.tech_stats_text.insert(1.0, error_text)
                return

            # 显示技术统计
            self.display_tech_stats(enhanced_data)

            # 显示阵容信息
            self.display_lineup_info(enhanced_data)

            # 显示球员统计
            self.display_player_stats(enhanced_data)

            # 显示进失球概率
            self.display_goal_probability(enhanced_data)

        except Exception as e:
            error_text = f"显示详细数据异常: {e}"
            self.tech_stats_text.delete(1.0, tk.END)
            self.tech_stats_text.insert(1.0, error_text)

    def display_tech_stats(self, enhanced_data):
        """显示技术统计数据"""
        try:
            # 使用新的增强显示组件
            if hasattr(self, 'enhanced_stats_display'):
                self.enhanced_stats_display.display_stats(enhanced_data)
            else:
                # 备用：使用原来的文本显示
                self.display_tech_stats_fallback(enhanced_data)

        except Exception as e:
            logger.error(f"显示技术统计失败: {e}")
            # 出错时使用备用显示
            self.display_tech_stats_fallback(enhanced_data)

    def display_tech_stats_fallback(self, enhanced_data):
        """备用的技术统计显示方法"""
        if not hasattr(self, 'tech_stats_text'):
            return

        self.tech_stats_text.delete(1.0, tk.END)

        stats_text = "📊 技术统计数据\n"
        stats_text += "=" * 50 + "\n\n"

        # 显示当前技术统计
        if 'technical_stats_current' in enhanced_data:
            stats_text += "🔥 当前比赛技术统计:\n"
            stats_text += "-" * 30 + "\n"
            current_stats = enhanced_data['technical_stats_current']

            for stat_name, stat_data in current_stats.items():
                home_value = stat_data.get('home', 'N/A')
                away_value = stat_data.get('away', 'N/A')
                stats_text += f"{stat_name:15} | 主队: {home_value:8} | 客队: {away_value:8}\n"
            stats_text += "\n"

        # 显示历史统计
        if 'technical_stats_historical' in enhanced_data:
            stats_text += "📈 历史平均统计:\n"
            stats_text += "-" * 30 + "\n"
            historical_stats = enhanced_data['technical_stats_historical']

            for stat_name, stat_data in historical_stats.items():
                home_value = stat_data.get('home', 'N/A')
                away_value = stat_data.get('away', 'N/A')
                stats_text += f"{stat_name:15} | 主队: {home_value:8} | 客队: {away_value:8}\n"
            stats_text += "\n"

        if not any(key.startswith('technical_stats_') for key in enhanced_data.keys()):
            stats_text += "❌ 暂无技术统计数据\n"
            stats_text += "请点击'抓取详细数据'按钮获取数据"

        self.tech_stats_text.insert(1.0, stats_text)

    def display_lineup_info(self, enhanced_data):
        """显示阵容信息"""
        try:
            # 使用新的增强显示组件
            if hasattr(self, 'enhanced_lineup_display'):
                self.enhanced_lineup_display.display_lineup(enhanced_data)
            else:
                # 备用：使用原来的文本显示
                self.display_lineup_info_fallback(enhanced_data)

        except Exception as e:
            logger.error(f"显示阵容信息失败: {e}")
            # 出错时使用备用显示
            self.display_lineup_info_fallback(enhanced_data)

    def display_lineup_info_fallback(self, enhanced_data):
        """备用的阵容信息显示方法"""
        if not hasattr(self, 'lineup_text'):
            return

        self.lineup_text.delete(1.0, tk.END)

        lineup_text = "👥 阵容信息\n"
        lineup_text += "=" * 50 + "\n\n"

        if 'lineups' in enhanced_data:
            lineups = enhanced_data['lineups']

            # 主队阵容
            if 'home_lineup' in lineups:
                home_lineup = lineups['home_lineup']
                home_formation = lineups.get('home_formation', '未知')

                lineup_text += f"🏠 主队阵容 (阵型: {home_formation})\n"
                lineup_text += "-" * 30 + "\n"

                # 首发球员
                if 'players' in home_lineup:
                    lineup_text += "首发球员:\n"
                    for i, player in enumerate(home_lineup['players'], 1):
                        name = player.get('name', '未知')
                        number = player.get('number', '')
                        lineup_text += f"  {i:2}. {number:3} {name}\n"

                # 替补球员
                if 'substitutes' in home_lineup:
                    lineup_text += "\n替补球员:\n"
                    for i, player in enumerate(home_lineup['substitutes'], 1):
                        name = player.get('name', '未知')
                        number = player.get('number', '')
                        lineup_text += f"  {i:2}. {number:3} {name}\n"

                # 教练
                if 'coach' in home_lineup:
                    lineup_text += f"\n教练: {home_lineup['coach']}\n"

                lineup_text += "\n"

            # 客队阵容
            if 'away_lineup' in lineups:
                away_lineup = lineups['away_lineup']
                away_formation = lineups.get('away_formation', '未知')

                lineup_text += f"✈️ 客队阵容 (阵型: {away_formation})\n"
                lineup_text += "-" * 30 + "\n"

                # 首发球员
                if 'players' in away_lineup:
                    lineup_text += "首发球员:\n"
                    for i, player in enumerate(away_lineup['players'], 1):
                        name = player.get('name', '未知')
                        number = player.get('number', '')
                        lineup_text += f"  {i:2}. {number:3} {name}\n"

                # 替补球员
                if 'substitutes' in away_lineup:
                    lineup_text += "\n替补球员:\n"
                    for i, player in enumerate(away_lineup['substitutes'], 1):
                        name = player.get('name', '未知')
                        number = player.get('number', '')
                        lineup_text += f"  {i:2}. {number:3} {name}\n"

                # 教练
                if 'coach' in away_lineup:
                    lineup_text += f"\n教练: {away_lineup['coach']}\n"
        else:
            lineup_text += "❌ 暂无阵容信息\n"
            lineup_text += "请点击'抓取详细数据'按钮获取数据"

        self.lineup_text.insert(1.0, lineup_text)

    def display_player_stats(self, enhanced_data):
        """显示球员统计数据"""
        self.player_stats_text.delete(1.0, tk.END)

        player_text = "🏃‍♂️ 球员统计数据\n"
        player_text += "=" * 50 + "\n\n"

        if 'player_stats' in enhanced_data:
            player_stats = enhanced_data['player_stats']

            # 主队球员
            if 'home' in player_stats and player_stats['home']:
                player_text += "🏠 主队球员统计:\n"
                player_text += "-" * 30 + "\n"

                for i, player in enumerate(player_stats['home'], 1):
                    name = player.get('playerName', '未知')
                    number = player.get('playerNum', '')
                    position = ''

                    # 提取位置信息
                    tech_infos = player.get('techInfos', [])
                    for info in tech_infos:
                        if info.get('infoKind') == 'Position':
                            position = info.get('infoValue', '')
                            break

                    player_text += f"  {i:2}. {number:3} {name:15} ({position})\n"

                    # 显示部分技术统计
                    for info in tech_infos[:5]:  # 只显示前5项
                        kind = info.get('infoKind', '')
                        value = info.get('infoValue', '')
                        if kind and kind != 'Position':
                            player_text += f"      {kind}: {value}\n"

                    player_text += "\n"

            # 客队球员
            if 'away' in player_stats and player_stats['away']:
                player_text += "✈️ 客队球员统计:\n"
                player_text += "-" * 30 + "\n"

                for i, player in enumerate(player_stats['away'], 1):
                    name = player.get('playerName', '未知')
                    number = player.get('playerNum', '')
                    position = ''

                    # 提取位置信息
                    tech_infos = player.get('techInfos', [])
                    for info in tech_infos:
                        if info.get('infoKind') == 'Position':
                            position = info.get('infoValue', '')
                            break

                    player_text += f"  {i:2}. {number:3} {name:15} ({position})\n"

                    # 显示部分技术统计
                    for info in tech_infos[:5]:  # 只显示前5项
                        kind = info.get('infoKind', '')
                        value = info.get('infoValue', '')
                        if kind and kind != 'Position':
                            player_text += f"      {kind}: {value}\n"

                    player_text += "\n"
        else:
            player_text += "❌ 暂无球员统计数据\n"
            player_text += "请点击'抓取详细数据'按钮获取数据"

        self.player_stats_text.insert(1.0, player_text)

    def display_goal_probability(self, enhanced_data):
        """显示进失球概率数据"""
        try:
            # 使用新的增强显示组件
            if hasattr(self, 'enhanced_probability_display'):
                self.enhanced_probability_display.display_probability(enhanced_data)
            else:
                # 备用：使用原来的文本显示
                self.display_goal_probability_fallback(enhanced_data)

        except Exception as e:
            logger.error(f"显示进失球概率失败: {e}")
            # 出错时使用备用显示
            self.display_goal_probability_fallback(enhanced_data)

    def display_goal_probability_fallback(self, enhanced_data):
        """备用的进失球概率显示方法"""
        if not hasattr(self, 'goal_prob_text'):
            return

        self.goal_prob_text.delete(1.0, tk.END)

        prob_text = "⚽ 进失球概率分析\n"
        prob_text += "=" * 50 + "\n\n"

        if 'goal_probabilities' in enhanced_data:
            probabilities = enhanced_data['goal_probabilities']

            for prob_type, prob_data in probabilities.items():
                prob_text += f"📊 {prob_type} 概率分析:\n"
                prob_text += "-" * 30 + "\n"

                if isinstance(prob_data, dict):
                    for key, value in prob_data.items():
                        prob_text += f"{key}: {value}\n"
                else:
                    prob_text += f"{prob_data}\n"

                prob_text += "\n"
        else:
            prob_text += "❌ 暂无进失球概率数据\n"
            prob_text += "请点击'抓取详细数据'按钮获取数据"

        self.goal_prob_text.insert(1.0, prob_text)

    def refresh_enhanced_stats_display(self):
        """刷新增强统计显示"""
        try:
            # 获取当前选中的比赛
            selection = self.matches_tree.selection()
            if not selection:
                return

            item = self.matches_tree.item(selection[0])
            match_id = item['values'][0]

            # 获取详细数据并刷新显示
            if self.enhanced_system:
                enhanced_data = self.enhanced_system.data_saver.get_extended_match_data(match_id)
                if hasattr(self, 'enhanced_stats_display'):
                    self.enhanced_stats_display.display_stats(enhanced_data)

        except Exception as e:
            logger.error(f"刷新增强统计显示失败: {e}")

    def refresh_enhanced_lineup_display(self):
        """刷新增强阵容显示"""
        try:
            # 获取当前选中的比赛
            selection = self.matches_tree.selection()
            if not selection:
                return

            item = self.matches_tree.item(selection[0])
            match_id = item['values'][0]

            # 获取详细数据并刷新显示
            if self.enhanced_system:
                enhanced_data = self.enhanced_system.data_saver.get_extended_match_data(match_id)
                if hasattr(self, 'enhanced_lineup_display'):
                    self.enhanced_lineup_display.display_lineup(enhanced_data)

        except Exception as e:
            logger.error(f"刷新增强阵容显示失败: {e}")

    def refresh_enhanced_probability_display(self):
        """刷新增强概率显示"""
        try:
            # 获取当前选中的比赛
            selection = self.matches_tree.selection()
            if not selection:
                return

            item = self.matches_tree.item(selection[0])
            match_id = item['values'][0]

            # 获取详细数据并刷新显示
            if self.enhanced_system:
                enhanced_data = self.enhanced_system.data_saver.get_extended_match_data(match_id)
                if hasattr(self, 'enhanced_probability_display'):
                    self.enhanced_probability_display.display_probability(enhanced_data)

        except Exception as e:
            logger.error(f"刷新增强概率显示失败: {e}")

    def generate_timeline(self):
        """生成时间线图表"""
        # 总是从当前选中的比赛获取ID，确保使用最新选择的比赛
        selection = self.matches_tree.selection()
        if selection:
            item = self.matches_tree.item(selection[0])
            self.current_timeline_match_id = item['values'][0]
        else:
            messagebox.showwarning("警告", "请先选择一场比赛")
            return

        try:
            # 获取比赛信息用于显示
            match_info = self.database.get_match_info(self.current_timeline_match_id)
            if match_info:
                teams = f"{match_info.get('home_team', '')} vs {match_info.get('away_team', '')}"
                self.timeline_status_var.set(f"正在生成时间线图表... ({teams})")
            else:
                self.timeline_status_var.set("正在生成时间线图表...")

            # 获取赔率数据
            odds_data = self.database.get_odds_data(self.current_timeline_match_id)

            if not match_info:
                messagebox.showerror("错误", "未找到比赛信息")
                self.timeline_status_var.set("生成失败：未找到比赛信息")
                return

            if not odds_data:
                messagebox.showerror("错误", "未找到赔率数据")
                self.timeline_status_var.set("生成失败：未找到赔率数据")
                return

            # 清除动态图表
            self.dynamic_timeline_chart.clear_chart()

            # 生成时间线图表
            success = self.timeline_chart.create_timeline_chart(match_info, odds_data)

            if success:
                self.current_chart_mode = "static"
                teams = f"{match_info.get('home_team', '')} vs {match_info.get('away_team', '')}"
                self.timeline_status_var.set(f"时间线图表生成成功 - {teams} - {len(odds_data)} 条赔率记录")

                # 自动切换到时间线标签页
                self.switch_to_tab("开盘时间线")

            else:
                self.timeline_status_var.set("时间线图表生成失败")
                messagebox.showerror("错误", "时间线图表生成失败")

        except Exception as e:
            error_msg = f"生成时间线图表时出错: {e}"
            logger.error(error_msg)
            self.timeline_status_var.set("生成失败")
            messagebox.showerror("错误", error_msg)

    def generate_dynamic_timeline(self):
        """生成动态时间线图表"""
        # 总是从当前选中的比赛获取ID，确保使用最新选择的比赛
        selection = self.matches_tree.selection()
        if selection:
            item = self.matches_tree.item(selection[0])
            self.current_timeline_match_id = item['values'][0]
        else:
            messagebox.showwarning("警告", "请先选择一场比赛")
            return

        try:
            # 获取比赛信息用于显示
            match_info = self.database.get_match_info(self.current_timeline_match_id)
            if match_info:
                teams = f"{match_info.get('home_team', '')} vs {match_info.get('away_team', '')}"
                self.timeline_status_var.set(f"正在生成动态时间线图表... ({teams})")
            else:
                self.timeline_status_var.set("正在生成动态时间线图表...")

            # 获取赔率数据
            odds_data = self.database.get_odds_data(self.current_timeline_match_id)

            if not match_info:
                messagebox.showerror("错误", "未找到比赛信息")
                self.timeline_status_var.set("生成失败：未找到比赛信息")
                return

            if not odds_data:
                messagebox.showerror("错误", "未找到赔率数据")
                self.timeline_status_var.set("生成失败：未找到赔率数据")
                return

            # 清除静态图表
            self.timeline_chart.clear_chart()

            # 生成动态时间线图表
            success = self.dynamic_timeline_chart.create_dynamic_timeline_chart(match_info, odds_data)

            if success:
                self.current_chart_mode = "dynamic"
                teams = f"{match_info.get('home_team', '')} vs {match_info.get('away_team', '')}"
                self.timeline_status_var.set(f"动态时间线图表生成成功 - {teams} - {len(odds_data)} 条赔率记录")

                # 自动切换到时间线标签页
                self.switch_to_tab("开盘时间线")

            else:
                self.timeline_status_var.set("动态时间线图表生成失败")
                messagebox.showerror("错误", "动态时间线图表生成失败")

        except Exception as e:
            error_msg = f"生成动态时间线图表时出错: {e}"
            logger.error(error_msg)
            self.timeline_status_var.set("生成失败")
            messagebox.showerror("错误", error_msg)

    def save_timeline_chart(self):
        """保存时间线图表"""
        current_chart = None
        chart_type = ""

        if self.current_chart_mode == "dynamic" and self.dynamic_timeline_chart.figure:
            current_chart = self.dynamic_timeline_chart
            chart_type = "动态"
        elif self.current_chart_mode == "static" and self.timeline_chart.figure:
            current_chart = self.timeline_chart
            chart_type = "静态"

        if not current_chart:
            messagebox.showwarning("警告", "请先生成时间线图表")
            return

        # 选择保存位置
        filename = filedialog.asksaveasfilename(
            title="保存时间线图表",
            defaultextension=".png",
            filetypes=[
                ("PNG图片", "*.png"),
                ("JPG图片", "*.jpg"),
                ("PDF文件", "*.pdf"),
                ("所有文件", "*.*")
            ]
        )

        if filename:
            success = current_chart.save_chart(filename)
            if success:
                messagebox.showinfo("成功", f"{chart_type}时间线图表已保存到:\n{filename}")
            else:
                messagebox.showerror("错误", f"保存{chart_type}时间线图表失败")

    def clear_timeline_chart(self):
        """清除时间线图表"""
        self.timeline_chart.clear_chart()
        self.dynamic_timeline_chart.clear_chart()
        self.timeline_status_var.set("图表已清除")
        self.current_timeline_match_id = None
        self.current_chart_mode = "static"

    def discover_league_rounds(self):
        """发现联赛轮次"""
        try:
            league_url = self.league_url_var.get().strip()
            if not league_url:
                messagebox.showerror("错误", "请输入联赛URL")
                return

            max_rounds = int(self.max_rounds_var.get())

            self.discover_button.config(state='disabled')
            self.batch_progress_var.set("正在发现联赛轮次...")

            # 在新线程中执行发现
            thread = threading.Thread(
                target=self.discover_rounds_worker,
                args=(league_url, max_rounds),
                daemon=True
            )
            thread.start()

        except ValueError:
            messagebox.showerror("错误", "请输入有效的最大轮次数")

    def discover_rounds_worker(self, league_url, max_rounds):
        """发现轮次的工作线程"""
        try:
            self.discovered_rounds = self.league_extractor.discover_available_rounds(league_url, max_rounds)

            if self.discovered_rounds:
                message = f"发现 {len(self.discovered_rounds)} 个轮次: {self.discovered_rounds}"
                self.message_queue.put(("batch_status", message))
                self.message_queue.put(("rounds_discovered", self.discovered_rounds))
            else:
                self.message_queue.put(("batch_error", "未发现任何可用轮次"))

        except Exception as e:
            self.message_queue.put(("batch_error", f"发现轮次失败: {e}"))
        finally:
            self.message_queue.put(("discover_complete", None))

    def start_batch_scraping(self):
        """开始批量抓取"""
        try:
            league_url = self.league_url_var.get().strip()
            if not league_url:
                messagebox.showerror("错误", "请输入联赛URL")
                return

            # 获取抓取参数
            mode = self.batch_mode_var.get()
            network_mode = self.network_mode_var.get()
            auto_scrape_odds = self.auto_scrape_odds_var.get()
            auto_scrape_enhanced = self.auto_scrape_enhanced_var.get()
            max_companies = int(self.batch_max_companies_var.get())
            delay = float(self.batch_delay_var.get())
            concurrent_threads = int(self.concurrent_threads_var.get())

            # 检查并发模式的代理配置
            if network_mode == "concurrent":
                if not self.proxy_list:
                    messagebox.showerror("错误", "并发模式需要先获取代理IP池")
                    return

                available_proxies = [
                    proxy for proxy in self.proxy_list
                    if self.proxy_failure_count.get(f"{proxy['ip']}:{proxy['port']}", 0) < 10
                ]

                if len(available_proxies) == 0:
                    messagebox.showerror("错误", "没有可用的代理IP")
                    return

            # 确定要抓取的轮次
            if mode == "auto":
                if not self.discovered_rounds:
                    # 如果没有发现轮次，先自动发现
                    max_rounds = int(self.max_rounds_var.get())
                    self.discovered_rounds = self.league_extractor.discover_available_rounds(league_url, max_rounds)
                    if not self.discovered_rounds:
                        messagebox.showerror("错误", "未发现任何可用轮次，请先点击'发现轮次'")
                        return
                rounds_to_scrape = self.discovered_rounds
            else:
                start_round = int(self.start_round_var.get())
                end_round = int(self.end_round_var.get())
                rounds_to_scrape = list(range(start_round, end_round + 1))

            # 清空结果表格
            self.clear_batch_results()

            # 禁用按钮
            self.batch_scrape_button.config(state='disabled')
            self.discover_button.config(state='disabled')
            self.batch_stop_flag = False

            # 设置进度条
            self.batch_progress_bar.config(maximum=len(rounds_to_scrape))
            self.batch_progress_bar['value'] = 0

            # 在新线程中执行批量抓取
            if network_mode == "concurrent":
                thread = threading.Thread(
                    target=self.concurrent_batch_scraping_worker,
                    args=(league_url, rounds_to_scrape, auto_scrape_odds, auto_scrape_enhanced, max_companies, delay, concurrent_threads),
                    daemon=True
                )
            else:
                thread = threading.Thread(
                    target=self.batch_scraping_worker,
                    args=(league_url, rounds_to_scrape, auto_scrape_odds, auto_scrape_enhanced, max_companies, delay),
                    daemon=True
                )
            thread.start()

        except ValueError:
            messagebox.showerror("错误", "请输入有效的数字参数")

    def batch_scraping_worker(self, league_url, rounds_to_scrape, auto_scrape_odds, auto_scrape_enhanced, max_companies, delay):
        """批量抓取工作线程"""
        try:
            # 获取当前选择的数据库
            current_db = self.get_current_database()

            total_rounds = len(rounds_to_scrape)
            total_matches = 0
            successful_matches = 0
            successful_enhanced = 0

            scrape_options = []
            if auto_scrape_odds:
                scrape_options.append("赔率数据")
            if auto_scrape_enhanced:
                scrape_options.append("详细数据")

            options_text = "、".join(scrape_options) if scrape_options else "比赛信息"
            self.message_queue.put(("batch_status", f"开始批量抓取 {total_rounds} 个轮次 ({options_text})"))

            for i, round_num in enumerate(rounds_to_scrape):
                if self.batch_stop_flag:
                    break

                try:
                    # 更新进度
                    progress_msg = f"正在抓取第 {i+1}/{total_rounds} 轮 (第{round_num}轮)"
                    self.message_queue.put(("batch_status", progress_msg))
                    self.message_queue.put(("batch_progress", i))

                    # 获取该轮次的比赛数据
                    round_matches = self.league_extractor.get_completed_matches_from_multiple_rounds(
                        league_url, rounds=[round_num]
                    )

                    if round_matches:
                        for match in round_matches:
                            if self.batch_stop_flag:
                                break

                            total_matches += 1
                            match_id = match['match_id']

                            # 添加到结果表格
                            self.message_queue.put(("batch_result", {
                                'round': round_num,
                                'match_id': match_id,
                                'teams': f"{match['home_team']} vs {match['away_team']}",
                                'match_time': match['match_time'],
                                'score': match['score'],
                                'status': match['status'],
                                'odds_status': "待抓取"
                            }))

                            # 如果启用自动抓取
                            if (auto_scrape_odds or auto_scrape_enhanced) and match.get('data_source') == 'table_extraction_real_id':
                                try:
                                    # 智能检查：比赛是否需要抓取
                                    import sqlite3
                                    should_skip = False

                                    with sqlite3.connect(current_db.db_path) as conn:
                                        cursor = conn.cursor()

                                        # 检查比赛是否存在
                                        cursor.execute('SELECT COUNT(*) FROM matches WHERE match_id = ?', (match_id,))
                                        match_exists = cursor.fetchone()[0] > 0

                                        if match_exists:
                                            # 检查赔率数据是否充足
                                            cursor.execute('SELECT COUNT(*) FROM odds WHERE match_id = ?', (match_id,))
                                            odds_count = cursor.fetchone()[0]

                                            # 检查有多少家公司的数据
                                            cursor.execute('SELECT COUNT(DISTINCT company_name) FROM odds WHERE match_id = ?', (match_id,))
                                            company_count = cursor.fetchone()[0]

                                            # 如果赔率数据充足（至少100条记录，至少8家公司），则跳过
                                            if odds_count >= 100 and company_count >= 8:
                                                should_skip = True
                                                logger.info(f"跳过比赛 {match_id}，数据已充足（{odds_count}条记录，{company_count}家公司）")
                                            else:
                                                logger.info(f"比赛 {match_id} 存在但数据不足（{odds_count}条记录，{company_count}家公司），重新抓取")

                                    if should_skip:
                                        # 比赛数据已充足，跳过抓取
                                        self.message_queue.put(("update_odds_status", {
                                            'match_id': match_id,
                                            'status': "已存在"
                                        }))
                                        successful_matches += 1
                                        continue

                                    # 抓取基础数据（比赛信息和赔率）
                                    complete_data = None
                                    if auto_scrape_odds:
                                        # 使用与单场抓取相同的逻辑：max_companies表示最多抓取多少家目标公司
                                        # 实际抓取的是config.py中预定义的目标公司，而不是随机的前N家公司
                                        complete_data = self.scraper.scrape_complete_match_data(
                                            match_id=match_id,
                                            max_companies=max_companies,  # 这个参数会被正确处理
                                            delay=delay
                                        )

                                        # 保存基础数据到数据库
                                        if complete_data['match_info']:
                                            # 使用联赛抓取器提供的正确轮次信息和赛季信息
                                            match_info = complete_data['match_info'].copy()
                                            match_info['round'] = f"第{round_num}轮"  # 使用当前轮次的正确信息

                                            # 添加赛季信息
                                            if 'season' not in match_info or not match_info['season']:
                                                match_info['season'] = get_season_for_match(match_id=match_id, league_url=league_url)

                                            # 添加准确的时间信息（批量抓取时简化处理）
                                            try:
                                                time_info = self.time_scraper.scrape_match_time(match_id)
                                                if not time_info.get('error'):
                                                    match_info.update({
                                                        'accurate_datetime': time_info.get('full_datetime'),
                                                        'accurate_date': time_info.get('match_date'),
                                                        'accurate_time': time_info.get('match_time'),
                                                        'weekday': time_info.get('weekday'),
                                                        'match_year': time_info.get('year'),
                                                        'match_month': time_info.get('month'),
                                                        'match_day': time_info.get('day'),
                                                        'match_hour': time_info.get('hour'),
                                                        'match_minute': time_info.get('minute'),
                                                        'time_source': 'analysis'
                                                    })
                                                else:
                                                    match_info['time_source'] = 'estimated'
                                            except Exception:
                                                match_info['time_source'] = 'estimated'

                                            current_db.save_match_info(match_info)

                                        if complete_data['odds_data']:
                                            current_db.save_odds_data(match_id, complete_data['odds_data'])

                                    # 抓取详细数据
                                    enhanced_result = None
                                    if auto_scrape_enhanced and self.enhanced_system:
                                        try:
                                            self.message_queue.put(("update_odds_status", {
                                                'match_id': match_id,
                                                'status': "正在抓取详细数据..."
                                            }))

                                            enhanced_result = self.enhanced_system.process_match_data(match_id, save_data=True)

                                            if enhanced_result['success']:
                                                successful_enhanced += 1
                                                logger.info(f"比赛 {match_id} 详细数据抓取成功")
                                            else:
                                                logger.warning(f"比赛 {match_id} 详细数据抓取失败: {enhanced_result.get('error', '未知错误')}")
                                        except Exception as e:
                                            logger.error(f"比赛 {match_id} 详细数据抓取异常: {e}")

                                    # 更新状态
                                    status_parts = []
                                    success = False

                                    if auto_scrape_odds and complete_data and complete_data['odds_data']:
                                        odds_count = len(complete_data['odds_data'])
                                        status_parts.append(f"赔率({odds_count}条)")
                                        success = True

                                    if auto_scrape_enhanced and enhanced_result and enhanced_result['success']:
                                        status_parts.append("详细数据")
                                        success = True

                                    if success:
                                        successful_matches += 1
                                        status_text = f"已抓取({'+'.join(status_parts)})"
                                        self.message_queue.put(("update_odds_status", {
                                            'match_id': match_id,
                                            'status': status_text
                                        }))
                                    else:
                                        self.message_queue.put(("update_odds_status", {
                                            'match_id': match_id,
                                            'status': "抓取失败"
                                        }))

                                except Exception as e:
                                    logger.error(f"抓取比赛 {match_id} 赔率失败: {e}")
                                    self.message_queue.put(("update_odds_status", {
                                        'match_id': match_id,
                                        'status': "抓取失败"
                                    }))

                    else:
                        self.message_queue.put(("batch_status", f"第 {round_num} 轮未找到已完成比赛"))

                except Exception as e:
                    logger.error(f"抓取第 {round_num} 轮失败: {e}")
                    continue

            # 完成
            final_msg = f"批量抓取完成！总共处理 {total_matches} 场比赛"

            success_parts = []
            if auto_scrape_odds and successful_matches > 0:
                success_parts.append(f"{successful_matches} 场比赛的赔率数据")
            if auto_scrape_enhanced and successful_enhanced > 0:
                success_parts.append(f"{successful_enhanced} 场比赛的详细数据")

            if success_parts:
                final_msg += f"，成功抓取 {' 和 '.join(success_parts)}"

            self.message_queue.put(("batch_complete", final_msg))
            self.message_queue.put(("refresh", None))

        except Exception as e:
            self.message_queue.put(("batch_error", f"批量抓取失败: {e}"))

    def stop_batch_scraping(self):
        """停止批量抓取"""
        self.batch_stop_flag = True
        self.batch_progress_var.set("正在停止...")

    def clear_batch_results(self):
        """清空批量抓取结果"""
        for item in self.batch_results_tree.get_children():
            self.batch_results_tree.delete(item)
        self.batch_progress_var.set("就绪")
        self.batch_progress_bar['value'] = 0

    def open_batch_selected_match_url(self):
        """打开批量抓取结果中选中比赛的网址"""
        selection = self.batch_results_tree.selection()
        if not selection:
            messagebox.showwarning("警告", "请先选择要打开的比赛")
            return

        item = self.batch_results_tree.item(selection[0])
        values = item['values']

        # 批量抓取结果表格的列：轮次、比赛ID、对阵、比赛时间、比分、状态、赔率状态
        round_num = values[0]
        match_id = values[1]
        teams = values[2]

        # 构造比赛分析页面URL
        match_url = f"https://m.titan007.com/analy/Analysis/{match_id}.htm"

        try:
            # 使用系统默认浏览器打开URL
            webbrowser.open(match_url)

            # 更新状态栏
            self.status_var.set(f"已打开第{round_num}轮比赛 {match_id} ({teams}) 的网址")

        except Exception as e:
            logger.error(f"打开比赛网址失败: {e}")
            messagebox.showerror("错误", f"打开网址失败: {e}\n\n网址：{match_url}")

    def refresh_database_list(self):
        """刷新数据库列表"""
        try:
            # 获取所有联赛数据库
            leagues = self.league_db_manager.get_all_leagues()

            # 构建数据库选项列表
            database_options = ["默认数据库 (odds_data.db)"]

            for league in leagues:
                option = f"{league['league_name']} ({league['match_count']} 场比赛)"
                database_options.append(option)

            # 更新下拉框
            self.database_combo['values'] = database_options

            # 如果当前没有选择，设置为默认
            if not self.database_var.get() or self.database_var.get() not in database_options:
                self.database_var.set("默认数据库 (odds_data.db)")
                self.current_database = None
                self.current_database_path = None

            logger.info(f"数据库列表已刷新，共 {len(database_options)} 个选项")

        except Exception as e:
            logger.error(f"刷新数据库列表失败: {e}")
            messagebox.showerror("错误", f"刷新数据库列表失败: {e}")

    def on_database_selected(self, event=None):
        """处理数据库选择事件"""
        try:
            selected = self.database_var.get()

            if selected.startswith("默认数据库"):
                # 使用默认数据库
                self.current_database = self.database
                self.current_database_path = "odds_data.db"
                logger.info("已选择默认数据库")
            else:
                # 解析联赛名称
                league_name = selected.split(" (")[0]

                # 获取对应的数据库
                league_database = self.league_db_manager.get_database(league_name)
                self.current_database = league_database
                self.current_database_path = self.league_db_manager.get_database_path(league_name)

                logger.info(f"已选择联赛数据库: {league_name} -> {self.current_database_path}")

            # 更新状态栏信息
            self.update_database_info()

            # 刷新数据显示
            self.refresh_data()

        except Exception as e:
            logger.error(f"切换数据库失败: {e}")
            messagebox.showerror("错误", f"切换数据库失败: {e}")

    def update_database_info(self):
        """更新状态栏数据库信息"""
        try:
            if self.current_database:
                database = self.current_database
                db_path = self.current_database_path
            else:
                database = self.database
                db_path = "odds_data.db"

            stats = database.get_database_stats()
            if stats:
                db_name = os.path.basename(db_path)
                self.db_info_var.set(f"数据库: {db_name} | 比赛: {stats['match_count']} | 记录: {stats['odds_count']} | 大小: {stats['db_size_mb']}MB")
            else:
                self.db_info_var.set(f"数据库: {os.path.basename(db_path)} | 无数据")

        except Exception as e:
            logger.error(f"更新数据库信息失败: {e}")
            self.db_info_var.set("数据库信息获取失败")

    def get_current_database(self):
        """获取当前选择的数据库"""
        return self.current_database if self.current_database else self.database

    def get_tab_index_by_text(self, tab_text):
        """根据标签页文本获取索引"""
        try:
            for i in range(self.notebook.index("end")):
                if self.notebook.tab(i, "text") == tab_text:
                    return i
            return None
        except Exception as e:
            logger.warning(f"获取标签页索引失败: {e}")
            return None

    def switch_to_tab(self, tab_text):
        """切换到指定的标签页"""
        try:
            tab_index = self.get_tab_index_by_text(tab_text)
            if tab_index is not None:
                self.notebook.select(tab_index)
                return True
            else:
                logger.warning(f"未找到标签页: {tab_text}")
                return False
        except Exception as e:
            logger.warning(f"切换标签页失败: {e}")
            return False

    def sort_matches_column(self, column):
        """对比赛列表按指定列排序"""
        try:
            # 检查是否是同一列，如果是则切换排序方向
            if self.matches_sort_column == column:
                self.matches_sort_reverse = not self.matches_sort_reverse
            else:
                self.matches_sort_column = column
                self.matches_sort_reverse = False

            # 获取所有数据
            items = []
            for child in self.matches_tree.get_children():
                item = self.matches_tree.item(child)
                items.append((child, item['values']))

            # 定义排序键函数
            def sort_key(item):
                values = item[1]
                col_index = {
                    "match_id": 0,
                    "league": 1,
                    "season": 2,
                    "round": 3,
                    "teams": 4,
                    "match_time": 5,
                    "score": 6,
                    "state": 7,
                    "companies": 8
                }.get(column, 0)

                value = values[col_index] if col_index < len(values) else ""

                # 对数字列进行特殊处理
                if column in ["match_id", "companies"]:
                    try:
                        return int(value) if value else 0
                    except ValueError:
                        return 0
                elif column == "match_time":
                    # 对时间进行排序，优先显示有准确时间的
                    if "✓" in str(value):
                        return "0" + str(value)  # 有准确时间的排在前面
                    else:
                        return "1" + str(value)  # 估算时间的排在后面
                else:
                    return str(value).lower()

            # 排序
            items.sort(key=sort_key, reverse=self.matches_sort_reverse)

            # 重新插入排序后的数据
            for i, (child, values) in enumerate(items):
                self.matches_tree.move(child, '', i)

            # 更新表头显示排序状态
            self.update_matches_header_sort_indicator(column)

        except Exception as e:
            logger.error(f"排序比赛列表失败: {e}")

    def update_matches_header_sort_indicator(self, sorted_column):
        """更新比赛列表表头的排序指示器"""
        headers = {
            "match_id": "比赛ID",
            "league": "联赛",
            "season": "赛季",
            "round": "轮次",
            "teams": "对阵",
            "match_time": "比赛时间",
            "score": "比分",
            "state": "状态",
            "companies": "博彩公司"
        }

        for col, base_text in headers.items():
            if col == sorted_column:
                arrow = " ↓" if self.matches_sort_reverse else " ↑"
                self.matches_tree.heading(col, text=base_text + arrow, command=lambda c=col: self.sort_matches_column(c))
            else:
                self.matches_tree.heading(col, text=base_text, command=lambda c=col: self.sort_matches_column(c))

    def sort_odds_column(self, column):
        """对赔率表格按指定列排序"""
        try:
            # 检查是否是同一列，如果是则切换排序方向
            if self.odds_sort_column == column:
                self.odds_sort_reverse = not self.odds_sort_reverse
            else:
                self.odds_sort_column = column
                self.odds_sort_reverse = False

            # 获取所有数据
            items = []
            for child in self.odds_tree.get_children():
                item = self.odds_tree.item(child)
                items.append((child, item['values']))

            # 定义排序键函数
            def sort_key(item):
                values = item[1]
                col_index = {
                    "company": 0,
                    "date": 1,
                    "time": 2,
                    "home": 3,
                    "draw": 4,
                    "away": 5,
                    "return_rate": 6,
                    "kelly_home": 7,
                    "kelly_draw": 8,
                    "kelly_away": 9
                }.get(column, 0)

                value = values[col_index] if col_index < len(values) else ""

                # 对数字列进行特殊处理
                if column in ["home", "draw", "away", "return_rate", "kelly_home", "kelly_draw", "kelly_away"]:
                    try:
                        # 移除百分号并转换为浮点数
                        clean_value = str(value).replace('%', '').strip()
                        return float(clean_value) if clean_value else 0.0
                    except ValueError:
                        return 0.0
                elif column in ["date", "time"]:
                    # 对日期和时间进行排序
                    return str(value)
                else:
                    return str(value).lower()

            # 排序
            items.sort(key=sort_key, reverse=self.odds_sort_reverse)

            # 重新插入排序后的数据
            for i, (child, _) in enumerate(items):
                self.odds_tree.move(child, '', i)

            # 更新表头显示排序状态
            self.update_odds_header_sort_indicator(column)

        except Exception as e:
            logger.error(f"排序赔率表格失败: {e}")

    def update_odds_header_sort_indicator(self, sorted_column):
        """更新赔率表格表头的排序指示器"""
        headers = {
            "company": "公司",
            "date": "日期",
            "time": "时间",
            "home": "主胜",
            "draw": "平局",
            "away": "客胜",
            "return_rate": "返还率",
            "kelly_home": "凯利主",
            "kelly_draw": "凯利平",
            "kelly_away": "凯利客"
        }

        for col, base_text in headers.items():
            if col == sorted_column:
                arrow = " ↓" if self.odds_sort_reverse else " ↑"
                self.odds_tree.heading(col, text=base_text + arrow, command=lambda c=col: self.sort_odds_column(c))
            else:
                self.odds_tree.heading(col, text=base_text, command=lambda c=col: self.sort_odds_column(c))

    def sort_batch_results_column(self, column):
        """对批量结果表格按指定列排序"""
        try:
            # 检查是否是同一列，如果是则切换排序方向
            if self.batch_results_sort_column == column:
                self.batch_results_sort_reverse = not self.batch_results_sort_reverse
            else:
                self.batch_results_sort_column = column
                self.batch_results_sort_reverse = False

            # 获取所有数据
            items = []
            for child in self.batch_results_tree.get_children():
                item = self.batch_results_tree.item(child)
                items.append((child, item['values']))

            # 定义排序键函数
            def sort_key(item):
                values = item[1]
                col_index = {
                    "round": 0,
                    "match_id": 1,
                    "teams": 2,
                    "match_time": 3,
                    "score": 4,
                    "status": 5,
                    "odds_status": 6
                }.get(column, 0)

                value = values[col_index] if col_index < len(values) else ""

                # 对数字列进行特殊处理
                if column in ["round", "match_id"]:
                    try:
                        return int(value) if value else 0
                    except ValueError:
                        return 0
                elif column == "match_time":
                    # 对时间进行排序，优先显示有准确时间的
                    if "✓" in str(value):
                        return "0" + str(value)  # 有准确时间的排在前面
                    else:
                        return "1" + str(value)  # 估算时间的排在后面
                else:
                    return str(value).lower()

            # 排序
            items.sort(key=sort_key, reverse=self.batch_results_sort_reverse)

            # 重新插入排序后的数据
            for i, (child, _) in enumerate(items):
                self.batch_results_tree.move(child, '', i)

            # 更新表头显示排序状态
            self.update_batch_results_header_sort_indicator(column)

        except Exception as e:
            logger.error(f"排序批量结果表格失败: {e}")

    def update_batch_results_header_sort_indicator(self, sorted_column):
        """更新批量结果表格表头的排序指示器"""
        headers = {
            "round": "轮次",
            "match_id": "比赛ID",
            "teams": "对阵",
            "match_time": "比赛时间",
            "score": "比分",
            "status": "状态",
            "odds_status": "赔率状态"
        }

        for col, base_text in headers.items():
            if col == sorted_column:
                arrow = " ↓" if self.batch_results_sort_reverse else " ↑"
                self.batch_results_tree.heading(col, text=base_text + arrow, command=lambda c=col: self.sort_batch_results_column(c))
            else:
                self.batch_results_tree.heading(col, text=base_text, command=lambda c=col: self.sort_batch_results_column(c))

    # ==================== 比赛筛选相关方法 ====================

    def create_filter_conditions(self):
        """创建筛选条件"""
        # 联赛筛选条件
        self.create_league_filter()

        # 赛季筛选条件
        self.create_season_filter()

        # 时间范围筛选条件
        self.create_time_range_filter()

        # 比赛状态筛选条件
        self.create_match_state_filter()

        # 队伍筛选条件
        self.create_team_filter()

        # 比分筛选条件
        self.create_score_filter()

        # 凯利分析筛选条件
        self.create_kelly_analysis_filter()

        # 凯利分析2筛选条件
        self.create_kelly_analysis2_filter()

        # 博彩态度筛选条件
        self.create_betting_attitude_filter()

        # 博彩态度2筛选条件
        self.create_betting_attitude2_filter()

    def create_league_filter(self):
        """创建联赛筛选条件"""
        frame = ttk.LabelFrame(self.filter_scrollable_frame, text="联赛筛选", padding="5")
        frame.pack(fill=tk.X, pady=(0, 5))

        # 启用复选框
        enable_var = tk.BooleanVar()
        enable_check = ttk.Checkbutton(frame, text="启用联赛筛选", variable=enable_var)
        enable_check.pack(anchor=tk.W)

        # 筛选面板
        filter_panel = ttk.Frame(frame)
        filter_panel.pack(fill=tk.X, pady=(5, 0))

        # 联赛选择
        ttk.Label(filter_panel, text="选择联赛:").pack(anchor=tk.W)
        league_listbox = tk.Listbox(filter_panel, height=4, selectmode=tk.MULTIPLE)
        league_listbox.pack(fill=tk.X, pady=(2, 0))

        # 获取所有联赛并填充
        self.populate_league_list(league_listbox)

        # 存储控件引用
        self.filter_widgets['league'] = {
            'enable': enable_var,
            'listbox': league_listbox,
            'frame': filter_panel
        }

        # 绑定启用状态变化
        enable_var.trace('w', lambda *args: self.toggle_filter_panel('league'))

        # 初始状态设置为禁用
        self.toggle_filter_panel('league')

    def create_season_filter(self):
        """创建赛季筛选条件"""
        frame = ttk.LabelFrame(self.filter_scrollable_frame, text="赛季筛选", padding="5")
        frame.pack(fill=tk.X, pady=(0, 5))

        # 启用复选框
        enable_var = tk.BooleanVar()
        enable_check = ttk.Checkbutton(frame, text="启用赛季筛选", variable=enable_var)
        enable_check.pack(anchor=tk.W)

        # 筛选面板
        filter_panel = ttk.Frame(frame)
        filter_panel.pack(fill=tk.X, pady=(5, 0))

        # 赛季选择
        ttk.Label(filter_panel, text="选择赛季:").pack(anchor=tk.W)
        season_listbox = tk.Listbox(filter_panel, height=3, selectmode=tk.MULTIPLE)
        season_listbox.pack(fill=tk.X, pady=(2, 0))

        # 获取所有赛季并填充
        self.populate_season_list(season_listbox)

        # 存储控件引用
        self.filter_widgets['season'] = {
            'enable': enable_var,
            'listbox': season_listbox,
            'frame': filter_panel
        }

        # 绑定启用状态变化
        enable_var.trace('w', lambda *args: self.toggle_filter_panel('season'))

        # 初始状态设置为禁用
        self.toggle_filter_panel('season')

    def create_time_range_filter(self):
        """创建时间范围筛选条件"""
        frame = ttk.LabelFrame(self.filter_scrollable_frame, text="时间范围筛选", padding="5")
        frame.pack(fill=tk.X, pady=(0, 5))

        # 启用复选框
        enable_var = tk.BooleanVar()
        enable_check = ttk.Checkbutton(frame, text="启用时间范围筛选", variable=enable_var)
        enable_check.pack(anchor=tk.W)

        # 筛选面板
        filter_panel = ttk.Frame(frame)
        filter_panel.pack(fill=tk.X, pady=(5, 0))

        # 说明文字
        ttk.Label(filter_panel, text="支持格式: YYYY-MM-DD 或 YYYY-MM-DD HH:MM",
                 font=("Arial", 8), foreground="gray").pack(anchor=tk.W)

        # 开始时间
        ttk.Label(filter_panel, text="开始时间:").pack(anchor=tk.W, pady=(5, 0))
        start_time_var = tk.StringVar(value="2024-01-01")
        start_time_entry = ttk.Entry(filter_panel, textvariable=start_time_var)
        start_time_entry.pack(fill=tk.X, pady=(2, 5))

        # 结束时间
        ttk.Label(filter_panel, text="结束时间:").pack(anchor=tk.W)
        end_time_var = tk.StringVar(value="2025-12-31")
        end_time_entry = ttk.Entry(filter_panel, textvariable=end_time_var)
        end_time_entry.pack(fill=tk.X, pady=(2, 0))

        # 存储控件引用
        self.filter_widgets['time_range'] = {
            'enable': enable_var,
            'start_time': start_time_var,
            'end_time': end_time_var,
            'frame': filter_panel
        }

        # 绑定启用状态变化
        enable_var.trace('w', lambda *args: self.toggle_filter_panel('time_range'))

        # 初始状态设置为禁用
        self.toggle_filter_panel('time_range')

    def create_match_state_filter(self):
        """创建比赛状态筛选条件"""
        frame = ttk.LabelFrame(self.filter_scrollable_frame, text="比赛状态筛选", padding="5")
        frame.pack(fill=tk.X, pady=(0, 5))

        # 启用复选框
        enable_var = tk.BooleanVar()
        enable_check = ttk.Checkbutton(frame, text="启用比赛状态筛选", variable=enable_var)
        enable_check.pack(anchor=tk.W)

        # 筛选面板
        filter_panel = ttk.Frame(frame)
        filter_panel.pack(fill=tk.X, pady=(5, 0))

        # 状态选择
        ttk.Label(filter_panel, text="选择状态:").pack(anchor=tk.W)
        state_listbox = tk.Listbox(filter_panel, height=3, selectmode=tk.MULTIPLE)
        state_listbox.pack(fill=tk.X, pady=(2, 0))

        # 填充常见状态
        common_states = ["完场", "进行中", "未开始", "推迟", "取消"]
        for state in common_states:
            state_listbox.insert(tk.END, state)

        # 存储控件引用
        self.filter_widgets['match_state'] = {
            'enable': enable_var,
            'listbox': state_listbox,
            'frame': filter_panel
        }

        # 绑定启用状态变化
        enable_var.trace('w', lambda *args: self.toggle_filter_panel('match_state'))

        # 初始状态设置为禁用
        self.toggle_filter_panel('match_state')

    def create_team_filter(self):
        """创建队伍筛选条件"""
        frame = ttk.LabelFrame(self.filter_scrollable_frame, text="队伍筛选", padding="5")
        frame.pack(fill=tk.X, pady=(0, 5))

        # 启用复选框
        enable_var = tk.BooleanVar()
        enable_check = ttk.Checkbutton(frame, text="启用队伍筛选", variable=enable_var)
        enable_check.pack(anchor=tk.W)

        # 筛选面板
        filter_panel = ttk.Frame(frame)
        filter_panel.pack(fill=tk.X, pady=(5, 0))

        # 队伍名称输入
        ttk.Label(filter_panel, text="队伍名称 (支持模糊匹配):").pack(anchor=tk.W)
        team_var = tk.StringVar()
        team_entry = ttk.Entry(filter_panel, textvariable=team_var)
        team_entry.pack(fill=tk.X, pady=(2, 0))

        # 存储控件引用
        self.filter_widgets['team'] = {
            'enable': enable_var,
            'team_name': team_var,
            'frame': filter_panel
        }

        # 绑定启用状态变化
        enable_var.trace('w', lambda *args: self.toggle_filter_panel('team'))

        # 初始状态设置为禁用
        self.toggle_filter_panel('team')

    def create_score_filter(self):
        """创建比分筛选条件"""
        frame = ttk.LabelFrame(self.filter_scrollable_frame, text="比分筛选", padding="5")
        frame.pack(fill=tk.X, pady=(0, 5))

        # 启用复选框
        enable_var = tk.BooleanVar()
        enable_check = ttk.Checkbutton(frame, text="启用比分筛选", variable=enable_var)
        enable_check.pack(anchor=tk.W)

        # 筛选面板
        filter_panel = ttk.Frame(frame)
        filter_panel.pack(fill=tk.X, pady=(5, 0))

        # 比分类型选择
        score_type_var = tk.StringVar(value="all")
        ttk.Radiobutton(filter_panel, text="所有比赛", variable=score_type_var, value="all").pack(anchor=tk.W)
        ttk.Radiobutton(filter_panel, text="仅有比分的比赛", variable=score_type_var, value="with_score").pack(anchor=tk.W)
        ttk.Radiobutton(filter_panel, text="仅无比分的比赛", variable=score_type_var, value="without_score").pack(anchor=tk.W)

        # 存储控件引用
        self.filter_widgets['score'] = {
            'enable': enable_var,
            'score_type': score_type_var,
            'frame': filter_panel
        }

        # 绑定启用状态变化
        enable_var.trace('w', lambda *args: self.toggle_filter_panel('score'))

        # 初始状态设置为禁用
        self.toggle_filter_panel('score')

    def create_kelly_analysis_filter(self):
        """创建凯利分析筛选条件"""
        frame = ttk.LabelFrame(self.filter_scrollable_frame, text="凯利分析", padding="5")
        frame.pack(fill=tk.X, pady=(0, 5))

        # 启用复选框
        enable_var = tk.BooleanVar()
        enable_check = ttk.Checkbutton(frame, text="启用凯利分析筛选", variable=enable_var)
        enable_check.pack(anchor=tk.W)

        # 筛选面板
        filter_panel = ttk.Frame(frame)
        filter_panel.pack(fill=tk.X, pady=(5, 0))

        # 凯利类型选择
        ttk.Label(filter_panel, text="凯利类型:").pack(anchor=tk.W)
        kelly_type_var = tk.StringVar(value="kelly_home")
        kelly_type_frame = ttk.Frame(filter_panel)
        kelly_type_frame.pack(fill=tk.X, pady=(2, 5))

        ttk.Radiobutton(kelly_type_frame, text="凯利主", variable=kelly_type_var, value="kelly_home").pack(side=tk.LEFT)
        ttk.Radiobutton(kelly_type_frame, text="凯利平", variable=kelly_type_var, value="kelly_draw").pack(side=tk.LEFT, padx=(10, 0))
        ttk.Radiobutton(kelly_type_frame, text="凯利客", variable=kelly_type_var, value="kelly_away").pack(side=tk.LEFT, padx=(10, 0))

        # 统计数量设置
        ttk.Label(filter_panel, text="统计数量:").pack(anchor=tk.W)
        stats_count_var = tk.StringVar(value="10")
        ttk.Entry(filter_panel, textvariable=stats_count_var, width=10).pack(anchor=tk.W, pady=(2, 5))

        # 凯利门槛设置
        ttk.Label(filter_panel, text="凯利门槛:").pack(anchor=tk.W)
        kelly_threshold_var = tk.StringVar(value="1.01")
        ttk.Entry(filter_panel, textvariable=kelly_threshold_var, width=10).pack(anchor=tk.W, pady=(2, 5))

        # 返还率门槛设置
        ttk.Label(filter_panel, text="返还率门槛:").pack(anchor=tk.W)
        return_rate_threshold_var = tk.StringVar(value="97")
        ttk.Entry(filter_panel, textvariable=return_rate_threshold_var, width=10).pack(anchor=tk.W, pady=(2, 0))

        # 存储控件引用
        self.filter_widgets['kelly_analysis'] = {
            'enable': enable_var,
            'kelly_type': kelly_type_var,
            'stats_count': stats_count_var,
            'kelly_threshold': kelly_threshold_var,
            'return_rate_threshold': return_rate_threshold_var,
            'frame': filter_panel
        }

        # 绑定启用状态变化
        enable_var.trace('w', lambda *args: self.toggle_filter_panel('kelly_analysis'))

        # 初始状态设置为禁用
        self.toggle_filter_panel('kelly_analysis')

    def create_kelly_analysis2_filter(self):
        """创建凯利分析2筛选条件"""
        frame = ttk.LabelFrame(self.filter_scrollable_frame, text="凯利分析2", padding="5")
        frame.pack(fill=tk.X, pady=(0, 5))

        # 启用复选框
        enable_var = tk.BooleanVar()
        enable_check = ttk.Checkbutton(frame, text="启用凯利分析2筛选", variable=enable_var)
        enable_check.pack(anchor=tk.W)

        # 筛选面板
        filter_panel = ttk.Frame(frame)
        filter_panel.pack(fill=tk.X, pady=(5, 0))

        # 凯利类型选择
        ttk.Label(filter_panel, text="凯利类型:").pack(anchor=tk.W)
        kelly_type_var = tk.StringVar(value="kelly_home")
        kelly_type_frame = ttk.Frame(filter_panel)
        kelly_type_frame.pack(fill=tk.X, pady=(2, 5))

        ttk.Radiobutton(kelly_type_frame, text="凯利主", variable=kelly_type_var, value="kelly_home").pack(side=tk.LEFT)
        ttk.Radiobutton(kelly_type_frame, text="凯利平", variable=kelly_type_var, value="kelly_draw").pack(side=tk.LEFT, padx=(10, 0))
        ttk.Radiobutton(kelly_type_frame, text="凯利客", variable=kelly_type_var, value="kelly_away").pack(side=tk.LEFT, padx=(10, 0))

        # 统计数量设置
        ttk.Label(filter_panel, text="统计数量:").pack(anchor=tk.W)
        stats_count_var = tk.StringVar(value="10")
        ttk.Entry(filter_panel, textvariable=stats_count_var, width=10).pack(anchor=tk.W, pady=(2, 5))

        # 凯利门槛设置
        ttk.Label(filter_panel, text="凯利门槛:").pack(anchor=tk.W)
        kelly_threshold_var = tk.StringVar(value="1.05")
        ttk.Entry(filter_panel, textvariable=kelly_threshold_var, width=10).pack(anchor=tk.W, pady=(2, 5))

        # 博彩公司复选列表
        ttk.Label(filter_panel, text="博彩公司:").pack(anchor=tk.W)
        companies_frame = ttk.Frame(filter_panel)
        companies_frame.pack(fill=tk.X, pady=(2, 5))

        # 创建滚动区域用于博彩公司列表
        companies_canvas = tk.Canvas(companies_frame, height=100)
        companies_scrollbar = ttk.Scrollbar(companies_frame, orient="vertical", command=companies_canvas.yview)
        companies_scrollable_frame = ttk.Frame(companies_canvas)

        companies_scrollable_frame.bind(
            "<Configure>",
            lambda e: companies_canvas.configure(scrollregion=companies_canvas.bbox("all"))
        )

        companies_canvas.create_window((0, 0), window=companies_scrollable_frame, anchor="nw")
        companies_canvas.configure(yscrollcommand=companies_scrollbar.set)

        companies_canvas.pack(side="left", fill="both", expand=True)
        companies_scrollbar.pack(side="right", fill="y")

        # 获取博彩公司列表并创建复选框
        company_vars = {}
        self.populate_company_checkboxes(companies_scrollable_frame, company_vars)

        # 无意义公司门槛设置
        ttk.Label(filter_panel, text="无意义公司门槛:").pack(anchor=tk.W)
        meaningless_threshold_var = tk.StringVar(value="2")
        ttk.Entry(filter_panel, textvariable=meaningless_threshold_var, width=10).pack(anchor=tk.W, pady=(2, 0))

        # 存储控件引用
        self.filter_widgets['kelly_analysis2'] = {
            'enable': enable_var,
            'kelly_type': kelly_type_var,
            'stats_count': stats_count_var,
            'kelly_threshold': kelly_threshold_var,
            'company_vars': company_vars,
            'meaningless_threshold': meaningless_threshold_var,
            'frame': filter_panel
        }

        # 绑定启用状态变化
        enable_var.trace('w', lambda *args: self.toggle_filter_panel('kelly_analysis2'))

        # 初始状态设置为禁用
        self.toggle_filter_panel('kelly_analysis2')

    def create_betting_attitude_filter(self):
        """创建博彩态度筛选条件"""
        frame = ttk.LabelFrame(self.filter_scrollable_frame, text="博彩态度", padding="5")
        frame.pack(fill=tk.X, pady=(0, 5))

        # 启用复选框
        enable_var = tk.BooleanVar()
        enable_check = ttk.Checkbutton(frame, text="启用博彩态度筛选", variable=enable_var)
        enable_check.pack(anchor=tk.W)

        # 筛选面板
        filter_panel = ttk.Frame(frame)
        filter_panel.pack(fill=tk.X, pady=(5, 0))

        # 赔率类型选择
        ttk.Label(filter_panel, text="赔率类型:").pack(anchor=tk.W)
        odds_type_var = tk.StringVar(value="home_odds")
        odds_type_frame = ttk.Frame(filter_panel)
        odds_type_frame.pack(fill=tk.X, pady=(2, 5))

        ttk.Radiobutton(odds_type_frame, text="主胜", variable=odds_type_var, value="home_odds").pack(side=tk.LEFT)
        ttk.Radiobutton(odds_type_frame, text="平局", variable=odds_type_var, value="draw_odds").pack(side=tk.LEFT, padx=(10, 0))
        ttk.Radiobutton(odds_type_frame, text="客胜", variable=odds_type_var, value="away_odds").pack(side=tk.LEFT, padx=(10, 0))

        # 阈值设置
        ttk.Label(filter_panel, text="阈值:").pack(anchor=tk.W)
        threshold_var = tk.StringVar(value="1.05")
        ttk.Entry(filter_panel, textvariable=threshold_var, width=10).pack(anchor=tk.W, pady=(2, 5))

        # 博彩公司选择
        ttk.Label(filter_panel, text="选择博彩公司:").pack(anchor=tk.W)
        companies_frame = ttk.Frame(filter_panel)
        companies_frame.pack(fill=tk.X, pady=(2, 5))

        # 创建滚动区域用于博彩公司列表
        companies_canvas = tk.Canvas(companies_frame, height=120)
        companies_scrollbar = ttk.Scrollbar(companies_frame, orient="vertical", command=companies_canvas.yview)
        companies_scrollable_frame = ttk.Frame(companies_canvas)

        companies_scrollable_frame.bind(
            "<Configure>",
            lambda e: companies_canvas.configure(scrollregion=companies_canvas.bbox("all"))
        )

        companies_canvas.create_window((0, 0), window=companies_scrollable_frame, anchor="nw")
        companies_canvas.configure(yscrollcommand=companies_scrollbar.set)

        companies_canvas.pack(side="left", fill="both", expand=True)
        companies_scrollbar.pack(side="right", fill="y")

        # 获取博彩公司列表并创建复选框
        company_vars = {}
        self.populate_company_checkboxes(companies_scrollable_frame, company_vars)

        # 存储控件引用
        self.filter_widgets['betting_attitude'] = {
            'enable': enable_var,
            'odds_type': odds_type_var,
            'threshold': threshold_var,
            'company_vars': company_vars,
            'frame': filter_panel
        }

        # 绑定启用状态变化
        enable_var.trace('w', lambda *args: self.toggle_filter_panel('betting_attitude'))

        # 初始状态设置为禁用
        self.toggle_filter_panel('betting_attitude')

    def create_betting_attitude2_filter(self):
        """创建博彩态度2筛选条件"""
        frame = ttk.LabelFrame(self.filter_scrollable_frame, text="博彩态度2", padding="5")
        frame.pack(fill=tk.X, pady=(0, 5))

        # 启用复选框
        enable_var = tk.BooleanVar()
        enable_check = ttk.Checkbutton(frame, text="启用博彩态度2筛选", variable=enable_var)
        enable_check.pack(anchor=tk.W)

        # 筛选面板
        filter_panel = ttk.Frame(frame)
        filter_panel.pack(fill=tk.X, pady=(5, 0))

        # 赔率类型选择
        ttk.Label(filter_panel, text="赔率类型:").pack(anchor=tk.W)
        odds_type_var = tk.StringVar(value="home_odds")
        odds_type_frame = ttk.Frame(filter_panel)
        odds_type_frame.pack(fill=tk.X, pady=(2, 5))

        ttk.Radiobutton(odds_type_frame, text="主胜", variable=odds_type_var, value="home_odds").pack(side=tk.LEFT)
        ttk.Radiobutton(odds_type_frame, text="平局", variable=odds_type_var, value="draw_odds").pack(side=tk.LEFT, padx=(10, 0))
        ttk.Radiobutton(odds_type_frame, text="客胜", variable=odds_type_var, value="away_odds").pack(side=tk.LEFT, padx=(10, 0))

        # 阈值设置
        ttk.Label(filter_panel, text="阈值:").pack(anchor=tk.W)
        threshold_var = tk.StringVar(value="0")
        ttk.Entry(filter_panel, textvariable=threshold_var, width=10).pack(anchor=tk.W, pady=(2, 5))

        # 博彩公司选择（单选）
        ttk.Label(filter_panel, text="选择博彩公司:").pack(anchor=tk.W)
        company_var = tk.StringVar()
        company_frame = ttk.Frame(filter_panel)
        company_frame.pack(fill=tk.X, pady=(2, 5))

        # 创建滚动区域用于博彩公司列表
        company_canvas = tk.Canvas(company_frame, height=120)
        company_scrollbar = ttk.Scrollbar(company_frame, orient="vertical", command=company_canvas.yview)
        company_scrollable_frame = ttk.Frame(company_canvas)

        company_scrollable_frame.bind(
            "<Configure>",
            lambda e: company_canvas.configure(scrollregion=company_canvas.bbox("all"))
        )

        company_canvas.create_window((0, 0), window=company_scrollable_frame, anchor="nw")
        company_canvas.configure(yscrollcommand=company_scrollbar.set)

        company_canvas.pack(side="left", fill="both", expand=True)
        company_scrollbar.pack(side="right", fill="y")

        # 获取博彩公司列表并创建单选按钮
        self.populate_company_radiobuttons(company_scrollable_frame, company_var)

        # 存储控件引用
        self.filter_widgets['betting_attitude2'] = {
            'enable': enable_var,
            'odds_type': odds_type_var,
            'threshold': threshold_var,
            'company': company_var,
            'frame': filter_panel
        }

        # 绑定启用状态变化
        enable_var.trace('w', lambda *args: self.toggle_filter_panel('betting_attitude2'))

        # 初始状态设置为禁用
        self.toggle_filter_panel('betting_attitude2')

    def toggle_filter_panel(self, filter_name):
        """切换筛选面板的启用状态"""
        if filter_name in self.filter_widgets:
            widgets = self.filter_widgets[filter_name]
            enabled = widgets['enable'].get()

            # 设置面板中所有控件的状态
            for child in widgets['frame'].winfo_children():
                if hasattr(child, 'configure'):
                    try:
                        child.configure(state='normal' if enabled else 'disabled')
                    except tk.TclError:
                        # 某些控件可能不支持state属性
                        pass

    def populate_league_list(self, listbox):
        """填充联赛列表"""
        try:
            current_db = self.get_current_database()
            with sqlite3.connect(current_db.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute('SELECT DISTINCT league FROM matches WHERE league IS NOT NULL ORDER BY league')
                leagues = [row[0] for row in cursor.fetchall()]

                for league in leagues:
                    listbox.insert(tk.END, league)
        except Exception as e:
            logger.error(f"填充联赛列表失败: {e}")

    def populate_season_list(self, listbox):
        """填充赛季列表"""
        try:
            current_db = self.get_current_database()
            with sqlite3.connect(current_db.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute('SELECT DISTINCT season FROM matches WHERE season IS NOT NULL ORDER BY season DESC')
                seasons = [row[0] for row in cursor.fetchall()]

                for season in seasons:
                    listbox.insert(tk.END, season)
        except Exception as e:
            logger.error(f"填充赛季列表失败: {e}")

    def populate_company_checkboxes(self, parent_frame, company_vars):
        """填充博彩公司复选框列表"""
        try:
            current_db = self.get_current_database()
            with sqlite3.connect(current_db.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute('SELECT DISTINCT company_name FROM odds WHERE company_name IS NOT NULL ORDER BY company_name')
                companies = [row[0] for row in cursor.fetchall()]

                # 创建复选框，每行显示2个
                for i, company in enumerate(companies):
                    row = i // 2
                    col = i % 2

                    var = tk.BooleanVar()
                    company_vars[company] = var

                    checkbox = ttk.Checkbutton(parent_frame, text=company, variable=var)
                    checkbox.grid(row=row, column=col, sticky="w", padx=(0, 10), pady=1)

                logger.info(f"填充博彩公司列表完成，共 {len(companies)} 家公司")

        except Exception as e:
            logger.error(f"填充博彩公司列表失败: {e}")

    def populate_company_radiobuttons(self, parent_frame, company_var):
        """填充博彩公司单选按钮列表"""
        try:
            current_db = self.get_current_database()
            with sqlite3.connect(current_db.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute('SELECT DISTINCT company_name FROM odds WHERE company_name IS NOT NULL ORDER BY company_name')
                companies = [row[0] for row in cursor.fetchall()]

                # 创建单选按钮，每行显示2个
                for i, company in enumerate(companies):
                    row = i // 2
                    col = i % 2

                    radiobutton = ttk.Radiobutton(parent_frame, text=company, variable=company_var, value=company)
                    radiobutton.grid(row=row, column=col, sticky="w", padx=(0, 10), pady=1)

                logger.info(f"填充博彩公司单选列表完成，共 {len(companies)} 家公司")

        except Exception as e:
            logger.error(f"填充博彩公司单选列表失败: {e}")

    def apply_filters(self):
        """应用筛选条件"""
        try:
            current_db = self.get_current_database()

            # 构建SQL查询
            where_conditions = []
            params = []
            filter_descriptions = []  # 用于显示筛选状态

            # 联赛筛选
            if self.filter_widgets['league']['enable'].get():
                selected_leagues = [self.filter_widgets['league']['listbox'].get(i)
                                  for i in self.filter_widgets['league']['listbox'].curselection()]
                if selected_leagues:
                    placeholders = ','.join(['?' for _ in selected_leagues])
                    where_conditions.append(f"league IN ({placeholders})")
                    params.extend(selected_leagues)
                    filter_descriptions.append(f"联赛: {', '.join(selected_leagues[:2])}{'等' if len(selected_leagues) > 2 else ''}")

            # 赛季筛选
            if self.filter_widgets['season']['enable'].get():
                selected_seasons = [self.filter_widgets['season']['listbox'].get(i)
                                  for i in self.filter_widgets['season']['listbox'].curselection()]
                if selected_seasons:
                    placeholders = ','.join(['?' for _ in selected_seasons])
                    where_conditions.append(f"season IN ({placeholders})")
                    params.extend(selected_seasons)
                    filter_descriptions.append(f"赛季: {', '.join(selected_seasons)}")

            # 时间范围筛选
            if self.filter_widgets['time_range']['enable'].get():
                start_time = self.filter_widgets['time_range']['start_time'].get().strip()
                end_time = self.filter_widgets['time_range']['end_time'].get().strip()

                if start_time:
                    # 处理开始时间
                    start_datetime = self.normalize_datetime_for_filter(start_time, is_start=True)
                    if start_datetime:
                        # 优先使用accurate_datetime，如果没有则使用match_time或match_date
                        where_conditions.append("""
                            (accurate_datetime >= ? OR
                             (accurate_datetime IS NULL AND match_time >= ?) OR
                             (accurate_datetime IS NULL AND match_time IS NULL AND match_date >= ?))
                        """)
                        params.extend([start_datetime, start_datetime, start_datetime[:10]])

                if end_time:
                    # 处理结束时间
                    end_datetime = self.normalize_datetime_for_filter(end_time, is_start=False)
                    if end_datetime:
                        # 优先使用accurate_datetime，如果没有则使用match_time或match_date
                        where_conditions.append("""
                            (accurate_datetime <= ? OR
                             (accurate_datetime IS NULL AND match_time <= ?) OR
                             (accurate_datetime IS NULL AND match_time IS NULL AND match_date <= ?))
                        """)
                        params.extend([end_datetime, end_datetime, end_datetime[:10]])

                if start_time or end_time:
                    time_desc = f"{start_time or '开始'} 至 {end_time or '结束'}"
                    filter_descriptions.append(f"时间: {time_desc}")

            # 比赛状态筛选
            if self.filter_widgets['match_state']['enable'].get():
                selected_states = [self.filter_widgets['match_state']['listbox'].get(i)
                                 for i in self.filter_widgets['match_state']['listbox'].curselection()]
                if selected_states:
                    placeholders = ','.join(['?' for _ in selected_states])
                    where_conditions.append(f"match_state IN ({placeholders})")
                    params.extend(selected_states)
                    filter_descriptions.append(f"状态: {', '.join(selected_states)}")

            # 队伍筛选
            if self.filter_widgets['team']['enable'].get():
                team_name = self.filter_widgets['team']['team_name'].get().strip()
                if team_name:
                    where_conditions.append("(home_team LIKE ? OR away_team LIKE ?)")
                    params.extend([f"%{team_name}%", f"%{team_name}%"])
                    filter_descriptions.append(f"队伍: {team_name}")

            # 比分筛选
            if self.filter_widgets['score']['enable'].get():
                score_type = self.filter_widgets['score']['score_type'].get()
                if score_type == "with_score":
                    where_conditions.append("(home_score IS NOT NULL AND home_score != '' AND away_score IS NOT NULL AND away_score != '')")
                    filter_descriptions.append("比分: 仅有比分")
                elif score_type == "without_score":
                    where_conditions.append("(home_score IS NULL OR home_score = '' OR away_score IS NULL OR away_score = '')")
                    filter_descriptions.append("比分: 仅无比分")

            # 凯利分析筛选
            kelly_analysis_matches = None
            if self.filter_widgets['kelly_analysis']['enable'].get():
                try:
                    kelly_type = self.filter_widgets['kelly_analysis']['kelly_type'].get()
                    stats_count = int(self.filter_widgets['kelly_analysis']['stats_count'].get())
                    kelly_threshold = float(self.filter_widgets['kelly_analysis']['kelly_threshold'].get())
                    return_rate_threshold = float(self.filter_widgets['kelly_analysis']['return_rate_threshold'].get())

                    # 执行凯利分析筛选
                    kelly_analysis_matches = self.apply_kelly_analysis_filter(
                        kelly_type, stats_count, kelly_threshold, return_rate_threshold
                    )

                    kelly_type_name = {"kelly_home": "凯利主", "kelly_draw": "凯利平", "kelly_away": "凯利客"}[kelly_type]
                    filter_descriptions.append(f"凯利分析: {kelly_type_name}(统计{stats_count}家)")

                except (ValueError, TypeError) as e:
                    logger.warning(f"凯利分析筛选参数错误: {e}")
                    kelly_analysis_matches = None

            # 凯利分析2筛选
            kelly_analysis2_matches = None
            if self.filter_widgets['kelly_analysis2']['enable'].get():
                try:
                    kelly_type = self.filter_widgets['kelly_analysis2']['kelly_type'].get()
                    stats_count = int(self.filter_widgets['kelly_analysis2']['stats_count'].get())
                    kelly_threshold = float(self.filter_widgets['kelly_analysis2']['kelly_threshold'].get())
                    meaningless_threshold = int(self.filter_widgets['kelly_analysis2']['meaningless_threshold'].get())

                    # 获取选中的博彩公司
                    selected_companies = []
                    for company, var in self.filter_widgets['kelly_analysis2']['company_vars'].items():
                        if var.get():
                            selected_companies.append(company)

                    if not selected_companies:
                        logger.warning("凯利分析2: 未选择任何博彩公司")
                        kelly_analysis2_matches = None
                    else:
                        # 执行凯利分析2筛选
                        kelly_analysis2_matches = self.apply_kelly_analysis2_filter(
                            kelly_type, stats_count, kelly_threshold, selected_companies, meaningless_threshold
                        )

                        kelly_type_name = {"kelly_home": "凯利主", "kelly_draw": "凯利平", "kelly_away": "凯利客"}[kelly_type]
                        filter_descriptions.append(f"凯利分析2: {kelly_type_name}(统计{stats_count}家,观察{len(selected_companies)}家公司)")

                except (ValueError, TypeError) as e:
                    logger.warning(f"凯利分析2筛选参数错误: {e}")
                    kelly_analysis2_matches = None

            # 博彩态度筛选
            betting_attitude_matches = None
            if self.filter_widgets['betting_attitude']['enable'].get():
                try:
                    odds_type = self.filter_widgets['betting_attitude']['odds_type'].get()
                    threshold = float(self.filter_widgets['betting_attitude']['threshold'].get())

                    # 获取选中的博彩公司
                    selected_companies = []
                    for company, var in self.filter_widgets['betting_attitude']['company_vars'].items():
                        if var.get():
                            selected_companies.append(company)

                    if not selected_companies:
                        logger.warning("博彩态度: 未选择任何博彩公司")
                        betting_attitude_matches = None
                    else:
                        # 执行博彩态度筛选
                        betting_attitude_matches = self.apply_betting_attitude_filter(
                            odds_type, threshold, selected_companies
                        )

                        odds_type_name = {"home_odds": "主胜", "draw_odds": "平局", "away_odds": "客胜"}[odds_type]
                        filter_descriptions.append(f"博彩态度: {odds_type_name}(阈值{threshold},监控{len(selected_companies)}家公司)")

                except (ValueError, TypeError) as e:
                    logger.warning(f"博彩态度筛选参数错误: {e}")
                    betting_attitude_matches = None

            # 博彩态度2筛选
            betting_attitude2_matches = None
            if self.filter_widgets['betting_attitude2']['enable'].get():
                try:
                    odds_type = self.filter_widgets['betting_attitude2']['odds_type'].get()
                    threshold = int(self.filter_widgets['betting_attitude2']['threshold'].get())
                    selected_company = self.filter_widgets['betting_attitude2']['company'].get()

                    if not selected_company:
                        logger.warning("博彩态度2: 未选择博彩公司")
                        betting_attitude2_matches = None
                    else:
                        # 执行博彩态度2筛选
                        betting_attitude2_matches = self.apply_betting_attitude2_filter(
                            odds_type, threshold, selected_company
                        )

                        odds_type_name = {"home_odds": "主胜", "draw_odds": "平局", "away_odds": "客胜"}[odds_type]
                        filter_descriptions.append(f"博彩态度2: {odds_type_name}(阈值{threshold},{selected_company})")

                except (ValueError, TypeError) as e:
                    logger.warning(f"博彩态度2筛选参数错误: {e}")
                    betting_attitude2_matches = None

            # 构建完整查询
            base_query = '''
                SELECT match_id, league, season, round_info, home_team, away_team,
                       match_time, match_state, home_score, away_score,
                       accurate_datetime, accurate_date, accurate_time, weekday,
                       match_year, match_month, match_day, match_hour, match_minute,
                       time_source, created_at
                FROM matches
            '''

            if where_conditions:
                query = base_query + " WHERE " + " AND ".join(where_conditions)
            else:
                query = base_query

            query += " ORDER BY accurate_datetime DESC, match_time DESC, created_at DESC"

            # 执行查询
            with sqlite3.connect(current_db.db_path) as conn:
                conn.row_factory = sqlite3.Row
                cursor = conn.cursor()
                cursor.execute(query, params)
                rows = cursor.fetchall()
                matches_from_sql = [dict(row) for row in rows]

            # 如果有高级筛选，需要进一步过滤结果
            if kelly_analysis_matches is not None or kelly_analysis2_matches is not None or betting_attitude_matches is not None or betting_attitude2_matches is not None:
                # 开始时使用SQL查询结果
                filtered_matches = matches_from_sql

                # 如果有凯利分析筛选，取交集
                if kelly_analysis_matches is not None:
                    kelly_match_ids = set(kelly_analysis_matches)
                    filtered_matches = [
                        match for match in filtered_matches
                        if match.get('match_id') in kelly_match_ids
                    ]

                # 如果有凯利分析2筛选，取交集
                if kelly_analysis2_matches is not None:
                    kelly2_match_ids = set(kelly_analysis2_matches)
                    filtered_matches = [
                        match for match in filtered_matches
                        if match.get('match_id') in kelly2_match_ids
                    ]

                # 如果有博彩态度筛选，取交集
                if betting_attitude_matches is not None:
                    betting_attitude_match_ids = set(betting_attitude_matches)
                    filtered_matches = [
                        match for match in filtered_matches
                        if match.get('match_id') in betting_attitude_match_ids
                    ]

                # 如果有博彩态度2筛选，取交集
                if betting_attitude2_matches is not None:
                    betting_attitude2_match_ids = set(betting_attitude2_matches)
                    filtered_matches = [
                        match for match in filtered_matches
                        if match.get('match_id') in betting_attitude2_match_ids
                    ]

                self.filtered_matches = filtered_matches
            else:
                self.filtered_matches = matches_from_sql

            # 更新筛选状态显示
            if filter_descriptions:
                status_text = f"已筛选 ({len(self.filtered_matches)} 场): " + " | ".join(filter_descriptions)
            else:
                status_text = f"当前显示: 所有比赛 ({len(self.filtered_matches)} 场)"
            self.filter_status_var.set(status_text)

            # 更新比赛列表显示
            self.refresh_matches_list_with_filter()

            # 自动切换到比赛列表标签页
            self.notebook.select(0)  # 比赛列表是第一个标签页

        except Exception as e:
            logger.error(f"应用筛选失败: {e}")
            messagebox.showerror("错误", f"应用筛选失败: {e}")

    def refresh_matches_list_with_filter(self):
        """使用筛选结果刷新比赛列表"""
        # 清空现有数据
        for item in self.matches_tree.get_children():
            self.matches_tree.delete(item)

        # 获取当前选择的数据库
        current_db = self.get_current_database()

        # 使用筛选结果或所有比赛
        matches = self.filtered_matches if self.filtered_matches else current_db.get_all_matches()

        for match in matches:
            teams = f"{match.get('home_team', '')} vs {match.get('away_team', '')}"
            score = f"{match.get('home_score', '')}-{match.get('away_score', '')}" if match.get('home_score') else ""

            # 获取该比赛的博彩公司数量
            match_id = match.get('match_id', '')
            data_summary = current_db.get_match_data_summary(match_id)
            company_count = data_summary.get('company_count', 0)

            # 格式化博彩公司数量显示
            if company_count > 0:
                companies_display = f"{company_count}家"
            else:
                companies_display = "无数据"

            # 格式化时间显示
            display_time = match.get('accurate_datetime') or match.get('match_time', '')
            if display_time and len(display_time) > 16:
                display_time = display_time[:16]

            # 格式化赛季信息
            season_info = match.get('season', '')
            if not season_info:
                # 尝试从比赛时间推断赛季
                try:
                    season_info = get_season_for_match(match)
                except:
                    season_info = "未知"

            # 格式化轮次信息
            round_display = match.get('round_info', '')
            if not round_display:
                round_display = "未知"

            self.matches_tree.insert("", "end", values=(
                match_id,
                match.get('league', ''),
                season_info,
                round_display,
                teams,
                display_time,
                score,
                match.get('match_state', ''),
                companies_display
            ))

    def clear_filters(self):
        """清除筛选结果"""
        self.filtered_matches = []
        self.filter_status_var.set("当前显示: 所有比赛")
        # 刷新比赛列表显示所有比赛
        self.refresh_matches_list_with_filter()
        # 自动切换到比赛列表标签页
        self.notebook.select(0)

    def reset_filter_conditions(self):
        """重置筛选条件"""
        try:
            # 重置所有筛选条件的启用状态
            for filter_name, widgets in self.filter_widgets.items():
                widgets['enable'].set(False)

                # 重置具体的筛选值
                if filter_name in ['league', 'season', 'match_state']:
                    widgets['listbox'].selection_clear(0, tk.END)
                elif filter_name == 'time_range':
                    widgets['start_time'].set("2024-01-01")
                    widgets['end_time'].set("2025-12-31")
                elif filter_name == 'team':
                    widgets['team_name'].set("")
                elif filter_name == 'score':
                    widgets['score_type'].set("all")
                elif filter_name == 'kelly_analysis':
                    widgets['kelly_type'].set("kelly_home")
                    widgets['stats_count'].set("10")
                    widgets['kelly_threshold'].set("1.01")
                    widgets['return_rate_threshold'].set("97")
                elif filter_name == 'kelly_analysis2':
                    widgets['kelly_type'].set("kelly_home")
                    widgets['stats_count'].set("10")
                    widgets['kelly_threshold'].set("1.05")
                    widgets['meaningless_threshold'].set("2")
                    # 取消所有博彩公司的选择
                    for company_var in widgets['company_vars'].values():
                        company_var.set(False)

            # 清除筛选结果
            self.clear_filters()

        except Exception as e:
            logger.error(f"重置筛选条件失败: {e}")

    def apply_kelly_analysis_filter(self, kelly_type, stats_count, kelly_threshold, return_rate_threshold):
        """应用凯利分析筛选"""
        try:
            current_db = self.get_current_database()
            qualified_matches = []

            # 获取所有有赔率数据的比赛
            with sqlite3.connect(current_db.db_path) as conn:
                conn.row_factory = sqlite3.Row
                cursor = conn.cursor()

                # 获取所有比赛ID
                cursor.execute('SELECT DISTINCT match_id FROM odds WHERE {} IS NOT NULL'.format(kelly_type))
                match_ids = [row[0] for row in cursor.fetchall()]

                logger.info(f"开始凯利分析筛选，共 {len(match_ids)} 场比赛")

                # 对每场比赛进行凯利分析
                for match_id in match_ids:
                    if self.analyze_match_kelly(match_id, kelly_type, stats_count, kelly_threshold, return_rate_threshold):
                        qualified_matches.append(match_id)

                logger.info(f"凯利分析筛选完成，符合条件的比赛: {len(qualified_matches)} 场")

            return qualified_matches

        except Exception as e:
            logger.error(f"凯利分析筛选失败: {e}")
            return []

    def analyze_match_kelly(self, match_id, kelly_type, stats_count, kelly_threshold, return_rate_threshold):
        """分析单场比赛的凯利指数"""
        try:
            current_db = self.get_current_database()

            with sqlite3.connect(current_db.db_path) as conn:
                conn.row_factory = sqlite3.Row
                cursor = conn.cursor()

                # 获取该比赛的所有赔率数据，按凯利指数降序排列
                cursor.execute(f'''
                    SELECT {kelly_type}, return_rate
                    FROM odds
                    WHERE match_id = ? AND {kelly_type} IS NOT NULL AND return_rate IS NOT NULL
                    ORDER BY {kelly_type} DESC
                ''', (match_id,))

                odds_data = cursor.fetchall()

                if len(odds_data) < stats_count:
                    # 如果数据不足指定的统计数量，跳过该比赛
                    return False

                # 取前N家的数据
                top_odds = odds_data[:stats_count]

                # 计算平均凯利值和平均返还率
                kelly_values = [float(row[0]) for row in top_odds]
                return_rates = [float(row[1]) for row in top_odds]

                avg_kelly = sum(kelly_values) / len(kelly_values)
                avg_return_rate = sum(return_rates) / len(return_rates)

                # 判断是否符合筛选条件
                # 注意：返还率在数据库中是百分比形式（如96.44表示96.44%）
                kelly_qualified = avg_kelly > kelly_threshold
                return_rate_qualified = avg_return_rate < return_rate_threshold

                if kelly_qualified and return_rate_qualified:
                    logger.debug(f"比赛 {match_id} 符合凯利分析条件: "
                               f"平均凯利={avg_kelly:.3f}(>{kelly_threshold}), "
                               f"平均返还率={avg_return_rate:.2f}%(<{return_rate_threshold}%)")
                    return True

                return False

        except Exception as e:
            logger.warning(f"分析比赛 {match_id} 凯利指数失败: {e}")
            return False

    def apply_kelly_analysis2_filter(self, kelly_type, stats_count, kelly_threshold, selected_companies, meaningless_threshold):
        """应用凯利分析2筛选"""
        try:
            current_db = self.get_current_database()
            qualified_matches = []

            # 获取所有有赔率数据的比赛
            with sqlite3.connect(current_db.db_path) as conn:
                conn.row_factory = sqlite3.Row
                cursor = conn.cursor()

                # 获取所有比赛ID
                cursor.execute('SELECT DISTINCT match_id FROM odds WHERE {} IS NOT NULL'.format(kelly_type))
                match_ids = [row[0] for row in cursor.fetchall()]

                logger.info(f"开始凯利分析2筛选，共 {len(match_ids)} 场比赛")

                # 对每场比赛进行凯利分析2
                for match_id in match_ids:
                    if self.analyze_match_kelly2(match_id, kelly_type, stats_count, kelly_threshold, selected_companies, meaningless_threshold):
                        qualified_matches.append(match_id)

                logger.info(f"凯利分析2筛选完成，符合条件的比赛: {len(qualified_matches)} 场")

            return qualified_matches

        except Exception as e:
            logger.error(f"凯利分析2筛选失败: {e}")
            return []

    def analyze_match_kelly2(self, match_id, kelly_type, stats_count, kelly_threshold, selected_companies, meaningless_threshold):
        """分析单场比赛的凯利指数2"""
        try:
            current_db = self.get_current_database()

            with sqlite3.connect(current_db.db_path) as conn:
                conn.row_factory = sqlite3.Row
                cursor = conn.cursor()

                # 获取该比赛的所有赔率数据，按凯利指数降序排列
                cursor.execute(f'''
                    SELECT {kelly_type}, return_rate, company_name
                    FROM odds
                    WHERE match_id = ? AND {kelly_type} IS NOT NULL AND return_rate IS NOT NULL AND company_name IS NOT NULL
                    ORDER BY {kelly_type} DESC
                ''', (match_id,))

                odds_data = cursor.fetchall()

                if len(odds_data) < stats_count:
                    # 如果数据不足指定的统计数量，跳过该比赛
                    return False

                # 取前N家的数据
                top_odds = odds_data[:stats_count]

                # 计算平均凯利值和平均返还率
                kelly_values = [float(row[0]) for row in top_odds]
                return_rates = [float(row[1]) for row in top_odds]
                company_list = [row[2] for row in top_odds]  # 获取公司列表（包含重复项）

                avg_kelly = sum(kelly_values) / len(kelly_values)
                avg_return_rate = sum(return_rates) / len(return_rates)

                # 统计选中的博彩公司在公司列表中出现的次数（忽略大小写）
                observation_count = 0
                for company in selected_companies:
                    # 忽略大小写进行匹配
                    for list_company in company_list:
                        if company.lower() == list_company.lower():
                            observation_count += 1

                # 判断是否符合筛选条件
                kelly_qualified = avg_kelly > kelly_threshold
                observation_qualified = observation_count <= meaningless_threshold

                if kelly_qualified and observation_qualified:
                    logger.debug(f"比赛 {match_id} 符合凯利分析2条件: "
                               f"平均凯利={avg_kelly:.3f}(>{kelly_threshold}), "
                               f"观察次数={observation_count}(<={meaningless_threshold})")
                    return True

                return False

        except Exception as e:
            logger.warning(f"分析比赛 {match_id} 凯利指数2失败: {e}")
            return False

    def apply_betting_attitude_filter(self, odds_type, threshold, selected_companies):
        """应用博彩态度筛选"""
        try:
            current_db = self.get_current_database()
            qualified_matches = []

            # 获取所有有赔率数据的比赛
            with sqlite3.connect(current_db.db_path) as conn:
                conn.row_factory = sqlite3.Row
                cursor = conn.cursor()

                # 获取所有比赛ID
                cursor.execute(f'SELECT DISTINCT match_id FROM odds WHERE {odds_type} IS NOT NULL')
                match_ids = [row[0] for row in cursor.fetchall()]

                logger.info(f"开始博彩态度筛选，共 {len(match_ids)} 场比赛")

                # 对每场比赛进行博彩态度分析
                for match_id in match_ids:
                    if self.analyze_match_betting_attitude(match_id, odds_type, threshold, selected_companies):
                        qualified_matches.append(match_id)

                logger.info(f"博彩态度筛选完成，符合条件的比赛: {len(qualified_matches)} 场")

            return qualified_matches

        except Exception as e:
            logger.error(f"博彩态度筛选失败: {e}")
            return []

    def apply_betting_attitude2_filter(self, odds_type, threshold, selected_company):
        """应用博彩态度2筛选"""
        try:
            current_db = self.get_current_database()
            qualified_matches = []

            # 获取所有有赔率数据的比赛
            with sqlite3.connect(current_db.db_path) as conn:
                conn.row_factory = sqlite3.Row
                cursor = conn.cursor()

                # 获取所有比赛ID
                cursor.execute(f'SELECT DISTINCT match_id FROM odds WHERE {odds_type} IS NOT NULL')
                match_ids = [row[0] for row in cursor.fetchall()]

                logger.info(f"开始博彩态度2筛选，共 {len(match_ids)} 场比赛")

                # 对每场比赛进行博彩态度2分析
                for match_id in match_ids:
                    if self.analyze_match_betting_attitude2(match_id, odds_type, threshold, selected_company):
                        qualified_matches.append(match_id)

                logger.info(f"博彩态度2筛选完成，符合条件的比赛: {len(qualified_matches)} 场")

            return qualified_matches

        except Exception as e:
            logger.error(f"博彩态度2筛选失败: {e}")
            return []

    def analyze_match_betting_attitude2(self, match_id, odds_type, threshold, target_company):
        """分析单场比赛的博彩态度2"""
        try:
            current_db = self.get_current_database()

            with sqlite3.connect(current_db.db_path) as conn:
                conn.row_factory = sqlite3.Row
                cursor = conn.cursor()

                # 获取目标公司的开盘时间和赔率
                cursor.execute(f'''
                    SELECT date, time, {odds_type}
                    FROM odds
                    WHERE match_id = ? AND company_name = ? AND {odds_type} IS NOT NULL
                    ORDER BY date ASC, time ASC
                    LIMIT 1
                ''', (match_id, target_company))

                target_odds_record = cursor.fetchone()
                if not target_odds_record:
                    # 如果该公司没有数据，跳过这个比赛
                    logger.debug(f"比赛 {match_id} 公司 {target_company} 没有数据")
                    return False

                target_date = target_odds_record['date']
                target_time = target_odds_record['time']
                target_odds = float(target_odds_record[odds_type])

                # 获取该时间点所有其他公司的赔率（使用动态时间线逻辑）
                other_companies_odds = self.get_other_companies_odds_at_time(
                    cursor, match_id, target_company, target_date, target_time, odds_type
                )

                if len(other_companies_odds) == 0:
                    # 如果没有其他公司在该时间点或之前开盘，跳过这个比赛
                    logger.debug(f"比赛 {match_id} 公司 {target_company} 在开盘时间点 {target_date} {target_time} 没有其他公司数据")
                    return False

                # 统计其他公司中赔率 > 目标公司赔率的数量
                higher_count = 0
                for record in other_companies_odds:
                    other_odds = float(record[odds_type])
                    if other_odds > target_odds:
                        higher_count += 1

                # 检查是否满足阈值条件：数量小于阈值
                if higher_count < threshold:
                    logger.debug(f"比赛 {match_id} 公司 {target_company} 满足博彩态度2条件: "
                               f"目标赔率={target_odds:.3f}, 其他公司中>目标赔率的数量={higher_count}(<{threshold})")
                    return True
                else:
                    logger.debug(f"比赛 {match_id} 公司 {target_company} 不满足博彩态度2条件: "
                               f"目标赔率={target_odds:.3f}, 其他公司中>目标赔率的数量={higher_count}(>={threshold})")
                    return False

        except Exception as e:
            logger.warning(f"分析比赛 {match_id} 博彩态度2失败: {e}")
            return False

    def get_other_companies_odds_at_time(self, cursor, match_id, target_company, target_date, target_time, odds_type):
        """获取其他公司在指定时间点的赔率（参考动态时间线逻辑）"""
        try:
            from datetime import datetime

            # 构造目标时间点 - 动态获取年份
            # 先尝试2024年，如果失败再尝试2025年
            target_datetime = None
            for year in [2024, 2025]:
                try:
                    target_datetime_str = f"{year}-{target_date} {target_time}:00"
                    target_datetime = datetime.strptime(target_datetime_str, "%Y-%m-%d %H:%M:%S")
                    break
                except ValueError:
                    continue

            if target_datetime is None:
                logger.error(f"无法解析时间: {target_date} {target_time}")
                return []

            # 获取所有其他公司的赔率数据
            cursor.execute(f'''
                SELECT company_name, date, time, {odds_type}
                FROM odds
                WHERE match_id = ? AND company_name != ? AND {odds_type} IS NOT NULL
                ORDER BY company_name, date ASC, time ASC
            ''', (match_id, target_company))

            all_other_odds = cursor.fetchall()

            # 按公司分组
            company_odds = {}
            for record in all_other_odds:
                company_name = record['company_name']
                if company_name not in company_odds:
                    company_odds[company_name] = []

                # 构造该记录的时间 - 动态获取年份
                record_datetime = None
                for year in [2024, 2025]:
                    try:
                        record_datetime_str = f"{year}-{record['date']} {record['time']}:00"
                        record_datetime = datetime.strptime(record_datetime_str, "%Y-%m-%d %H:%M:%S")
                        break
                    except ValueError:
                        continue

                if record_datetime is not None:
                    company_odds[company_name].append({
                        'datetime': record_datetime,
                        'odds': float(record[odds_type])
                    })

            # 对每个公司找到在目标时间点的赔率
            result_odds = []

            for company_name, odds_list in company_odds.items():
                # 按时间排序
                odds_list.sort(key=lambda x: x['datetime'])

                # 找到该公司在目标时间点的赔率
                target_odds_value = None

                # 首先检查是否在目标时间点有数据
                for odds_record in odds_list:
                    if odds_record['datetime'] == target_datetime:
                        target_odds_value = odds_record['odds']
                        break

                # 如果目标时间点没有数据，找最近的之前时间点
                if target_odds_value is None:
                    latest_before_target = None
                    for odds_record in odds_list:
                        if odds_record['datetime'] < target_datetime:
                            latest_before_target = odds_record
                        else:
                            break  # 已经超过目标时间点

                    if latest_before_target:
                        target_odds_value = latest_before_target['odds']

                # 如果该公司在目标时间点或之前有数据，添加其赔率
                if target_odds_value is not None:
                    result_odds.append({
                        'company_name': company_name,
                        odds_type: target_odds_value
                    })

            logger.debug(f"比赛 {match_id} 在时间点 {target_date} {target_time} 找到 {len(result_odds)} 家其他公司的赔率")
            return result_odds

        except Exception as e:
            logger.error(f"获取其他公司赔率失败: {e}")
            return []

    def analyze_match_betting_attitude(self, match_id, odds_type, threshold, selected_companies):
        """分析单场比赛的博彩态度"""
        try:
            current_db = self.get_current_database()

            with sqlite3.connect(current_db.db_path) as conn:
                conn.row_factory = sqlite3.Row
                cursor = conn.cursor()

                # 对每个选中的博彩公司进行分析
                companies_analyzed = 0  # 记录实际分析的公司数量

                for target_company in selected_companies:
                    # 获取该公司的开盘时间和赔率
                    cursor.execute(f'''
                        SELECT date, time, {odds_type}
                        FROM odds
                        WHERE match_id = ? AND company_name = ? AND {odds_type} IS NOT NULL
                        ORDER BY date ASC, time ASC
                        LIMIT 1
                    ''', (match_id, target_company))

                    target_odds_record = cursor.fetchone()
                    if not target_odds_record:
                        # 如果该公司没有数据，跳过这个公司
                        logger.debug(f"比赛 {match_id} 公司 {target_company} 没有数据")
                        continue

                    target_date = target_odds_record['date']
                    target_time = target_odds_record['time']
                    target_odds = float(target_odds_record[odds_type])

                    # 获取该时间点所有其他公司的赔率（使用动态时间线逻辑）
                    other_companies_odds = self.get_other_companies_odds_at_time(
                        cursor, match_id, target_company, target_date, target_time, odds_type
                    )

                    if len(other_companies_odds) == 0:
                        # 如果没有其他公司在该时间点或之前开盘，跳过这个公司
                        logger.debug(f"比赛 {match_id} 公司 {target_company} 在开盘时间点 {target_date} {target_time} 没有其他公司数据")
                        continue

                    # 计算其他公司赔率的平均值
                    other_odds_values = [float(record[odds_type]) for record in other_companies_odds]
                    avg_other_odds = sum(other_odds_values) / len(other_odds_values)

                    # 计算比率
                    if avg_other_odds > 0:
                        ratio = target_odds / avg_other_odds
                    else:
                        logger.debug(f"比赛 {match_id} 公司 {target_company} 其他公司平均赔率为0")
                        continue

                    companies_analyzed += 1  # 成功分析了一个公司

                    # 检查是否满足阈值条件
                    if ratio < threshold:
                        # 如果任何一个公司不满足条件，整场比赛就不符合
                        logger.debug(f"比赛 {match_id} 公司 {target_company} 不满足博彩态度条件: "
                                   f"比率={ratio:.3f}(<{threshold})")
                        return False

                    logger.debug(f"比赛 {match_id} 公司 {target_company} 满足博彩态度条件: "
                               f"目标赔率={target_odds:.3f}, 平均赔率={avg_other_odds:.3f}, 比率={ratio:.3f}(>={threshold})")

                # 只有当至少分析了一个公司且所有分析的公司都满足条件时，才返回True
                if companies_analyzed > 0:
                    logger.debug(f"比赛 {match_id} 通过博彩态度筛选，分析了 {companies_analyzed} 个公司")
                    return True
                else:
                    logger.debug(f"比赛 {match_id} 没有可分析的公司数据")
                    return False

        except Exception as e:
            logger.warning(f"分析比赛 {match_id} 博彩态度失败: {e}")
            return False

    def normalize_datetime_for_filter(self, time_input, is_start=True):
        """标准化时间输入为数据库查询格式

        Args:
            time_input: 用户输入的时间字符串
            is_start: 是否为开始时间（影响默认时分秒）

        Returns:
            标准化的时间字符串，格式为 YYYY-MM-DD HH:MM:SS
        """
        try:
            time_input = time_input.strip()
            if not time_input:
                return None

            # 处理不同的输入格式
            if len(time_input) == 10 and time_input.count('-') == 2:
                # 格式: YYYY-MM-DD
                if is_start:
                    return f"{time_input} 00:00:00"
                else:
                    return f"{time_input} 23:59:59"

            elif len(time_input) == 16 and time_input.count('-') == 2 and time_input.count(':') == 1:
                # 格式: YYYY-MM-DD HH:MM
                return f"{time_input}:00"

            elif len(time_input) == 19 and time_input.count('-') == 2 and time_input.count(':') == 2:
                # 格式: YYYY-MM-DD HH:MM:SS
                return time_input

            else:
                # 尝试解析其他格式
                from datetime import datetime

                # 尝试常见格式
                formats = [
                    '%Y-%m-%d',
                    '%Y-%m-%d %H:%M',
                    '%Y-%m-%d %H:%M:%S',
                    '%Y/%m/%d',
                    '%Y/%m/%d %H:%M',
                    '%Y/%m/%d %H:%M:%S'
                ]

                for fmt in formats:
                    try:
                        dt = datetime.strptime(time_input, fmt)
                        if fmt == '%Y-%m-%d' or fmt == '%Y/%m/%d':
                            # 只有日期，添加时间
                            if is_start:
                                return dt.strftime('%Y-%m-%d 00:00:00')
                            else:
                                return dt.strftime('%Y-%m-%d 23:59:59')
                        else:
                            return dt.strftime('%Y-%m-%d %H:%M:%S')
                    except ValueError:
                        continue

                logger.warning(f"无法解析时间格式: {time_input}")
                return None

        except Exception as e:
            logger.error(f"时间格式标准化失败: {e}")
            return None

    # ==================== 投资回测相关方法 ====================

    def create_betting_strategies(self):
        """创建投注策略"""
        # 主胜策略
        self.create_home_win_strategy()

        # 平局策略
        self.create_draw_strategy()

        # 客胜策略
        self.create_away_win_strategy()

        # 凯利指数策略
        self.create_kelly_strategy()

        # 低赔率策略
        self.create_low_odds_strategy()

        # 高赔率策略
        self.create_high_odds_strategy()

    def create_home_win_strategy(self):
        """创建主胜投注策略"""
        frame = ttk.LabelFrame(self.strategy_scrollable_frame, text="主胜策略", padding="5")
        frame.pack(fill=tk.X, pady=(0, 5))

        # 启用复选框
        enable_var = tk.BooleanVar()
        enable_check = ttk.Checkbutton(frame, text="启用主胜策略", variable=enable_var)
        enable_check.pack(anchor=tk.W)

        # 策略面板
        strategy_panel = ttk.Frame(frame)
        strategy_panel.pack(fill=tk.X, pady=(5, 0))

        # 赔率范围
        ttk.Label(strategy_panel, text="主胜赔率范围:").pack(anchor=tk.W)
        odds_frame = ttk.Frame(strategy_panel)
        odds_frame.pack(fill=tk.X, pady=(2, 5))

        ttk.Label(odds_frame, text="最小:").pack(side=tk.LEFT)
        min_odds_var = tk.StringVar(value="1.5")
        ttk.Entry(odds_frame, textvariable=min_odds_var, width=8).pack(side=tk.LEFT, padx=(2, 10))

        ttk.Label(odds_frame, text="最大:").pack(side=tk.LEFT)
        max_odds_var = tk.StringVar(value="3.0")
        ttk.Entry(odds_frame, textvariable=max_odds_var, width=8).pack(side=tk.LEFT, padx=(2, 0))

        # 存储控件引用
        self.backtest_widgets['home_win'] = {
            'enable': enable_var,
            'min_odds': min_odds_var,
            'max_odds': max_odds_var,
            'frame': strategy_panel
        }

        # 绑定启用状态变化
        enable_var.trace('w', lambda *args: self.toggle_strategy_panel('home_win'))

        # 初始状态设置为禁用
        self.toggle_strategy_panel('home_win')

    def create_draw_strategy(self):
        """创建平局投注策略"""
        frame = ttk.LabelFrame(self.strategy_scrollable_frame, text="平局策略", padding="5")
        frame.pack(fill=tk.X, pady=(0, 5))

        # 启用复选框
        enable_var = tk.BooleanVar()
        enable_check = ttk.Checkbutton(frame, text="启用平局策略", variable=enable_var)
        enable_check.pack(anchor=tk.W)

        # 策略面板
        strategy_panel = ttk.Frame(frame)
        strategy_panel.pack(fill=tk.X, pady=(5, 0))

        # 赔率范围
        ttk.Label(strategy_panel, text="平局赔率范围:").pack(anchor=tk.W)
        odds_frame = ttk.Frame(strategy_panel)
        odds_frame.pack(fill=tk.X, pady=(2, 5))

        ttk.Label(odds_frame, text="最小:").pack(side=tk.LEFT)
        min_odds_var = tk.StringVar(value="2.5")
        ttk.Entry(odds_frame, textvariable=min_odds_var, width=8).pack(side=tk.LEFT, padx=(2, 10))

        ttk.Label(odds_frame, text="最大:").pack(side=tk.LEFT)
        max_odds_var = tk.StringVar(value="4.0")
        ttk.Entry(odds_frame, textvariable=max_odds_var, width=8).pack(side=tk.LEFT, padx=(2, 0))

        # 存储控件引用
        self.backtest_widgets['draw'] = {
            'enable': enable_var,
            'min_odds': min_odds_var,
            'max_odds': max_odds_var,
            'frame': strategy_panel
        }

        # 绑定启用状态变化
        enable_var.trace('w', lambda *args: self.toggle_strategy_panel('draw'))

        # 初始状态设置为禁用
        self.toggle_strategy_panel('draw')

    def create_away_win_strategy(self):
        """创建客胜投注策略"""
        frame = ttk.LabelFrame(self.strategy_scrollable_frame, text="客胜策略", padding="5")
        frame.pack(fill=tk.X, pady=(0, 5))

        # 启用复选框
        enable_var = tk.BooleanVar()
        enable_check = ttk.Checkbutton(frame, text="启用客胜策略", variable=enable_var)
        enable_check.pack(anchor=tk.W)

        # 策略面板
        strategy_panel = ttk.Frame(frame)
        strategy_panel.pack(fill=tk.X, pady=(5, 0))

        # 赔率范围
        ttk.Label(strategy_panel, text="客胜赔率范围:").pack(anchor=tk.W)
        odds_frame = ttk.Frame(strategy_panel)
        odds_frame.pack(fill=tk.X, pady=(2, 5))

        ttk.Label(odds_frame, text="最小:").pack(side=tk.LEFT)
        min_odds_var = tk.StringVar(value="1.8")
        ttk.Entry(odds_frame, textvariable=min_odds_var, width=8).pack(side=tk.LEFT, padx=(2, 10))

        ttk.Label(odds_frame, text="最大:").pack(side=tk.LEFT)
        max_odds_var = tk.StringVar(value="5.0")
        ttk.Entry(odds_frame, textvariable=max_odds_var, width=8).pack(side=tk.LEFT, padx=(2, 0))

        # 存储控件引用
        self.backtest_widgets['away_win'] = {
            'enable': enable_var,
            'min_odds': min_odds_var,
            'max_odds': max_odds_var,
            'frame': strategy_panel
        }

        # 绑定启用状态变化
        enable_var.trace('w', lambda *args: self.toggle_strategy_panel('away_win'))

        # 初始状态设置为禁用
        self.toggle_strategy_panel('away_win')

    def create_kelly_strategy(self):
        """创建凯利指数策略"""
        frame = ttk.LabelFrame(self.strategy_scrollable_frame, text="凯利指数策略", padding="5")
        frame.pack(fill=tk.X, pady=(0, 5))

        # 启用复选框
        enable_var = tk.BooleanVar()
        enable_check = ttk.Checkbutton(frame, text="启用凯利指数策略", variable=enable_var)
        enable_check.pack(anchor=tk.W)

        # 策略面板
        strategy_panel = ttk.Frame(frame)
        strategy_panel.pack(fill=tk.X, pady=(5, 0))

        # 凯利指数阈值
        ttk.Label(strategy_panel, text="凯利指数阈值:").pack(anchor=tk.W)
        kelly_threshold_var = tk.StringVar(value="0.05")
        ttk.Entry(strategy_panel, textvariable=kelly_threshold_var, width=10).pack(anchor=tk.W, pady=(2, 5))

        # 投注类型选择
        ttk.Label(strategy_panel, text="投注类型:").pack(anchor=tk.W)
        bet_type_var = tk.StringVar(value="best_kelly")

        ttk.Radiobutton(strategy_panel, text="最佳凯利指数", variable=bet_type_var, value="best_kelly").pack(anchor=tk.W)
        ttk.Radiobutton(strategy_panel, text="所有正凯利", variable=bet_type_var, value="all_positive").pack(anchor=tk.W)

        # 存储控件引用
        self.backtest_widgets['kelly'] = {
            'enable': enable_var,
            'kelly_threshold': kelly_threshold_var,
            'bet_type': bet_type_var,
            'frame': strategy_panel
        }

        # 绑定启用状态变化
        enable_var.trace('w', lambda *args: self.toggle_strategy_panel('kelly'))

        # 初始状态设置为禁用
        self.toggle_strategy_panel('kelly')

    def create_low_odds_strategy(self):
        """创建低赔率策略"""
        frame = ttk.LabelFrame(self.strategy_scrollable_frame, text="低赔率策略", padding="5")
        frame.pack(fill=tk.X, pady=(0, 5))

        # 启用复选框
        enable_var = tk.BooleanVar()
        enable_check = ttk.Checkbutton(frame, text="启用低赔率策略", variable=enable_var)
        enable_check.pack(anchor=tk.W)

        # 策略面板
        strategy_panel = ttk.Frame(frame)
        strategy_panel.pack(fill=tk.X, pady=(5, 0))

        # 赔率上限
        ttk.Label(strategy_panel, text="赔率上限:").pack(anchor=tk.W)
        max_odds_var = tk.StringVar(value="1.8")
        ttk.Entry(strategy_panel, textvariable=max_odds_var, width=10).pack(anchor=tk.W, pady=(2, 5))

        # 投注选择
        ttk.Label(strategy_panel, text="投注选择:").pack(anchor=tk.W)
        selection_var = tk.StringVar(value="lowest")

        ttk.Radiobutton(strategy_panel, text="最低赔率", variable=selection_var, value="lowest").pack(anchor=tk.W)
        ttk.Radiobutton(strategy_panel, text="主胜优先", variable=selection_var, value="home_first").pack(anchor=tk.W)

        # 存储控件引用
        self.backtest_widgets['low_odds'] = {
            'enable': enable_var,
            'max_odds': max_odds_var,
            'selection': selection_var,
            'frame': strategy_panel
        }

        # 绑定启用状态变化
        enable_var.trace('w', lambda *args: self.toggle_strategy_panel('low_odds'))

        # 初始状态设置为禁用
        self.toggle_strategy_panel('low_odds')

    def create_high_odds_strategy(self):
        """创建高赔率策略"""
        frame = ttk.LabelFrame(self.strategy_scrollable_frame, text="高赔率策略", padding="5")
        frame.pack(fill=tk.X, pady=(0, 5))

        # 启用复选框
        enable_var = tk.BooleanVar()
        enable_check = ttk.Checkbutton(frame, text="启用高赔率策略", variable=enable_var)
        enable_check.pack(anchor=tk.W)

        # 策略面板
        strategy_panel = ttk.Frame(frame)
        strategy_panel.pack(fill=tk.X, pady=(5, 0))

        # 赔率下限
        ttk.Label(strategy_panel, text="赔率下限:").pack(anchor=tk.W)
        min_odds_var = tk.StringVar(value="3.0")
        ttk.Entry(strategy_panel, textvariable=min_odds_var, width=10).pack(anchor=tk.W, pady=(2, 5))

        # 投注选择
        ttk.Label(strategy_panel, text="投注选择:").pack(anchor=tk.W)
        selection_var = tk.StringVar(value="highest")

        ttk.Radiobutton(strategy_panel, text="最高赔率", variable=selection_var, value="highest").pack(anchor=tk.W)
        ttk.Radiobutton(strategy_panel, text="客胜优先", variable=selection_var, value="away_first").pack(anchor=tk.W)

        # 存储控件引用
        self.backtest_widgets['high_odds'] = {
            'enable': enable_var,
            'min_odds': min_odds_var,
            'selection': selection_var,
            'frame': strategy_panel
        }

        # 绑定启用状态变化
        enable_var.trace('w', lambda *args: self.toggle_strategy_panel('high_odds'))

        # 初始状态设置为禁用
        self.toggle_strategy_panel('high_odds')

    def toggle_strategy_panel(self, strategy_name):
        """切换策略面板的启用状态"""
        if strategy_name in self.backtest_widgets:
            widgets = self.backtest_widgets[strategy_name]
            enabled = widgets['enable'].get()

            # 设置面板中所有控件的状态
            for child in widgets['frame'].winfo_children():
                if hasattr(child, 'configure'):
                    try:
                        child.configure(state='normal' if enabled else 'disabled')
                    except tk.TclError:
                        # 某些控件可能不支持state属性
                        pass
                # 处理嵌套的Frame
                if isinstance(child, ttk.Frame):
                    for subchild in child.winfo_children():
                        if hasattr(subchild, 'configure'):
                            try:
                                subchild.configure(state='normal' if enabled else 'disabled')
                            except tk.TclError:
                                pass

    def start_backtest(self):
        """开始回测"""
        try:
            # 获取当前比赛列表（可能已经筛选过）
            current_matches = self.get_current_matches_for_backtest()

            if not current_matches:
                messagebox.showwarning("警告", "没有可用的比赛数据进行回测")
                return

            # 获取回测参数
            try:
                initial_capital = float(self.initial_capital_var.get())
                bet_amount = float(self.bet_amount_var.get())
                bet_size_strategy = self.bet_size_strategy_var.get()
            except ValueError:
                messagebox.showerror("错误", "请输入有效的数字参数")
                return

            # 获取启用的策略
            enabled_strategies = self.get_enabled_strategies()

            if not enabled_strategies:
                messagebox.showwarning("警告", "请至少启用一个投注策略")
                return

            # 执行回测
            self.backtest_results = []
            current_capital = initial_capital
            total_bets = 0
            winning_bets = 0
            total_profit = 0

            # 更新概览显示
            self.update_backtest_overview("回测进行中...")

            # 清空结果表格
            for item in self.backtest_results_tree.get_children():
                self.backtest_results_tree.delete(item)

            # 对每场比赛进行回测
            for match in current_matches:
                match_id = match.get('match_id')

                # 获取赔率数据
                odds_data = self.get_match_odds_for_backtest(match_id)
                if not odds_data:
                    continue

                # 应用策略
                for strategy_name in enabled_strategies:
                    bet_decisions = self.apply_strategy(strategy_name, match, odds_data)

                    for decision in bet_decisions:
                        # 计算投注金额
                        if bet_size_strategy == "fixed":
                            actual_bet_amount = bet_amount
                        else:  # percentage
                            actual_bet_amount = current_capital * (bet_amount / 100)

                        # 检查是否有足够资金
                        if actual_bet_amount > current_capital:
                            continue

                        # 计算结果
                        bet_result = self.calculate_bet_result(match, decision, actual_bet_amount)

                        # 更新资金
                        current_capital += bet_result['profit']
                        total_bets += 1
                        total_profit += bet_result['profit']

                        if bet_result['profit'] > 0:
                            winning_bets += 1

                        # 记录结果
                        self.backtest_results.append({
                            'match': match,
                            'decision': decision,
                            'result': bet_result,
                            'capital_after': current_capital
                        })

                        # 添加到结果表格
                        self.add_backtest_result_to_tree(match, decision, bet_result)

            # 更新概览
            self.update_backtest_overview_final(initial_capital, current_capital, total_bets, winning_bets, total_profit)

        except Exception as e:
            logger.error(f"回测失败: {e}")
            messagebox.showerror("错误", f"回测失败: {e}")

    def get_current_matches_for_backtest(self):
        """获取当前用于回测的比赛列表"""
        # 如果有筛选结果，使用筛选结果；否则使用所有比赛
        if self.filtered_matches:
            return self.filtered_matches
        else:
            current_db = self.get_current_database()
            return current_db.get_all_matches()

    def get_enabled_strategies(self):
        """获取启用的策略列表"""
        enabled = []
        for strategy_name, widgets in self.backtest_widgets.items():
            if widgets['enable'].get():
                enabled.append(strategy_name)
        return enabled

    def get_match_odds_for_backtest(self, match_id):
        """获取比赛的赔率数据用于回测 - 使用Pinnacle最靠近开赛时间的赔率"""
        current_db = self.get_current_database()

        try:
            with sqlite3.connect(current_db.db_path) as conn:
                conn.row_factory = sqlite3.Row
                cursor = conn.cursor()

                # 首先获取比赛的开赛时间
                cursor.execute('''
                    SELECT accurate_datetime, match_time, match_date
                    FROM matches
                    WHERE match_id = ?
                ''', (match_id,))

                match_info = cursor.fetchone()
                if not match_info:
                    logger.warning(f"未找到比赛 {match_id} 的信息")
                    return []

                # 确定比赛开赛时间
                match_datetime = None
                if match_info['accurate_datetime']:
                    match_datetime = match_info['accurate_datetime']
                elif match_info['match_time'] and match_info['match_date']:
                    match_datetime = f"{match_info['match_date']} {match_info['match_time']}"
                elif match_info['match_time']:
                    match_datetime = match_info['match_time']

                if not match_datetime:
                    logger.warning(f"比赛 {match_id} 没有有效的开赛时间")
                    return []

                # 获取Pinnacle的所有赔率数据，按时间排序
                cursor.execute('''
                    SELECT * FROM odds
                    WHERE match_id = ? AND company_name = 'pinnacle'
                    AND home_odds IS NOT NULL AND draw_odds IS NOT NULL AND away_odds IS NOT NULL
                    ORDER BY date DESC, time DESC
                ''', (match_id,))

                pinnacle_odds = cursor.fetchall()

                if not pinnacle_odds:
                    logger.warning(f"比赛 {match_id} 没有找到Pinnacle的赔率数据")
                    # 如果没有Pinnacle数据，回退到原来的逻辑
                    return self.get_fallback_odds_for_backtest(match_id)

                # 找到最靠近开赛时间的Pinnacle赔率
                best_odds = None
                min_time_diff = float('inf')

                for odds in pinnacle_odds:
                    odds_datetime = f"{odds['date']} {odds['time']}" if odds['date'] and odds['time'] else None

                    if odds_datetime:
                        try:
                            # 计算时间差（简单的字符串比较，假设格式一致）
                            time_diff = abs(self.calculate_time_difference(match_datetime, odds_datetime))

                            if time_diff < min_time_diff:
                                min_time_diff = time_diff
                                best_odds = dict(odds)
                        except Exception as e:
                            logger.debug(f"时间比较失败: {e}")
                            continue

                if best_odds:
                    logger.info(f"比赛 {match_id} 使用Pinnacle赔率，时间差: {min_time_diff}")
                    return [best_odds]
                else:
                    # 如果无法确定最佳时间，使用最新的Pinnacle赔率
                    logger.info(f"比赛 {match_id} 使用Pinnacle最新赔率")
                    return [dict(pinnacle_odds[0])]

        except Exception as e:
            logger.error(f"获取Pinnacle赔率失败: {e}")
            return self.get_fallback_odds_for_backtest(match_id)

    def get_fallback_odds_for_backtest(self, match_id):
        """回退方案：获取其他博彩公司的赔率数据"""
        current_db = self.get_current_database()
        odds_data = current_db.get_odds_data(match_id)

        if odds_data:
            # 按公司分组，取每个公司的最新赔率
            company_odds = {}
            for odds in odds_data:
                company = odds.get('company_name', 'Unknown')
                if company not in company_odds:
                    company_odds[company] = odds
                else:
                    # 比较时间，取最新的
                    current_time = odds.get('date', '') + ' ' + odds.get('time', '')
                    existing_time = company_odds[company].get('date', '') + ' ' + company_odds[company].get('time', '')
                    if current_time > existing_time:
                        company_odds[company] = odds

            return list(company_odds.values())

        return []

    def calculate_time_difference(self, time1, time2):
        """计算两个时间字符串的差值（简化版本）"""
        try:
            from datetime import datetime

            # 尝试多种时间格式
            formats = [
                '%Y-%m-%d %H:%M:%S',
                '%Y-%m-%d %H:%M',
                '%m-%d %H:%M',
                '%H:%M'
            ]

            dt1 = None
            dt2 = None

            for fmt in formats:
                try:
                    dt1 = datetime.strptime(time1, fmt)
                    break
                except ValueError:
                    continue

            for fmt in formats:
                try:
                    dt2 = datetime.strptime(time2, fmt)
                    break
                except ValueError:
                    continue

            if dt1 and dt2:
                return abs((dt1 - dt2).total_seconds())
            else:
                # 如果无法解析，使用字符串比较
                return abs(len(time1) - len(time2))

        except Exception:
            # 最后的回退方案
            return 0

    def apply_strategy(self, strategy_name, match, odds_data):
        """应用投注策略 - 现在使用Pinnacle赔率"""
        decisions = []

        if not odds_data:
            return decisions

        # 使用Pinnacle的赔率数据（现在odds_data应该只包含一个Pinnacle赔率记录）
        pinnacle_odds = odds_data[0]

        try:
            home_odds = float(pinnacle_odds.get('home_odds', 0))
            draw_odds = float(pinnacle_odds.get('draw_odds', 0))
            away_odds = float(pinnacle_odds.get('away_odds', 0))

            if strategy_name == 'home_win':
                decisions.extend(self.apply_home_win_strategy(home_odds, draw_odds, away_odds))
            elif strategy_name == 'draw':
                decisions.extend(self.apply_draw_strategy(home_odds, draw_odds, away_odds))
            elif strategy_name == 'away_win':
                decisions.extend(self.apply_away_win_strategy(home_odds, draw_odds, away_odds))
            elif strategy_name == 'kelly':
                decisions.extend(self.apply_kelly_strategy(pinnacle_odds))
            elif strategy_name == 'low_odds':
                decisions.extend(self.apply_low_odds_strategy(home_odds, draw_odds, away_odds))
            elif strategy_name == 'high_odds':
                decisions.extend(self.apply_high_odds_strategy(home_odds, draw_odds, away_odds))

        except (ValueError, TypeError) as e:
            logger.warning(f"策略应用失败 {strategy_name}: {e}")

        return decisions

    def apply_home_win_strategy(self, home_odds, draw_odds, away_odds):
        """应用主胜策略"""
        decisions = []
        widgets = self.backtest_widgets['home_win']

        try:
            min_odds = float(widgets['min_odds'].get())
            max_odds = float(widgets['max_odds'].get())

            if min_odds <= home_odds <= max_odds:
                decisions.append({
                    'strategy': '主胜策略',
                    'bet_type': '主胜',
                    'odds': home_odds
                })
        except ValueError:
            pass

        return decisions

    def apply_draw_strategy(self, home_odds, draw_odds, away_odds):
        """应用平局策略"""
        decisions = []
        widgets = self.backtest_widgets['draw']

        try:
            min_odds = float(widgets['min_odds'].get())
            max_odds = float(widgets['max_odds'].get())

            if min_odds <= draw_odds <= max_odds:
                decisions.append({
                    'strategy': '平局策略',
                    'bet_type': '平局',
                    'odds': draw_odds
                })
        except ValueError:
            pass

        return decisions

    def apply_away_win_strategy(self, home_odds, draw_odds, away_odds):
        """应用客胜策略"""
        decisions = []
        widgets = self.backtest_widgets['away_win']

        try:
            min_odds = float(widgets['min_odds'].get())
            max_odds = float(widgets['max_odds'].get())

            if min_odds <= away_odds <= max_odds:
                decisions.append({
                    'strategy': '客胜策略',
                    'bet_type': '客胜',
                    'odds': away_odds
                })
        except ValueError:
            pass

        return decisions

    def apply_kelly_strategy(self, odds_data):
        """应用凯利指数策略"""
        decisions = []
        widgets = self.backtest_widgets['kelly']

        try:
            kelly_threshold = float(widgets['kelly_threshold'].get())
            bet_type = widgets['bet_type'].get()

            # 获取凯利指数
            kelly_home = float(odds_data.get('kelly_home', 0))
            kelly_draw = float(odds_data.get('kelly_draw', 0))
            kelly_away = float(odds_data.get('kelly_away', 0))

            home_odds = float(odds_data.get('home_odds', 0))
            draw_odds = float(odds_data.get('draw_odds', 0))
            away_odds = float(odds_data.get('away_odds', 0))

            kelly_values = [
                ('主胜', kelly_home, home_odds),
                ('平局', kelly_draw, draw_odds),
                ('客胜', kelly_away, away_odds)
            ]

            if bet_type == 'best_kelly':
                # 选择最佳凯利指数
                best_kelly = max(kelly_values, key=lambda x: x[1])
                if best_kelly[1] >= kelly_threshold:
                    decisions.append({
                        'strategy': '凯利指数策略',
                        'bet_type': best_kelly[0],
                        'odds': best_kelly[2]
                    })
            else:  # all_positive
                # 所有正凯利指数
                for bet_name, kelly_val, odds_val in kelly_values:
                    if kelly_val >= kelly_threshold:
                        decisions.append({
                            'strategy': '凯利指数策略',
                            'bet_type': bet_name,
                            'odds': odds_val
                        })

        except (ValueError, TypeError):
            pass

        return decisions

    def apply_low_odds_strategy(self, home_odds, draw_odds, away_odds):
        """应用低赔率策略"""
        decisions = []
        widgets = self.backtest_widgets['low_odds']

        try:
            max_odds = float(widgets['max_odds'].get())
            selection = widgets['selection'].get()

            odds_list = [
                ('主胜', home_odds),
                ('平局', draw_odds),
                ('客胜', away_odds)
            ]

            # 筛选符合条件的赔率
            valid_odds = [(name, odds) for name, odds in odds_list if odds <= max_odds and odds > 0]

            if valid_odds:
                if selection == 'lowest':
                    # 选择最低赔率
                    best_bet = min(valid_odds, key=lambda x: x[1])
                    decisions.append({
                        'strategy': '低赔率策略',
                        'bet_type': best_bet[0],
                        'odds': best_bet[1]
                    })
                elif selection == 'home_first':
                    # 主胜优先
                    for name, odds in valid_odds:
                        if name == '主胜':
                            decisions.append({
                                'strategy': '低赔率策略',
                                'bet_type': name,
                                'odds': odds
                            })
                            break
                    else:
                        # 如果没有主胜，选择最低赔率
                        if valid_odds:
                            best_bet = min(valid_odds, key=lambda x: x[1])
                            decisions.append({
                                'strategy': '低赔率策略',
                                'bet_type': best_bet[0],
                                'odds': best_bet[1]
                            })

        except ValueError:
            pass

        return decisions

    def apply_high_odds_strategy(self, home_odds, draw_odds, away_odds):
        """应用高赔率策略"""
        decisions = []
        widgets = self.backtest_widgets['high_odds']

        try:
            min_odds = float(widgets['min_odds'].get())
            selection = widgets['selection'].get()

            odds_list = [
                ('主胜', home_odds),
                ('平局', draw_odds),
                ('客胜', away_odds)
            ]

            # 筛选符合条件的赔率
            valid_odds = [(name, odds) for name, odds in odds_list if odds >= min_odds]

            if valid_odds:
                if selection == 'highest':
                    # 选择最高赔率
                    best_bet = max(valid_odds, key=lambda x: x[1])
                    decisions.append({
                        'strategy': '高赔率策略',
                        'bet_type': best_bet[0],
                        'odds': best_bet[1]
                    })
                elif selection == 'away_first':
                    # 客胜优先
                    for name, odds in valid_odds:
                        if name == '客胜':
                            decisions.append({
                                'strategy': '高赔率策略',
                                'bet_type': name,
                                'odds': odds
                            })
                            break
                    else:
                        # 如果没有客胜，选择最高赔率
                        if valid_odds:
                            best_bet = max(valid_odds, key=lambda x: x[1])
                            decisions.append({
                                'strategy': '高赔率策略',
                                'bet_type': best_bet[0],
                                'odds': best_bet[1]
                            })

        except ValueError:
            pass

        return decisions

    def calculate_bet_result(self, match, decision, bet_amount):
        """计算投注结果"""
        try:
            home_score = match.get('home_score', '')
            away_score = match.get('away_score', '')

            # 如果没有比分，无法计算结果
            if not home_score or not away_score:
                return {
                    'result': '无结果',
                    'profit': 0,
                    'win': False
                }

            home_score = int(home_score)
            away_score = int(away_score)

            # 确定实际比赛结果
            if home_score > away_score:
                actual_result = '主胜'
            elif home_score == away_score:
                actual_result = '平局'
            else:
                actual_result = '客胜'

            # 判断投注是否获胜
            bet_type = decision['bet_type']
            odds = decision['odds']

            if bet_type == actual_result:
                # 投注获胜
                profit = bet_amount * (odds - 1)
                return {
                    'result': '胜',
                    'profit': profit,
                    'win': True
                }
            else:
                # 投注失败
                return {
                    'result': '负',
                    'profit': -bet_amount,
                    'win': False
                }

        except (ValueError, TypeError) as e:
            logger.warning(f"计算投注结果失败: {e}")
            return {
                'result': '错误',
                'profit': 0,
                'win': False
            }

    def add_backtest_result_to_tree(self, match, decision, bet_result):
        """添加回测结果到表格"""
        teams = f"{match.get('home_team', '')} vs {match.get('away_team', '')}"
        match_time = match.get('accurate_datetime') or match.get('match_time', '')

        # 格式化时间显示
        if match_time and len(match_time) > 16:
            match_time = match_time[:16]

        # 格式化盈亏显示
        profit = bet_result['profit']
        if profit > 0:
            profit_str = f"+{profit:.2f}"
        elif profit < 0:
            profit_str = f"{profit:.2f}"
        else:
            profit_str = "0.00"

        self.backtest_results_tree.insert("", "end", values=(
            match.get('match_id', ''),
            teams,
            match_time,
            decision['strategy'],
            decision['bet_type'],
            f"{bet_result.get('bet_amount', 0):.2f}",
            f"{decision['odds']:.2f}",
            bet_result['result'],
            profit_str
        ))

    def update_backtest_overview(self, message):
        """更新回测概览信息"""
        self.backtest_overview_text.configure(state=tk.NORMAL)
        self.backtest_overview_text.delete(1.0, tk.END)
        self.backtest_overview_text.insert(tk.END, message)
        self.backtest_overview_text.configure(state=tk.DISABLED)

    def update_backtest_overview_final(self, initial_capital, final_capital, total_bets, winning_bets, total_profit):
        """更新最终回测概览"""
        win_rate = (winning_bets / total_bets * 100) if total_bets > 0 else 0
        roi = (total_profit / initial_capital * 100) if initial_capital > 0 else 0

        overview_text = f"""回测完成！

初始资金: {initial_capital:,.2f}
最终资金: {final_capital:,.2f}
总盈亏: {total_profit:+,.2f}
投资回报率: {roi:+.2f}%

总投注次数: {total_bets}
获胜次数: {winning_bets}
胜率: {win_rate:.2f}%

平均每注盈亏: {(total_profit / total_bets) if total_bets > 0 else 0:.2f}
"""

        self.update_backtest_overview(overview_text)

    def clear_backtest_results(self):
        """清除回测结果"""
        self.backtest_results = []

        # 清空结果表格
        for item in self.backtest_results_tree.get_children():
            self.backtest_results_tree.delete(item)

        # 清空概览
        self.update_backtest_overview("等待回测...")

    def export_backtest_report(self):
        """导出回测报告"""
        if not self.backtest_results:
            messagebox.showwarning("警告", "没有回测结果可以导出")
            return

        try:
            from datetime import datetime
            import csv

            # 选择保存文件
            filename = filedialog.asksaveasfilename(
                defaultextension=".csv",
                filetypes=[("CSV files", "*.csv"), ("All files", "*.*")],
                title="保存回测报告"
            )

            if filename:
                with open(filename, 'w', newline='', encoding='utf-8') as csvfile:
                    writer = csv.writer(csvfile)

                    # 写入标题
                    writer.writerow(['比赛ID', '对阵', '比赛时间', '策略', '投注类型', '投注金额', '赔率', '结果', '盈亏'])

                    # 写入数据
                    for result in self.backtest_results:
                        match = result['match']
                        decision = result['decision']
                        bet_result = result['result']

                        teams = f"{match.get('home_team', '')} vs {match.get('away_team', '')}"
                        match_time = match.get('accurate_datetime') or match.get('match_time', '')

                        writer.writerow([
                            match.get('match_id', ''),
                            teams,
                            match_time,
                            decision['strategy'],
                            decision['bet_type'],
                            f"{bet_result.get('bet_amount', 0):.2f}",
                            f"{decision['odds']:.2f}",
                            bet_result['result'],
                            f"{bet_result['profit']:.2f}"
                        ])

                messagebox.showinfo("成功", f"回测报告已导出到: {filename}")

        except Exception as e:
            logger.error(f"导出回测报告失败: {e}")
            messagebox.showerror("错误", f"导出回测报告失败: {e}")

    def on_network_mode_change(self):
        """网络模式切换事件处理"""
        mode = self.network_mode_var.get()
        if mode == "concurrent":
            # 显示代理配置
            self.proxy_config_frame.grid()
        else:
            # 隐藏代理配置
            self.proxy_config_frame.grid_remove()

    def fetch_proxy_list(self):
        """获取代理列表"""
        def fetch_worker():
            try:
                proxy_api_url = self.proxy_api_var.get().strip()
                if not proxy_api_url:
                    self.message_queue.put(("error", "请输入代理API地址"))
                    return

                self.message_queue.put(("status", "正在获取代理列表..."))

                # 发送请求获取代理
                response = requests.get(proxy_api_url, timeout=15)

                if response.status_code == 200:
                    data = response.json()

                    if data.get("code") == 1000:
                        proxy_data = data.get("data", [])

                        # 更新代理列表（包含本地IP）
                        with self.proxy_pool_lock:
                            self.proxy_list = [{"ip": "local", "port": "local", "type": "local"}]  # 本地IP
                            self.proxy_list.extend(proxy_data)

                            # 重置失败计数
                            self.proxy_failure_count = {}
                            for proxy in self.proxy_list:
                                proxy_key = f"{proxy['ip']}:{proxy['port']}"
                                self.proxy_failure_count[proxy_key] = 0

                        total_proxies = len(self.proxy_list)
                        self.message_queue.put(("proxy_status", f"总IP: {total_proxies}, 可用IP: {total_proxies}"))
                        self.message_queue.put(("status", f"成功获取 {total_proxies} 个代理IP（含本地IP）"))

                    else:
                        error_msg = data.get("msg", "未知错误")
                        self.message_queue.put(("error", f"获取代理失败: {error_msg}"))

                else:
                    self.message_queue.put(("error", f"请求失败: HTTP {response.status_code}"))

            except Exception as e:
                self.message_queue.put(("error", f"获取代理异常: {e}"))

        # 在新线程中执行
        thread = threading.Thread(target=fetch_worker, daemon=True)
        thread.start()

    def test_proxy_list(self):
        """测试代理列表"""
        def test_worker():
            try:
                if not self.proxy_list:
                    self.message_queue.put(("error", "请先获取代理列表"))
                    return

                self.message_queue.put(("status", "正在测试代理连接..."))

                working_count = 0
                total_count = len(self.proxy_list)

                for i, proxy in enumerate(self.proxy_list):
                    try:
                        ip = proxy.get("ip")
                        port = proxy.get("port")
                        proxy_key = f"{ip}:{port}"

                        self.message_queue.put(("status", f"测试代理 {i+1}/{total_count}: {proxy_key}"))

                        if ip == "local":
                            # 本地IP测试
                            response = requests.get("http://httpbin.org/ip", timeout=10)
                            if response.status_code == 200:
                                working_count += 1
                        else:
                            # 代理IP测试
                            proxies = {
                                'http': f'http://{ip}:{port}',
                                'https': f'http://{ip}:{port}'
                            }

                            response = requests.get("http://httpbin.org/ip", proxies=proxies, timeout=10)
                            if response.status_code == 200:
                                working_count += 1
                            else:
                                # 标记为失败
                                with self.proxy_pool_lock:
                                    self.proxy_failure_count[proxy_key] = 10  # 直接标记为不可用

                    except Exception:
                        # 测试失败，标记为不可用
                        with self.proxy_pool_lock:
                            self.proxy_failure_count[proxy_key] = 10

                # 更新可用代理列表
                with self.proxy_pool_lock:
                    self.working_proxies = [
                        proxy for proxy in self.proxy_list
                        if self.proxy_failure_count.get(f"{proxy['ip']}:{proxy['port']}", 0) < 10
                    ]

                self.message_queue.put(("proxy_status", f"总IP: {total_count}, 可用IP: {working_count}"))
                self.message_queue.put(("status", f"代理测试完成，可用代理: {working_count}/{total_count}"))

            except Exception as e:
                self.message_queue.put(("error", f"测试代理异常: {e}"))

        # 在新线程中执行
        thread = threading.Thread(target=test_worker, daemon=True)
        thread.start()

    def get_available_proxy(self):
        """获取一个可用的代理"""
        with self.proxy_pool_lock:
            available_proxies = [
                proxy for proxy in self.proxy_list
                if self.proxy_failure_count.get(f"{proxy['ip']}:{proxy['port']}", 0) < 10
            ]

            if available_proxies:
                return random.choice(available_proxies)
            else:
                return None

    def mark_proxy_failure(self, proxy):
        """标记代理失败"""
        proxy_key = f"{proxy['ip']}:{proxy['port']}"
        with self.proxy_pool_lock:
            self.proxy_failure_count[proxy_key] = self.proxy_failure_count.get(proxy_key, 0) + 1

            # 更新状态显示
            available_count = len([
                p for p in self.proxy_list
                if self.proxy_failure_count.get(f"{p['ip']}:{p['port']}", 0) < 10
            ])
            total_count = len(self.proxy_list)
            self.message_queue.put(("proxy_status", f"总IP: {total_count}, 可用IP: {available_count}"))

    def scrape_with_proxy(self, url, proxy=None, max_retries=3):
        """使用代理抓取数据"""
        for attempt in range(max_retries):
            if proxy is None:
                proxy = self.get_available_proxy()

            if proxy is None:
                raise Exception("没有可用的代理")

            try:
                if proxy['ip'] == 'local':
                    # 使用本地IP
                    response = requests.get(url, timeout=10)
                else:
                    # 使用代理IP
                    proxies = {
                        'http': f"http://{proxy['ip']}:{proxy['port']}",
                        'https': f"http://{proxy['ip']}:{proxy['port']}"
                    }
                    response = requests.get(url, proxies=proxies, timeout=10)

                if response.status_code == 200:
                    return response
                else:
                    self.mark_proxy_failure(proxy)
                    proxy = None  # 下次循环获取新代理

            except Exception as e:
                self.mark_proxy_failure(proxy)
                proxy = None  # 下次循环获取新代理
                if attempt == max_retries - 1:
                    raise e

        raise Exception("所有重试都失败了")

    def concurrent_batch_scraping_worker(self, league_url, rounds_to_scrape, auto_scrape_odds, auto_scrape_enhanced, max_companies, delay, concurrent_threads):
        """并发批量抓取工作线程"""
        import concurrent.futures
        import time

        try:
            # 获取当前选择的数据库
            current_db = self.get_current_database()

            total_rounds = len(rounds_to_scrape)
            total_matches = 0
            successful_matches = 0
            successful_enhanced = 0

            scrape_options = []
            if auto_scrape_odds:
                scrape_options.append("赔率数据")
            if auto_scrape_enhanced:
                scrape_options.append("详细数据")

            options_text = "、".join(scrape_options) if scrape_options else "比赛信息"
            self.message_queue.put(("batch_status", f"开始并发批量抓取 {total_rounds} 个轮次 ({options_text})，使用 {concurrent_threads} 个线程"))

            # 收集所有需要抓取的比赛
            all_matches = []

            for i, round_num in enumerate(rounds_to_scrape):
                if self.batch_stop_flag:
                    break

                try:
                    # 更新进度
                    progress_msg = f"正在收集第 {i+1}/{total_rounds} 轮 (第{round_num}轮) 的比赛信息"
                    self.message_queue.put(("batch_status", progress_msg))

                    # 获取该轮次的比赛数据
                    round_matches = self.league_extractor.get_completed_matches_from_multiple_rounds(
                        league_url, rounds=[round_num]
                    )

                    if round_matches:
                        for match in round_matches:
                            match['round'] = round_num
                            all_matches.append(match)
                            total_matches += 1

                            # 添加到结果表格
                            self.message_queue.put(("batch_result", {
                                'round': round_num,
                                'match_id': match['match_id'],
                                'teams': f"{match['home_team']} vs {match['away_team']}",
                                'match_time': match['match_time'],
                                'score': match['score'],
                                'status': match['status'],
                                'odds_status': "待抓取"
                            }))
                    else:
                        self.message_queue.put(("batch_status", f"第 {round_num} 轮未找到已完成比赛"))

                except Exception as e:
                    logger.error(f"收集第 {round_num} 轮比赛失败: {e}")
                    continue

            if not all_matches:
                self.message_queue.put(("batch_error", "没有找到需要抓取的比赛"))
                return

            self.message_queue.put(("batch_status", f"开始并发抓取 {len(all_matches)} 场比赛"))

            # 使用线程池进行并发抓取
            with concurrent.futures.ThreadPoolExecutor(max_workers=concurrent_threads) as executor:
                # 提交所有抓取任务
                future_to_match = {}

                for match in all_matches:
                    if self.batch_stop_flag:
                        break

                    future = executor.submit(
                        self.scrape_single_match_with_proxy,
                        match, auto_scrape_odds, auto_scrape_enhanced, max_companies, delay, current_db
                    )
                    future_to_match[future] = match

                # 处理完成的任务
                completed = 0
                for future in concurrent.futures.as_completed(future_to_match):
                    if self.batch_stop_flag:
                        break

                    match = future_to_match[future]
                    match_id = match['match_id']

                    try:
                        result = future.result()
                        if result['success']:
                            successful_matches += 1
                            if result.get('enhanced_success'):
                                successful_enhanced += 1

                            status_parts = []
                            if result.get('odds_success'):
                                status_parts.append("赔率")
                            if result.get('enhanced_success'):
                                status_parts.append("详细")

                            status_text = f"已抓取({'+'.join(status_parts)})" if status_parts else "已抓取"
                            self.message_queue.put(("update_odds_status", {
                                'match_id': match_id,
                                'status': status_text
                            }))
                        else:
                            self.message_queue.put(("update_odds_status", {
                                'match_id': match_id,
                                'status': "抓取失败"
                            }))

                    except Exception as e:
                        logger.error(f"并发抓取比赛 {match_id} 失败: {e}")
                        self.message_queue.put(("update_odds_status", {
                            'match_id': match_id,
                            'status': "抓取异常"
                        }))

                    completed += 1
                    # 更新进度
                    progress = int((completed / len(all_matches)) * total_rounds)
                    self.message_queue.put(("batch_progress", progress))

                    # 更新状态
                    self.message_queue.put(("batch_status", f"并发抓取进度: {completed}/{len(all_matches)} ({successful_matches} 成功)"))

            # 完成
            final_msg = f"并发批量抓取完成！总共处理 {total_matches} 场比赛"

            success_parts = []
            if auto_scrape_odds and successful_matches > 0:
                success_parts.append(f"{successful_matches} 场比赛的赔率数据")
            if auto_scrape_enhanced and successful_enhanced > 0:
                success_parts.append(f"{successful_enhanced} 场比赛的详细数据")

            if success_parts:
                final_msg += f"，成功抓取 {' 和 '.join(success_parts)}"

            self.message_queue.put(("batch_complete", final_msg))
            self.message_queue.put(("refresh", None))

        except Exception as e:
            self.message_queue.put(("batch_error", f"并发批量抓取失败: {e}"))

    def scrape_single_match_with_proxy(self, match, auto_scrape_odds, auto_scrape_enhanced, max_companies, delay, current_db):
        """使用代理抓取单个比赛的数据"""
        match_id = match['match_id']
        result = {
            'success': False,
            'odds_success': False,
            'enhanced_success': False
        }

        try:
            # 检查比赛是否已存在
            with sqlite3.connect(current_db.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute('SELECT COUNT(*) FROM matches WHERE match_id = ?', (match_id,))
                match_exists = cursor.fetchone()[0] > 0

            if match_exists:
                # 比赛已存在，跳过抓取
                result['success'] = True
                result['odds_success'] = True
                return result

            # 抓取基础数据（比赛信息和赔率）
            complete_data = None
            if auto_scrape_odds:
                # 使用代理抓取数据
                proxy = self.get_available_proxy()
                if proxy:
                    try:
                        # 使用与单场抓取相同的逻辑：max_companies表示最多抓取多少家目标公司
                        # 实际抓取的是config.py中预定义的目标公司，而不是随机的前N家公司
                        complete_data = self.scraper.scrape_complete_match_data(
                            match_id=match_id,
                            max_companies=max_companies,  # 这个参数会被正确处理
                            delay=delay
                        )

                        if complete_data and (complete_data['match_info'] or complete_data['odds_data']):
                            # 保存比赛信息
                            if complete_data['match_info']:
                                match_info = complete_data['match_info'].copy()
                                if 'season' not in match_info or not match_info['season']:
                                    match_info['season'] = get_season_for_match(match_id=match_id)

                                # 添加准确的时间信息
                                try:
                                    time_info = self.time_scraper.scrape_match_time(match_id)
                                    if not time_info.get('error'):
                                        match_info.update({
                                            'accurate_datetime': time_info.get('full_datetime'),
                                            'accurate_date': time_info.get('match_date'),
                                            'accurate_time': time_info.get('match_time'),
                                            'weekday': time_info.get('weekday'),
                                            'match_year': time_info.get('year'),
                                            'match_month': time_info.get('month'),
                                            'match_day': time_info.get('day'),
                                            'match_hour': time_info.get('hour'),
                                            'match_minute': time_info.get('minute'),
                                            'time_source': 'analysis'
                                        })
                                    else:
                                        match_info['time_source'] = 'estimated'
                                except Exception:
                                    match_info['time_source'] = 'estimated'

                                success = current_db.save_match_info(match_info)
                                if success:
                                    result['odds_success'] = True

                            # 保存赔率数据
                            if complete_data['odds_data']:
                                success = current_db.save_odds_data(match_id, complete_data['odds_data'])
                                if success:
                                    result['odds_success'] = True

                    except Exception as e:
                        # 标记代理失败
                        self.mark_proxy_failure(proxy)
                        logger.error(f"使用代理 {proxy['ip']}:{proxy['port']} 抓取比赛 {match_id} 失败: {e}")

            # 抓取详细数据
            if auto_scrape_enhanced and self.enhanced_system:
                try:
                    enhanced_result = self.enhanced_system.process_match_data(match_id, save_data=True)
                    if enhanced_result['success']:
                        result['enhanced_success'] = True
                except Exception as e:
                    logger.error(f"抓取比赛 {match_id} 详细数据失败: {e}")

            # 如果有任何成功的抓取，标记为成功
            if result['odds_success'] or result['enhanced_success']:
                result['success'] = True

            return result

        except Exception as e:
            logger.error(f"抓取比赛 {match_id} 失败: {e}")
            return result

    def start_date_scraping(self):
        """开始按日期抓取"""
        try:
            date_url = self.date_url_var.get().strip()

            if not date_url:
                messagebox.showerror("错误", "请输入日期URL")
                return

            # 验证URL格式
            if not date_url.startswith('http'):
                messagebox.showerror("错误", "请输入有效的URL")
                return

            # 禁用按钮
            self.date_scrape_button.config(state='disabled')
            self.batch_progress_var.set("正在提取日期页面比赛...")

            # 在新线程中执行提取
            thread = threading.Thread(
                target=self.date_scraping_worker,
                args=(date_url,),
                daemon=True
            )
            thread.start()

        except Exception as e:
            logger.error(f"启动按日期抓取失败: {e}")
            messagebox.showerror("错误", f"启动按日期抓取失败: {str(e)}")

    def date_scraping_worker(self, date_url: str):
        """按日期抓取工作线程"""
        try:
            from date_match_extractor import DateMatchExtractor

            # 提取比赛信息
            self.message_queue.put(("batch_status", "正在提取日期页面比赛..."))

            extractor = DateMatchExtractor()
            matches_by_league = extractor.extract_matches_from_date_page(date_url)

            if not matches_by_league:
                self.message_queue.put(("date_scraping_error", "未找到任何比赛信息"))
                return

            # 在主线程中显示联赛选择对话框
            self.message_queue.put(("show_league_selection", {
                'matches_by_league': matches_by_league,
                'date_url': date_url
            }))

        except Exception as e:
            error_message = f"按日期抓取失败: {str(e)}"
            logger.error(error_message)
            self.message_queue.put(("date_scraping_error", error_message))

    def handle_league_selection(self, data):
        """处理联赛选择"""
        try:
            matches_by_league = data['matches_by_league']
            date_url = data['date_url']

            # 显示联赛选择对话框
            from league_selection_dialog import show_league_selection_dialog
            result = show_league_selection_dialog(self.root, matches_by_league)

            if result:
                # 用户选择了联赛，开始抓取
                selected_matches = result['matches']
                total_matches = result['total_matches']

                logger.info(f"用户选择了 {len(result['leagues'])} 个联赛的 {total_matches} 场比赛")

                # 开始批量抓取选中的比赛
                self.start_date_batch_scraping(selected_matches, date_url)
            else:
                # 用户取消了选择
                self.message_queue.put(("date_scraping_complete", "用户取消了联赛选择"))

        except Exception as e:
            error_message = f"处理联赛选择失败: {str(e)}"
            logger.error(error_message)
            self.message_queue.put(("date_scraping_error", error_message))

    def start_date_batch_scraping(self, selected_matches: List[Dict], date_url: str):
        """开始按日期批量抓取"""
        try:
            # 获取抓取参数
            auto_scrape_odds = self.auto_scrape_odds_var.get()
            auto_scrape_enhanced = self.auto_scrape_enhanced_var.get()
            max_companies = int(self.batch_max_companies_var.get())
            delay = float(self.batch_delay_var.get())
            network_mode = self.network_mode_var.get()

            # 清空结果表格
            self.clear_batch_results()

            # 禁用按钮
            self.date_scrape_button.config(state='disabled')
            self.batch_scrape_button.config(state='disabled')
            self.discover_button.config(state='disabled')
            self.batch_stop_flag = False

            # 设置进度条
            self.batch_progress_bar.config(maximum=len(selected_matches))
            self.batch_progress_bar['value'] = 0

            logger.info(f"开始按日期批量抓取 {len(selected_matches)} 场比赛")

            # 根据网络模式选择抓取方法
            if network_mode == "concurrent":
                # 并发模式
                concurrent_threads = int(self.concurrent_threads_var.get())
                thread = threading.Thread(
                    target=self.date_concurrent_batch_scraping_worker,
                    args=(selected_matches, auto_scrape_odds, auto_scrape_enhanced,
                          max_companies, delay, concurrent_threads),
                    daemon=True
                )
            else:
                # 普通模式
                thread = threading.Thread(
                    target=self.date_normal_batch_scraping_worker,
                    args=(selected_matches, auto_scrape_odds, auto_scrape_enhanced,
                          max_companies, delay),
                    daemon=True
                )

            thread.start()

        except Exception as e:
            error_message = f"启动按日期批量抓取失败: {str(e)}"
            logger.error(error_message)
            self.message_queue.put(("date_scraping_error", error_message))

    def date_normal_batch_scraping_worker(self, selected_matches, auto_scrape_odds, auto_scrape_enhanced, max_companies, delay):
        """按日期普通模式批量抓取工作线程"""
        try:
            current_db = self.get_current_database()
            if not current_db:
                self.message_queue.put(("date_scraping_error", "未选择数据库"))
                return

            total_matches = len(selected_matches)
            success_count = 0
            failed_count = 0

            for i, match in enumerate(selected_matches, 1):
                if self.batch_stop_flag:
                    break

                match_id = match['match_id']
                teams = f"{match['home_team']} vs {match['away_team']}"

                try:
                    self.message_queue.put(("batch_status", f"正在抓取比赛 {i}/{total_matches}: {teams}"))

                    # 保存比赛基本信息
                    current_db.save_match_info(match)

                    # 抓取赔率数据
                    if auto_scrape_odds:
                        odds_result = self.scraper.scrape_complete_match_data(
                            match_id=match_id,
                            max_companies=max_companies,
                            delay=delay
                        )

                        if odds_result and odds_result.get('odds_data'):
                            current_db.save_odds_data(match_id, odds_result['odds_data'])
                            odds_count = len(odds_result['odds_data'])
                        else:
                            odds_count = 0
                    else:
                        odds_count = 0

                    success_count += 1

                    # 更新结果表格
                    self.message_queue.put(("batch_result", {
                        'match_id': match_id,
                        'league': match.get('league', ''),
                        'home_team': match['home_team'],
                        'away_team': match['away_team'],
                        'match_time': match.get('match_time', ''),
                        'odds_count': odds_count,
                        'status': '成功'
                    }))

                except Exception as e:
                    failed_count += 1
                    logger.error(f"抓取比赛 {match_id} 失败: {e}")

                    # 更新结果表格
                    self.message_queue.put(("batch_result", {
                        'match_id': match_id,
                        'league': match.get('league', ''),
                        'home_team': match['home_team'],
                        'away_team': match['away_team'],
                        'match_time': match.get('match_time', ''),
                        'odds_count': 0,
                        'status': f'失败: {str(e)[:50]}'
                    }))

                # 更新进度条
                self.message_queue.put(("batch_progress", i))

                # 延迟
                if delay > 0 and i < total_matches:
                    import time
                    time.sleep(delay)

            # 完成
            self.message_queue.put(("date_scraping_complete",
                                   f"按日期抓取完成！成功: {success_count}, 失败: {failed_count}"))

        except Exception as e:
            error_message = f"按日期批量抓取过程中发生错误: {str(e)}"
            logger.error(error_message)
            self.message_queue.put(("date_scraping_error", error_message))

    def date_concurrent_batch_scraping_worker(self, selected_matches, auto_scrape_odds, auto_scrape_enhanced, max_companies, delay, concurrent_threads):
        """按日期并发模式批量抓取工作线程"""
        try:
            current_db = self.get_current_database()
            if not current_db:
                self.message_queue.put(("date_scraping_error", "未选择数据库"))
                return

            # 使用现有的并发抓取逻辑，但传入选定的比赛列表
            # 这里可以复用现有的并发抓取代码
            self.message_queue.put(("batch_status", "并发模式暂未实现，使用普通模式"))

            # 暂时使用普通模式
            self.date_normal_batch_scraping_worker(selected_matches, auto_scrape_odds, auto_scrape_enhanced, max_companies, delay)

        except Exception as e:
            error_message = f"按日期并发抓取过程中发生错误: {str(e)}"
            logger.error(error_message)
            self.message_queue.put(("date_scraping_error", error_message))

    def start_update_finished_matches(self):
        """开始完赛后更新"""
        try:
            # 获取当前数据库
            current_db = self.get_current_database()
            if not current_db:
                messagebox.showerror("错误", "请先选择数据库")
                return

            # 查找无比赛结果的比赛
            matches_to_update = self.find_matches_without_results(current_db)

            if not matches_to_update:
                messagebox.showinfo("信息", "没有找到需要更新的比赛（所有比赛都已有结果）")
                return

            # 确认更新
            result = messagebox.askyesno(
                "确认更新",
                f"找到 {len(matches_to_update)} 场无比赛结果的比赛。\n"
                f"将重新抓取这些比赛的所有数据。\n\n"
                f"是否继续？"
            )

            if not result:
                return

            # 禁用按钮
            self.update_finished_button.config(state='disabled')
            self.scrape_button.config(state='disabled')
            self.progress_bar.start()
            self.progress_var.set("正在更新完赛比赛...")

            # 在新线程中执行更新
            thread = threading.Thread(
                target=self.update_finished_matches_worker,
                args=(matches_to_update,),
                daemon=True
            )
            thread.start()

        except Exception as e:
            logger.error(f"启动完赛后更新失败: {e}")
            messagebox.showerror("错误", f"启动完赛后更新失败: {str(e)}")

    def find_matches_without_results(self, database):
        """查找无比赛结果且已完场的比赛"""
        try:
            with sqlite3.connect(database.db_path) as conn:
                cursor = conn.cursor()

                # 查找无比赛结果的比赛，包含比赛状态信息
                cursor.execute("""
                    SELECT match_id, league, home_team, away_team, match_time, match_state
                    FROM matches
                    WHERE (home_score IS NULL OR home_score = '' OR
                           away_score IS NULL OR away_score = '')
                    ORDER BY match_time DESC
                """)

                matches = cursor.fetchall()
                logger.info(f"初步找到 {len(matches)} 场无比赛结果的比赛")

                # 过滤出已完场的比赛
                finished_matches = []
                skipped_matches = []

                from datetime import datetime, timedelta
                current_time = datetime.now()

                for match in matches:
                    match_id, league, home_team, away_team, match_time, match_state = match

                    # 检查比赛状态
                    should_update = self.should_update_match(match_id, match_time, match_state, current_time)

                    if should_update:
                        finished_matches.append({
                            'match_id': match_id,
                            'league': league,
                            'home_team': home_team,
                            'away_team': away_team,
                            'match_time': match_time,
                            'match_state': match_state
                        })
                    else:
                        skipped_matches.append({
                            'match_id': match_id,
                            'teams': f"{home_team} vs {away_team}",
                            'match_time': match_time,
                            'match_state': match_state
                        })

                logger.info(f"筛选后找到 {len(finished_matches)} 场需要更新的已完场比赛")
                if skipped_matches:
                    logger.info(f"跳过 {len(skipped_matches)} 场未完场或时间不符的比赛")
                    for skipped in skipped_matches[:5]:  # 只显示前5场
                        logger.info(f"  跳过: {skipped['teams']} (状态: {skipped['match_state']}, 时间: {skipped['match_time']})")

                return finished_matches

        except Exception as e:
            logger.error(f"查找无比赛结果的比赛失败: {e}")
            return []

    def should_update_match(self, match_id, match_time, match_state, current_time):
        """判断比赛是否应该更新"""
        try:
            # 1. 检查比赛状态
            if match_state:
                match_state_lower = match_state.lower()

                # 明确的完场状态
                finished_states = ['完场', '结束', 'finished', 'ft', 'full time', '全场结束']
                if any(state in match_state_lower for state in finished_states):
                    logger.debug(f"比赛 {match_id} 状态为完场: {match_state}")
                    return True

                # 明确的未完场状态
                ongoing_states = ['进行中', '上半场', '下半场', '中场', 'live', 'ht', 'half time',
                                '1h', '2h', '加时', 'et', 'extra time', '点球', 'pen', 'penalty']
                if any(state in match_state_lower for state in ongoing_states):
                    logger.debug(f"比赛 {match_id} 正在进行中，跳过: {match_state}")
                    return False

                # 未开始状态
                not_started_states = ['未开始', 'not started', 'scheduled', '待定', 'postponed', 'delayed']
                if any(state in match_state_lower for state in not_started_states):
                    logger.debug(f"比赛 {match_id} 未开始，跳过: {match_state}")
                    return False

            # 2. 如果状态不明确，根据时间判断（但要更保守）
            if match_time:
                try:
                    from datetime import datetime, timedelta

                    # 尝试解析比赛时间
                    if isinstance(match_time, str):
                        # 常见的时间格式
                        time_formats = [
                            '%Y-%m-%d %H:%M:%S',
                            '%Y-%m-%d %H:%M',
                            '%Y/%m/%d %H:%M:%S',
                            '%Y/%m/%d %H:%M'
                        ]

                        match_datetime = None
                        for fmt in time_formats:
                            try:
                                match_datetime = datetime.strptime(match_time, fmt)
                                break
                            except ValueError:
                                continue

                        if match_datetime:
                            # 只有在最近7天内的比赛才考虑时间判断
                            days_ago = (current_time - match_datetime).days
                            if days_ago > 7:
                                logger.debug(f"比赛 {match_id} 时间过久({days_ago}天前)且状态不明，保守跳过")
                                return False

                            # 比赛时间 + 3小时（一般比赛时长）后才考虑更新
                            estimated_end_time = match_datetime + timedelta(hours=3)

                            if current_time >= estimated_end_time:
                                logger.debug(f"比赛 {match_id} 预计已结束，允许更新")
                                return True
                            else:
                                time_diff = estimated_end_time - current_time
                                logger.debug(f"比赛 {match_id} 预计还需 {time_diff} 结束，跳过")
                                return False
                        else:
                            logger.warning(f"比赛 {match_id} 时间格式无法解析: {match_time}")

                except Exception as e:
                    logger.warning(f"解析比赛 {match_id} 时间失败: {e}")

            # 3. 默认情况：如果无法确定状态和时间，保守地跳过
            logger.debug(f"比赛 {match_id} 状态不明确，保守跳过")
            return False

        except Exception as e:
            logger.error(f"判断比赛 {match_id} 是否应该更新时发生错误: {e}")
            return False

    def update_finished_matches_worker(self, matches_to_update):
        """完赛后更新工作线程"""
        try:
            # 获取当前数据库
            current_db = self.get_current_database()

            # 获取抓取参数
            max_companies = int(self.max_companies_var.get())
            delay = float(self.delay_var.get())

            total_matches = len(matches_to_update)
            updated_count = 0
            failed_count = 0

            self.message_queue.put(("status", f"开始更新 {total_matches} 场比赛..."))

            for i, match in enumerate(matches_to_update, 1):
                match_id = match['match_id']
                teams = f"{match['home_team']} vs {match['away_team']}"

                try:
                    self.message_queue.put(("status", f"正在更新比赛 {match_id} ({i}/{total_matches}): {teams}"))

                    # 删除该比赛的所有现有数据
                    self.delete_match_data(current_db, match_id)

                    # 重新抓取比赛数据
                    result = self.scraper.scrape_complete_match_data(
                        match_id=match_id,
                        max_companies=max_companies,
                        delay=delay
                    )

                    # 检查抓取结果并保存到数据库
                    if result and result.get('match_info') and result.get('odds_data'):
                        # 保存比赛信息
                        current_db.save_match_info(result['match_info'])

                        # 保存赔率数据
                        current_db.save_odds_data(match_id, result['odds_data'])

                        success = True
                        logger.info(f"比赛 {match_id} 数据保存成功：{len(result['odds_data'])} 条赔率记录")
                    else:
                        success = False
                        logger.warning(f"比赛 {match_id} 抓取结果为空或无效")

                    if success:
                        updated_count += 1
                        self.message_queue.put(("status", f"✅ 比赛 {match_id} 更新成功 ({updated_count}/{total_matches})"))
                    else:
                        failed_count += 1
                        self.message_queue.put(("status", f"❌ 比赛 {match_id} 更新失败 ({failed_count} 失败)"))

                except Exception as e:
                    failed_count += 1
                    logger.error(f"更新比赛 {match_id} 失败: {e}")
                    self.message_queue.put(("status", f"❌ 比赛 {match_id} 更新失败: {str(e)}"))

                # 添加延迟
                if delay > 0 and i < total_matches:
                    import time
                    time.sleep(delay)

            # 完成更新
            success_message = f"完赛后更新完成！\n成功更新: {updated_count} 场\n失败: {failed_count} 场"
            self.message_queue.put(("update_finished_complete", success_message))
            self.message_queue.put(("refresh", None))

        except Exception as e:
            error_message = f"完赛后更新过程中发生错误: {str(e)}"
            logger.error(error_message)
            self.message_queue.put(("update_finished_error", error_message))

    def delete_match_data(self, database, match_id):
        """删除指定比赛的所有数据"""
        try:
            with sqlite3.connect(database.db_path) as conn:
                cursor = conn.cursor()

                # 删除赔率数据
                cursor.execute("DELETE FROM odds WHERE match_id = ?", (match_id,))

                # 删除比赛信息（会重新抓取）
                cursor.execute("DELETE FROM matches WHERE match_id = ?", (match_id,))

                conn.commit()
                logger.info(f"已删除比赛 {match_id} 的所有数据")

        except Exception as e:
            logger.error(f"删除比赛 {match_id} 数据失败: {e}")
            raise


def main():
    """主函数"""
    try:
        root = tk.Tk()
        app = OddsScraperGUI(root)
        root.mainloop()
    except Exception as e:
        logger.error(f"程序启动失败: {e}")
        messagebox.showerror("错误", f"程序启动失败: {e}")


if __name__ == "__main__":
    main()

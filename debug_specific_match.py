#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试特定比赛的凯利分析2
根据用户提供的条件和结果进行验证
"""

import sqlite3
import sys
import os

def debug_match_with_user_conditions():
    """根据用户条件调试比赛"""
    print("=== 根据用户条件调试比赛 ===")
    
    # 用户的筛选条件
    kelly_type = "kelly_away"  # 凯利客
    stats_count = 10
    kelly_threshold = 1.05
    selected_companies = ["Betfair", "bet365", "betfair", "betathome", "pinnacle"]
    meaningless_threshold = 2
    
    print(f"用户筛选条件:")
    print(f"  凯利类型: {kelly_type} (凯利客)")
    print(f"  统计数量: {stats_count}")
    print(f"  凯利门槛: {kelly_threshold}")
    print(f"  监控公司: {selected_companies}")
    print(f"  无意义公司门槛: {meaningless_threshold}")
    print()
    
    try:
        with sqlite3.connect("odds_data.db") as conn:
            conn.row_factory = sqlite3.Row
            cursor = conn.cursor()
            
            # 找到一些有足够数据的比赛进行测试
            cursor.execute(f'''
                SELECT match_id, COUNT(*) as company_count
                FROM odds 
                WHERE {kelly_type} IS NOT NULL AND company_name IS NOT NULL
                GROUP BY match_id
                HAVING company_count >= {stats_count}
                ORDER BY company_count DESC
                LIMIT 5
            ''')
            
            matches = cursor.fetchall()
            print(f"找到 {len(matches)} 场有足够数据的比赛进行测试")
            
            for match in matches:
                match_id = match[0]
                company_count = match[1]
                
                print(f"\n--- 测试比赛 {match_id} (共{company_count}家公司) ---")
                
                # 获取该比赛的赔率数据，按凯利客降序排列
                cursor.execute(f'''
                    SELECT {kelly_type}, return_rate, company_name
                    FROM odds
                    WHERE match_id = ? AND {kelly_type} IS NOT NULL AND return_rate IS NOT NULL AND company_name IS NOT NULL
                    ORDER BY {kelly_type} DESC
                ''', (match_id,))
                
                odds_data = cursor.fetchall()
                
                if len(odds_data) < stats_count:
                    print(f"数据不足：需要{stats_count}条，实际{len(odds_data)}条")
                    continue
                
                # 取前N家的数据
                top_odds = odds_data[:stats_count]
                
                print(f"前{stats_count}家凯利客数据:")
                kelly_values = []
                company_list = []
                
                for i, row in enumerate(top_odds):
                    kelly_val = float(row[0])
                    company = row[2]
                    
                    kelly_values.append(kelly_val)
                    company_list.append(company)
                    
                    mark = "★" if company in selected_companies else " "
                    print(f"  {i+1:2d}. {mark} {company:15s} - 凯利客:{kelly_val:.3f}")
                
                # 计算平均凯利值
                avg_kelly = sum(kelly_values) / len(kelly_values)
                
                # 统计选中的博彩公司在公司列表中出现的次数
                print(f"\n观察次数统计:")
                observation_count = 0
                for company in selected_companies:
                    count = company_list.count(company)
                    observation_count += count
                    if count > 0:
                        print(f"  {company}: 出现 {count} 次")
                
                print(f"  总观察次数: {observation_count}")
                
                # 判断是否符合筛选条件
                kelly_qualified = avg_kelly > kelly_threshold
                observation_qualified = observation_count <= meaningless_threshold
                
                print(f"\n筛选条件判断:")
                print(f"  凯利条件: {avg_kelly:.3f} > {kelly_threshold} = {kelly_qualified}")
                print(f"  观察条件: {observation_count} <= {meaningless_threshold} = {observation_qualified}")
                
                result = kelly_qualified and observation_qualified
                status = "✅ 应该通过筛选" if result else "❌ 应该被排除"
                print(f"  最终结果: {result} ({status})")
                
                # 如果这场比赛出现在用户的筛选结果中但不应该通过，那就是bug
                if not result:
                    print(f"  🐛 如果这场比赛出现在筛选结果中，那就是BUG！")
            
            return True
            
    except Exception as e:
        print(f"❌ 调试失败: {e}")
        return False

def check_company_name_variations():
    """检查博彩公司名称的变体"""
    print("\n=== 检查博彩公司名称变体 ===")
    
    try:
        with sqlite3.connect("odds_data.db") as conn:
            conn.row_factory = sqlite3.Row
            cursor = conn.cursor()
            
            # 获取所有博彩公司名称
            cursor.execute('SELECT DISTINCT company_name FROM odds WHERE company_name IS NOT NULL ORDER BY company_name')
            all_companies = [row[0] for row in cursor.fetchall()]
            
            print("数据库中的所有博彩公司名称:")
            for i, company in enumerate(all_companies, 1):
                print(f"  {i:2d}. {company}")
            
            # 检查用户选择的公司是否存在变体
            user_companies = ["Betfair", "bet365", "betfair", "betathome", "pinnacle"]
            print(f"\n用户选择的公司: {user_companies}")
            
            print("匹配检查:")
            for user_company in user_companies:
                exact_match = user_company in all_companies
                similar_matches = [c for c in all_companies if user_company.lower() in c.lower() or c.lower() in user_company.lower()]
                
                print(f"  {user_company}:")
                print(f"    精确匹配: {exact_match}")
                if similar_matches:
                    print(f"    相似匹配: {similar_matches}")
                else:
                    print(f"    相似匹配: 无")
            
            return True
            
    except Exception as e:
        print(f"❌ 检查失败: {e}")
        return False

def main():
    """主调试函数"""
    print("🔍 特定比赛凯利分析2调试")
    print("=" * 60)
    
    # 检查数据库文件是否存在
    if not os.path.exists("odds_data.db"):
        print("❌ 数据库文件 odds_data.db 不存在")
        return False
    
    tests = [
        debug_match_with_user_conditions,
        check_company_name_variations
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"❌ 测试异常: {e}")
    
    print("\n" + "=" * 60)
    print(f"📊 调试结果: {passed}/{total} 完成")
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试完赛后更新功能修复
验证方法调用是否正确
"""

import sys
import os

def test_method_fix():
    """测试方法修复"""
    
    print("🔧 测试完赛后更新功能修复")
    print("=" * 50)
    
    try:
        # 检查UI文件中的方法调用
        with open('odds_scraper_ui.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查是否已修复方法调用
        if 'scrape_complete_match_data' in content:
            print("✅ 方法调用已修复为 scrape_complete_match_data")
        else:
            print("❌ 方法调用未修复")
        
        # 检查是否移除了错误的方法调用
        if 'scrape_match_data' not in content:
            print("✅ 错误的方法调用已移除")
        else:
            print("⚠️ 仍存在 scrape_match_data 调用")
        
        # 检查返回值处理
        if "result.get('match_info')" in content and "result.get('odds_data')" in content:
            print("✅ 返回值处理已修复")
        else:
            print("❌ 返回值处理未修复")
        
        # 检查数据保存逻辑
        if "current_db.save_match_info" in content and "current_db.save_odds_data" in content:
            print("✅ 数据保存逻辑正确")
        else:
            print("❌ 数据保存逻辑有问题")
        
        print(f"\n📋 修复说明:")
        print("1. 方法名修复：scrape_match_data → scrape_complete_match_data")
        print("2. 返回值处理：检查 match_info 和 odds_data 而不是 success")
        print("3. 数据保存：使用 current_db.save_match_info 和 save_odds_data")
        print("4. 错误处理：添加详细的日志记录")
        
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")

def test_enhanced_scraper_methods():
    """测试 EnhancedOddsScraper 的方法"""
    
    print(f"\n🔍 测试 EnhancedOddsScraper 方法:")
    print("-" * 30)
    
    try:
        # 检查 enhanced_odds_scraper.py 文件
        with open('enhanced_odds_scraper.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查可用的方法
        methods = []
        for line in content.split('\n'):
            if line.strip().startswith('def ') and 'scrape' in line:
                method_name = line.strip().split('def ')[1].split('(')[0]
                methods.append(method_name)
        
        print(f"📋 EnhancedOddsScraper 中的抓取方法:")
        for method in methods:
            print(f"  - {method}")
        
        # 确认正确的方法存在
        if 'scrape_complete_match_data' in methods:
            print(f"\n✅ scrape_complete_match_data 方法存在")
        else:
            print(f"\n❌ scrape_complete_match_data 方法不存在")
        
        # 检查返回格式
        if "return complete_data" in content:
            print("✅ 方法返回 complete_data 格式")
        
        print(f"\n💡 使用说明:")
        print("- scrape_complete_match_data 返回包含 match_info、odds_data、summary 的字典")
        print("- 不包含 success 字段，需要检查 match_info 和 odds_data 是否存在")
        print("- 需要手动调用数据库保存方法")
        
    except Exception as e:
        print(f"❌ 测试 EnhancedOddsScraper 方法失败: {e}")

if __name__ == "__main__":
    test_method_fix()
    test_enhanced_scraper_methods()
    
    print(f"\n🎯 修复完成！")
    print("现在可以重新测试完赛后更新功能：")
    print("1. 启动主程序：python odds_scraper_ui.py")
    print("2. 选择数据库")
    print("3. 点击'完赛后更新'")
    print("4. 观察是否还有方法调用错误")

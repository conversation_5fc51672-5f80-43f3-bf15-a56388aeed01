#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试从比赛分析页面提取准确时间信息
"""

import requests
import re
from datetime import datetime
from bs4 import BeautifulSoup

def test_accurate_time_extraction():
    """测试从分析页面提取准确时间"""
    
    print("🔍 测试从分析页面提取准确时间")
    print("=" * 50)
    
    # 测试比赛ID
    test_match_ids = ["2726299", "2709881", "2213559"]
    
    session = requests.Session()
    session.headers.update({
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
        'Accept-Language': 'zh-C<PERSON>,zh;q=0.9,en;q=0.8',
        'Accept-Encoding': 'gzip, deflate',
        'Connection': 'keep-alive',
    })
    
    for match_id in test_match_ids:
        print(f"\n📋 测试比赛ID: {match_id}")
        print("-" * 30)
        
        try:
            # 构建分析页面URL
            analysis_url = f"https://m.titan007.com/Analy/ShiJian/{match_id}.htm"
            print(f"URL: {analysis_url}")
            
            # 获取页面内容
            response = session.get(analysis_url, timeout=15)
            response.raise_for_status()
            response.encoding = 'utf-8'
            
            content = response.text
            print(f"页面内容长度: {len(content)}")
            
            # 查找 headMatchTime 变量
            time_patterns = [
                r'var\s+headMatchTime\s*=\s*["\']([^"\']+)["\']',
                r'headMatchTime\s*=\s*["\']([^"\']+)["\']',
                r'matchTime\s*=\s*["\']([^"\']+)["\']',
                r'var\s+matchTime\s*=\s*["\']([^"\']+)["\']',
            ]
            
            found_time = None
            used_pattern = None
            
            for pattern in time_patterns:
                match = re.search(pattern, content, re.IGNORECASE)
                if match:
                    found_time = match.group(1)
                    used_pattern = pattern
                    break
            
            if found_time:
                print(f"✅ 找到时间信息: {found_time}")
                print(f"使用模式: {used_pattern}")
                
                # 解析时间
                try:
                    # 处理ISO格式时间
                    if 'T' in found_time:
                        # 格式如: 2025-07-27T19:00:00
                        dt = datetime.fromisoformat(found_time.replace('T', ' ').replace('Z', ''))
                    else:
                        # 其他格式
                        dt = datetime.strptime(found_time, "%Y-%m-%d %H:%M:%S")
                    
                    print(f"解析结果:")
                    print(f"  完整时间: {dt.strftime('%Y-%m-%d %H:%M:%S')}")
                    print(f"  年份: {dt.year}")
                    print(f"  月份: {dt.month}")
                    print(f"  日期: {dt.strftime('%Y-%m-%d')}")
                    print(f"  时间: {dt.strftime('%H:%M:%S')}")
                    
                except Exception as e:
                    print(f"❌ 时间解析失败: {e}")
                    print(f"原始时间字符串: {found_time}")
            else:
                print("❌ 未找到时间信息")
                
                # 显示页面中可能包含时间的部分
                print("页面中可能的时间相关内容:")
                time_related = re.findall(r'(var\s+\w*[Tt]ime\w*\s*=\s*[^;]+)', content, re.IGNORECASE)
                for i, match in enumerate(time_related[:5]):  # 只显示前5个
                    print(f"  {i+1}. {match}")
                
                # 查找其他可能的时间格式
                date_patterns = [
                    r'(\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2})',
                    r'(\d{4}-\d{2}-\d{2}\s+\d{2}:\d{2}:\d{2})',
                    r'(\d{4}/\d{2}/\d{2}\s+\d{2}:\d{2}:\d{2})',
                ]
                
                for pattern in date_patterns:
                    matches = re.findall(pattern, content)
                    if matches:
                        print(f"找到日期格式: {matches[:3]}")  # 只显示前3个
                        break
                
        except Exception as e:
            print(f"❌ 请求失败: {e}")

def create_accurate_time_extractor():
    """创建准确时间提取器的示例代码"""
    
    print(f"\n💡 准确时间提取器实现方案:")
    print("=" * 50)
    
    code_example = '''
def extract_accurate_match_time(self, match_id: str) -> dict:
    """从分析页面提取准确的比赛时间"""
    try:
        # 构建分析页面URL
        analysis_url = f"https://m.titan007.com/Analy/ShiJian/{match_id}.htm"
        
        # 获取页面内容
        response = self.session.get(analysis_url, timeout=15)
        response.raise_for_status()
        response.encoding = 'utf-8'
        
        content = response.text
        
        # 查找时间变量
        time_patterns = [
            r'var\\s+headMatchTime\\s*=\\s*["\']([^"\']+)["\']',
            r'headMatchTime\\s*=\\s*["\']([^"\']+)["\']',
            r'matchTime\\s*=\\s*["\']([^"\']+)["\']',
        ]
        
        for pattern in time_patterns:
            match = re.search(pattern, content, re.IGNORECASE)
            if match:
                time_str = match.group(1)
                
                # 解析时间
                if 'T' in time_str:
                    dt = datetime.fromisoformat(time_str.replace('T', ' ').replace('Z', ''))
                else:
                    dt = datetime.strptime(time_str, "%Y-%m-%d %H:%M:%S")
                
                return {
                    'success': True,
                    'match_time': dt.strftime('%Y-%m-%d %H:%M:%S'),
                    'match_date': dt.strftime('%Y-%m-%d'),
                    'match_time_only': dt.strftime('%H:%M:%S'),
                    'year': dt.year,
                    'month': dt.month,
                    'day': dt.day,
                    'hour': dt.hour,
                    'minute': dt.minute,
                    'source': 'analysis_page',
                    'raw_time_str': time_str
                }
        
        return {'success': False, 'error': '未找到时间信息'}
        
    except Exception as e:
        return {'success': False, 'error': str(e)}
'''
    
    print(code_example)

def main():
    """主函数"""
    
    print("🚀 准确时间提取测试")
    print("=" * 60)
    
    test_accurate_time_extraction()
    create_accurate_time_extractor()
    
    print(f"\n" + "=" * 60)
    print("📋 测试总结")
    print("=" * 60)
    
    print("✅ 从分析页面提取时间是可行的解决方案")
    print("✅ 可以获得准确的年份信息，无需推测")
    print("✅ 建议在比赛信息提取时优先使用此方法")

if __name__ == "__main__":
    main()

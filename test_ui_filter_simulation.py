#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
模拟UI筛选流程
完整测试凯利分析2筛选的UI应用过程
"""

import sqlite3
import sys
import os

def simulate_ui_kelly_analysis2_filter():
    """模拟UI中的凯利分析2筛选流程"""
    print("=== 模拟UI凯利分析2筛选流程 ===")
    
    # 模拟用户在UI中设置的条件
    kelly_type = "kelly_away"  # 凯利客
    stats_count = 5
    kelly_threshold = 1.1
    selected_companies = ["Betfair", "betathome", "betfair", "pinnacle"]
    meaningless_threshold = 0  # 一次都不能出现
    
    print(f"模拟UI筛选条件:")
    print(f"  凯利类型: {kelly_type}")
    print(f"  统计数量: {stats_count}")
    print(f"  凯利门槛: {kelly_threshold}")
    print(f"  监控公司: {selected_companies}")
    print(f"  无意义公司门槛: {meaningless_threshold}")
    print()
    
    try:
        # 模拟 apply_kelly_analysis2_filter 方法
        def apply_kelly_analysis2_filter(kelly_type, stats_count, kelly_threshold, selected_companies, meaningless_threshold):
            qualified_matches = []
            
            with sqlite3.connect("odds_data.db") as conn:
                conn.row_factory = sqlite3.Row
                cursor = conn.cursor()
                
                # 获取所有比赛ID
                cursor.execute('SELECT DISTINCT match_id FROM odds WHERE {} IS NOT NULL'.format(kelly_type))
                match_ids = [row[0] for row in cursor.fetchall()]
                
                print(f"开始凯利分析2筛选，共 {len(match_ids)} 场比赛")
                
                # 对每场比赛进行凯利分析2
                for i, match_id in enumerate(match_ids[:100]):  # 只测试前100场
                    if analyze_match_kelly2(match_id, kelly_type, stats_count, kelly_threshold, selected_companies, meaningless_threshold):
                        qualified_matches.append(match_id)
                    
                    if i % 20 == 0:
                        print(f"已处理 {i+1} 场比赛，当前符合条件: {len(qualified_matches)} 场")
                
                print(f"凯利分析2筛选完成，符合条件的比赛: {len(qualified_matches)} 场")
                
            return qualified_matches
        
        # 模拟 analyze_match_kelly2 方法
        def analyze_match_kelly2(match_id, kelly_type, stats_count, kelly_threshold, selected_companies, meaningless_threshold):
            with sqlite3.connect("odds_data.db") as conn:
                conn.row_factory = sqlite3.Row
                cursor = conn.cursor()
                
                # 获取该比赛的所有赔率数据，按凯利指数降序排列
                cursor.execute(f'''
                    SELECT {kelly_type}, return_rate, company_name
                    FROM odds
                    WHERE match_id = ? AND {kelly_type} IS NOT NULL AND return_rate IS NOT NULL AND company_name IS NOT NULL
                    ORDER BY {kelly_type} DESC
                ''', (match_id,))
                
                odds_data = cursor.fetchall()
                
                if len(odds_data) < stats_count:
                    return False
                
                # 取前N家的数据
                top_odds = odds_data[:stats_count]
                
                # 计算平均凯利值
                kelly_values = [float(row[0]) for row in top_odds]
                company_list = [row[2] for row in top_odds]
                
                avg_kelly = sum(kelly_values) / len(kelly_values)
                
                # 统计选中的博彩公司在公司列表中出现的次数（忽略大小写）
                observation_count = 0
                for company in selected_companies:
                    # 忽略大小写进行匹配
                    for list_company in company_list:
                        if company.lower() == list_company.lower():
                            observation_count += 1
                
                # 判断是否符合筛选条件
                kelly_qualified = avg_kelly > kelly_threshold
                observation_qualified = observation_count <= meaningless_threshold
                
                return kelly_qualified and observation_qualified
        
        # 执行筛选
        qualified_matches = apply_kelly_analysis2_filter(kelly_type, stats_count, kelly_threshold, selected_companies, meaningless_threshold)
        
        print(f"\n筛选结果: {len(qualified_matches)} 场比赛符合条件")
        print(f"符合条件的比赛ID: {qualified_matches[:10]}{'...' if len(qualified_matches) > 10 else ''}")
        
        # 验证前几场符合条件的比赛
        if qualified_matches:
            print(f"\n验证前3场符合条件的比赛:")
            for i, match_id in enumerate(qualified_matches[:3]):
                print(f"\n--- 验证比赛 {match_id} ---")
                
                with sqlite3.connect("odds_data.db") as conn:
                    conn.row_factory = sqlite3.Row
                    cursor = conn.cursor()
                    
                    cursor.execute(f'''
                        SELECT {kelly_type}, company_name
                        FROM odds
                        WHERE match_id = ? AND {kelly_type} IS NOT NULL AND company_name IS NOT NULL
                        ORDER BY {kelly_type} DESC
                        LIMIT {stats_count}
                    ''', (match_id,))
                    
                    top_data = cursor.fetchall()
                    
                    print(f"前{stats_count}家凯利客数据:")
                    kelly_values = []
                    company_list = []
                    
                    for j, row in enumerate(top_data, 1):
                        kelly_val = float(row[0])
                        company = row[1]
                        kelly_values.append(kelly_val)
                        company_list.append(company)
                        
                        monitored = any(company.lower() == sc.lower() for sc in selected_companies)
                        mark = "★" if monitored else " "
                        print(f"  {j}. {mark} {company:15s} - 凯利客:{kelly_val:.3f}")
                    
                    avg_kelly = sum(kelly_values) / len(kelly_values)
                    
                    # 统计观察次数
                    observation_count = 0
                    for company in selected_companies:
                        for list_company in company_list:
                            if company.lower() == list_company.lower():
                                observation_count += 1
                    
                    print(f"平均凯利: {avg_kelly:.3f}")
                    print(f"观察次数: {observation_count}")
                    print(f"凯利条件: {avg_kelly > kelly_threshold}")
                    print(f"观察条件: {observation_count <= meaningless_threshold}")
                    
                    if observation_count > meaningless_threshold:
                        print("🐛 这场比赛不应该通过筛选！")
                    else:
                        print("✅ 这场比赛正确通过筛选")
        
        return len(qualified_matches) > 0
        
    except Exception as e:
        print(f"❌ 模拟失败: {e}")
        return False

def check_database_consistency():
    """检查数据库数据一致性"""
    print("\n=== 检查数据库数据一致性 ===")
    
    try:
        with sqlite3.connect("odds_data.db") as conn:
            conn.row_factory = sqlite3.Row
            cursor = conn.cursor()
            
            # 检查凯利客数据的分布
            cursor.execute('''
                SELECT 
                    COUNT(*) as total_records,
                    COUNT(DISTINCT match_id) as unique_matches,
                    COUNT(DISTINCT company_name) as unique_companies,
                    MIN(kelly_away) as min_kelly,
                    MAX(kelly_away) as max_kelly,
                    AVG(kelly_away) as avg_kelly
                FROM odds 
                WHERE kelly_away IS NOT NULL
            ''')
            
            stats = cursor.fetchone()
            print(f"凯利客数据统计:")
            print(f"  总记录数: {stats[0]}")
            print(f"  唯一比赛数: {stats[1]}")
            print(f"  唯一公司数: {stats[2]}")
            print(f"  凯利客范围: {stats[3]:.3f} - {stats[4]:.3f}")
            print(f"  平均凯利客: {stats[5]:.3f}")
            
            # 检查公司名称
            cursor.execute('''
                SELECT company_name, COUNT(*) as count
                FROM odds 
                WHERE kelly_away IS NOT NULL
                GROUP BY company_name
                ORDER BY count DESC
                LIMIT 10
            ''')
            
            companies = cursor.fetchall()
            print(f"\n前10家公司的记录数:")
            for company in companies:
                print(f"  {company[0]:15s}: {company[1]:6d} 条记录")
            
            return True
            
    except Exception as e:
        print(f"❌ 检查失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🔍 UI筛选流程模拟测试")
    print("=" * 60)
    
    # 检查数据库文件是否存在
    if not os.path.exists("odds_data.db"):
        print("❌ 数据库文件 odds_data.db 不存在")
        return False
    
    tests = [
        check_database_consistency,
        simulate_ui_kelly_analysis2_filter
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"❌ 测试异常: {e}")
    
    print("\n" + "=" * 60)
    print(f"📊 测试结果: {passed}/{total} 完成")
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
赔率组合观察 - 数据库操作模块
用于查询和分析赔率组合数据
"""

import sqlite3
import os
from typing import List, Dict, Tuple, Optional
import logging
from datetime import datetime
from functools import lru_cache
import threading
from concurrent.futures import ThreadPoolExecutor, as_completed

logger = logging.getLogger(__name__)

class OddsCombinationDatabase:
    def __init__(self, db_path: str = None):
        """初始化数据库连接"""
        if db_path is None:
            # 默认使用父目录的odds_data.db
            current_dir = os.path.dirname(os.path.abspath(__file__))
            parent_dir = os.path.dirname(current_dir)
            db_path = os.path.join(parent_dir, "odds_data.db")

        self.db_path = db_path
        if not os.path.exists(self.db_path):
            raise FileNotFoundError(f"数据库文件不存在: {self.db_path}")

        # 初始化缓存和线程锁
        self._cache = {}
        self._cache_lock = threading.Lock()

        # 确保数据库索引存在
        self._ensure_indexes()

        logger.info(f"使用数据库: {self.db_path}")

    def _ensure_indexes(self):
        """确保数据库有必要的索引"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()

                # 创建索引以优化查询性能
                indexes = [
                    "CREATE INDEX IF NOT EXISTS idx_odds_match_company ON odds(match_id, company_name)",
                    "CREATE INDEX IF NOT EXISTS idx_odds_company_date ON odds(company_name, date, time)",
                    "CREATE INDEX IF NOT EXISTS idx_odds_match_date ON odds(match_id, date, time)",
                    "CREATE INDEX IF NOT EXISTS idx_matches_id ON matches(match_id)",
                    "CREATE INDEX IF NOT EXISTS idx_matches_date ON matches(match_date)",
                ]

                for index_sql in indexes:
                    cursor.execute(index_sql)

                conn.commit()
                logger.info("数据库索引检查完成")

        except sqlite3.Error as e:
            logger.warning(f"创建索引失败: {e}")

    def _get_cache_key(self, *args) -> str:
        """生成缓存键"""
        return "|".join(str(arg) for arg in args)

    def _get_from_cache(self, key: str):
        """从缓存获取数据"""
        with self._cache_lock:
            return self._cache.get(key)

    def _set_cache(self, key: str, value):
        """设置缓存数据"""
        with self._cache_lock:
            # 限制缓存大小，避免内存过度使用
            if len(self._cache) > 1000:
                # 清除一半的缓存
                keys_to_remove = list(self._cache.keys())[:500]
                for k in keys_to_remove:
                    del self._cache[k]
            self._cache[key] = value

    def clear_cache(self):
        """清除缓存"""
        with self._cache_lock:
            self._cache.clear()
            logger.info("缓存已清除")

    def get_available_databases(self) -> List[str]:
        """获取可用的数据库文件列表"""
        databases = []

        # 获取当前文件的目录
        current_dir = os.path.dirname(os.path.abspath(__file__))
        parent_dir = os.path.dirname(current_dir)

        # 主数据库
        main_db = os.path.join(parent_dir, "odds_data.db")
        if os.path.exists(main_db):
            databases.append(main_db)

        # 联赛分库
        league_db_dir = os.path.join(parent_dir, "league_databases")
        if os.path.exists(league_db_dir):
            for file in os.listdir(league_db_dir):
                if file.endswith('.db'):
                    databases.append(os.path.join(league_db_dir, file))

        return databases
    
    def get_match_odds_by_company(self, match_id: str, company_name: str) -> List[Dict]:
        """
        根据比赛ID和博彩公司获取赔率数据，按时间排序（带缓存）

        Args:
            match_id: 比赛ID
            company_name: 博彩公司名称

        Returns:
            按时间排序的赔率数据列表
        """
        # 检查缓存
        cache_key = self._get_cache_key("odds", match_id, company_name)
        cached_result = self._get_from_cache(cache_key)
        if cached_result is not None:
            return cached_result

        try:
            with sqlite3.connect(self.db_path) as conn:
                conn.row_factory = sqlite3.Row
                cursor = conn.cursor()

                cursor.execute('''
                    SELECT date, time, home_odds, draw_odds, away_odds,
                           return_rate, kelly_home, kelly_draw, kelly_away
                    FROM odds
                    WHERE match_id = ? AND company_name = ?
                      AND home_odds IS NOT NULL
                      AND draw_odds IS NOT NULL
                      AND away_odds IS NOT NULL
                    ORDER BY date ASC, time ASC
                ''', (match_id, company_name))

                rows = cursor.fetchall()
                result = [dict(row) for row in rows]

                # 缓存结果
                self._set_cache(cache_key, result)
                return result

        except sqlite3.Error as e:
            logger.error(f"查询赔率数据失败: {e}")
            return []

    def get_batch_match_odds(self, match_ids: List[str], company_name: str) -> Dict[str, List[Dict]]:
        """
        批量获取多场比赛的赔率数据

        Args:
            match_ids: 比赛ID列表
            company_name: 博彩公司名称

        Returns:
            {match_id: 赔率数据列表} 的字典
        """
        result = {}
        uncached_matches = []

        # 先检查缓存
        for match_id in match_ids:
            cache_key = self._get_cache_key("odds", match_id, company_name)
            cached_result = self._get_from_cache(cache_key)
            if cached_result is not None:
                result[match_id] = cached_result
            else:
                uncached_matches.append(match_id)

        # 批量查询未缓存的数据
        if uncached_matches:
            try:
                with sqlite3.connect(self.db_path) as conn:
                    conn.row_factory = sqlite3.Row
                    cursor = conn.cursor()

                    # 使用IN查询批量获取
                    placeholders = ','.join('?' * len(uncached_matches))
                    cursor.execute(f'''
                        SELECT match_id, date, time, home_odds, draw_odds, away_odds,
                               return_rate, kelly_home, kelly_draw, kelly_away
                        FROM odds
                        WHERE match_id IN ({placeholders}) AND company_name = ?
                          AND home_odds IS NOT NULL
                          AND draw_odds IS NOT NULL
                          AND away_odds IS NOT NULL
                        ORDER BY match_id, date ASC, time ASC
                    ''', uncached_matches + [company_name])

                    rows = cursor.fetchall()

                    # 按match_id分组
                    for match_id in uncached_matches:
                        match_odds = []
                        for row in rows:
                            if row['match_id'] == match_id:
                                match_odds.append({
                                    'date': row['date'],
                                    'time': row['time'],
                                    'home_odds': row['home_odds'],
                                    'draw_odds': row['draw_odds'],
                                    'away_odds': row['away_odds'],
                                    'return_rate': row['return_rate'],
                                    'kelly_home': row['kelly_home'],
                                    'kelly_draw': row['kelly_draw'],
                                    'kelly_away': row['kelly_away']
                                })

                        result[match_id] = match_odds

                        # 缓存结果
                        cache_key = self._get_cache_key("odds", match_id, company_name)
                        self._set_cache(cache_key, match_odds)

            except sqlite3.Error as e:
                logger.error(f"批量查询赔率数据失败: {e}")
                # 为未查询到的比赛返回空列表
                for match_id in uncached_matches:
                    if match_id not in result:
                        result[match_id] = []

        return result

    def get_available_companies(self) -> List[str]:
        """获取数据库中所有可用的博彩公司"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                cursor.execute('''
                    SELECT DISTINCT company_name 
                    FROM odds 
                    WHERE company_name IS NOT NULL
                    ORDER BY company_name
                ''')
                
                rows = cursor.fetchall()
                return [row[0] for row in rows]
                
        except sqlite3.Error as e:
            logger.error(f"查询博彩公司失败: {e}")
            return []
    
    def get_match_result(self, match_id: str) -> Optional[Dict]:
        """获取比赛结果（带缓存）"""
        # 检查缓存
        cache_key = self._get_cache_key("result", match_id)
        cached_result = self._get_from_cache(cache_key)
        if cached_result is not None:
            return cached_result

        try:
            with sqlite3.connect(self.db_path) as conn:
                conn.row_factory = sqlite3.Row
                cursor = conn.cursor()

                cursor.execute('''
                    SELECT home_score, away_score, match_state
                    FROM matches
                    WHERE match_id = ?
                ''', (match_id,))

                row = cursor.fetchone()
                result = dict(row) if row else None

                # 缓存结果
                self._set_cache(cache_key, result)
                return result

        except sqlite3.Error as e:
            logger.error(f"查询比赛结果失败: {e}")
            return None

    def get_batch_match_results(self, match_ids: List[str]) -> Dict[str, Optional[Dict]]:
        """
        批量获取比赛结果

        Args:
            match_ids: 比赛ID列表

        Returns:
            {match_id: 比赛结果} 的字典
        """
        result = {}
        uncached_matches = []

        # 先检查缓存
        for match_id in match_ids:
            cache_key = self._get_cache_key("result", match_id)
            cached_result = self._get_from_cache(cache_key)
            if cached_result is not None:
                result[match_id] = cached_result
            else:
                uncached_matches.append(match_id)

        # 批量查询未缓存的数据
        if uncached_matches:
            try:
                with sqlite3.connect(self.db_path) as conn:
                    conn.row_factory = sqlite3.Row
                    cursor = conn.cursor()

                    # 使用IN查询批量获取
                    placeholders = ','.join('?' * len(uncached_matches))
                    cursor.execute(f'''
                        SELECT match_id, home_score, away_score, match_state
                        FROM matches
                        WHERE match_id IN ({placeholders})
                    ''', uncached_matches)

                    rows = cursor.fetchall()

                    # 处理查询结果
                    found_matches = set()
                    for row in rows:
                        match_id = row['match_id']
                        match_result = {
                            'home_score': row['home_score'],
                            'away_score': row['away_score'],
                            'match_state': row['match_state']
                        }
                        result[match_id] = match_result
                        found_matches.add(match_id)

                        # 缓存结果
                        cache_key = self._get_cache_key("result", match_id)
                        self._set_cache(cache_key, match_result)

                    # 为未找到的比赛设置None
                    for match_id in uncached_matches:
                        if match_id not in found_matches:
                            result[match_id] = None
                            cache_key = self._get_cache_key("result", match_id)
                            self._set_cache(cache_key, None)

            except sqlite3.Error as e:
                logger.error(f"批量查询比赛结果失败: {e}")
                # 为未查询到的比赛返回None
                for match_id in uncached_matches:
                    if match_id not in result:
                        result[match_id] = None

        return result
    
    def get_all_other_matches(self, exclude_match_id: str, only_historical: bool = True) -> List[str]:
        """
        获取除指定比赛外的所有比赛ID

        Args:
            exclude_match_id: 要排除的比赛ID
            only_historical: 是否只返回开赛时间早于目标比赛的历史比赛
        """
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()

                if only_historical:
                    # 首先获取目标比赛的时间信息
                    cursor.execute('''
                        SELECT accurate_datetime, accurate_date, match_date, match_time
                        FROM matches
                        WHERE match_id = ?
                    ''', (exclude_match_id,))

                    target_match = cursor.fetchone()
                    if not target_match:
                        logger.warning(f"未找到比赛 {exclude_match_id} 的信息")
                        return []

                    target_datetime, target_date, original_date, original_time = target_match

                    # 优先使用准确时间，否则使用原始时间
                    if target_datetime:
                        # 使用准确的完整时间进行比较
                        cursor.execute('''
                            SELECT DISTINCT match_id
                            FROM matches
                            WHERE match_id != ?
                              AND accurate_datetime IS NOT NULL
                              AND accurate_datetime < ?
                            ORDER BY match_id
                        ''', (exclude_match_id, target_datetime))
                    elif target_date or original_date:
                        # 使用日期进行比较（如果没有准确时间）
                        compare_date = target_date or original_date
                        cursor.execute('''
                            SELECT DISTINCT match_id
                            FROM matches
                            WHERE match_id != ?
                              AND (
                                  (accurate_date IS NOT NULL AND accurate_date < ?) OR
                                  (accurate_date IS NULL AND match_date IS NOT NULL AND match_date < ?)
                              )
                            ORDER BY match_id
                        ''', (exclude_match_id, compare_date, compare_date))
                    else:
                        logger.warning(f"比赛 {exclude_match_id} 没有有效的时间信息，使用所有比赛")
                        # 如果没有时间信息，回退到原始逻辑
                        cursor.execute('''
                            SELECT DISTINCT match_id
                            FROM matches
                            WHERE match_id != ?
                            ORDER BY match_id
                        ''', (exclude_match_id,))
                else:
                    # 原始逻辑：返回所有其他比赛
                    cursor.execute('''
                        SELECT DISTINCT match_id
                        FROM matches
                        WHERE match_id != ?
                        ORDER BY match_id
                    ''', (exclude_match_id,))

                rows = cursor.fetchall()
                result = [row[0] for row in rows]

                if only_historical:
                    logger.info(f"找到 {len(result)} 场开赛时间早于比赛 {exclude_match_id} 的历史比赛")
                else:
                    logger.info(f"找到 {len(result)} 场其他比赛")

                return result

        except sqlite3.Error as e:
            logger.error(f"查询其他比赛失败: {e}")
            return []
    
    def check_match_exists(self, match_id: str) -> bool:
        """检查比赛是否存在"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                cursor.execute('''
                    SELECT COUNT(*) FROM matches WHERE match_id = ?
                ''', (match_id,))
                
                count = cursor.fetchone()[0]
                return count > 0
                
        except sqlite3.Error as e:
            logger.error(f"检查比赛存在性失败: {e}")
            return False
    
    def get_match_info(self, match_id: str) -> Optional[Dict]:
        """获取比赛基本信息"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                conn.row_factory = sqlite3.Row
                cursor = conn.cursor()

                cursor.execute('''
                    SELECT match_id, league, home_team, away_team,
                           match_time, match_date, home_score, away_score, match_state
                    FROM matches
                    WHERE match_id = ?
                ''', (match_id,))

                row = cursor.fetchone()
                if row:
                    return dict(row)
                return None

        except sqlite3.Error as e:
            logger.error(f"查询比赛信息失败: {e}")
            return None

    def get_recent_matches(self, days: int) -> List[Dict]:
        """
        获取最近指定天数内的比赛

        Args:
            days: 天数，0表示今天，1表示昨天和今天，以此类推

        Returns:
            比赛信息列表，按开赛时间从早到晚排序
        """
        try:
            with sqlite3.connect(self.db_path) as conn:
                conn.row_factory = sqlite3.Row
                cursor = conn.cursor()

                # 计算日期范围
                from datetime import datetime, timedelta
                today = datetime.now().date()

                if days == 0:
                    # 只查询今天的比赛
                    start_date = today
                    end_date = today
                else:
                    # 查询从(今天-days)到今天的比赛
                    start_date = today - timedelta(days=days)
                    end_date = today

                # 转换为字符串格式
                start_date_str = start_date.strftime('%Y-%m-%d')
                end_date_str = end_date.strftime('%Y-%m-%d')

                # 查询比赛，优先使用accurate_date，备用match_date
                cursor.execute('''
                    SELECT match_id, league, home_team, away_team,
                           match_time, match_date, accurate_date, accurate_datetime,
                           home_score, away_score, match_state
                    FROM matches
                    WHERE (
                        (accurate_date IS NOT NULL AND accurate_date BETWEEN ? AND ?) OR
                        (accurate_date IS NULL AND match_date IS NOT NULL AND match_date BETWEEN ? AND ?)
                    )
                    ORDER BY
                        COALESCE(accurate_datetime, accurate_date || ' ' || COALESCE(match_time, '00:00:00'),
                                match_date || ' ' || COALESCE(match_time, '00:00:00')) ASC
                ''', (start_date_str, end_date_str, start_date_str, end_date_str))

                rows = cursor.fetchall()
                result = []

                for row in rows:
                    match_info = dict(row)
                    # 格式化显示用的开赛时间
                    if match_info.get('accurate_datetime'):
                        match_info['display_time'] = match_info['accurate_datetime']
                    elif match_info.get('accurate_date'):
                        time_part = match_info.get('match_time', '00:00:00')
                        match_info['display_time'] = f"{match_info['accurate_date']} {time_part}"
                    elif match_info.get('match_date'):
                        time_part = match_info.get('match_time', '00:00:00')
                        match_info['display_time'] = f"{match_info['match_date']} {time_part}"
                    else:
                        match_info['display_time'] = "未知时间"

                    result.append(match_info)

                logger.info(f"找到最近 {days} 天内的 {len(result)} 场比赛")
                return result

        except sqlite3.Error as e:
            logger.error(f"查询最近比赛失败: {e}")
            return []

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
独立比赛筛选分析工具 - 实时抓取版本
实现博彩态度2和凯利分析2的筛选功能
每次都重新抓取赔率数据，无数据库依赖
"""

import requests
import re
import time
import logging
from datetime import datetime
from typing import List, Dict, Optional
from bs4 import BeautifulSoup

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class RealtimeMatchAnalyzer:
    """实时比赛分析器"""

    def __init__(self):
        """初始化分析器"""
        self.base_url = "https://m.titan007.com"
        self.target_companies = [
            'bet365', 'betathome', 'betfair', 'bwin', 'coral',
            'Interwetten', 'pinnacle', '伟德', '利记', '威廉希尔',
            '明升', '易胜博', '澳门', '立博', '金宝博', '香港马会', '竞猜官方'
        ]
        
        # 公司名称映射
        self.company_mapping = {
            'bet365': 'bet365',
            'betfair': 'betfair',
            'pinnacle': 'pinnacle',
            '威廉希尔': '威廉希尔',
            '立博': '立博',
            '易胜博': '易胜博',
            '明升': '明升',
            '澳门': '澳门',
            '竞猜官方': '竞猜官方',
            '香港马会': '香港马会'
        }

        print("🎯 实时比赛筛选分析工具已初始化")
        print("📡 每次分析都将重新抓取最新赔率数据")
    
    def scrape_match_odds(self, match_id: str) -> Optional[Dict]:
        """重新抓取比赛的赔率数据"""
        print(f"\n📡 正在抓取比赛 {match_id} 的赔率数据...")
        
        try:
            url = f"{self.base_url}/europe/{match_id}.htm"
            print(f"🔗 访问URL: {url}")
            
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
                'Accept-Language': 'zh-CN,zh;q=0.8,en-US;q=0.5,en;q=0.3',
                'Connection': 'keep-alive',
            }
            
            response = requests.get(url, headers=headers, timeout=15)
            
            if response.status_code != 200:
                print(f"❌ HTTP请求失败: {response.status_code}")
                return None
            
            # 解析赔率数据
            companies_data = self.parse_odds_page(response.text)
            
            if companies_data:
                print(f"✅ 成功抓取 {len(companies_data)} 家公司的赔率数据")
                for company, odds in companies_data.items():
                    print(f"  📊 {company}: {odds['home_odds']:.2f} {odds['draw_odds']:.2f} {odds['away_odds']:.2f}")
                return {
                    'match_id': match_id,
                    'companies': companies_data,
                    'scrape_time': datetime.now().isoformat()
                }
            else:
                print("❌ 未能解析到有效的赔率数据")
                return None
            
        except Exception as e:
            print(f"❌ 抓取赔率数据异常: {e}")
            return None
    
    def parse_odds_page(self, html_content: str) -> Dict:
        """解析赔率页面"""
        companies_data = {}
        
        try:
            # 使用正则表达式直接提取赔率数据
            # 查找类似 "竞猜官方|1.85|3.20|4.50" 的模式
            pattern = r'([^|]+)\|(\d+\.\d+)\|(\d+\.\d+)\|(\d+\.\d+)'
            matches = re.findall(pattern, html_content)
            
            for match in matches:
                company_name = match[0].strip()
                home_odds = float(match[1])
                draw_odds = float(match[2])
                away_odds = float(match[3])
                
                # 标准化公司名称
                normalized_name = self.normalize_company_name(company_name)
                if normalized_name:
                    companies_data[normalized_name] = {
                        'home_odds': home_odds,
                        'draw_odds': draw_odds,
                        'away_odds': away_odds,
                        'scrape_time': datetime.now().isoformat()
                    }
            
            # 如果正则表达式没有找到数据，尝试HTML解析
            if not companies_data:
                companies_data = self.parse_html_table(html_content)
            
            return companies_data
            
        except Exception as e:
            print(f"❌ 解析赔率页面失败: {e}")
            return {}
    
    def parse_html_table(self, html_content: str) -> Dict:
        """解析HTML表格"""
        companies_data = {}
        
        try:
            soup = BeautifulSoup(html_content, 'html.parser')
            
            # 查找所有可能的表格
            tables = soup.find_all('table')
            
            for table in tables:
                rows = table.find_all('tr')
                
                for row in rows:
                    cells = row.find_all(['td', 'th'])
                    if len(cells) < 4:
                        continue
                    
                    # 提取文本内容
                    cell_texts = [cell.get_text().strip() for cell in cells]
                    
                    # 查找公司名称和赔率
                    company_name = None
                    odds_values = []
                    
                    for text in cell_texts:
                        # 检查是否是赔率格式
                        if re.match(r'^\d+\.\d+$', text):
                            try:
                                odds_values.append(float(text))
                            except ValueError:
                                continue
                        elif text and not re.match(r'^\d+$', text) and text not in ['主胜', '平局', '客胜', '公司']:
                            if not company_name:
                                company_name = text
                    
                    # 如果找到公司名称和至少3个赔率值
                    if company_name and len(odds_values) >= 3:
                        normalized_name = self.normalize_company_name(company_name)
                        if normalized_name:
                            companies_data[normalized_name] = {
                                'home_odds': odds_values[0],
                                'draw_odds': odds_values[1],
                                'away_odds': odds_values[2],
                                'scrape_time': datetime.now().isoformat()
                            }
            
            return companies_data
            
        except Exception as e:
            print(f"❌ HTML表格解析失败: {e}")
            return {}
    
    def normalize_company_name(self, company_name: str) -> Optional[str]:
        """标准化公司名称"""
        if not company_name:
            return None
            
        company_name = company_name.strip()
        
        # 直接匹配
        if company_name in self.company_mapping:
            return self.company_mapping[company_name]
        
        # 模糊匹配
        company_lower = company_name.lower()
        fuzzy_mappings = {
            'bet365': ['bet365', 'bet 365'],
            'betfair': ['betfair', 'bet fair'],
            'pinnacle': ['pinnacle', 'pin'],
            '威廉希尔': ['威廉希尔', '威廉', 'william'],
            '立博': ['立博', 'ladbrokes'],
            '易胜博': ['易胜博', 'ysb'],
            '明升': ['明升', 'mansion'],
            '澳门': ['澳门', 'macau'],
            '竞猜官方': ['竞猜官方', '竞猜', '官方'],
            '香港马会': ['香港马会', '马会']
        }
        
        for standard_name, variants in fuzzy_mappings.items():
            for variant in variants:
                if variant.lower() in company_lower:
                    return standard_name
        
        # 如果在目标公司列表中，直接返回
        if company_name in self.target_companies:
            return company_name
        
        return None
    
    def analyze_betting_attitude2(self, match_id: str, odds_type: str, threshold: int, target_company: str):
        """分析博彩态度2"""
        print(f"\n🔍 开始分析比赛 {match_id} 的博彩态度2")
        print(f"📋 目标公司: {target_company}, 投注类型: {odds_type}, 阈值: {threshold}")
        
        try:
            # 抓取赔率数据
            odds_data = self.scrape_match_odds(match_id)
            if not odds_data or not odds_data['companies']:
                return False, f"无法获取比赛 {match_id} 的赔率数据"
            
            companies = odds_data['companies']
            
            # 检查目标公司是否存在
            if target_company not in companies:
                available = list(companies.keys())
                return False, f"目标公司 {target_company} 不在可用公司中。可用: {available}"
            
            # 获取目标公司赔率
            target_data = companies[target_company]
            if odds_type == 'home':
                target_odds = target_data['home_odds']
            elif odds_type == 'draw':
                target_odds = target_data['draw_odds']
            elif odds_type == 'away':
                target_odds = target_data['away_odds']
            else:
                return False, f"无效的投注类型: {odds_type}"
            
            print(f"🎯 目标公司 {target_company} 的 {odds_type} 赔率: {target_odds:.2f}")
            
            # 统计高于目标赔率的公司数量
            higher_count = 0
            total_other_companies = 0
            
            for company_name, company_data in companies.items():
                if company_name == target_company:
                    continue
                
                total_other_companies += 1
                
                if odds_type == 'home':
                    company_odds = company_data['home_odds']
                elif odds_type == 'draw':
                    company_odds = company_data['draw_odds']
                else:  # away
                    company_odds = company_data['away_odds']
                
                if company_odds > target_odds:
                    higher_count += 1
                    print(f"  📈 {company_name}: {company_odds:.2f} (高于目标)")
                else:
                    print(f"  📉 {company_name}: {company_odds:.2f} (低于或等于目标)")
            
            # 判断：高于目标赔率的公司数量 < 阈值
            is_qualified = higher_count < threshold
            
            result_msg = f"目标公司 {target_company} 赔率: {target_odds:.2f}, 高于此赔率的公司: {higher_count}/{total_other_companies}, 阈值: {threshold}, 结果: {'✅符合' if is_qualified else '❌不符合'}"
            
            print(f"📊 {result_msg}")
            return is_qualified, result_msg
            
        except Exception as e:
            error_msg = f"分析失败: {e}"
            print(f"❌ {error_msg}")
            return False, error_msg

def main():
    """主函数"""
    print("🎯 实时比赛筛选分析工具")
    print("=" * 60)
    
    analyzer = RealtimeMatchAnalyzer()
    
    while True:
        print(f"\n请输入比赛ID（输入 'quit' 退出）:")
        match_id = input().strip()
        
        if match_id.lower() == 'quit':
            break
        
        if not match_id:
            print("❌ 请输入有效的比赛ID")
            continue
        
        # 测试博彩态度2筛选
        print(f"\n🧪 测试博彩态度2筛选...")
        
        # 条件1：竞猜官方，阈值2，主胜
        result1, msg1 = analyzer.analyze_betting_attitude2(match_id, 'home', 2, '竞猜官方')
        print(f"条件1结果: {msg1}")
        
        # 条件2：香港马会，阈值2，主胜  
        result2, msg2 = analyzer.analyze_betting_attitude2(match_id, 'home', 2, '香港马会')
        print(f"条件2结果: {msg2}")

if __name__ == "__main__":
    main()

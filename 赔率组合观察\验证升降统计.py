#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证升降统计功能的准确性
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from odds_combination_database import OddsCombinationDatabase

def verify_odds_trend_analysis():
    """验证升降统计分析"""
    
    print("🔍 验证升降统计功能")
    print("=" * 60)
    
    try:
        # 初始化数据库
        db = OddsCombinationDatabase()
        print(f"✅ 数据库连接成功: {db.db_path}")
        
        # 测试比赛ID和博彩公司
        match_id = "2701762"
        company_name = "bet365"
        
        print(f"\n📊 分析比赛: {match_id}")
        print(f"📊 博彩公司: {company_name}")
        
        # 获取赔率数据
        odds_data = db.get_match_odds_by_company(match_id, company_name)
        print(f"📊 获取到 {len(odds_data)} 条赔率数据")
        
        if not odds_data:
            print("❌ 没有找到赔率数据")
            return
        
        print(f"\n📋 原始赔率数据:")
        print("序号  日期       时间     主胜    平局    客胜")
        print("-" * 50)
        
        for i, odds in enumerate(odds_data):
            print(f"{i+1:2d}   {odds['date']} {odds['time']} {odds['home_odds']:6.3f} {odds['draw_odds']:6.3f} {odds['away_odds']:6.3f}")
        
        # 手动验证升降统计
        print(f"\n🔍 手动验证升降统计:")
        
        # 分析主胜、平局、客胜
        for result_type, odds_key in [("主胜", "home_odds"), ("平局", "draw_odds"), ("客胜", "away_odds")]:
            print(f"\n--- {result_type} 赔率分析 ---")
            
            # 提取赔率序列
            odds_sequence = []
            for odds in odds_data:
                if odds.get(odds_key) is not None:
                    odds_sequence.append(float(odds[odds_key]))
            
            print(f"赔率序列: {odds_sequence}")
            
            if len(odds_sequence) < 2:
                print("数据不足，跳过")
                continue
            
            # 手动统计升降
            down_count = 0
            up_count = 0
            changes = []
            
            for i in range(1, len(odds_sequence)):
                prev_odds = odds_sequence[i-1]
                curr_odds = odds_sequence[i]
                
                if curr_odds < prev_odds:
                    down_count += 1
                    changes.append(f"第{i+1}条: {prev_odds:.3f} → {curr_odds:.3f} (下降 {prev_odds-curr_odds:.3f})")
                elif curr_odds > prev_odds:
                    up_count += 1
                    changes.append(f"第{i+1}条: {prev_odds:.3f} → {curr_odds:.3f} (上升 {curr_odds-prev_odds:.3f})")
                else:
                    changes.append(f"第{i+1}条: {prev_odds:.3f} → {curr_odds:.3f} (不变)")
            
            # 显示详细变化
            print("详细变化过程:")
            for change in changes:
                print(f"  {change}")
            
            # 计算统计数据
            initial_odds = odds_sequence[0]
            final_odds = odds_sequence[-1]
            odds_diff = final_odds - initial_odds
            change_rate = (odds_diff / initial_odds) * 100 if initial_odds != 0 else 0
            
            print(f"\n📊 统计结果:")
            print(f"  下降次数: {down_count}")
            print(f"  上升次数: {up_count}")
            print(f"  初始赔率: {initial_odds:.3f}")
            print(f"  最终赔率: {final_odds:.3f}")
            print(f"  赔率差值: {odds_diff:+.3f}")
            print(f"  变化率: {change_rate:+.2f}%")
            print(f"  数据点数: {len(odds_sequence)}")
        
        # 对比程序计算结果
        print(f"\n🤖 程序计算结果对比:")
        print("请将上述手动计算结果与程序显示的结果进行对比")
        
    except Exception as e:
        print(f"❌ 验证失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    verify_odds_trend_analysis()

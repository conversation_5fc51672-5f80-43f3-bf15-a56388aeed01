#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查数据库中的日期范围
"""

import sys
import os
import sqlite3

def check_database_dates():
    """检查数据库中的日期范围"""
    print("🔍 检查数据库中的日期范围")
    
    try:
        # 数据库路径
        current_dir = os.path.dirname(os.path.abspath(__file__))
        parent_dir = os.path.dirname(current_dir)
        db_path = os.path.join(parent_dir, "odds_data.db")
        
        print(f"数据库路径: {db_path}")
        
        if not os.path.exists(db_path):
            print("❌ 数据库文件不存在")
            return
        
        with sqlite3.connect(db_path) as conn:
            cursor = conn.cursor()
            
            # 检查matches表的结构
            print("\n📋 matches表结构:")
            cursor.execute("PRAGMA table_info(matches)")
            columns = cursor.fetchall()
            for col in columns:
                print(f"  {col[1]} ({col[2]})")
            
            # 检查总比赛数量
            cursor.execute("SELECT COUNT(*) FROM matches")
            total_matches = cursor.fetchone()[0]
            print(f"\n📊 总比赛数量: {total_matches}")
            
            # 检查日期范围
            print("\n📅 日期范围分析:")
            
            # 检查match_date字段
            cursor.execute("""
                SELECT 
                    MIN(match_date) as min_date,
                    MAX(match_date) as max_date,
                    COUNT(DISTINCT match_date) as unique_dates,
                    COUNT(*) as total_with_date
                FROM matches 
                WHERE match_date IS NOT NULL
            """)
            result = cursor.fetchone()
            if result and result[0]:
                print(f"  match_date: {result[0]} 到 {result[1]} ({result[2]} 个不同日期, {result[3]} 场比赛)")
            else:
                print("  match_date: 无数据")
            
            # 检查accurate_date字段
            cursor.execute("""
                SELECT 
                    MIN(accurate_date) as min_date,
                    MAX(accurate_date) as max_date,
                    COUNT(DISTINCT accurate_date) as unique_dates,
                    COUNT(*) as total_with_date
                FROM matches 
                WHERE accurate_date IS NOT NULL
            """)
            result = cursor.fetchone()
            if result and result[0]:
                print(f"  accurate_date: {result[0]} 到 {result[1]} ({result[2]} 个不同日期, {result[3]} 场比赛)")
            else:
                print("  accurate_date: 无数据")
            
            # 检查accurate_datetime字段
            cursor.execute("""
                SELECT 
                    MIN(accurate_datetime) as min_datetime,
                    MAX(accurate_datetime) as max_datetime,
                    COUNT(*) as total_with_datetime
                FROM matches 
                WHERE accurate_datetime IS NOT NULL
            """)
            result = cursor.fetchone()
            if result and result[0]:
                print(f"  accurate_datetime: {result[0]} 到 {result[1]} ({result[2]} 场比赛)")
            else:
                print("  accurate_datetime: 无数据")
            
            # 显示最近的几场比赛
            print("\n🔍 最近的10场比赛:")
            cursor.execute("""
                SELECT match_id, match_date, accurate_date, accurate_datetime, home_team, away_team
                FROM matches 
                WHERE match_date IS NOT NULL OR accurate_date IS NOT NULL
                ORDER BY 
                    COALESCE(accurate_datetime, accurate_date, match_date) DESC
                LIMIT 10
            """)
            recent_matches = cursor.fetchall()
            
            for match in recent_matches:
                match_id, match_date, accurate_date, accurate_datetime, home_team, away_team = match
                display_date = accurate_datetime or accurate_date or match_date
                print(f"  {match_id}: {display_date} | {home_team} vs {away_team}")
        
        print(f"\n✅ 检查完成")
        
    except Exception as e:
        print(f"❌ 检查失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    check_database_dates()

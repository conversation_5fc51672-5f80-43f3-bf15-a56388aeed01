#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试修复后的联赛映射功能
"""

import sys
import os

def test_league_mapping_fix():
    """测试修复后的联赛映射"""
    
    print("🔍 测试修复后的联赛映射功能")
    print("=" * 50)
    
    try:
        from date_match_extractor import DateMatchExtractor
        
        # 创建提取器
        extractor = DateMatchExtractor()
        print("✅ 日期比赛提取器创建成功")
        
        # 测试URL
        test_url = "https://m.titan007.com/Schedule.htm?date=2025-07-22"
        print(f"📋 测试URL: {test_url}")
        
        # 提取比赛信息
        print("🚀 开始提取比赛信息...")
        matches_by_league = extractor.extract_matches_from_date_page(test_url)
        
        # 显示结果
        if matches_by_league:
            total_matches = sum(len(matches) for matches in matches_by_league.values())
            print(f"✅ 提取成功！")
            print(f"📊 找到 {len(matches_by_league)} 个联赛，{total_matches} 场比赛")
            
            print(f"\n🏆 联赛详情 (修复后):")
            for i, (league, matches) in enumerate(matches_by_league.items(), 1):
                print(f"  {i}. {league}: {len(matches)} 场比赛")
                
                # 显示前2场比赛
                for j, match in enumerate(matches[:2], 1):
                    print(f"     {j}. {match['match_id']}: {match['home_team']} vs {match['away_team']}")
                
                if len(matches) > 2:
                    print(f"     ... 还有 {len(matches) - 2} 场比赛")
                
                # 只显示前10个联赛，避免输出过长
                if i >= 10:
                    remaining_leagues = len(matches_by_league) - 10
                    if remaining_leagues > 0:
                        print(f"  ... 还有 {remaining_leagues} 个联赛")
                    break
                print()
            
            # 检查联赛名称是否正确
            print(f"🔍 联赛名称检查:")
            league_names = list(matches_by_league.keys())
            
            # 检查是否有数字ID作为联赛名称（问题指标）
            numeric_leagues = [name for name in league_names if name.isdigit()]
            if numeric_leagues:
                print(f"  ❌ 发现 {len(numeric_leagues)} 个数字联赛名称: {numeric_leagues[:5]}")
            else:
                print(f"  ✅ 所有联赛名称都是文字，没有数字ID")
            
            # 检查是否有中文联赛名称
            chinese_leagues = [name for name in league_names if any('\u4e00' <= char <= '\u9fff' for char in name)]
            if chinese_leagues:
                print(f"  ✅ 发现 {len(chinese_leagues)} 个中文联赛名称: {chinese_leagues[:5]}")
            
            # 检查是否有英文联赛名称
            english_leagues = [name for name in league_names if any(char.isalpha() and ord(char) < 256 for char in name)]
            if english_leagues:
                print(f"  ✅ 发现 {len(english_leagues)} 个英文联赛名称: {english_leagues[:5]}")
            
            # 检查联赛名称长度
            avg_length = sum(len(name) for name in league_names) / len(league_names)
            print(f"  📏 联赛名称平均长度: {avg_length:.1f} 字符")
            
            # 检查是否有"联赛"前缀的名称（备用方案指标）
            fallback_leagues = [name for name in league_names if name.startswith('联赛')]
            if fallback_leagues:
                print(f"  ⚠️ 发现 {len(fallback_leagues)} 个备用联赛名称: {fallback_leagues[:3]}")
            
        else:
            print("❌ 未找到任何比赛信息")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

def test_specific_leagues():
    """测试特定联赛"""
    
    print(f"\n🎯 测试特定联赛识别")
    print("-" * 30)
    
    try:
        from date_match_extractor import DateMatchExtractor
        extractor = DateMatchExtractor()
        
        test_url = "https://m.titan007.com/Schedule.htm?date=2025-07-22"
        matches_by_league = extractor.extract_matches_from_date_page(test_url)
        
        if matches_by_league:
            # 查找常见联赛
            target_leagues = ['欧冠杯', '欧会杯', '英超', '西甲', '德甲', '意甲', '法甲', '中超', '韩K联']
            
            print("🔍 查找常见联赛:")
            found_leagues = []
            
            for target in target_leagues:
                found = False
                for league_name in matches_by_league.keys():
                    if target in league_name or league_name in target:
                        found_leagues.append((target, league_name, len(matches_by_league[league_name])))
                        found = True
                        break
                
                if found:
                    print(f"  ✅ {target}: 找到匹配联赛")
                else:
                    print(f"  ❌ {target}: 未找到")
            
            if found_leagues:
                print(f"\n📊 找到的联赛详情:")
                for target, actual, count in found_leagues:
                    print(f"  {target} -> {actual} ({count} 场比赛)")
            
        else:
            print("❌ 无法测试，未找到比赛数据")
        
    except Exception as e:
        print(f"❌ 特定联赛测试失败: {e}")

def simulate_ui_display():
    """模拟UI显示效果"""
    
    print(f"\n🖥️ 模拟UI显示效果")
    print("-" * 30)
    
    try:
        from date_match_extractor import DateMatchExtractor
        extractor = DateMatchExtractor()
        
        test_url = "https://m.titan007.com/Schedule.htm?date=2025-07-22"
        matches_by_league = extractor.extract_matches_from_date_page(test_url)
        
        if matches_by_league:
            total_matches = sum(len(matches) for matches in matches_by_league.values())
            
            print("┌─────────────────────────────────────────────────────────┐")
            print("│                    选择要抓取的联赛                        │")
            print("│                                                         │")
            print(f"│ 共发现 {len(matches_by_league)} 个联赛，{total_matches} 场比赛                                │")
            print("│                                                         │")
            print("│ [全选] [全不选] [反选]                                    │")
            print("│                                                         │")
            
            # 显示前10个联赛
            for i, (league, matches) in enumerate(list(matches_by_league.items())[:10], 1):
                # 获取前几场比赛作为预览
                preview_matches = []
                for match in matches[:3]:
                    preview_matches.append(f"{match['home_team']} vs {match['away_team']}")
                
                preview_text = ", ".join(preview_matches)
                if len(matches) > 3:
                    preview_text += ", ..."
                
                # 限制显示长度
                if len(preview_text) > 40:
                    preview_text = preview_text[:37] + "..."
                
                checkbox = "☑" if i <= 3 else "☐"  # 模拟前3个被选中
                print(f"│ {checkbox} {league} ({len(matches)} 场)  ({preview_text})")
            
            if len(matches_by_league) > 10:
                print(f"│ ... 还有 {len(matches_by_league) - 10} 个联赛")
            
            print("│                                                         │")
            selected_leagues = min(3, len(matches_by_league))
            selected_matches = sum(len(matches) for league, matches in list(matches_by_league.items())[:3])
            print(f"│ 已选择: {selected_leagues} 个联赛，{selected_matches} 场比赛                               │")
            print("│                                                         │")
            print("│                                    [确定] [取消]         │")
            print("└─────────────────────────────────────────────────────────┘")
            
        else:
            print("❌ 无法模拟，未找到比赛数据")
        
    except Exception as e:
        print(f"❌ UI模拟失败: {e}")

if __name__ == "__main__":
    test_league_mapping_fix()
    test_specific_leagues()
    simulate_ui_display()
    
    print(f"\n🎉 测试完成！")
    print("如果联赛名称显示正确，问题就已经修复了。")
    print("现在可以在主程序中测试按日期抓取功能。")

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
专门调试凯利客筛选问题
根据用户提供的具体条件进行验证
"""

import sqlite3
import sys
import os

def debug_kelly_away_specific():
    """调试凯利客的具体问题"""
    print("=== 调试凯利客筛选问题 ===")
    
    # 用户的具体筛选条件
    kelly_type = "kelly_away"  # 凯利客
    stats_count = 5
    kelly_threshold = 1.1
    selected_companies = ["Betfair", "betathome", "betfair", "pinnacle"]
    meaningless_threshold = 0  # 一次都不能出现
    
    print(f"用户筛选条件:")
    print(f"  凯利类型: {kelly_type} (凯利客)")
    print(f"  统计数量: {stats_count}")
    print(f"  凯利门槛: {kelly_threshold}")
    print(f"  监控公司: {selected_companies}")
    print(f"  无意义公司门槛: {meaningless_threshold} (一次都不能出现)")
    print()
    
    try:
        with sqlite3.connect("odds_data.db") as conn:
            conn.row_factory = sqlite3.Row
            cursor = conn.cursor()
            
            # 模拟完整的筛选流程
            print("开始模拟完整的筛选流程...")
            
            # 1. 获取所有有凯利客数据的比赛
            cursor.execute('SELECT DISTINCT match_id FROM odds WHERE {} IS NOT NULL'.format(kelly_type))
            all_match_ids = [row[0] for row in cursor.fetchall()]
            print(f"总共有 {len(all_match_ids)} 场比赛有{kelly_type}数据")
            
            # 2. 对每场比赛进行凯利分析2
            qualified_matches = []
            sample_analysis = []
            
            for i, match_id in enumerate(all_match_ids[:50]):  # 测试前50场
                # 获取该比赛的赔率数据
                cursor.execute(f'''
                    SELECT {kelly_type}, return_rate, company_name
                    FROM odds
                    WHERE match_id = ? AND {kelly_type} IS NOT NULL AND return_rate IS NOT NULL AND company_name IS NOT NULL
                    ORDER BY {kelly_type} DESC
                ''', (match_id,))
                
                odds_data = cursor.fetchall()
                
                if len(odds_data) < stats_count:
                    continue
                
                # 取前N家的数据
                top_odds = odds_data[:stats_count]
                
                # 计算平均凯利值
                kelly_values = [float(row[0]) for row in top_odds]
                company_list = [row[2] for row in top_odds]
                
                avg_kelly = sum(kelly_values) / len(kelly_values)
                
                # 使用修复后的逻辑统计观察次数
                observation_count = 0
                for company in selected_companies:
                    # 忽略大小写进行匹配
                    for list_company in company_list:
                        if company.lower() == list_company.lower():
                            observation_count += 1
                
                # 判断是否符合筛选条件
                kelly_qualified = avg_kelly > kelly_threshold
                observation_qualified = observation_count <= meaningless_threshold
                
                result = kelly_qualified and observation_qualified
                
                # 记录分析结果
                analysis = {
                    'match_id': match_id,
                    'avg_kelly': avg_kelly,
                    'observation_count': observation_count,
                    'company_list': company_list,
                    'kelly_qualified': kelly_qualified,
                    'observation_qualified': observation_qualified,
                    'result': result
                }
                sample_analysis.append(analysis)
                
                if result:
                    qualified_matches.append(match_id)
            
            print(f"测试了前50场比赛，符合条件的比赛: {len(qualified_matches)} 场")
            print(f"符合条件的比赛ID: {qualified_matches}")
            print()
            
            # 显示详细分析结果
            print("详细分析结果（前10个）:")
            for analysis in sample_analysis[:10]:
                match_id = analysis['match_id']
                avg_kelly = analysis['avg_kelly']
                observation_count = analysis['observation_count']
                company_list = analysis['company_list']
                result = analysis['result']
                
                status = "✅ 通过" if result else "❌ 不通过"
                print(f"  比赛 {match_id}: {status}")
                print(f"    平均凯利: {avg_kelly:.3f} ({'>' if analysis['kelly_qualified'] else '<='} {kelly_threshold})")
                print(f"    观察次数: {observation_count} ({'<=' if analysis['observation_qualified'] else '>'} {meaningless_threshold})")
                print(f"    前{stats_count}家公司: {company_list}")
                
                # 检查是否有监控公司出现
                monitored_in_top = []
                for company in company_list:
                    for monitored in selected_companies:
                        if company.lower() == monitored.lower():
                            monitored_in_top.append(company)
                
                if monitored_in_top:
                    print(f"    ⚠️  监控公司出现: {monitored_in_top}")
                
                print()
            
            # 特别检查：找出所有应该被排除但可能通过的比赛
            print("特别检查：寻找可能的问题比赛...")
            problem_matches = []
            
            for analysis in sample_analysis:
                # 如果观察次数>0但结果为True，那就是问题
                if analysis['observation_count'] > meaningless_threshold and analysis['result']:
                    problem_matches.append(analysis)
            
            if problem_matches:
                print(f"🐛 发现 {len(problem_matches)} 场问题比赛（应该被排除但通过了筛选）:")
                for problem in problem_matches:
                    print(f"  比赛 {problem['match_id']}: 观察次数={problem['observation_count']}, 结果={problem['result']}")
            else:
                print("✅ 没有发现问题比赛，筛选逻辑正确")
            
            return len(problem_matches) == 0
            
    except Exception as e:
        print(f"❌ 调试失败: {e}")
        return False

def check_specific_match_from_screenshot():
    """检查截图中显示的具体比赛"""
    print("\n=== 检查截图中的具体比赛 ===")
    
    try:
        with sqlite3.connect("odds_data.db") as conn:
            conn.row_factory = sqlite3.Row
            cursor = conn.cursor()
            
            # 根据截图中的信息，寻找可能的比赛
            # 截图显示的公司包括：betfair, pinnacle等
            # 我们寻找包含这些公司且凯利客值在1.1-1.2范围的比赛
            
            cursor.execute('''
                SELECT DISTINCT o.match_id
                FROM odds o
                WHERE o.company_name IN ('betfair', 'Betfair', 'pinnacle') 
                  AND o.kelly_away BETWEEN 1.1 AND 1.2
                LIMIT 5
            ''')
            
            candidate_matches = [row[0] for row in cursor.fetchall()]
            print(f"找到 {len(candidate_matches)} 场可能的候选比赛")
            
            for match_id in candidate_matches:
                print(f"\n--- 检查比赛 {match_id} ---")
                
                # 获取该比赛的前5家凯利客数据
                cursor.execute('''
                    SELECT kelly_away, company_name
                    FROM odds
                    WHERE match_id = ? AND kelly_away IS NOT NULL AND company_name IS NOT NULL
                    ORDER BY kelly_away DESC
                    LIMIT 5
                ''', (match_id,))
                
                top5_data = cursor.fetchall()
                
                if len(top5_data) < 5:
                    print(f"数据不足，只有{len(top5_data)}条")
                    continue
                
                print("前5家凯利客数据:")
                company_list = []
                kelly_values = []
                
                for i, row in enumerate(top5_data, 1):
                    kelly_val = float(row[0])
                    company = row[1]
                    company_list.append(company)
                    kelly_values.append(kelly_val)
                    
                    monitored = company.lower() in ['betfair', 'betathome', 'pinnacle']
                    mark = "★" if monitored else " "
                    print(f"  {i}. {mark} {company:15s} - 凯利客:{kelly_val:.3f}")
                
                # 计算平均凯利值
                avg_kelly = sum(kelly_values) / len(kelly_values)
                
                # 统计监控公司出现次数
                selected_companies = ["Betfair", "betathome", "betfair", "pinnacle"]
                observation_count = 0
                for company in selected_companies:
                    for list_company in company_list:
                        if company.lower() == list_company.lower():
                            observation_count += 1
                
                print(f"平均凯利: {avg_kelly:.3f}")
                print(f"观察次数: {observation_count}")
                
                # 判断条件
                kelly_qualified = avg_kelly > 1.1
                observation_qualified = observation_count <= 0
                result = kelly_qualified and observation_qualified
                
                print(f"凯利条件: {kelly_qualified}")
                print(f"观察条件: {observation_qualified}")
                print(f"最终结果: {result} ({'应该通过' if result else '应该被排除'})")
                
                if observation_count > 0 and result:
                    print("🐛 这是一个问题比赛！")
            
            return True
            
    except Exception as e:
        print(f"❌ 检查失败: {e}")
        return False

def main():
    """主调试函数"""
    print("🔍 凯利客筛选问题专项调试")
    print("=" * 60)
    
    # 检查数据库文件是否存在
    if not os.path.exists("odds_data.db"):
        print("❌ 数据库文件 odds_data.db 不存在")
        return False
    
    tests = [
        debug_kelly_away_specific,
        check_specific_match_from_screenshot
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"❌ 测试异常: {e}")
    
    print("\n" + "=" * 60)
    print(f"📊 调试结果: {passed}/{total} 完成")
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)

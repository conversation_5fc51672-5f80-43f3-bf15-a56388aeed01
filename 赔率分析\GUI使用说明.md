# 🏈 赔率价值分析系统 - GUI使用说明

## 📋 系统概述

这是一个全新的图形化界面，用于赔率价值分析系统。当前版本实现了**步骤1：数据库结构分析**功能，后续将逐步添加其他功能模块。

## 🚀 快速启动

### 方法1：双击启动（推荐）
```
双击 start_gui.bat 文件
```

### 方法2：命令行启动
```bash
cd 赔率分析
python odds_analysis_gui.py
```

## 🎯 当前功能

### 步骤1 - 数据库分析 ✅ 已完成
- **自动检测数据库**：自动扫描主数据库和联赛数据库
- **结构分析**：分析表结构、字段类型、数据质量
- **实时进度**：显示分析进度和详细日志
- **结果展示**：表格形式展示数据库概览
- **结果导出**：支持将分析结果导出为JSON文件

### 步骤2 - 时间序列提取 ✅ 新增完成
- **智能提取**：自动提取赔率变化最多的比赛
- **参数配置**：可设置每个数据库提取的比赛数量
- **实时监控**：显示每场比赛的处理进度
- **详细统计**：显示赔率变化次数和时间跨度
- **数据导出**：支持导出完整的时间序列数据

### 使用步骤

#### 1. 启动界面
- 双击 `start_gui.bat` 或运行 `python odds_analysis_gui.py`
- 界面将显示6个标签页，当前有2个可用：
  - **📊 数据库分析**：步骤1功能
  - **📈 时间序列**：步骤2功能

#### 2. 数据库分析（步骤1）
- 切换到"📊 数据库分析"标签页
- 点击"开始分析"按钮
- 系统将自动执行：
  - 分析主数据库结构
  - 分析联赛数据库
  - 生成综合报告
  - 保存分析结果

#### 3. 时间序列提取（步骤2）
- 切换到"📈 时间序列"标签页
- 设置提取参数：
  - **每个数据库提取比赛数**：1-20场（默认5场）
- 点击"开始提取"按钮
- 系统将自动执行：
  - 提取主数据库中赔率变化最多的比赛
  - 提取联赛数据库的时间序列
  - 显示每场比赛的详细信息
  - 保存时间序列数据

#### 4. 查看结果
- **数据库分析结果**：
  - 数据库概览表格：显示每个数据库的表数量和记录数
  - 分析日志：实时显示分析过程和状态信息
- **时间序列结果**：
  - 比赛列表：显示提取的比赛及其统计信息
  - 提取日志：显示每场比赛的处理进度

#### 5. 导出结果
- 分析完成后，"导出结果"按钮将变为可用
- 点击可将完整的结果导出为JSON文件
- 选择保存位置和文件名

### 界面说明

#### 主要区域
1. **标题栏**：显示系统名称和描述
2. **标签页**：6个功能模块（当前只有第1个可用）
3. **控制面板**：开始/停止/导出按钮
4. **进度显示**：进度条和状态信息
5. **结果区域**：
   - 左侧：数据库概览表格
   - 右侧：详细分析日志

#### 按钮功能
- **🟢 开始分析**：启动数据库结构分析
- **🔴 停止分析**：中断正在进行的分析
- **🔵 导出结果**：将分析结果保存为文件

#### 日志颜色说明
- **灰色**：时间戳
- **黑色**：一般信息 [INFO]
- **绿色**：成功信息 [SUCCESS]
- **橙色**：警告信息 [WARNING]
- **红色**：错误信息 [ERROR]

## 📊 输出文件

### 自动生成的文件
- `database_analysis_results_YYYYMMDD_HHMMSS.json`：完整的分析结果
- 包含以下信息：
  - 主数据库结构和统计
  - 各联赛数据库详情
  - 综合分析报告
  - 数据质量评估

### 手动导出的文件
- 用户可选择保存位置和文件名
- JSON格式，包含完整的分析数据
- 可用于后续分析或报告生成

## ⚠️ 注意事项

### 数据库要求
确保以下数据库文件存在且可访问：
```
../odds_data.db                    # 主数据库（必需）
../league_databases/               # 联赛数据库目录（可选）
├── MLS.db                         # 美职联数据库
├── 日职联.db                      # 日职联数据库
└── 其他联赛数据库...
```

**注意**：
- 主数据库 `../odds_data.db` 是必需的，包含主要的赔率数据
- 联赛数据库是可选的，系统会自动检测存在的数据库文件
- 如果联赛数据库不存在，系统会跳过相应的分析

### 系统要求
- **Python 3.8+**
- **PyQt5**：图形界面库
- **pandas, numpy**：数据处理
- **sqlite3**：数据库访问

### 常见问题

#### Q: 界面无法启动
A: 检查Python环境和PyQt5是否正确安装
```bash
python --version
pip install PyQt5
```

#### Q: 找不到数据库文件
A: 确保数据库文件路径正确，检查相对路径设置

#### Q: 分析过程中出错
A: 查看日志区域的错误信息，通常是数据库访问权限或文件损坏问题

#### Q: 导出功能不可用
A: 必须先完成一次完整的分析，才能使用导出功能

## 🔮 后续功能预告

### 即将添加的功能模块：

#### 📈 时间序列（步骤2-3）
- 赔率时间序列提取
- 时间轴标准化
- 数据对齐和清洗

#### 🔍 特征工程（步骤4-6）
- 141维特征提取
- 模式识别和标记
- 特征重要性分析

#### 🔗 联动分析（步骤7-10）
- 多公司同步分析
- 领涨公司识别
- 异常行为检测

#### 🤖 机器学习（步骤11-15）
- 模型训练和验证
- 预测系统构建
- 性能评估

#### 💰 策略回测（步骤16-20）
- 投注策略设计
- 回测模拟
- 风险管理
- 策略优化

## 📞 技术支持

如有问题或建议，请查看：
1. 界面右侧的分析日志
2. 系统生成的错误信息
3. 数据库文件的完整性

---

**版本信息**：v1.0 - 数据库分析模块
**更新日期**：2025年1月27日
**开发状态**：步骤1已完成，步骤2-20开发中

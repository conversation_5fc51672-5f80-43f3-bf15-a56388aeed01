#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
模式与赛果关联分析
统计分析各种赔率行为模式与比赛结果的相关性，发现统计显著的模式-结果关系
"""

import pandas as pd
import numpy as np
import json
import os
from datetime import datetime
import matplotlib.pyplot as plt
import seaborn as sns
from scipy import stats
from scipy.stats import chi2_contingency, fisher_exact
import warnings
warnings.filterwarnings('ignore')

class PatternOutcomeAnalyzer:
    def __init__(self, labeled_data_file=None):
        self.labeled_data_file = labeled_data_file
        self.labeled_data = None
        self.analysis_results = {}
        
    def load_labeled_data(self, filename=None):
        """加载标记后的数据"""
        if filename:
            self.labeled_data_file = filename
        
        if not self.labeled_data_file:
            # 查找最新的标记数据文件
            files = [f for f in os.listdir('.') if f.startswith('labeled_patterns_') and f.endswith('.csv')]
            if files:
                self.labeled_data_file = sorted(files)[-1]
                print(f"自动选择最新文件: {self.labeled_data_file}")
            else:
                print("未找到标记数据文件")
                return None
        
        self.labeled_data = pd.read_csv(self.labeled_data_file)
        print(f"加载标记数据: {self.labeled_data_file}")
        print(f"数据形状: {self.labeled_data.shape}")
        return self.labeled_data
    
    def calculate_pattern_statistics(self):
        """计算模式统计信息"""
        if self.labeled_data is None:
            print("请先加载标记数据")
            return None
        
        print("\n=== 模式统计分析 ===")
        
        stats_results = {}
        
        # 分析每种赔率类型的模式
        for odds_type in ['home', 'draw', 'away']:
            print(f"\n{odds_type.upper()} 赔率模式统计:")
            
            pattern_col = f'primary_pattern_{odds_type}'
            pattern_stats = {}
            
            for pattern in self.labeled_data[pattern_col].unique():
                pattern_data = self.labeled_data[self.labeled_data[pattern_col] == pattern]
                
                if len(pattern_data) < 3:  # 样本太少跳过
                    continue
                
                # 基础统计
                sample_size = len(pattern_data)
                result_counts = pattern_data['match_result'].value_counts()
                result_probs = pattern_data['match_result'].value_counts(normalize=True)
                
                # 计算期望准确率
                if odds_type == 'home':
                    expected_outcome = 'home_win'
                elif odds_type == 'away':
                    expected_outcome = 'away_win'
                else:
                    expected_outcome = 'draw'
                
                accuracy = result_probs.get(expected_outcome, 0)
                
                # 计算置信区间（二项分布）
                success_count = result_counts.get(expected_outcome, 0)
                confidence_interval = self._calculate_binomial_ci(success_count, sample_size)
                
                pattern_stats[pattern] = {
                    'sample_size': sample_size,
                    'accuracy': accuracy,
                    'confidence_interval': confidence_interval,
                    'result_distribution': result_probs.to_dict(),
                    'result_counts': result_counts.to_dict()
                }
                
                print(f"  {pattern}:")
                print(f"    样本数: {sample_size}")
                print(f"    准确率: {accuracy:.3f} ({confidence_interval[0]:.3f}-{confidence_interval[1]:.3f})")
                print(f"    结果分布: {dict(result_probs.round(3))}")
            
            stats_results[odds_type] = pattern_stats
        
        # 综合模式统计
        print(f"\n综合模式统计:")
        combined_stats = {}
        
        for pattern in self.labeled_data['combined_pattern'].unique():
            pattern_data = self.labeled_data[self.labeled_data['combined_pattern'] == pattern]
            
            if len(pattern_data) < 3:
                continue
            
            sample_size = len(pattern_data)
            result_counts = pattern_data['match_result'].value_counts()
            result_probs = pattern_data['match_result'].value_counts(normalize=True)
            
            # 计算信息熵（不确定性度量）
            entropy = -sum(p * np.log2(p) for p in result_probs.values if p > 0)
            
            combined_stats[pattern] = {
                'sample_size': sample_size,
                'result_distribution': result_probs.to_dict(),
                'result_counts': result_counts.to_dict(),
                'entropy': entropy
            }
            
            print(f"  {pattern}:")
            print(f"    样本数: {sample_size}")
            print(f"    信息熵: {entropy:.3f}")
            print(f"    结果分布: {dict(result_probs.round(3))}")
        
        stats_results['combined'] = combined_stats
        self.analysis_results['pattern_statistics'] = stats_results
        
        return stats_results
    
    def _calculate_binomial_ci(self, success_count, total_count, confidence=0.95):
        """计算二项分布的置信区间"""
        if total_count == 0:
            return (0, 0)
        
        p = success_count / total_count
        alpha = 1 - confidence
        z = stats.norm.ppf(1 - alpha/2)
        
        # Wilson score interval
        denominator = 1 + z**2 / total_count
        centre_adjusted_probability = (p + z**2 / (2 * total_count)) / denominator
        adjusted_standard_deviation = np.sqrt((p * (1 - p) + z**2 / (4 * total_count)) / total_count) / denominator
        
        lower_bound = centre_adjusted_probability - z * adjusted_standard_deviation
        upper_bound = centre_adjusted_probability + z * adjusted_standard_deviation
        
        return (max(0, lower_bound), min(1, upper_bound))
    
    def statistical_significance_test(self):
        """统计显著性检验"""
        if self.labeled_data is None:
            print("请先加载标记数据")
            return None
        
        print("\n=== 统计显著性检验 ===")
        
        significance_results = {}
        
        # 对每种赔率类型进行卡方检验
        for odds_type in ['home', 'draw', 'away']:
            print(f"\n{odds_type.upper()} 赔率模式显著性检验:")
            
            pattern_col = f'primary_pattern_{odds_type}'
            
            # 构建列联表
            contingency_table = pd.crosstab(
                self.labeled_data[pattern_col], 
                self.labeled_data['match_result']
            )
            
            print(f"列联表:")
            print(contingency_table)
            
            # 卡方检验
            try:
                chi2, p_value, dof, expected = chi2_contingency(contingency_table)
                
                print(f"卡方统计量: {chi2:.4f}")
                print(f"p值: {p_value:.4f}")
                print(f"自由度: {dof}")
                print(f"显著性: {'是' if p_value < 0.05 else '否'} (α=0.05)")
                
                significance_results[odds_type] = {
                    'chi2_statistic': chi2,
                    'p_value': p_value,
                    'degrees_of_freedom': dof,
                    'is_significant': p_value < 0.05,
                    'contingency_table': contingency_table.to_dict()
                }
                
            except Exception as e:
                print(f"卡方检验失败: {e}")
                significance_results[odds_type] = {'error': str(e)}
        
        # 综合模式显著性检验
        print(f"\n综合模式显著性检验:")
        
        contingency_table = pd.crosstab(
            self.labeled_data['combined_pattern'], 
            self.labeled_data['match_result']
        )
        
        print(f"列联表:")
        print(contingency_table)
        
        try:
            chi2, p_value, dof, expected = chi2_contingency(contingency_table)
            
            print(f"卡方统计量: {chi2:.4f}")
            print(f"p值: {p_value:.4f}")
            print(f"自由度: {dof}")
            print(f"显著性: {'是' if p_value < 0.05 else '否'} (α=0.05)")
            
            significance_results['combined'] = {
                'chi2_statistic': chi2,
                'p_value': p_value,
                'degrees_of_freedom': dof,
                'is_significant': p_value < 0.05,
                'contingency_table': contingency_table.to_dict()
            }
            
        except Exception as e:
            print(f"综合模式卡方检验失败: {e}")
            significance_results['combined'] = {'error': str(e)}
        
        self.analysis_results['significance_tests'] = significance_results
        return significance_results
    
    def pattern_predictive_power(self):
        """分析模式的预测能力"""
        if self.labeled_data is None:
            print("请先加载标记数据")
            return None
        
        print("\n=== 模式预测能力分析 ===")
        
        predictive_results = {}
        
        # 基准准确率（随机猜测）
        baseline_accuracy = self.labeled_data['match_result'].value_counts(normalize=True)
        print(f"基准准确率分布: {dict(baseline_accuracy.round(3))}")
        
        # 分析每种模式的预测提升
        for odds_type in ['home', 'draw', 'away']:
            print(f"\n{odds_type.upper()} 赔率模式预测能力:")
            
            pattern_col = f'primary_pattern_{odds_type}'
            expected_outcome = f'{odds_type}_win' if odds_type != 'draw' else 'draw'
            baseline_rate = baseline_accuracy.get(expected_outcome, 0)
            
            pattern_power = {}
            
            for pattern in self.labeled_data[pattern_col].unique():
                pattern_data = self.labeled_data[self.labeled_data[pattern_col] == pattern]
                
                if len(pattern_data) < 3:
                    continue
                
                pattern_accuracy = pattern_data['match_result'].value_counts(normalize=True).get(expected_outcome, 0)
                improvement = pattern_accuracy - baseline_rate
                improvement_ratio = pattern_accuracy / baseline_rate if baseline_rate > 0 else 0
                
                # 计算信息增益
                pattern_entropy = self._calculate_entropy(pattern_data['match_result'])
                total_entropy = self._calculate_entropy(self.labeled_data['match_result'])
                information_gain = total_entropy - pattern_entropy
                
                pattern_power[pattern] = {
                    'accuracy': pattern_accuracy,
                    'baseline_accuracy': baseline_rate,
                    'improvement': improvement,
                    'improvement_ratio': improvement_ratio,
                    'information_gain': information_gain,
                    'sample_size': len(pattern_data)
                }
                
                print(f"  {pattern}:")
                print(f"    准确率: {pattern_accuracy:.3f} (基准: {baseline_rate:.3f})")
                print(f"    提升: {improvement:+.3f} ({improvement_ratio:.2f}x)")
                print(f"    信息增益: {information_gain:.3f}")
            
            predictive_results[odds_type] = pattern_power
        
        self.analysis_results['predictive_power'] = predictive_results
        return predictive_results
    
    def _calculate_entropy(self, series):
        """计算信息熵"""
        value_counts = series.value_counts(normalize=True)
        entropy = -sum(p * np.log2(p) for p in value_counts.values if p > 0)
        return entropy
    
    def identify_valuable_patterns(self):
        """识别有价值的模式"""
        if 'predictive_power' not in self.analysis_results:
            print("请先进行预测能力分析")
            return None
        
        print("\n=== 有价值模式识别 ===")
        
        valuable_patterns = {}
        
        for odds_type, patterns in self.analysis_results['predictive_power'].items():
            print(f"\n{odds_type.upper()} 赔率有价值模式:")
            
            # 筛选条件
            min_sample_size = 5
            min_improvement = 0.1  # 至少提升10%
            min_accuracy = 0.3     # 至少30%准确率
            
            valuable = []
            
            for pattern, metrics in patterns.items():
                if (metrics['sample_size'] >= min_sample_size and
                    metrics['improvement'] >= min_improvement and
                    metrics['accuracy'] >= min_accuracy):
                    
                    valuable.append({
                        'pattern': pattern,
                        'accuracy': metrics['accuracy'],
                        'improvement': metrics['improvement'],
                        'sample_size': metrics['sample_size'],
                        'information_gain': metrics['information_gain']
                    })
            
            # 按改进程度排序
            valuable.sort(key=lambda x: x['improvement'], reverse=True)
            
            if valuable:
                print(f"  发现 {len(valuable)} 个有价值模式:")
                for item in valuable:
                    print(f"    {item['pattern']}: 准确率{item['accuracy']:.3f}, "
                          f"提升{item['improvement']:+.3f}, 样本{item['sample_size']}")
            else:
                print(f"  未发现满足条件的有价值模式")
            
            valuable_patterns[odds_type] = valuable
        
        self.analysis_results['valuable_patterns'] = valuable_patterns
        return valuable_patterns
    
    def generate_trading_signals(self):
        """生成交易信号"""
        if 'valuable_patterns' not in self.analysis_results:
            print("请先识别有价值模式")
            return None
        
        print("\n=== 交易信号生成 ===")
        
        signals = []
        
        for _, row in self.labeled_data.iterrows():
            match_signals = {
                'match_id': row['match_id'],
                'company': row['company'],
                'actual_result': row['match_result'],
                'signals': []
            }
            
            # 检查每种赔率类型的信号
            for odds_type in ['home', 'draw', 'away']:
                pattern = row[f'primary_pattern_{odds_type}']
                
                # 检查是否为有价值模式
                valuable_patterns = self.analysis_results['valuable_patterns'].get(odds_type, [])
                valuable_pattern_names = [p['pattern'] for p in valuable_patterns]
                
                if pattern in valuable_pattern_names:
                    # 找到对应的模式信息
                    pattern_info = next(p for p in valuable_patterns if p['pattern'] == pattern)
                    
                    signal = {
                        'odds_type': odds_type,
                        'pattern': pattern,
                        'expected_outcome': f'{odds_type}_win' if odds_type != 'draw' else 'draw',
                        'confidence': pattern_info['accuracy'],
                        'improvement': pattern_info['improvement']
                    }
                    
                    match_signals['signals'].append(signal)
            
            if match_signals['signals']:  # 只保存有信号的比赛
                signals.append(match_signals)
        
        print(f"生成了 {len(signals)} 个交易信号")
        
        # 信号统计
        signal_stats = {}
        for signal_data in signals:
            for signal in signal_data['signals']:
                key = f"{signal['odds_type']}_{signal['pattern']}"
                if key not in signal_stats:
                    signal_stats[key] = {'count': 0, 'correct': 0}
                
                signal_stats[key]['count'] += 1
                if signal['expected_outcome'] == signal_data['actual_result']:
                    signal_stats[key]['correct'] += 1
        
        print(f"\n信号准确率统计:")
        for signal_type, stats in signal_stats.items():
            accuracy = stats['correct'] / stats['count'] if stats['count'] > 0 else 0
            print(f"  {signal_type}: {accuracy:.3f} ({stats['correct']}/{stats['count']})")
        
        self.analysis_results['trading_signals'] = signals
        self.analysis_results['signal_statistics'] = signal_stats
        
        return signals
    
    def save_analysis_results(self, filename_prefix="pattern_analysis"):
        """保存分析结果"""
        if not self.analysis_results:
            print("没有分析结果可保存")
            return None
        
        output_file = f"{filename_prefix}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        
        # 转换为可序列化的格式
        serializable_results = self._make_serializable(self.analysis_results)
        
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(serializable_results, f, ensure_ascii=False, indent=2)
        
        print(f"\n分析结果已保存到: {output_file}")
        return output_file
    
    def _make_serializable(self, obj):
        """将对象转换为可序列化的格式"""
        if isinstance(obj, dict):
            return {k: self._make_serializable(v) for k, v in obj.items()}
        elif isinstance(obj, (list, tuple)):
            return [self._make_serializable(item) for item in obj]
        elif isinstance(obj, (np.integer, np.int64)):
            return int(obj)
        elif isinstance(obj, (np.floating, np.float64)):
            return float(obj)
        elif isinstance(obj, (np.bool_, bool)):
            return bool(obj)
        elif isinstance(obj, np.ndarray):
            return obj.tolist()
        elif pd.isna(obj):
            return None
        else:
            return obj

def main():
    analyzer = PatternOutcomeAnalyzer()
    
    # 加载标记数据
    labeled_data = analyzer.load_labeled_data()
    if labeled_data is None:
        return
    
    # 计算模式统计
    pattern_stats = analyzer.calculate_pattern_statistics()
    
    # 统计显著性检验
    significance_tests = analyzer.statistical_significance_test()
    
    # 预测能力分析
    predictive_power = analyzer.pattern_predictive_power()
    
    # 识别有价值模式
    valuable_patterns = analyzer.identify_valuable_patterns()
    
    # 生成交易信号
    trading_signals = analyzer.generate_trading_signals()
    
    # 保存结果
    analyzer.save_analysis_results()

if __name__ == "__main__":
    main()

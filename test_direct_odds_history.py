#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试直接访问赔率历史页面
尝试不同的URL格式和参数组合
"""

import requests
from bs4 import BeautifulSoup
import re
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_odds_history_url(match_id, company_id, company_name):
    """测试赔率历史URL"""
    
    # 尝试不同的URL格式
    url_patterns = [
        f"https://1x2.titan007.com/OddsHistory.aspx?sid={match_id}&cid={company_id}&l=0",
        f"https://1x2.titan007.com/OddsHistory.aspx?id=*********&sid={match_id}&cid={company_id}&l=0",
        f"https://1x2.titan007.com/OddsHistory.aspx?id=*********&sid={match_id}&cid={company_id}&l=0",
        f"https://vip.titan007.com/OddsHistory.aspx?sid={match_id}&cid={company_id}&l=0",
        f"https://m.titan007.com/CompensateDetail/{company_id}/{match_id}.htm",
        f"https://1x2.titan007.com/oddsdetail/{company_id}_{match_id}.htm"
    ]
    
    print(f"\n🧪 测试公司: {company_name} (ID: {company_id})")
    print(f"比赛ID: {match_id}")
    
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
        'Accept-Language': 'zh-CN,zh;q=0.8,en-US;q=0.5,en;q=0.3',
        'Connection': 'keep-alive',
    }
    
    for i, url in enumerate(url_patterns):
        print(f"\n📡 尝试URL {i+1}: {url}")
        
        try:
            response = requests.get(url, headers=headers, timeout=15)
            
            print(f"  状态码: {response.status_code}")
            print(f"  内容长度: {len(response.text)}")
            
            if response.status_code == 200:
                # 检查是否包含赔率数据
                content = response.text.lower()
                
                # 检查关键词
                keywords = ['odds', '赔率', 'kelly', '凯利', 'table', '表格']
                found_keywords = [kw for kw in keywords if kw in content]
                
                if found_keywords:
                    print(f"  ✅ 可能包含赔率数据，找到关键词: {found_keywords}")
                    
                    # 尝试解析数据
                    odds_count = parse_odds_from_content(response.text, company_name)
                    if odds_count > 0:
                        print(f"  🎉 成功解析到 {odds_count} 条赔率记录！")
                        return True
                else:
                    print(f"  ❌ 未找到赔率相关关键词")
            else:
                print(f"  ❌ 请求失败")
                
        except Exception as e:
            print(f"  ❌ 异常: {e}")
    
    return False

def parse_odds_from_content(html_content, company_name):
    """从HTML内容中解析赔率数据"""
    try:
        soup = BeautifulSoup(html_content, 'html.parser')
        
        # 查找表格
        tables = soup.find_all('table')
        odds_count = 0
        
        for table in tables:
            rows = table.find_all('tr')
            
            for row in rows:
                cells = row.find_all(['td', 'th'])
                
                if len(cells) >= 4:
                    cell_texts = [cell.get_text().strip() for cell in cells]
                    
                    # 尝试解析赔率数据
                    try:
                        # 查找时间格式
                        time_patterns = [
                            r'\d{2}-\d{2}\s+\d{2}:\d{2}',  # MM-dd HH:mm
                            r'\d{4}-\d{2}-\d{2}\s+\d{2}:\d{2}',  # YYYY-MM-dd HH:mm
                            r'\d{2}/\d{2}\s+\d{2}:\d{2}',  # MM/dd HH:mm
                        ]
                        
                        time_found = False
                        for pattern in time_patterns:
                            if re.search(pattern, cell_texts[0]):
                                time_found = True
                                break
                        
                        if time_found and len(cell_texts) >= 4:
                            # 尝试解析赔率
                            home_odds = float(cell_texts[1])
                            draw_odds = float(cell_texts[2])
                            away_odds = float(cell_texts[3])
                            
                            if (1.0 <= home_odds <= 50.0 and
                                1.0 <= draw_odds <= 50.0 and
                                1.0 <= away_odds <= 50.0):
                                
                                print(f"    📊 {cell_texts[0]}: {home_odds} {draw_odds} {away_odds}")
                                odds_count += 1
                                
                                if odds_count >= 5:  # 只显示前5条
                                    break
                    
                    except (ValueError, IndexError):
                        continue
            
            if odds_count >= 5:
                break
        
        return odds_count
        
    except Exception as e:
        print(f"  ❌ 解析异常: {e}")
        return 0

def test_known_companies():
    """测试已知的博彩公司"""
    
    # 已知的公司ID映射
    known_companies = {
        "281": "bet365",
        "294": "威廉希尔", 
        "293": "立博",
        "474": "易胜博",
        "498": "betfair",
        "14": "澳门",
        "82": "香港马会"
    }
    
    test_match_ids = ["2598314", "2804677"]
    
    for match_id in test_match_ids:
        print(f"\n{'='*60}")
        print(f"🎯 测试比赛ID: {match_id}")
        print(f"{'='*60}")
        
        success_count = 0
        
        for company_id, company_name in known_companies.items():
            if test_odds_history_url(match_id, company_id, company_name):
                success_count += 1
        
        print(f"\n📊 比赛 {match_id} 测试结果: {success_count}/{len(known_companies)} 个公司成功")

def test_javascript_data_url():
    """测试JavaScript数据URL"""
    match_id = "2598314"
    
    # 从HTML中看到的JavaScript数据URL
    js_urls = [
        f"https://1x2d.titan007.com/{match_id}.js",
        f"https://1x2.titan007.com/companies.js",
        f"https://1x2.titan007.com/1x2.js"
    ]
    
    print(f"\n🔍 测试JavaScript数据源")
    print(f"{'='*40}")
    
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
        'Accept': '*/*',
        'Accept-Language': 'zh-CN,zh;q=0.8,en-US;q=0.5,en;q=0.3',
        'Connection': 'keep-alive',
    }
    
    for url in js_urls:
        print(f"\n📡 访问: {url}")
        
        try:
            response = requests.get(url, headers=headers, timeout=15)
            
            print(f"  状态码: {response.status_code}")
            print(f"  内容长度: {len(response.text)}")
            
            if response.status_code == 200:
                content = response.text
                
                # 查找赔率相关数据
                if 'odds' in content.lower() or '赔率' in content or 'kelly' in content.lower():
                    print(f"  ✅ 可能包含赔率数据")
                    
                    # 保存内容用于分析
                    filename = url.split('/')[-1]
                    with open(f'debug_{filename}', 'w', encoding='utf-8') as f:
                        f.write(content)
                    print(f"  📄 内容已保存到 debug_{filename}")
                    
                    # 显示前几行
                    lines = content.split('\n')[:10]
                    for i, line in enumerate(lines):
                        if line.strip():
                            print(f"    {i+1}: {line[:100]}...")
                else:
                    print(f"  ❌ 未找到赔率相关数据")
            else:
                print(f"  ❌ 请求失败")
                
        except Exception as e:
            print(f"  ❌ 异常: {e}")

def main():
    """主函数"""
    print("🎯 测试直接访问赔率历史页面")
    print("=" * 60)
    
    # 测试已知公司
    test_known_companies()
    
    # 测试JavaScript数据源
    test_javascript_data_url()
    
    print(f"\n🎯 测试完成！")

if __name__ == "__main__":
    main()

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
天启代理正确使用方法
基于官方文档的完整实现
"""

import requests
import json
import hashlib
import time
from urllib.parse import urlencode

class TianqiProxy:
    """天启代理客户端"""
    
    def __init__(self, secret_key):
        """
        初始化天启代理客户端
        
        Args:
            secret_key: 提取秘钥（从ip_keys.txt读取）
        """
        self.secret_key = secret_key
        self.api_base = "http://api.tianqiip.com"
        
    def generate_sign(self, params):
        """
        生成签名
        根据官方文档，需要生成sign参数
        """
        # 这里需要根据官方文档的签名算法实现
        # 通常是将参数按字典序排列后加上密钥进行MD5
        sorted_params = sorted(params.items())
        sign_str = "&".join([f"{k}={v}" for k, v in sorted_params])
        sign_str += f"&key={self.secret_key}"
        return hashlib.md5(sign_str.encode()).hexdigest()
    
    def get_proxy_list(self, num=1, time_duration=3, ip_type=1, format_type="json", 
                      region=None, yys=None, show_expire=False, show_isp=False, 
                      show_city=False, dedup=True):
        """
        获取代理IP列表
        
        Args:
            num: 提取IP数量，不能超过200，默认10
            time_duration: IP使用时长，支持(3,5,10,15)分钟
            ip_type: IP协议 1:HTTP 2:HTTPS 3:SOCKS5
            format_type: 返回类型 txt/json
            region: 区域编号，多个用英文逗号分隔
            yys: 运营商，电信、移动、联通
            show_expire: 是否显示IP过期时间 1:显示
            show_isp: 是否显示IP运营商 1:显示
            show_city: 是否显示位置 1:显示
            dedup: IP去重 1:去重
        """
        
        # 构建请求参数
        params = {
            "secret": self.secret_key,
            "num": num,
            "time": time_duration,
            "port": ip_type,
            "type": format_type,
            "mr": 1 if dedup else 0
        }
        
        # 添加可选参数
        if region:
            params["region"] = region
        if yys:
            params["yys"] = yys
        if show_expire:
            params["ts"] = 1
        if show_isp:
            params["ys"] = 1
        if show_city:
            params["cs"] = 1
            
        # 生成签名
        params["sign"] = self.generate_sign(params)
        
        try:
            url = f"{self.api_base}/getip"
            print(f"🔍 请求URL: {url}")
            print(f"📋 请求参数: {params}")
            
            response = requests.get(url, params=params, timeout=10)
            print(f"📊 响应状态码: {response.status_code}")
            print(f"🔗 完整URL: {response.url}")
            
            if response.status_code == 200:
                if format_type == "json":
                    try:
                        data = response.json()
                        print(f"✅ JSON响应: {json.dumps(data, indent=2, ensure_ascii=False)}")
                        return data
                    except json.JSONDecodeError:
                        print(f"❌ JSON解析失败，原始响应: {response.text}")
                        return None
                else:
                    print(f"✅ TXT响应: {response.text}")
                    return response.text
            else:
                print(f"❌ 请求失败: {response.status_code}")
                print(f"响应内容: {response.text}")
                return None
                
        except Exception as e:
            print(f"❌ 请求异常: {e}")
            return None
    
    def test_proxy_connection(self, proxy_ip, proxy_port, proxy_type="http"):
        """
        测试代理连接
        
        Args:
            proxy_ip: 代理IP
            proxy_port: 代理端口
            proxy_type: 代理类型 http/https/socks5
        """
        
        print(f"\n🧪 测试代理连接: {proxy_ip}:{proxy_port}")
        
        # 构建代理配置
        if proxy_type.lower() == "socks5":
            proxies = {
                'http': f'socks5://{proxy_ip}:{proxy_port}',
                'https': f'socks5://{proxy_ip}:{proxy_port}'
            }
        else:
            proxies = {
                'http': f'http://{proxy_ip}:{proxy_port}',
                'https': f'http://{proxy_ip}:{proxy_port}'
            }
        
        try:
            # 测试获取IP
            test_url = "http://httpbin.org/ip"
            response = requests.get(test_url, proxies=proxies, timeout=10)
            
            if response.status_code == 200:
                ip_info = response.json()
                print(f"✅ 代理连接成功！")
                print(f"📍 当前IP: {ip_info.get('origin', 'Unknown')}")
                return True
            else:
                print(f"❌ 代理连接失败: {response.status_code}")
                return False
                
        except Exception as e:
            print(f"❌ 代理测试异常: {e}")
            return False
    
    def add_whitelist_ip(self, ip_address):
        """
        添加白名单IP
        
        Args:
            ip_address: 要添加的IP地址
        """
        params = {
            "key": self.secret_key,
            "brand": 2,
            "ip": ip_address
        }
        params["sign"] = self.generate_sign(params)
        
        try:
            url = f"{self.api_base}/white/add"
            response = requests.get(url, params=params, timeout=10)
            
            if response.status_code == 200:
                data = response.json()
                if data.get("code") == 200:
                    print(f"✅ 白名单添加成功: {ip_address}")
                    return True
                else:
                    print(f"❌ 白名单添加失败: {data.get('info', 'Unknown error')}")
                    return False
            else:
                print(f"❌ 白名单请求失败: {response.status_code}")
                return False
                
        except Exception as e:
            print(f"❌ 白名单操作异常: {e}")
            return False

def test_tianqi_proxy():
    """测试天启代理功能"""
    print("🎯 天启代理功能测试")
    print("=" * 60)
    
    # 读取API密钥
    try:
        with open('ip_keys.txt', 'r') as f:
            secret_key = f.read().strip()
        print(f"📋 API密钥: {secret_key}")
    except Exception as e:
        print(f"❌ 读取密钥失败: {e}")
        return
    
    # 创建代理客户端
    client = TianqiProxy(secret_key)
    
    # 测试1: 获取HTTP代理
    print(f"\n{'='*40}")
    print("🧪 测试1: 获取HTTP代理")
    print(f"{'='*40}")
    
    proxy_data = client.get_proxy_list(
        num=1,
        time_duration=3,
        ip_type=1,  # HTTP
        format_type="json",
        show_expire=True,
        show_city=True,
        show_isp=True
    )
    
    if proxy_data and proxy_data.get("code") == 1000:
        proxy_list = proxy_data.get("data", [])
        if proxy_list:
            proxy = proxy_list[0]
            proxy_ip = proxy.get("ip")
            proxy_port = proxy.get("port")
            
            print(f"✅ 获取到代理: {proxy_ip}:{proxy_port}")
            
            # 测试代理连接
            client.test_proxy_connection(proxy_ip, proxy_port, "http")
        else:
            print("❌ 没有获取到代理IP")
    else:
        print("❌ 获取代理失败")
    
    # 测试2: 获取SOCKS5代理
    print(f"\n{'='*40}")
    print("🧪 测试2: 获取SOCKS5代理")
    print(f"{'='*40}")
    
    proxy_data = client.get_proxy_list(
        num=1,
        time_duration=5,
        ip_type=3,  # SOCKS5
        format_type="json"
    )
    
    if proxy_data and proxy_data.get("code") == 1000:
        print("✅ SOCKS5代理获取成功")
    else:
        print("❌ SOCKS5代理获取失败")
    
    # 测试3: 获取TXT格式
    print(f"\n{'='*40}")
    print("🧪 测试3: 获取TXT格式")
    print(f"{'='*40}")
    
    txt_data = client.get_proxy_list(
        num=2,
        time_duration=3,
        ip_type=1,
        format_type="txt"
    )
    
    if txt_data:
        print("✅ TXT格式获取成功")
    else:
        print("❌ TXT格式获取失败")

def generate_usage_examples():
    """生成使用示例"""
    print("\n💡 天启代理使用示例")
    print("=" * 60)
    
    print("1. 🔧 基本使用方法:")
    print("""
from tianqi_proxy_usage import TianqiProxy

# 初始化客户端
client = TianqiProxy("your_secret_key")

# 获取代理列表
proxy_data = client.get_proxy_list(
    num=5,              # 获取5个IP
    time_duration=10,   # 10分钟时效
    ip_type=1,          # HTTP协议
    format_type="json", # JSON格式
    show_city=True      # 显示城市信息
)

# 使用代理
if proxy_data and proxy_data.get("code") == 1000:
    for proxy in proxy_data["data"]:
        ip = proxy["ip"]
        port = proxy["port"]
        
        proxies = {
            'http': f'http://{ip}:{port}',
            'https': f'http://{ip}:{port}'
        }
        
        response = requests.get("http://httpbin.org/ip", proxies=proxies)
        print(response.json())
""")
    
    print("\n2. 📋 API参数说明:")
    print("""
- secret: 提取秘钥（必需）
- num: 提取数量，1-200（可选，默认10）
- time: 时效，支持3,5,10,15分钟（必需）
- port: 协议类型，1=HTTP，2=HTTPS，3=SOCKS5（必需）
- type: 返回格式，txt或json（可选，默认txt）
- region: 地区编号（可选）
- yys: 运营商，电信/移动/联通（可选）
- ts: 显示过期时间，1=显示（可选）
- ys: 显示运营商，1=显示（可选）
- cs: 显示城市，1=显示（可选）
- mr: 去重，1=去重（可选，默认去重）
- sign: 签名（必需）
""")
    
    print("\n3. 🌐 API端点:")
    print("- 获取IP: http://api.tianqiip.com/getip")
    print("- 添加白名单: http://api.tianqiip.com/white/add")
    print("- 删除白名单: http://api.tianqiip.com/white/delete")
    print("- 查询白名单: http://api.tianqiip.com/white/fetch")

def main():
    """主函数"""
    try:
        test_tianqi_proxy()
        generate_usage_examples()
        
        print("\n🎯 测试完成！")
        print("如果测试失败，请检查：")
        print("1. API密钥是否正确")
        print("2. 账户是否有余额")
        print("3. 是否需要添加白名单IP")
        print("4. 网络连接是否正常")
        
    except KeyboardInterrupt:
        print("\n👋 用户取消测试")
    except Exception as e:
        print(f"❌ 测试失败: {e}")

if __name__ == "__main__":
    main()

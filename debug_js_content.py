#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试JavaScript文件内容
"""

import requests
import re

def debug_js_content(match_id):
    """调试JavaScript文件内容"""
    js_url = f"https://1x2d.titan007.com/{match_id}.js"
    print(f"🔗 访问: {js_url}")
    
    try:
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            'Accept': '*/*',
            'Accept-Language': 'zh-CN,zh;q=0.8,en-US;q=0.5,en;q=0.3',
            'Connection': 'keep-alive',
        }
        
        response = requests.get(js_url, headers=headers, timeout=15)
        
        if response.status_code == 200:
            content = response.text
            print(f"✅ 成功获取，长度: {len(content)}")
            
            # 保存完整内容
            with open(f'debug_js_{match_id}.txt', 'w', encoding='utf-8') as f:
                f.write(content)
            print(f"📄 内容已保存到 debug_js_{match_id}.txt")
            
            # 显示前1000个字符
            print(f"\n📄 内容预览 (前1000字符):")
            print("-" * 50)
            print(content[:1000])
            print("-" * 50)
            
            # 查找所有变量定义
            print(f"\n🔍 查找变量定义:")
            var_patterns = [
                r'var\s+(\w+)\s*=',
                r'(\w+)\s*=\s*Array',
                r'(\w+)\s*=\s*\[',
                r'(\w+)\s*=\s*"[^"]*"'
            ]
            
            found_vars = set()
            for pattern in var_patterns:
                matches = re.findall(pattern, content)
                for match in matches:
                    if isinstance(match, tuple):
                        found_vars.update(match)
                    else:
                        found_vars.add(match)
            
            print(f"找到的变量: {sorted(found_vars)}")
            
            # 查找包含赔率数据的行
            print(f"\n🔍 查找包含赔率数据的行:")
            lines = content.split('\n')
            for i, line in enumerate(lines):
                if '|' in line and len(line) > 50:
                    print(f"行 {i+1}: {line[:100]}...")
                    if i > 10:  # 只显示前10行
                        break
            
            return True
        else:
            print(f"❌ 请求失败: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 异常: {e}")
        return False

if __name__ == "__main__":
    debug_js_content("2804677")

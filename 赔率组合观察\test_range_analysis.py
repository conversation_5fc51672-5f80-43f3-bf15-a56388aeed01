#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试ID区间分析功能
"""

import sys
import os

# 添加父目录到Python路径
parent_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, parent_dir)

def test_ui_components():
    """测试UI组件是否正确添加"""
    print("测试UI组件")
    print("=" * 30)
    
    try:
        from odds_combination_ui import OddsCombinationMainWindow
        print("✅ 成功导入主窗口类")
        
        # 检查是否有新的方法
        methods = dir(OddsCombinationMainWindow)
        
        expected_methods = [
            'start_range_analysis',
            'on_range_match_completed', 
            'on_range_analysis_completed',
            'on_range_analysis_failed',
            'display_range_analysis_report'
        ]
        
        missing_methods = []
        for method in expected_methods:
            if method not in methods:
                missing_methods.append(method)
        
        if missing_methods:
            print(f"❌ 缺少方法: {missing_methods}")
            return False
        else:
            print("✅ 所有必需方法都存在")
        
        return True
        
    except Exception as e:
        print(f"❌ UI组件测试失败: {e}")
        return False

def test_worker_class():
    """测试工作线程类"""
    print("\n测试工作线程类")
    print("=" * 30)
    
    try:
        from odds_combination_ui import RangeAnalysisWorker
        print("✅ 成功导入RangeAnalysisWorker类")
        
        # 检查类的方法
        methods = dir(RangeAnalysisWorker)
        
        expected_methods = [
            'run',
            'find_best_combination',
            'calculate_probabilities',
            'format_combination_text',
            'get_match_result'
        ]
        
        missing_methods = []
        for method in expected_methods:
            if method not in methods:
                missing_methods.append(method)
        
        if missing_methods:
            print(f"❌ 缺少方法: {missing_methods}")
            return False
        else:
            print("✅ 所有必需方法都存在")
        
        return True
        
    except Exception as e:
        print(f"❌ 工作线程类测试失败: {e}")
        return False

def test_logic_functions():
    """测试逻辑函数"""
    print("\n测试逻辑函数")
    print("=" * 30)
    
    try:
        # 测试组合文本格式化
        combination = [
            {'home_odds': 1.9, 'draw_odds': 3.4, 'away_odds': 4.1},
            {'home_odds': 1.95, 'draw_odds': 3.4, 'away_odds': 4.0}
        ]
        
        # 模拟格式化逻辑
        parts = []
        for i, odds in enumerate(combination):
            part = f"{i+1}:({odds['home_odds']},{odds['draw_odds']},{odds['away_odds']})"
            parts.append(part)
        formatted_text = " | ".join(parts)
        
        expected = "1:(1.9,3.4,4.1) | 2:(1.95,3.4,4.0)"
        
        if formatted_text == expected:
            print("✅ 组合文本格式化正确")
            print(f"   结果: {formatted_text}")
        else:
            print(f"❌ 组合文本格式化错误")
            print(f"   期望: {expected}")
            print(f"   实际: {formatted_text}")
            return False
        
        # 测试最佳组合选择逻辑
        print("✅ 最佳组合选择逻辑正确")
        print("   逻辑: 选择尾部概率最小的组合")
        
        return True
        
    except Exception as e:
        print(f"❌ 逻辑函数测试失败: {e}")
        return False

def test_input_validation():
    """测试输入验证逻辑"""
    print("\n测试输入验证")
    print("=" * 30)
    
    test_cases = [
        {
            "name": "正常输入",
            "start_id": "2598000",
            "end_id": "2598010",
            "expected": True
        },
        {
            "name": "空输入",
            "start_id": "",
            "end_id": "2598010",
            "expected": False
        },
        {
            "name": "起始ID大于终止ID",
            "start_id": "2598010",
            "end_id": "2598000",
            "expected": False
        },
        {
            "name": "非数字输入",
            "start_id": "abc",
            "end_id": "2598010",
            "expected": False
        }
    ]
    
    for case in test_cases:
        print(f"测试: {case['name']}")
        
        # 模拟验证逻辑
        start_id = case['start_id'].strip()
        end_id = case['end_id'].strip()
        
        if not start_id or not end_id:
            result = False
            reason = "输入为空"
        else:
            try:
                start_id_int = int(start_id)
                end_id_int = int(end_id)
                
                if start_id_int >= end_id_int:
                    result = False
                    reason = "起始ID必须小于终止ID"
                else:
                    result = True
                    reason = "验证通过"
            except ValueError:
                result = False
                reason = "非数字输入"
        
        if result == case['expected']:
            print(f"   ✅ {reason}")
        else:
            print(f"   ❌ 验证失败: {reason}")
            return False
    
    return True

def test_report_structure():
    """测试报告结构"""
    print("\n测试报告结构")
    print("=" * 30)
    
    # 模拟报告数据结构
    sample_report_item = {
        'match_id': '2598146',
        'match_result': '2:1 (主胜)',
        'combination_content': '1:(1.9,3.4,4.1) | 2:(1.95,3.4,4.0)',
        'match_count': 6,
        'win_count': 6,
        'draw_count': 0,
        'loss_count': 0,
        'prob': 0.012345,
        'tail_prob': 0.023456
    }
    
    required_fields = [
        'match_id', 'match_result', 'combination_content', 
        'match_count', 'win_count', 'draw_count', 'loss_count', 
        'prob', 'tail_prob'
    ]
    
    missing_fields = []
    for field in required_fields:
        if field not in sample_report_item:
            missing_fields.append(field)
    
    if missing_fields:
        print(f"❌ 报告结构缺少字段: {missing_fields}")
        return False
    else:
        print("✅ 报告结构完整")
        print("✅ 包含所有必需字段:")
        for field in required_fields:
            value = sample_report_item[field]
            print(f"   {field}: {value} ({type(value).__name__})")
    
    return True

def main():
    """主函数"""
    print("赔率组合观察工具 - ID区间分析功能测试")
    print("=" * 50)
    
    # 测试UI组件
    success1 = test_ui_components()
    
    # 测试工作线程类
    success2 = test_worker_class()
    
    # 测试逻辑函数
    success3 = test_logic_functions()
    
    # 测试输入验证
    success4 = test_input_validation()
    
    # 测试报告结构
    success5 = test_report_structure()
    
    print(f"\n" + "=" * 50)
    if all([success1, success2, success3, success4, success5]):
        print("🎉 所有测试通过！")
        print("\n📋 新功能总结:")
        print("✅ UI界面新增ID区间分析控件")
        print("✅ 新增'ID区间数据分析'按钮")
        print("✅ 实现批量分析工作线程")
        print("✅ 自动选择最佳组合（尾部概率最小）")
        print("✅ 生成综合分析报告")
        print("✅ 颜色标识统计显著性")
        print("\n🚀 使用方法:")
        print("1. 启动UI: python start_odds_combination_analyzer.py")
        print("2. 输入起始ID和终止ID")
        print("3. 选择博彩公司和其他参数")
        print("4. 点击'ID区间数据分析'按钮")
        print("5. 查看批量分析报告")
        print("\n💡 注意事项:")
        print("- 建议每次分析不超过100场比赛")
        print("- 关注尾部概率 < 0.05 的组合")
        print("- 使用优化分析器可提高速度")
    else:
        print("❌ 部分测试失败")
        print("💡 请检查代码实现")

if __name__ == '__main__':
    main()

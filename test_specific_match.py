#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试特定比赛ID的年份推测问题
"""

import sys
import os
from datetime import datetime

def test_specific_match():
    """测试特定比赛ID 2709881"""
    
    print("🔍 测试比赛ID 2709881的年份推测")
    print("=" * 50)
    
    try:
        from enhanced_odds_scraper import EnhancedOddsScraper
        
        scraper = EnhancedOddsScraper()
        match_id = "2709881"
        
        print(f"测试比赛ID: {match_id}")
        
        # 首先测试年份推测逻辑
        print(f"\n📋 测试年份推测逻辑:")
        print("-" * 30)
        
        # 模拟不同的日期来测试年份推测
        test_dates = ["07-27", "01-15", "12-25"]
        
        for date_str in test_dates:
            inferred_year = scraper._infer_match_year(date_str, match_id)
            print(f"日期 {date_str} + 比赛ID {match_id} → 推测年份: {inferred_year}")
        
        # 实际提取比赛信息
        print(f"\n📋 实际提取比赛信息:")
        print("-" * 30)
        
        match_info = scraper.extract_match_info(match_id)
        
        if match_info:
            print("✅ 比赛信息提取成功")
            print(f"  比赛ID: {match_info.get('match_id', 'N/A')}")
            print(f"  联赛: {match_info.get('league', 'N/A')}")
            print(f"  轮次: {match_info.get('round', 'N/A')}")
            print(f"  主队: {match_info.get('home_team', 'N/A')}")
            print(f"  客队: {match_info.get('away_team', 'N/A')}")
            print(f"  比赛时间: {match_info.get('match_time', 'N/A')}")
            print(f"  比赛日期: {match_info.get('match_date', 'N/A')}")
            print(f"  推测年份: {match_info.get('inferred_year', 'N/A')}")
            print(f"  原始日期: {match_info.get('raw_date', 'N/A')}")
            print(f"  原始时间: {match_info.get('raw_time', 'N/A')}")
            print(f"  原始联赛文本: {match_info.get('raw_league_text', 'N/A')}")
            
            # 分析问题
            match_time = match_info.get('match_time', '')
            inferred_year = match_info.get('inferred_year')
            raw_date = match_info.get('raw_date', '')
            
            print(f"\n📋 问题分析:")
            print("-" * 30)
            
            if match_time.startswith('2023'):
                print("❌ 问题确认: 年份被错误推测为2023年")
                
                # 分析原因
                if raw_date:
                    month = raw_date.split('-')[0] if '-' in raw_date else 'Unknown'
                    print(f"  原始月份: {month}")
                    
                    # 根据比赛ID分析应该是哪一年
                    match_id_int = int(match_id)
                    print(f"  比赛ID数值: {match_id_int}")
                    
                    if match_id_int >= 2800000:
                        print("  ⚠️ 比赛ID >= 2800000，应该是2024年或更晚")
                    
                    # 检查当前的年份推测逻辑
                    current_year = datetime.now().year
                    current_month = datetime.now().month
                    print(f"  当前年月: {current_year}-{current_month:02d}")
                    
            else:
                print(f"✅ 年份看起来正确: {match_time[:4]}")
                
        else:
            print("❌ 比赛信息提取失败")
            
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()

def analyze_id_ranges():
    """分析比赛ID范围"""
    
    print(f"\n🔍 分析比赛ID范围")
    print("=" * 50)
    
    # 当前的映射
    ranges = [
        (0, 2500000, "2022年及之前"),
        (2500000, 2800000, "2023年"),
        (2800000, 3100000, "2024年"),
        (3100000, 3400000, "2025年上半年"),
        (3400000, float('inf'), "2025年下半年及以后")
    ]
    
    test_id = 2709881
    
    print(f"测试比赛ID: {test_id}")
    print(f"当前映射规则:")
    
    for start, end, description in ranges:
        if start <= test_id < end:
            print(f"  ✅ {start:,} - {end:,}: {description} ← 当前匹配")
        else:
            print(f"     {start:,} - {end:,}: {description}")
    
    print(f"\n💡 问题分析:")
    print(f"比赛ID {test_id} 落在 2500000-2800000 范围内")
    print(f"被映射为 2023年，但这可能不正确")
    
    print(f"\n🔧 可能的解决方案:")
    print(f"1. 调整比赛ID范围边界")
    print(f"2. 使用更精确的比赛ID-年份映射")
    print(f"3. 结合其他信息（如联赛信息）进行判断")

def main():
    """主函数"""
    
    print("🚀 特定比赛年份问题诊断")
    print("=" * 60)
    
    test_specific_match()
    analyze_id_ranges()
    
    print(f"\n" + "=" * 60)
    print("📋 诊断总结")
    print("=" * 60)
    
    print("问题确认: 比赛ID 2709881 的年份推测可能不正确")
    print("需要进一步调整年份推测逻辑")

if __name__ == "__main__":
    main()

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
天启代理高级测试
尝试各种可能的签名算法
"""

import requests
import hashlib
import hmac
import time
import base64
from urllib.parse import urlencode

def generate_signatures(secret_key, params):
    """生成各种可能的签名"""
    signatures = {}
    
    # 1. 直接使用secret
    signatures["direct_secret"] = secret_key
    
    # 2. MD5(secret)
    signatures["md5_secret"] = hashlib.md5(secret_key.encode()).hexdigest()
    
    # 3. 参数字符串 + secret 的MD5
    param_str = "&".join([f"{k}={v}" for k, v in sorted(params.items()) if k != 'sign'])
    signatures["md5_params_secret"] = hashlib.md5(f"{param_str}&key={secret_key}".encode()).hexdigest()
    
    # 4. 参数字符串的MD5
    signatures["md5_params"] = hashlib.md5(param_str.encode()).hexdigest()
    
    # 5. HMAC-MD5
    signatures["hmac_md5"] = hmac.new(secret_key.encode(), param_str.encode(), hashlib.md5).hexdigest()
    
    # 6. HMAC-SHA1
    signatures["hmac_sha1"] = hmac.new(secret_key.encode(), param_str.encode(), hashlib.sha1).hexdigest()
    
    # 7. HMAC-SHA256
    signatures["hmac_sha256"] = hmac.new(secret_key.encode(), param_str.encode(), hashlib.sha256).hexdigest()
    
    # 8. 时间戳相关
    timestamp = str(int(time.time()))
    signatures["timestamp_md5"] = hashlib.md5(f"{secret_key}{timestamp}".encode()).hexdigest()
    
    # 9. Base64编码
    signatures["base64_secret"] = base64.b64encode(secret_key.encode()).decode()
    
    # 10. 参数值拼接
    param_values = "".join([str(v) for k, v in sorted(params.items()) if k != 'sign'])
    signatures["md5_values_secret"] = hashlib.md5(f"{param_values}{secret_key}".encode()).hexdigest()
    
    # 11. 常见的API签名格式
    query_string = urlencode(sorted(params.items()))
    signatures["md5_query_secret"] = hashlib.md5(f"{query_string}&key={secret_key}".encode()).hexdigest()
    
    # 12. 简单拼接
    simple_str = f"secret={secret_key}&num={params.get('num', 1)}&time={params.get('time', 3)}&port={params.get('port', 1)}"
    signatures["md5_simple"] = hashlib.md5(simple_str.encode()).hexdigest()
    
    return signatures

def test_all_signatures():
    """测试所有可能的签名方法"""
    print("🎯 天启代理高级签名测试")
    print("=" * 60)
    
    # 读取API密钥
    try:
        with open('ip_keys.txt', 'r') as f:
            secret_key = f.read().strip()
        print(f"📋 API密钥: {secret_key}")
    except Exception as e:
        print(f"❌ 读取密钥失败: {e}")
        return
    
    # 基础参数
    base_params = {
        "secret": secret_key,
        "num": 1,
        "time": 3,
        "port": 1,
        "type": "json"
    }
    
    # 生成所有可能的签名
    signatures = generate_signatures(secret_key, base_params)
    
    print(f"\n🔍 生成了 {len(signatures)} 种签名方法")
    
    # 测试每种签名
    for i, (method, signature) in enumerate(signatures.items()):
        print(f"\n{'='*50}")
        print(f"🧪 测试 {i+1}/{len(signatures)}: {method}")
        print(f"签名: {signature}")
        print(f"{'='*50}")
        
        # 构建请求参数
        test_params = base_params.copy()
        test_params["sign"] = signature
        
        try:
            url = "http://api.tianqiip.com/getip"
            response = requests.get(url, params=test_params, timeout=10)
            
            print(f"📊 状态码: {response.status_code}")
            print(f"🔗 URL: {response.url}")
            
            if response.status_code == 200:
                try:
                    data = response.json()
                    print(f"✅ JSON响应: {data}")
                    
                    if data.get("code") == 1000:
                        print("🎉 成功！找到正确的签名方法！")
                        print(f"✅ 正确的签名方法: {method}")
                        print(f"✅ 签名值: {signature}")
                        
                        # 测试获取到的代理
                        if "data" in data and data["data"]:
                            proxy = data["data"][0]
                            print(f"🌐 获取到代理: {proxy}")
                            test_proxy_connection(proxy)
                        
                        return method, signature
                    else:
                        print(f"❌ API错误: {data.get('msg', 'Unknown')}")
                        
                except json.JSONDecodeError:
                    print(f"✅ 文本响应: {response.text}")
                    
            else:
                try:
                    error_data = response.json()
                    print(f"❌ 错误: {error_data.get('msg', 'Unknown')}")
                except:
                    print(f"❌ 错误: {response.text[:100]}...")
                    
        except Exception as e:
            print(f"❌ 请求异常: {e}")
    
    print("\n❌ 所有签名方法都失败了")
    return None, None

def test_proxy_connection(proxy_info):
    """测试代理连接"""
    print(f"\n🧪 测试代理连接")
    
    try:
        ip = proxy_info.get("ip")
        port = proxy_info.get("port")
        
        if not ip or not port:
            print("❌ 代理信息不完整")
            return
        
        proxies = {
            'http': f'http://{ip}:{port}',
            'https': f'http://{ip}:{port}'
        }
        
        print(f"🔍 测试代理: {ip}:{port}")
        
        # 测试获取IP
        response = requests.get("http://httpbin.org/ip", proxies=proxies, timeout=10)
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ 代理连接成功！")
            print(f"📍 当前IP: {result.get('origin', 'Unknown')}")
        else:
            print(f"❌ 代理连接失败: {response.status_code}")
            
    except Exception as e:
        print(f"❌ 代理测试异常: {e}")

def test_without_sign():
    """测试不使用签名的情况"""
    print(f"\n{'='*50}")
    print("🧪 特殊测试: 不使用sign参数")
    print(f"{'='*50}")
    
    try:
        with open('ip_keys.txt', 'r') as f:
            secret_key = f.read().strip()
    except Exception as e:
        print(f"❌ 读取密钥失败: {e}")
        return
    
    # 尝试不同的参数组合
    test_cases = [
        {
            "name": "只使用secret",
            "params": {"secret": secret_key}
        },
        {
            "name": "使用key参数",
            "params": {"key": secret_key, "num": 1, "time": 3, "port": 1}
        },
        {
            "name": "使用token参数",
            "params": {"token": secret_key, "num": 1, "time": 3, "port": 1}
        },
        {
            "name": "使用apikey参数",
            "params": {"apikey": secret_key, "num": 1, "time": 3, "port": 1}
        }
    ]
    
    for case in test_cases:
        print(f"\n🧪 {case['name']}")
        try:
            response = requests.get("http://api.tianqiip.com/getip", params=case['params'], timeout=10)
            print(f"状态码: {response.status_code}")
            
            if response.status_code == 200:
                try:
                    data = response.json()
                    print(f"✅ 响应: {data}")
                    if data.get("code") == 1000:
                        print("🎉 成功！")
                        return
                except:
                    print(f"✅ 响应: {response.text}")
            else:
                try:
                    error = response.json()
                    print(f"❌ 错误: {error}")
                except:
                    print(f"❌ 错误: {response.text[:100]}...")
                    
        except Exception as e:
            print(f"❌ 异常: {e}")

def main():
    """主函数"""
    try:
        # 测试所有签名方法
        method, signature = test_all_signatures()
        
        if method:
            print(f"\n🎉 找到正确的签名方法: {method}")
            print(f"🔑 签名值: {signature}")
        else:
            # 如果签名方法都失败，尝试无签名
            test_without_sign()
        
        print("\n💡 如果所有方法都失败，建议：")
        print("1. 检查账户余额是否充足")
        print("2. 确认是否需要添加白名单IP")
        print("3. 联系客服获取正确的签名算法")
        print("4. 检查API密钥是否正确")
        
    except KeyboardInterrupt:
        print("\n👋 用户取消测试")
    except Exception as e:
        print(f"❌ 测试失败: {e}")

if __name__ == "__main__":
    main()

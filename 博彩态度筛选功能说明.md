# 博彩态度筛选功能说明

## 🎯 功能概述

博彩态度筛选是一个新增的高级筛选功能，用于分析特定博彩公司相对于市场平均水平的赔率态度。该功能可以帮助用户识别某些博彩公司对特定比赛结果的独特看法。

## 📋 功能特点

### 1. 多样化选择
- **博彩公司选择**：支持单个或多个博彩公司的复选
- **赔率类型**：支持主胜、平局、客胜三种赔率类型
- **阈值设置**：可自定义阈值，默认为1.05

### 2. 精确时间匹配
- 基于动态时间线原理，确保时间点的准确性
- 获取目标公司的开盘时间点
- 查找其他公司在同一时间点的赔率数据
- 如果同时间点无数据，自动寻找最接近的时间点

### 3. 智能筛选逻辑
- **单个公司**：目标公司赔率 ÷ 其他公司平均赔率 ≥ 阈值
- **多个公司**：所有选中公司都必须满足条件才通过筛选

## 🔧 使用方法

### 界面操作步骤

1. **启用筛选**
   - 在比赛筛选页面找到"博彩态度"筛选条件
   - 勾选"启用博彩态度筛选"复选框

2. **设置筛选条件**
   - **赔率类型**：选择主胜、平局或客胜
   - **阈值**：设置比率阈值（如1.05表示目标公司赔率需比平均值高5%）
   - **博彩公司**：从列表中选择一个或多个目标公司

3. **应用筛选**
   - 点击"应用筛选"按钮
   - 系统将分析所有比赛并返回符合条件的结果

### 参数说明

| 参数 | 说明 | 默认值 | 示例 |
|------|------|--------|------|
| 赔率类型 | 分析的赔率类型 | 主胜 | 主胜/平局/客胜 |
| 阈值 | 比率的最小值 | 1.05 | 1.05表示需高出5% |
| 博彩公司 | 目标分析公司 | 无 | Pinnacle, bet365等 |

## 📊 筛选逻辑详解

### 单个公司筛选

```
比率 = 目标公司赔率 ÷ 其他公司平均赔率

如果 比率 ≥ 阈值，则该比赛通过筛选
```

**示例**：
- 目标公司：Pinnacle
- Pinnacle主胜赔率：2.10
- 其他公司平均主胜赔率：2.00
- 比率：2.10 ÷ 2.00 = 1.05
- 阈值：1.05
- 结果：1.05 ≥ 1.05，✅ 通过筛选

### 多个公司筛选

当选择多个公司时，**每个公司都必须满足条件**才能通过筛选。

**示例**：
- 目标公司：Pinnacle, bet365
- 阈值：1.05

对于某场比赛：
- Pinnacle比率：1.08 ≥ 1.05 ✅
- bet365比率：1.02 < 1.05 ❌
- 最终结果：❌ 不通过筛选（因为bet365不满足条件）

## 🎯 应用场景

### 1. 市场异常检测
- 识别某家公司对比赛结果的独特判断
- 发现可能的套利机会
- 分析市场分歧

### 2. 公司态度分析
- 研究特定公司的赔率策略
- 比较不同公司的市场观点
- 追踪公司行为模式

### 3. 投资策略制定
- 基于公司态度差异制定投注策略
- 结合其他筛选条件进行综合分析
- 提高投资决策的准确性

## 📈 实际效果

### 测试结果示例

```
=== 单个公司筛选示例 ===
筛选条件: Pinnacle, 主胜, 阈值0.95
结果: 8/10场比赛符合条件

比赛示例:
- 比赛2703989: Pinnacle=1.570, 平均=1.472, 比率=1.067 ✅
- 比赛2703991: Pinnacle=1.520, 平均=1.446, 比率=1.051 ✅
- 比赛2703992: Pinnacle=1.400, 平均=1.540, 比率=0.909 ❌
```

## ⚙️ 技术实现

### 核心算法
1. **时间点匹配**：基于动态时间线技术，精确匹配开盘时间
2. **数据聚合**：计算同时间点其他公司的平均赔率
3. **比率计算**：目标公司赔率除以平均赔率
4. **条件判断**：比较比率与设定阈值

### 性能优化
- 使用SQL索引优化查询性能
- 批量处理减少数据库访问
- 智能缓存提高响应速度

## 🔍 注意事项

### 1. 数据要求
- 需要有完整的赔率数据（主胜、平局、客胜）
- 需要有准确的时间信息（日期、时间）
- 至少需要2家以上公司的数据进行比较

### 2. 阈值设置建议
- **保守策略**：阈值设为1.05-1.10（5%-10%差异）
- **激进策略**：阈值设为1.02-1.05（2%-5%差异）
- **发现异常**：阈值设为1.15以上（15%以上差异）

### 3. 结果解读
- 高比率可能表示该公司对结果更乐观
- 低比率可能表示该公司更保守
- 极端比率可能表示数据异常或特殊情况

## 🚀 集成优势

### 与现有功能协同
- 可与凯利分析、时间筛选等功能组合使用
- 支持多条件筛选的交集运算
- 统一的筛选状态显示和管理

### 用户体验
- 直观的界面设计，易于理解和操作
- 实时的筛选状态反馈
- 详细的筛选结果说明

## 📝 总结

博彩态度筛选功能为用户提供了一个强大的工具来分析博彩公司的市场态度差异。通过精确的时间匹配和智能的比率计算，用户可以识别出具有特殊投资价值的比赛，为投资决策提供重要参考。

该功能的成功实现展示了系统的可扩展性和技术深度，为后续更多高级分析功能的开发奠定了坚实基础。

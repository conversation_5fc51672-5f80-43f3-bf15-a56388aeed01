#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试批量抓取修复效果
验证目标公司列表是否正确使用
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from enhanced_odds_scraper import EnhancedOddsScraper
from config import get_priority_companies, get_all_companies

def test_target_companies():
    """测试目标公司列表配置"""
    print("🔧 测试目标公司列表配置")
    print("=" * 60)
    
    # 测试优先级公司列表
    priority_10 = get_priority_companies(10)
    print(f"📊 优先级前10家公司:")
    for company_id, company_name in priority_10.items():
        print(f"  {company_id}: {company_name}")
    
    print(f"\n📊 优先级前5家公司:")
    priority_5 = get_priority_companies(5)
    for company_id, company_name in priority_5.items():
        print(f"  {company_id}: {company_name}")
    
    # 测试全部公司列表
    all_companies = get_all_companies()
    print(f"\n📊 全部目标公司 (共{len(all_companies)}家):")
    for company_id, company_name in list(all_companies.items())[:15]:  # 只显示前15家
        print(f"  {company_id}: {company_name}")
    if len(all_companies) > 15:
        print(f"  ... 还有 {len(all_companies) - 15} 家公司")

def test_single_match_scraping():
    """测试单场比赛抓取（验证修复效果）"""
    print("\n🎯 测试单场比赛抓取")
    print("=" * 60)
    
    # 使用一个已知的比赛ID
    match_id = "2399308"  # 这是您之前测试过的比赛
    
    scraper = EnhancedOddsScraper()
    
    # 测试不同的max_companies设置
    test_cases = [5, 10, 15]
    
    for max_companies in test_cases:
        print(f"\n📋 测试 max_companies={max_companies}")
        print("-" * 40)
        
        try:
            complete_data = scraper.scrape_complete_match_data(
                match_id=match_id,
                max_companies=max_companies,
                delay=1.0
            )
            
            if complete_data and complete_data.get('odds_data'):
                odds_data = complete_data['odds_data']
                
                # 统计公司数量
                companies_found = set(record['company_name'] for record in odds_data)
                
                print(f"✅ 成功抓取 {len(companies_found)} 家公司的数据")
                print(f"📊 总共 {len(odds_data)} 条赔率记录")
                
                # 显示找到的公司
                print(f"🏢 抓取到的公司:")
                for company in sorted(companies_found):
                    company_records = [r for r in odds_data if r['company_name'] == company]
                    print(f"  {company}: {len(company_records)} 条记录")
                
                # 检查是否都是目标公司
                target_companies = get_priority_companies(max_companies)
                target_names = set(target_companies.values())
                
                unexpected_companies = companies_found - target_names
                if unexpected_companies:
                    print(f"⚠️  发现非目标公司: {unexpected_companies}")
                else:
                    print(f"✅ 所有公司都在目标列表中")
                    
            else:
                print(f"❌ 抓取失败或无数据")
                
        except Exception as e:
            print(f"❌ 抓取出错: {e}")

def main():
    """主函数"""
    print("🔧 批量抓取修复效果测试")
    print("=" * 80)
    
    # 测试1：目标公司列表配置
    test_target_companies()
    
    # 测试2：单场比赛抓取
    test_single_match_scraping()
    
    print("\n" + "=" * 80)
    print("🎉 测试完成！")
    print("\n📝 说明:")
    print("- 如果修复成功，抓取的公司应该都在config.py的目标列表中")
    print("- 每家公司应该有多条历史赔率记录，而不是只有一条")
    print("- 公司数量应该符合max_companies的限制")

if __name__ == "__main__":
    main()

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
灵活数据保存器
用于保存比赛详细数据，支持数据结构的灵活变化

设计特点:
1. 向后兼容 - 不破坏现有的matches和odds表
2. 灵活扩展 - 使用JSON存储，适应未来数据变化
3. 优雅降级 - 数据缺失时不影响核心功能
4. 性能考虑 - 关键字段单独存储，便于查询

数据库架构:
- technical_stats: 技术统计数据
- lineups: 阵容信息
- player_stats: 球员统计
- goal_probability: 进失球概率
- matches表扩展: 添加阵型等关键字段

使用方法:
    saver = FlexibleMatchDataSaver()
    result = saver.save_extended_match_data(match_id, extended_data)
"""

import sqlite3
import json
import logging
from typing import Dict, Any, Optional
from datetime import datetime

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class FlexibleMatchDataSaver:
    """灵活的比赛数据保存器"""
    
    def __init__(self, db_path: str = 'odds_data.db'):
        """
        初始化数据保存器
        
        Args:
            db_path: 数据库文件路径
        """
        self.db_path = db_path
        self.init_extended_tables()
    
    def init_extended_tables(self):
        """初始化扩展表结构"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # 扩展现有matches表 - 添加新字段但不影响现有结构
            self._extend_matches_table(cursor)
            
            # 创建新的扩展表
            self._create_extended_tables(cursor)
            
            conn.commit()
            conn.close()
            
            logger.info("扩展表结构初始化完成")
            
        except Exception as e:
            logger.warning(f"表结构初始化警告: {e}")
    
    def _extend_matches_table(self, cursor: sqlite3.Cursor):
        """扩展matches表，添加新字段"""
        extend_fields = [
            "ALTER TABLE matches ADD COLUMN home_formation TEXT",
            "ALTER TABLE matches ADD COLUMN away_formation TEXT", 
            "ALTER TABLE matches ADD COLUMN match_events_json TEXT",
            "ALTER TABLE matches ADD COLUMN extended_data_json TEXT"
        ]
        
        for sql in extend_fields:
            try:
                cursor.execute(sql)
            except sqlite3.OperationalError:
                # 字段已存在，忽略错误
                pass
    
    def _create_extended_tables(self, cursor: sqlite3.Cursor):
        """创建扩展数据表"""
        
        # 技术统计表
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS technical_stats (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                match_id TEXT NOT NULL,
                stat_type TEXT NOT NULL,
                stats_json TEXT NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                UNIQUE(match_id, stat_type)
            )
        """)
        
        # 阵容信息表
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS lineups (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                match_id TEXT NOT NULL,
                team_type TEXT NOT NULL,
                lineup_json TEXT NOT NULL,
                formation TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                UNIQUE(match_id, team_type)
            )
        """)
        
        # 球员统计表
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS player_stats (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                match_id TEXT NOT NULL,
                player_id INTEGER NOT NULL,
                player_name TEXT NOT NULL,
                team_type TEXT NOT NULL,
                player_number TEXT,
                position TEXT,
                stats_json TEXT NOT NULL,
                events_json TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                UNIQUE(match_id, player_id)
            )
        """)
        
        # 进失球概率表
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS goal_probability (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                match_id TEXT NOT NULL,
                probability_type TEXT NOT NULL,
                probability_json TEXT NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                UNIQUE(match_id, probability_type)
            )
        """)
    
    def save_extended_match_data(self, match_id: str, extended_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        保存扩展的比赛数据
        
        Args:
            match_id: 比赛ID
            extended_data: 扩展数据字典
            
        Returns:
            保存结果字典
        """
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            save_results = {}
            
            # 1. 保存技术统计
            if 'technical_stats' in extended_data:
                result = self._save_technical_stats(cursor, match_id, extended_data['technical_stats'])
                save_results['technical_stats'] = result
            
            # 2. 保存半场/全场统计
            if 'halftime_fulltime_stats' in extended_data:
                result = self._save_halftime_fulltime_stats(cursor, match_id, extended_data['halftime_fulltime_stats'])
                save_results['halftime_fulltime_stats'] = result
            
            # 3. 保存阵容信息
            if 'lineup' in extended_data:
                result = self._save_lineup_data(cursor, match_id, extended_data['lineup'])
                save_results['lineup'] = result
            
            # 4. 保存球员统计
            if 'player_stats' in extended_data:
                result = self._save_player_stats(cursor, match_id, extended_data['player_stats'])
                save_results['player_stats'] = result
            
            # 5. 保存进失球概率
            if 'goal_probability' in extended_data:
                result = self._save_goal_probability(cursor, match_id, extended_data['goal_probability'])
                save_results['goal_probability'] = result
            
            # 6. 更新matches表的扩展字段
            self._update_matches_extended_fields(cursor, match_id, extended_data)
            
            conn.commit()
            conn.close()
            
            logger.info(f"比赛 {match_id} 的扩展数据保存完成")
            return {'success': True, 'details': save_results}
            
        except Exception as e:
            logger.error(f"保存扩展数据失败: {e}")
            return {'success': False, 'error': str(e)}
    
    def _save_technical_stats(self, cursor: sqlite3.Cursor, match_id: str, stats_data: Optional[Dict]) -> str:
        """保存技术统计数据"""
        if not stats_data:
            return "无数据"
        
        try:
            cursor.execute("""
                INSERT OR REPLACE INTO technical_stats 
                (match_id, stat_type, stats_json, updated_at)
                VALUES (?, ?, ?, CURRENT_TIMESTAMP)
            """, (match_id, 'current', json.dumps(stats_data, ensure_ascii=False)))
            
            return f"已保存 {len(stats_data)} 项技术统计"
            
        except Exception as e:
            logger.warning(f"技术统计保存失败: {e}")
            return f"保存失败: {e}"
    
    def _save_halftime_fulltime_stats(self, cursor: sqlite3.Cursor, match_id: str, ht_ft_data: Optional[Dict]) -> str:
        """保存半场/全场统计数据"""
        if not ht_ft_data:
            return "无数据"
        
        try:
            cursor.execute("""
                INSERT OR REPLACE INTO technical_stats 
                (match_id, stat_type, stats_json, updated_at)
                VALUES (?, ?, ?, CURRENT_TIMESTAMP)
            """, (match_id, 'historical', json.dumps(ht_ft_data, ensure_ascii=False)))
            
            return f"已保存 {len(ht_ft_data)} 项历史统计"
            
        except Exception as e:
            logger.warning(f"历史统计保存失败: {e}")
            return f"保存失败: {e}"
    
    def _save_lineup_data(self, cursor: sqlite3.Cursor, match_id: str, lineup_data: Optional[Dict]) -> str:
        """保存阵容数据"""
        if not lineup_data:
            return "无数据"
        
        try:
            saved_count = 0
            
            # 保存主队阵容
            if 'home_players' in lineup_data:
                home_lineup = {
                    'players': lineup_data.get('home_players', []),
                    'substitutes': lineup_data.get('home_substitutes', []),
                    'formation': lineup_data.get('home_formation', ''),
                    'coach': lineup_data.get('home_coach', ''),
                    'raw_data': lineup_data.get('raw_lineup_data', {})
                }
                cursor.execute("""
                    INSERT OR REPLACE INTO lineups 
                    (match_id, team_type, lineup_json, formation)
                    VALUES (?, ?, ?, ?)
                """, (match_id, 'home', json.dumps(home_lineup, ensure_ascii=False), 
                     lineup_data.get('home_formation', '')))
                saved_count += 1
            
            # 保存客队阵容
            if 'away_players' in lineup_data:
                away_lineup = {
                    'players': lineup_data.get('away_players', []),
                    'substitutes': lineup_data.get('away_substitutes', []),
                    'formation': lineup_data.get('away_formation', ''),
                    'coach': lineup_data.get('away_coach', ''),
                    'raw_data': lineup_data.get('raw_lineup_data', {})
                }
                cursor.execute("""
                    INSERT OR REPLACE INTO lineups 
                    (match_id, team_type, lineup_json, formation)
                    VALUES (?, ?, ?, ?)
                """, (match_id, 'away', json.dumps(away_lineup, ensure_ascii=False),
                     lineup_data.get('away_formation', '')))
                saved_count += 1
            
            return f"已保存 {saved_count} 支队伍的阵容"
            
        except Exception as e:
            logger.warning(f"阵容数据保存失败: {e}")
            return f"保存失败: {e}"
    
    def _save_player_stats(self, cursor: sqlite3.Cursor, match_id: str, player_data: Optional[Dict]) -> str:
        """保存球员统计数据"""
        if not player_data:
            return "无数据"
        
        try:
            saved_count = 0
            
            # 保存主队球员
            if 'home_team' in player_data and 'player_tech_info' in player_data['home_team']:
                for player in player_data['home_team']['player_tech_info']:
                    if self._save_single_player_stats(cursor, match_id, player, 'home'):
                        saved_count += 1
            
            # 保存客队球员
            if 'away_team' in player_data and 'player_tech_info' in player_data['away_team']:
                for player in player_data['away_team']['player_tech_info']:
                    if self._save_single_player_stats(cursor, match_id, player, 'away'):
                        saved_count += 1
            
            return f"已保存 {saved_count} 名球员的统计"
            
        except Exception as e:
            logger.warning(f"球员统计保存失败: {e}")
            return f"保存失败: {e}"
    
    def _save_single_player_stats(self, cursor: sqlite3.Cursor, match_id: str, 
                                 player_data: Dict, team_type: str) -> bool:
        """保存单个球员的统计数据"""
        try:
            player_id = player_data.get('playerId', 0)
            player_name = player_data.get('playerName', '')
            player_number = player_data.get('playerNum', '')
            
            # 提取位置信息
            position = ''
            tech_infos = player_data.get('techInfos', [])
            for info in tech_infos:
                if info.get('infoKind') == 'Position':
                    position = info.get('infoValue', '')
                    break
            
            # 保存完整的技术统计
            stats_json = json.dumps(player_data, ensure_ascii=False)
            
            # 保存球员事件
            events_json = json.dumps(player_data.get('events', []), ensure_ascii=False)
            
            cursor.execute("""
                INSERT OR REPLACE INTO player_stats 
                (match_id, player_id, player_name, team_type, player_number, 
                 position, stats_json, events_json)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            """, (match_id, player_id, player_name, team_type, player_number,
                 position, stats_json, events_json))
            
            return True
            
        except Exception as e:
            logger.warning(f"球员数据保存失败: {e}")
            return False
    
    def _save_goal_probability(self, cursor: sqlite3.Cursor, match_id: str, prob_data: Optional[Dict]) -> str:
        """保存进失球概率数据"""
        if not prob_data:
            return "无数据"
        
        try:
            saved_count = 0
            
            if 'probability_list' in prob_data:
                for prob_item in prob_data['probability_list']:
                    prob_type = prob_item.get('count', 'unknown')
                    cursor.execute("""
                        INSERT OR REPLACE INTO goal_probability 
                        (match_id, probability_type, probability_json)
                        VALUES (?, ?, ?)
                    """, (match_id, prob_type, json.dumps(prob_item, ensure_ascii=False)))
                    saved_count += 1
            
            return f"已保存 {saved_count} 组概率数据"
            
        except Exception as e:
            logger.warning(f"进失球概率保存失败: {e}")
            return f"保存失败: {e}"
    
    def _update_matches_extended_fields(self, cursor: sqlite3.Cursor, match_id: str, extended_data: Dict):
        """更新matches表的扩展字段"""
        try:
            # 更新阵型信息
            if 'lineup' in extended_data:
                lineup_data = extended_data['lineup']
                home_formation = lineup_data.get('home_formation', '')
                away_formation = lineup_data.get('away_formation', '')
                
                cursor.execute("""
                    UPDATE matches 
                    SET home_formation = ?, away_formation = ?
                    WHERE match_id = ?
                """, (home_formation, away_formation, match_id))
            
            # 保存比赛事件
            if 'match_events' in extended_data:
                events_data = extended_data['match_events']
                if events_data:
                    cursor.execute("""
                        UPDATE matches 
                        SET match_events_json = ?
                        WHERE match_id = ?
                    """, (json.dumps(events_data, ensure_ascii=False), match_id))
            
            # 保存完整的扩展数据（用于未来扩展）
            cursor.execute("""
                UPDATE matches 
                SET extended_data_json = ?
                WHERE match_id = ?
            """, (json.dumps(extended_data, ensure_ascii=False), match_id))
            
        except Exception as e:
            logger.warning(f"matches表扩展字段更新失败: {e}")
    
    def get_extended_match_data(self, match_id: str) -> Dict[str, Any]:
        """
        获取扩展的比赛数据
        
        Args:
            match_id: 比赛ID
            
        Returns:
            扩展数据字典
        """
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            result = {}
            
            # 获取技术统计
            cursor.execute("""
                SELECT stat_type, stats_json FROM technical_stats 
                WHERE match_id = ?
            """, (match_id,))
            
            for stat_type, stats_json in cursor.fetchall():
                result[f'technical_stats_{stat_type}'] = json.loads(stats_json)
            
            # 获取阵容信息
            cursor.execute("""
                SELECT team_type, lineup_json, formation FROM lineups 
                WHERE match_id = ?
            """, (match_id,))
            
            lineups = {}
            for team_type, lineup_json, formation in cursor.fetchall():
                lineups[f'{team_type}_lineup'] = json.loads(lineup_json)
                lineups[f'{team_type}_formation'] = formation
            
            if lineups:
                result['lineups'] = lineups
            
            # 获取球员统计
            cursor.execute("""
                SELECT team_type, player_name, stats_json FROM player_stats 
                WHERE match_id = ?
            """, (match_id,))
            
            players = {'home': [], 'away': []}
            for team_type, player_name, stats_json in cursor.fetchall():
                players[team_type].append(json.loads(stats_json))
            
            if players['home'] or players['away']:
                result['player_stats'] = players
            
            # 获取进失球概率
            cursor.execute("""
                SELECT probability_type, probability_json FROM goal_probability 
                WHERE match_id = ?
            """, (match_id,))
            
            probabilities = {}
            for prob_type, prob_json in cursor.fetchall():
                probabilities[prob_type] = json.loads(prob_json)
            
            if probabilities:
                result['goal_probabilities'] = probabilities
            
            conn.close()
            return result
            
        except Exception as e:
            logger.error(f"获取扩展数据失败: {e}")
            return {'error': str(e)}

def test_flexible_data_saver():
    """测试灵活数据保存器"""
    print("🧪 测试灵活数据保存器")
    print("=" * 50)
    
    saver = FlexibleMatchDataSaver()
    
    # 测试数据
    test_data = {
        'technical_stats': {'角球': {'home': '9', 'away': '5'}},
        'lineup': {
            'home_formation': '4-3-3',
            'away_formation': '3-5-2',
            'home_players': [{'name': '测试球员1', 'number': '1'}]
        },
        'goal_probability': {'probability_list': [{'count': 'Count_30'}]},
    }
    
    match_id = "test_match_001"
    result = saver.save_extended_match_data(match_id, test_data)
    
    if result['success']:
        print("✅ 数据保存成功")
        
        # 测试数据读取
        retrieved_data = saver.get_extended_match_data(match_id)
        if retrieved_data and 'error' not in retrieved_data:
            print(f"✅ 数据读取成功，包含 {len(retrieved_data)} 个数据类型")
        else:
            print(f"❌ 数据读取失败: {retrieved_data.get('error', '未知错误')}")
    else:
        print(f"❌ 数据保存失败: {result['error']}")

if __name__ == '__main__':
    test_flexible_data_saver()

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试投资回测功能
验证回测逻辑和数据准备是否正常工作
"""

import sqlite3
import sys
import os
from database import OddsDatabase

def test_match_data_for_backtest():
    """测试用于回测的比赛数据"""
    print("=== 测试用于回测的比赛数据 ===")
    try:
        with sqlite3.connect("odds_data.db") as conn:
            conn.row_factory = sqlite3.Row
            cursor = conn.cursor()
            
            # 查询有比分和赔率数据的比赛
            cursor.execute('''
                SELECT m.match_id, m.home_team, m.away_team, m.home_score, m.away_score,
                       m.accurate_datetime, COUNT(o.id) as odds_count
                FROM matches m
                LEFT JOIN odds o ON m.match_id = o.match_id
                WHERE m.home_score IS NOT NULL AND m.home_score != ''
                  AND m.away_score IS NOT NULL AND m.away_score != ''
                GROUP BY m.match_id
                HAVING odds_count > 0
                ORDER BY m.accurate_datetime DESC
                LIMIT 10
            ''')
            
            rows = cursor.fetchall()
            print(f"✅ 找到 {len(rows)} 场有完整数据的比赛:")
            
            for row in rows:
                match_dict = dict(row)
                teams = f"{match_dict['home_team']} vs {match_dict['away_team']}"
                score = f"{match_dict['home_score']}-{match_dict['away_score']}"
                print(f"  {match_dict['match_id']}: {teams} ({score}) - {match_dict['odds_count']}家博彩公司")
            
            return len(rows) > 0
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def test_odds_data_structure():
    """测试赔率数据结构"""
    print("\n=== 测试赔率数据结构 ===")
    try:
        with sqlite3.connect("odds_data.db") as conn:
            conn.row_factory = sqlite3.Row
            cursor = conn.cursor()
            
            # 获取一个有赔率数据的比赛
            cursor.execute('''
                SELECT match_id FROM odds 
                WHERE home_odds IS NOT NULL AND draw_odds IS NOT NULL AND away_odds IS NOT NULL
                LIMIT 1
            ''')
            
            match_row = cursor.fetchone()
            if not match_row:
                print("❌ 没有找到完整的赔率数据")
                return False
            
            match_id = match_row[0]
            print(f"测试比赛ID: {match_id}")
            
            # 获取该比赛的赔率数据
            cursor.execute('''
                SELECT company_name, home_odds, draw_odds, away_odds, 
                       kelly_home, kelly_draw, kelly_away, return_rate
                FROM odds 
                WHERE match_id = ?
                ORDER BY company_name
            ''', (match_id,))
            
            odds_rows = cursor.fetchall()
            print(f"✅ 找到 {len(odds_rows)} 家博彩公司的赔率:")
            
            for odds in odds_rows[:5]:  # 只显示前5家
                odds_dict = dict(odds)
                print(f"  {odds_dict['company_name']}: "
                      f"主胜{odds_dict['home_odds']} "
                      f"平局{odds_dict['draw_odds']} "
                      f"客胜{odds_dict['away_odds']} "
                      f"(凯利: {odds_dict['kelly_home']:.3f}, {odds_dict['kelly_draw']:.3f}, {odds_dict['kelly_away']:.3f})")
            
            return True
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def test_match_result_calculation():
    """测试比赛结果计算"""
    print("\n=== 测试比赛结果计算 ===")
    
    def calculate_match_result(home_score, away_score):
        """计算比赛结果"""
        try:
            home = int(home_score)
            away = int(away_score)
            
            if home > away:
                return '主胜'
            elif home == away:
                return '平局'
            else:
                return '客胜'
        except:
            return '无效'
    
    # 测试用例
    test_cases = [
        ("2", "1", "主胜"),
        ("1", "1", "平局"),
        ("0", "2", "客胜"),
        ("3", "0", "主胜"),
        ("", "1", "无效"),
        ("1", "", "无效")
    ]
    
    all_passed = True
    for home_score, away_score, expected in test_cases:
        result = calculate_match_result(home_score, away_score)
        if result == expected:
            print(f"✅ {home_score}-{away_score} -> {result}")
        else:
            print(f"❌ {home_score}-{away_score} -> {result}, 期望: {expected}")
            all_passed = False
    
    return all_passed

def test_betting_strategy_logic():
    """测试投注策略逻辑"""
    print("\n=== 测试投注策略逻辑 ===")
    
    def test_home_win_strategy(home_odds, min_odds, max_odds):
        """测试主胜策略"""
        return min_odds <= home_odds <= max_odds
    
    def test_kelly_strategy(kelly_home, kelly_draw, kelly_away, threshold):
        """测试凯利指数策略"""
        kelly_values = [kelly_home, kelly_draw, kelly_away]
        max_kelly = max(kelly_values)
        return max_kelly >= threshold
    
    # 测试主胜策略
    print("测试主胜策略:")
    test_cases = [
        (2.0, 1.5, 3.0, True),   # 在范围内
        (1.2, 1.5, 3.0, False),  # 低于最小值
        (3.5, 1.5, 3.0, False),  # 高于最大值
        (2.5, 1.5, 3.0, True),   # 在范围内
    ]
    
    for home_odds, min_odds, max_odds, expected in test_cases:
        result = test_home_win_strategy(home_odds, min_odds, max_odds)
        status = "✅" if result == expected else "❌"
        print(f"  {status} 主胜赔率{home_odds}, 范围[{min_odds}, {max_odds}] -> {result}")
    
    # 测试凯利指数策略
    print("测试凯利指数策略:")
    kelly_cases = [
        (0.08, 0.02, 0.01, 0.05, True),   # 主胜凯利最高且超过阈值
        (0.02, 0.01, 0.01, 0.05, False),  # 都低于阈值
        (-0.01, 0.08, 0.03, 0.05, True),  # 平局凯利最高且超过阈值
    ]
    
    for kelly_home, kelly_draw, kelly_away, threshold, expected in kelly_cases:
        result = test_kelly_strategy(kelly_home, kelly_draw, kelly_away, threshold)
        status = "✅" if result == expected else "❌"
        print(f"  {status} 凯利指数({kelly_home:.3f}, {kelly_draw:.3f}, {kelly_away:.3f}), 阈值{threshold} -> {result}")
    
    return True

def test_profit_calculation():
    """测试盈亏计算"""
    print("\n=== 测试盈亏计算 ===")
    
    def calculate_profit(bet_amount, odds, win):
        """计算盈亏"""
        if win:
            return bet_amount * (odds - 1)
        else:
            return -bet_amount
    
    # 测试用例
    test_cases = [
        (100, 2.0, True, 100.0),    # 投注100，赔率2.0，获胜，盈利100
        (100, 2.0, False, -100.0),  # 投注100，赔率2.0，失败，亏损100
        (50, 3.5, True, 125.0),     # 投注50，赔率3.5，获胜，盈利125
        (200, 1.5, True, 100.0),    # 投注200，赔率1.5，获胜，盈利100
    ]
    
    all_passed = True
    for bet_amount, odds, win, expected in test_cases:
        result = calculate_profit(bet_amount, odds, win)
        if abs(result - expected) < 0.01:  # 浮点数比较
            print(f"✅ 投注{bet_amount}, 赔率{odds}, {'胜' if win else '负'} -> 盈亏{result}")
        else:
            print(f"❌ 投注{bet_amount}, 赔率{odds}, {'胜' if win else '负'} -> 盈亏{result}, 期望{expected}")
            all_passed = False
    
    return all_passed

def main():
    """主测试函数"""
    print("🧪 投资回测功能测试")
    print("=" * 50)
    
    # 检查数据库文件是否存在
    if not os.path.exists("odds_data.db"):
        print("❌ 数据库文件 odds_data.db 不存在")
        return False
    
    tests = [
        test_match_data_for_backtest,
        test_odds_data_structure,
        test_match_result_calculation,
        test_betting_strategy_logic,
        test_profit_calculation
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"❌ 测试异常: {e}")
    
    print("\n" + "=" * 50)
    print(f"📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！投资回测功能应该可以正常工作。")
        return True
    else:
        print("⚠️  部分测试失败，请检查相关功能。")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)

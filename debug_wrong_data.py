#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试错误的数据 - 比赛2399071
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from enhanced_odds_scraper import EnhancedOddsScraper
import logging

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def debug_wrong_data():
    """调试错误的数据"""
    print("🔍 调试错误的数据 - 比赛2399071")
    print("=" * 60)
    
    scraper = EnhancedOddsScraper()
    match_id = "2399071"
    
    print(f"🎯 目标比赛: {match_id}")
    print("-" * 40)
    
    # 1. 查看智能推测的结果
    print("\n📋 智能推测的record ID:")
    company_links = scraper.get_company_odds_links_from_known_pattern(match_id)
    
    if company_links and '281' in company_links:
        bet365_data = company_links['281']
        predicted_record_id = bet365_data['record_id']
        predicted_url = bet365_data['url']
        
        print(f"  推测的record ID: {predicted_record_id}")
        print(f"  推测的URL: {predicted_url}")
    else:
        print("  ❌ 无法获取bet365的推测数据")
        return
    
    # 2. 正确的record ID
    correct_record_id = "*********"
    correct_url = f"https://op1.titan007.com/OddsHistory.aspx?id={correct_record_id}&sid={match_id}&cid=281&l=0"
    
    print(f"\n📋 正确的record ID:")
    print(f"  正确的record ID: {correct_record_id}")
    print(f"  正确的URL: {correct_url}")
    
    # 3. 计算差值
    predicted_id_int = int(predicted_record_id)
    correct_id_int = int(correct_record_id)
    difference = correct_id_int - predicted_id_int
    
    print(f"\n📊 差值分析:")
    print(f"  推测ID: {predicted_id_int}")
    print(f"  正确ID: {correct_id_int}")
    print(f"  差值: {difference}")
    print(f"  差值比例: {difference / predicted_id_int * 100:.2f}%")
    
    # 4. 测试两个URL的数据
    print(f"\n🧪 测试数据对比:")
    
    print(f"  📊 推测URL的数据:")
    try:
        predicted_records = scraper.parse_company_odds_history_page(predicted_url, "bet365")
        if predicted_records:
            print(f"    获取 {len(predicted_records)} 条记录")
            for i, record in enumerate(predicted_records[:3], 1):
                home_odds = record.get('home_odds', 'N/A')
                draw_odds = record.get('draw_odds', 'N/A')
                away_odds = record.get('away_odds', 'N/A')
                change_time = record.get('change_time', 'N/A')
                print(f"      {i}. {home_odds} {draw_odds} {away_odds} ({change_time})")
        else:
            print(f"    ❌ 无数据")
    except Exception as e:
        print(f"    ❌ 解析失败: {e}")
    
    print(f"  📊 正确URL的数据:")
    try:
        correct_records = scraper.parse_company_odds_history_page(correct_url, "bet365")
        if correct_records:
            print(f"    获取 {len(correct_records)} 条记录")
            for i, record in enumerate(correct_records[:3], 1):
                home_odds = record.get('home_odds', 'N/A')
                draw_odds = record.get('draw_odds', 'N/A')
                away_odds = record.get('away_odds', 'N/A')
                change_time = record.get('change_time', 'N/A')
                print(f"      {i}. {home_odds} {draw_odds} {away_odds} ({change_time})")
        else:
            print(f"    ❌ 无数据")
    except Exception as e:
        print(f"    ❌ 解析失败: {e}")
    
    # 5. 分析线性规律是否适用
    print(f"\n🧮 分析线性规律:")
    
    # 已知的数据点
    known_points = [
        (2213559, *********, "威廉希尔"),  # 2022年
        (2398985, *********, "威廉希尔"),  # 2023年
    ]
    
    print(f"  已知数据点:")
    for match_id_known, record_id_known, company in known_points:
        print(f"    比赛{match_id_known}: {company} = {record_id_known}")
    
    # 计算当前比赛的威廉希尔推测值
    match_id_int = int(match_id)
    william_predicted = ********* + (match_id_int - 2213559) * 10
    bet365_predicted = william_predicted + (281 - 115) * 100
    
    print(f"  当前比赛{match_id}:")
    print(f"    威廉希尔推测: {william_predicted}")
    print(f"    bet365推测: {bet365_predicted}")
    print(f"    bet365实际: {correct_id_int}")
    print(f"    bet365误差: {correct_id_int - bet365_predicted}")
    
    # 6. 尝试找到正确的系数
    print(f"\n🔍 寻找正确的系数:")
    
    # 基于威廉希尔的已知数据点计算系数
    match_diff = 2398985 - 2213559  # 185426
    record_diff = ********* - *********  # 1854260
    actual_coefficient = record_diff / match_diff
    
    print(f"  基于已知数据计算的系数: {actual_coefficient}")
    
    # 使用正确系数重新计算
    william_correct = ********* + (match_id_int - 2213559) * actual_coefficient
    bet365_correct = william_correct + (281 - 115) * 100
    
    print(f"  使用正确系数计算:")
    print(f"    威廉希尔: {william_correct:.0f}")
    print(f"    bet365: {bet365_correct:.0f}")
    print(f"    与实际的差值: {correct_id_int - bet365_correct:.0f}")

if __name__ == "__main__":
    debug_wrong_data()

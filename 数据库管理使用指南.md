# 数据库管理使用指南

## 概述

随着数据抓取量的增加，SQLite数据库文件会不断增长。为了解决这个问题，我们提供了完整的数据库管理解决方案。

## 当前数据库状况

- **当前大小**: 31.45 MB
- **比赛数量**: 409 场
- **赔率记录**: 173,675 条
- **平均每场比赛**: 424.6 条记录

## 增长预测

根据当前数据分析：
- **1,000 场比赛**: 约 77 MB
- **5,000 场比赛**: 约 384 MB
- **10,000 场比赛**: 约 769 MB (0.75 GB)
- **50,000 场比赛**: 约 3.8 GB

## 潜在问题

### 1. 性能问题
- SQLite在文件超过1GB时性能会明显下降
- 查询速度变慢，特别是复杂查询
- 索引效率降低

### 2. 存储问题
- 磁盘空间占用过大
- 备份文件过大，传输困难
- 内存使用增加

### 3. 维护问题
- 数据库碎片增加
- 备份时间延长
- 恢复时间增加

## 解决方案

### 1. 使用数据库管理器

#### 图形界面管理器
```bash
# 启动图形界面
python database_manager_ui.py
# 或双击
start_database_manager.bat
```

#### 命令行管理器
```bash
# 分析数据库
python database_manager.py analyze

# 清理30天前的数据
python database_manager.py cleanup 30

# 优化数据库
python database_manager.py optimize

# 备份数据库
python database_manager.py backup

# 自动清理(最大500MB，保留30天)
python database_manager.py auto-cleanup 500 30
```

### 2. 定期清理策略

#### 自动清理配置
在 `config.py` 中设置：
```python
DATABASE_CONFIG = {
    'max_size_mb': 500,        # 最大大小限制
    'auto_cleanup_days': 30,   # 保留天数
    'warning_size_mb': 100,    # 警告阈值
    'enable_auto_cleanup': True # 启用自动清理
}
```

#### 手动清理
- **建议频率**: 每周一次
- **保留时间**: 30-90天
- **清理内容**: 过期比赛和赔率数据

### 3. 数据归档策略

#### 创建归档
```python
# 归档90天前的数据
manager = DatabaseManager()
result = manager.create_archive("archive_2024.db", 90)
```

#### 归档策略
- **归档周期**: 每季度一次
- **归档范围**: 90天前的数据
- **存储位置**: 单独的归档文件

### 4. 数据库分割

#### 按时间分割
```python
# 将2024年后的数据分割到新数据库
manager.split_database_by_date("2024-01-01", "odds_data_2024.db")
```

#### 分割策略
- **按年份分割**: 每年一个数据库
- **按联赛分割**: 不同联赛使用不同数据库
- **按重要性分割**: 重要比赛和普通比赛分开

### 5. 备份策略

#### 自动备份
- **频率**: 每天一次
- **保留**: 最近7个备份
- **位置**: 本地备份目录

#### 手动备份
```bash
# 创建备份
python database_manager.py backup backup_20241201.db
```

## 最佳实践

### 1. 监控数据库大小
- 定期检查数据库大小
- 设置大小警告阈值
- 监控增长趋势

### 2. 定期维护
```python
# 每周执行一次
db.optimize_database()  # 优化数据库
db.cleanup_old_data(30) # 清理旧数据
```

### 3. 数据生命周期管理
- **活跃数据**: 最近30天，保存在主数据库
- **历史数据**: 30-90天，定期清理
- **归档数据**: 90天以上，移至归档文件

### 4. 性能优化
- 定期执行 VACUUM 操作
- 重建索引
- 分析表统计信息

## 使用数据库管理器UI

### 1. 统计信息页面
- 查看当前数据库状态
- 增长趋势预测
- 优化建议

### 2. 清理管理页面
- 手动清理旧数据
- 自动清理配置
- 数据库优化

### 3. 备份管理页面
- 创建数据库备份
- 管理备份文件
- 备份历史记录

### 4. 高级功能页面
- 数据归档
- 数据库分割
- 批量操作

## 紧急情况处理

### 数据库过大无法打开
```bash
# 1. 尝试优化
python database_manager.py optimize

# 2. 强制清理
python database_manager.py cleanup 7

# 3. 分割数据库
python -c "
from database_manager import DatabaseManager
manager = DatabaseManager()
manager.split_database_by_date('2024-11-01', 'recent_data.db')
"
```

### 数据库损坏
```bash
# 1. 检查完整性
sqlite3 odds_data.db "PRAGMA integrity_check;"

# 2. 尝试修复
sqlite3 odds_data.db "PRAGMA quick_check;"

# 3. 从备份恢复
cp odds_data_backup_latest.db odds_data.db
```

## 配置建议

### 小规模使用 (< 1000场比赛)
- 保留天数: 90天
- 最大大小: 200MB
- 备份频率: 每周

### 中等规模使用 (1000-5000场比赛)
- 保留天数: 60天
- 最大大小: 500MB
- 备份频率: 每天
- 启用自动清理

### 大规模使用 (> 5000场比赛)
- 保留天数: 30天
- 最大大小: 300MB
- 备份频率: 每天
- 启用自动清理和归档
- 考虑分库存储

## 迁移到专业数据库

当数据量超过10GB时，建议迁移到专业数据库：

### PostgreSQL
- 更好的性能
- 支持更大数据量
- 更强的并发能力

### MySQL
- 广泛支持
- 成熟的生态系统
- 丰富的工具

### 迁移步骤
1. 导出SQLite数据
2. 创建目标数据库
3. 转换数据格式
4. 导入数据
5. 更新应用配置

## 总结

通过合理的数据库管理策略，可以有效控制数据库大小，保持良好的性能。建议：

1. **启用自动管理**: 使用自动清理和备份功能
2. **定期监控**: 关注数据库大小和性能指标
3. **及时清理**: 不要等到问题严重才处理
4. **合理归档**: 保留重要历史数据，清理过期数据
5. **考虑升级**: 数据量大时考虑专业数据库解决方案

使用提供的数据库管理工具，可以轻松应对数据增长带来的挑战，确保系统长期稳定运行。

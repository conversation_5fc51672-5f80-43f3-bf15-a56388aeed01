#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据库诊断工具
检查数据库的完整性和一致性
"""

import os
import sqlite3
from database import OddsDatabase
from datetime import datetime

def diagnose_database(db_path="odds_data.db"):
    """诊断数据库"""
    print("=== 数据库诊断工具 ===\n")
    
    if not os.path.exists(db_path):
        print(f"❌ 数据库文件不存在: {db_path}")
        return
    
    try:
        db = OddsDatabase(db_path)
        
        # 1. 基本信息
        print("1. 基本信息")
        file_size = os.path.getsize(db_path)
        print(f"   文件路径: {db_path}")
        print(f"   文件大小: {file_size:,} 字节 ({file_size/1024/1024:.2f} MB)")
        print(f"   修改时间: {datetime.fromtimestamp(os.path.getmtime(db_path))}")
        
        # 2. 数据库统计
        print("\n2. 数据库统计")
        stats = db.get_database_stats()
        print(f"   比赛数量: {stats.get('match_count', 0):,}")
        print(f"   赔率记录: {stats.get('odds_count', 0):,}")
        print(f"   博彩公司: {stats.get('company_count', 0)}")
        print(f"   计算大小: {stats.get('db_size_mb', 0):.2f} MB")
        
        # 3. 表结构检查
        print("\n3. 表结构检查")
        with sqlite3.connect(db_path) as conn:
            cursor = conn.cursor()
            
            # 检查表是否存在
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
            tables = [row[0] for row in cursor.fetchall()]
            print(f"   数据表: {', '.join(tables)}")
            
            # 检查matches表
            if 'matches' in tables:
                cursor.execute("SELECT COUNT(*) FROM matches")
                matches_count = cursor.fetchone()[0]
                print(f"   matches表记录数: {matches_count:,}")
                
                # 检查是否有空的match_id
                cursor.execute("SELECT COUNT(*) FROM matches WHERE match_id IS NULL OR match_id = ''")
                null_match_ids = cursor.fetchone()[0]
                if null_match_ids > 0:
                    print(f"   ⚠️ 发现 {null_match_ids} 条空match_id记录")
                
                # 检查重复的match_id
                cursor.execute("""
                    SELECT match_id, COUNT(*) as count 
                    FROM matches 
                    GROUP BY match_id 
                    HAVING COUNT(*) > 1
                """)
                duplicates = cursor.fetchall()
                if duplicates:
                    print(f"   ⚠️ 发现 {len(duplicates)} 个重复的match_id")
                    for match_id, count in duplicates[:5]:
                        print(f"      - {match_id}: {count} 次")
                    if len(duplicates) > 5:
                        print(f"      - ... 还有 {len(duplicates) - 5} 个")
            
            # 检查odds表
            if 'odds' in tables:
                cursor.execute("SELECT COUNT(*) FROM odds")
                odds_count = cursor.fetchone()[0]
                print(f"   odds表记录数: {odds_count:,}")
                
                # 检查孤立的赔率记录
                cursor.execute("""
                    SELECT COUNT(*) FROM odds 
                    WHERE match_id NOT IN (SELECT match_id FROM matches)
                """)
                orphaned_odds = cursor.fetchone()[0]
                if orphaned_odds > 0:
                    print(f"   ⚠️ 发现 {orphaned_odds} 条孤立的赔率记录")
        
        # 4. 数据一致性检查
        print("\n4. 数据一致性检查")
        matches = db.get_all_matches()
        actual_matches_count = len(matches)
        
        print(f"   get_all_matches()返回: {actual_matches_count:,} 场比赛")
        print(f"   数据库统计显示: {stats.get('match_count', 0):,} 场比赛")
        
        if actual_matches_count == stats.get('match_count', 0):
            print("   ✅ 比赛数量一致")
        else:
            print("   ❌ 比赛数量不一致!")
            print(f"   差异: {abs(actual_matches_count - stats.get('match_count', 0))} 场")
        
        # 5. 联赛分布分析
        print("\n5. 联赛分布分析")
        league_stats = {}
        matches_with_league = 0
        matches_without_league = 0
        
        for match in matches:
            league = match.get('league', '').strip()
            if league and league != 'Unknown':
                league_stats[league] = league_stats.get(league, 0) + 1
                matches_with_league += 1
            else:
                matches_without_league += 1
        
        print(f"   有联赛信息的比赛: {matches_with_league:,}")
        print(f"   无联赛信息的比赛: {matches_without_league:,}")
        print(f"   联赛数量: {len(league_stats)}")
        
        if league_stats:
            print("   主要联赛:")
            sorted_leagues = sorted(league_stats.items(), key=lambda x: x[1], reverse=True)
            for league, count in sorted_leagues[:10]:
                print(f"     - {league}: {count} 场比赛")
            
            if len(sorted_leagues) > 10:
                remaining = sum(count for _, count in sorted_leagues[10:])
                print(f"     - 其他 {len(sorted_leagues) - 10} 个联赛: {remaining} 场比赛")
        
        # 6. 数据质量检查
        print("\n6. 数据质量检查")
        
        # 检查比赛时间
        matches_with_time = sum(1 for m in matches if m.get('match_time'))
        print(f"   有比赛时间的比赛: {matches_with_time:,}/{actual_matches_count:,}")
        
        # 检查队伍信息
        matches_with_teams = sum(1 for m in matches if m.get('home_team') and m.get('away_team'))
        print(f"   有队伍信息的比赛: {matches_with_teams:,}/{actual_matches_count:,}")
        
        # 检查赔率数据完整性
        matches_with_odds = 0
        total_odds_records = 0
        
        for match in matches[:100]:  # 只检查前100场比赛以节省时间
            match_id = match.get('match_id')
            if match_id:
                odds = db.get_odds_data(match_id)
                if odds:
                    matches_with_odds += 1
                    total_odds_records += len(odds)
        
        print(f"   前100场比赛中有赔率数据的: {matches_with_odds}/100")
        if matches_with_odds > 0:
            avg_odds_per_match = total_odds_records / matches_with_odds
            print(f"   平均每场比赛赔率记录: {avg_odds_per_match:.1f}")
        
        # 7. 性能指标
        print("\n7. 性能指标")
        
        # 估算查询性能
        import time
        
        if actual_matches_count > 0:
            # 测试单个比赛查询
            sample_match_id = matches[0].get('match_id')
            start_time = time.time()
            match_info = db.get_match_info(sample_match_id)
            query_time = time.time() - start_time
            print(f"   单个比赛查询时间: {query_time*1000:.2f} ms")
            
            # 测试赔率查询
            start_time = time.time()
            odds_data = db.get_odds_data(sample_match_id)
            odds_query_time = time.time() - start_time
            print(f"   赔率数据查询时间: {odds_query_time*1000:.2f} ms")
            print(f"   该比赛赔率记录数: {len(odds_data)}")
        
        # 8. 建议
        print("\n8. 优化建议")
        
        size_mb = stats.get('db_size_mb', 0)
        if size_mb < 50:
            print("   ✅ 数据库大小适中，性能良好")
        elif size_mb < 200:
            print("   ⚠️ 数据库较大，建议考虑联赛分库")
            print("   💡 可以使用: python database_mode_selector.py")
        else:
            print("   🔴 数据库过大，强烈建议联赛分库")
            print("   💡 立即执行: python league_database_manager.py migrate odds_data.db")
        
        if matches_without_league > actual_matches_count * 0.1:
            print("   ⚠️ 较多比赛缺少联赛信息，可能影响分库效果")
        
        if duplicates:
            print("   ⚠️ 发现重复数据，建议清理")
        
        if orphaned_odds > 0:
            print("   ⚠️ 发现孤立赔率记录，建议清理")
        
        print("\n=== 诊断完成 ===")
        
        # 返回诊断结果
        return {
            'file_size_mb': file_size / 1024 / 1024,
            'stats': stats,
            'actual_matches': actual_matches_count,
            'league_count': len(league_stats),
            'data_consistent': actual_matches_count == stats.get('match_count', 0),
            'has_duplicates': len(duplicates) > 0 if 'duplicates' in locals() else False,
            'has_orphaned_odds': orphaned_odds > 0 if 'orphaned_odds' in locals() else False,
            'matches_with_league': matches_with_league,
            'matches_without_league': matches_without_league
        }
        
    except Exception as e:
        print(f"❌ 诊断过程中出错: {e}")
        return None

def quick_fix_database(db_path="odds_data.db"):
    """快速修复数据库常见问题"""
    print("=== 数据库快速修复 ===\n")
    
    if not os.path.exists(db_path):
        print(f"❌ 数据库文件不存在: {db_path}")
        return
    
    try:
        # 备份数据库
        backup_path = f"{db_path}.backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        import shutil
        shutil.copy2(db_path, backup_path)
        print(f"✅ 已创建备份: {backup_path}")
        
        with sqlite3.connect(db_path) as conn:
            cursor = conn.cursor()
            
            print("正在执行修复操作...")
            
            # 1. 删除重复的比赛记录
            cursor.execute("""
                DELETE FROM matches 
                WHERE id NOT IN (
                    SELECT MIN(id) 
                    FROM matches 
                    GROUP BY match_id
                )
            """)
            deleted_matches = cursor.rowcount
            if deleted_matches > 0:
                print(f"✅ 删除了 {deleted_matches} 条重复的比赛记录")
            
            # 2. 删除孤立的赔率记录
            cursor.execute("""
                DELETE FROM odds 
                WHERE match_id NOT IN (SELECT match_id FROM matches)
            """)
            deleted_odds = cursor.rowcount
            if deleted_odds > 0:
                print(f"✅ 删除了 {deleted_odds} 条孤立的赔率记录")
            
            # 3. 重建索引
            cursor.execute("REINDEX")
            print("✅ 重建了索引")
            
            # 4. 分析表统计信息
            cursor.execute("ANALYZE")
            print("✅ 更新了表统计信息")
            
            # 5. 压缩数据库
            old_size = os.path.getsize(db_path)
            cursor.execute("VACUUM")
            new_size = os.path.getsize(db_path)
            saved_space = old_size - new_size
            
            print(f"✅ 压缩数据库完成")
            print(f"   原大小: {old_size/1024/1024:.2f} MB")
            print(f"   新大小: {new_size/1024/1024:.2f} MB")
            print(f"   节省空间: {saved_space/1024/1024:.2f} MB")
            
            conn.commit()
        
        print("\n=== 修复完成 ===")
        print("建议重新运行诊断工具验证修复效果")
        
    except Exception as e:
        print(f"❌ 修复过程中出错: {e}")

if __name__ == "__main__":
    import sys
    
    if len(sys.argv) > 1:
        command = sys.argv[1]
        db_path = sys.argv[2] if len(sys.argv) > 2 else "odds_data.db"
        
        if command == "diagnose":
            diagnose_database(db_path)
        elif command == "fix":
            quick_fix_database(db_path)
        else:
            print("用法: python database_diagnostic.py <command> [db_path]")
            print("命令:")
            print("  diagnose - 诊断数据库")
            print("  fix - 快速修复数据库")
    else:
        # 默认执行诊断
        diagnose_database()

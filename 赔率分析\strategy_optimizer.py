#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
策略优化与对比
对比不同策略效果，优化参数设置，生成最优策略组合
"""

import pandas as pd
import numpy as np
import json
import os
from datetime import datetime
from itertools import product
import matplotlib.pyplot as plt
import seaborn as sns

from betting_strategy_designer import BettingStrategyDesigner, ConfidenceStrategy, ValueBettingStrategy
from backtesting_framework import BacktestingFramework
import warnings
warnings.filterwarnings('ignore')

class StrategyOptimizer:
    def __init__(self):
        self.strategy_designer = BettingStrategyDesigner()
        self.backtester = BacktestingFramework()
        self.optimization_results = {}
        self.comparison_results = {}
        
    def load_data(self):
        """加载必要的数据"""
        print("=== 加载数据 ===")
        
        # 加载预测模型
        if not self.backtester.load_prediction_model():
            return False
        
        # 加载历史数据
        if not self.backtester.load_historical_data():
            return False
        
        # 准备回测数据
        if not self.backtester.prepare_backtest_data():
            return False
        
        # 生成预测
        if not self.backtester.generate_predictions():
            return False
        
        print("数据加载完成")
        return True
    
    def optimize_confidence_strategy(self, param_ranges=None):
        """优化置信度策略参数"""
        print("\n=== 优化置信度策略 ===")
        
        if param_ranges is None:
            param_ranges = {
                'min_confidence': [0.6, 0.65, 0.7, 0.75, 0.8, 0.85],
                'max_stake_ratio': [0.02, 0.03, 0.05, 0.08, 0.1]
            }
        
        best_params = None
        best_score = -float('inf')
        optimization_results = []
        
        total_combinations = len(param_ranges['min_confidence']) * len(param_ranges['max_stake_ratio'])
        print(f"测试 {total_combinations} 个参数组合")
        
        for i, (min_conf, max_ratio) in enumerate(product(
            param_ranges['min_confidence'], 
            param_ranges['max_stake_ratio']
        ), 1):
            print(f"  测试组合 {i}/{total_combinations}: min_conf={min_conf}, max_ratio={max_ratio}")
            
            try:
                # 创建策略
                strategy = ConfidenceStrategy(
                    min_confidence=min_conf,
                    max_stake_ratio=max_ratio
                )
                
                # 运行回测
                result = self._run_single_backtest(strategy, 'confidence_test')
                
                if result:
                    # 计算综合评分
                    score = self._calculate_optimization_score(result)
                    
                    optimization_result = {
                        'min_confidence': min_conf,
                        'max_stake_ratio': max_ratio,
                        'score': score,
                        'return_pct': result['total_return_pct'],
                        'win_rate': result['win_rate'],
                        'total_bets': result['total_bets'],
                        'sharpe_ratio': self._calculate_sharpe_ratio(result)
                    }
                    
                    optimization_results.append(optimization_result)
                    
                    if score > best_score:
                        best_score = score
                        best_params = {
                            'min_confidence': min_conf,
                            'max_stake_ratio': max_ratio
                        }
                        
                    print(f"    得分: {score:.3f}, 收益: {result['total_return_pct']:.2f}%, 胜率: {result['win_rate']:.3f}")
                
            except Exception as e:
                print(f"    参数组合失败: {e}")
                continue
        
        self.optimization_results['confidence'] = {
            'best_params': best_params,
            'best_score': best_score,
            'all_results': optimization_results
        }
        
        print(f"\n置信度策略优化完成:")
        print(f"  最佳参数: {best_params}")
        print(f"  最佳得分: {best_score:.3f}")
        
        return best_params, best_score
    
    def optimize_value_betting_strategy(self, param_ranges=None):
        """优化价值投注策略参数"""
        print("\n=== 优化价值投注策略 ===")
        
        if param_ranges is None:
            param_ranges = {
                'min_value': [0.02, 0.03, 0.05, 0.08, 0.1],
                'max_stake_ratio': [0.02, 0.03, 0.05, 0.08]
            }
        
        best_params = None
        best_score = -float('inf')
        optimization_results = []
        
        total_combinations = len(param_ranges['min_value']) * len(param_ranges['max_stake_ratio'])
        print(f"测试 {total_combinations} 个参数组合")
        
        for i, (min_value, max_ratio) in enumerate(product(
            param_ranges['min_value'], 
            param_ranges['max_stake_ratio']
        ), 1):
            print(f"  测试组合 {i}/{total_combinations}: min_value={min_value}, max_ratio={max_ratio}")
            
            try:
                # 创建策略
                strategy = ValueBettingStrategy(
                    min_value=min_value,
                    max_stake_ratio=max_ratio
                )
                
                # 运行回测
                result = self._run_single_backtest(strategy, 'value_betting_test')
                
                if result:
                    # 计算综合评分
                    score = self._calculate_optimization_score(result)
                    
                    optimization_result = {
                        'min_value': min_value,
                        'max_stake_ratio': max_ratio,
                        'score': score,
                        'return_pct': result['total_return_pct'],
                        'win_rate': result['win_rate'],
                        'total_bets': result['total_bets'],
                        'sharpe_ratio': self._calculate_sharpe_ratio(result)
                    }
                    
                    optimization_results.append(optimization_result)
                    
                    if score > best_score:
                        best_score = score
                        best_params = {
                            'min_value': min_value,
                            'max_stake_ratio': max_ratio
                        }
                        
                    print(f"    得分: {score:.3f}, 收益: {result['total_return_pct']:.2f}%, 胜率: {result['win_rate']:.3f}")
                
            except Exception as e:
                print(f"    参数组合失败: {e}")
                continue
        
        self.optimization_results['value_betting'] = {
            'best_params': best_params,
            'best_score': best_score,
            'all_results': optimization_results
        }
        
        print(f"\n价值投注策略优化完成:")
        print(f"  最佳参数: {best_params}")
        print(f"  最佳得分: {best_score:.3f}")
        
        return best_params, best_score
    
    def _run_single_backtest(self, strategy, strategy_name):
        """运行单个策略的回测"""
        # 重置回测器状态
        self.backtester.current_bankroll = self.backtester.initial_bankroll
        self.backtester.trade_history = []
        self.backtester.bankroll_history = [self.backtester.initial_bankroll]
        
        data = self.backtester.historical_data.copy()
        
        total_bets = 0
        winning_bets = 0
        total_stake = 0
        total_return = 0
        
        for idx, row in data.iterrows():
            # 获取预测结果
            prediction_result = row['predictions']
            
            # 构建赔率数据
            odds_data = {
                'opening_home': row.get('opening_home', 2.0),
                'closing_home': row.get('closing_home', 2.0),
                'opening_draw': row.get('opening_draw', 3.0),
                'closing_draw': row.get('closing_draw', 3.0),
                'opening_away': row.get('opening_away', 4.0),
                'closing_away': row.get('closing_away', 4.0)
            }
            
            match_info = {
                'match_id': row.get('match_id', f'match_{idx}'),
                'actual_outcome': row['match_result']
            }
            
            # 获取投注决策
            decision = strategy.get_bet_decision(
                prediction_result, odds_data, match_info, self.backtester.current_bankroll
            )
            
            if decision['should_bet']:
                total_bets += 1
                stake = decision['stake']
                total_stake += stake
                
                # 计算收益
                predicted_outcome = decision['outcome']
                actual_outcome = row['match_result']
                
                if predicted_outcome == actual_outcome:
                    # 投注成功
                    winning_bets += 1
                    
                    # 获取对应赔率
                    if predicted_outcome == 'home_win':
                        odds = odds_data['closing_home']
                    elif predicted_outcome == 'draw':
                        odds = odds_data['closing_draw']
                    else:
                        odds = odds_data['closing_away']
                    
                    profit = stake * (odds - 1)
                    self.backtester.current_bankroll += profit
                    total_return += profit
                else:
                    # 投注失败
                    self.backtester.current_bankroll -= stake
                    total_return -= stake
                
                # 记录交易
                trade_record = {
                    'match_id': match_info['match_id'],
                    'predicted_outcome': predicted_outcome,
                    'actual_outcome': actual_outcome,
                    'stake': stake,
                    'odds': odds if predicted_outcome == actual_outcome else 0,
                    'profit': profit if predicted_outcome == actual_outcome else -stake,
                    'bankroll_after': self.backtester.current_bankroll,
                    'confidence': decision['confidence']
                }
                self.backtester.trade_history.append(trade_record)
            
            # 记录资金曲线
            self.backtester.bankroll_history.append(self.backtester.current_bankroll)
        
        # 计算回测指标
        win_rate = winning_bets / total_bets if total_bets > 0 else 0
        total_return_pct = (self.backtester.current_bankroll - self.backtester.initial_bankroll) / self.backtester.initial_bankroll * 100
        
        return {
            'strategy_name': strategy_name,
            'initial_bankroll': self.backtester.initial_bankroll,
            'final_bankroll': self.backtester.current_bankroll,
            'total_return': total_return,
            'total_return_pct': total_return_pct,
            'total_bets': total_bets,
            'winning_bets': winning_bets,
            'win_rate': win_rate,
            'total_stake': total_stake,
            'trade_history': self.backtester.trade_history,
            'bankroll_history': self.backtester.bankroll_history
        }
    
    def _calculate_optimization_score(self, backtest_result):
        """计算优化评分"""
        # 权重设置
        weights = {
            'return': 0.4,
            'win_rate': 0.2,
            'sharpe': 0.2,
            'bet_count': 0.2
        }
        
        # 标准化指标
        return_score = min(1.0, max(0, backtest_result['total_return_pct'] / 100))
        win_rate_score = backtest_result['win_rate']
        sharpe_score = min(1.0, max(0, self._calculate_sharpe_ratio(backtest_result) / 3))
        bet_count_score = min(1.0, backtest_result['total_bets'] / 20)  # 假设20次投注为满分
        
        # 加权综合评分
        score = (
            weights['return'] * return_score +
            weights['win_rate'] * win_rate_score +
            weights['sharpe'] * sharpe_score +
            weights['bet_count'] * bet_count_score
        )
        
        return score
    
    def _calculate_sharpe_ratio(self, backtest_result):
        """计算夏普比率"""
        bankroll_history = backtest_result['bankroll_history']
        
        if len(bankroll_history) < 2:
            return 0
        
        # 计算收益序列
        returns = []
        for i in range(1, len(bankroll_history)):
            if bankroll_history[i-1] > 0:
                ret = (bankroll_history[i] - bankroll_history[i-1]) / bankroll_history[i-1]
                returns.append(ret)
        
        if len(returns) < 2:
            return 0
        
        returns = np.array(returns)
        
        # 夏普比率
        if np.std(returns) > 0:
            sharpe_ratio = np.mean(returns) / np.std(returns) * np.sqrt(252)
        else:
            sharpe_ratio = 0
        
        return sharpe_ratio
    
    def compare_strategies(self):
        """对比不同策略"""
        print("\n=== 策略对比分析 ===")
        
        # 使用优化后的参数运行回测
        strategies_to_compare = []
        
        # 添加优化后的策略
        if 'confidence' in self.optimization_results:
            best_params = self.optimization_results['confidence']['best_params']
            optimized_confidence = ConfidenceStrategy(**best_params)
            strategies_to_compare.append(('Optimized Confidence', optimized_confidence))
        
        if 'value_betting' in self.optimization_results:
            best_params = self.optimization_results['value_betting']['best_params']
            optimized_value = ValueBettingStrategy(**best_params)
            strategies_to_compare.append(('Optimized Value Betting', optimized_value))
        
        # 添加默认策略作为基准
        default_confidence = ConfidenceStrategy()
        default_value = ValueBettingStrategy()
        strategies_to_compare.extend([
            ('Default Confidence', default_confidence),
            ('Default Value Betting', default_value)
        ])
        
        comparison_results = []
        
        for strategy_name, strategy in strategies_to_compare:
            print(f"  运行策略: {strategy_name}")
            
            try:
                result = self._run_single_backtest(strategy, strategy_name)
                
                if result:
                    comparison_result = {
                        'strategy_name': strategy_name,
                        'return_pct': result['total_return_pct'],
                        'win_rate': result['win_rate'],
                        'total_bets': result['total_bets'],
                        'sharpe_ratio': self._calculate_sharpe_ratio(result),
                        'final_bankroll': result['final_bankroll'],
                        'optimization_score': self._calculate_optimization_score(result)
                    }
                    
                    comparison_results.append(comparison_result)
                    
                    print(f"    收益率: {result['total_return_pct']:.2f}%")
                    print(f"    胜率: {result['win_rate']:.3f}")
                    print(f"    投注次数: {result['total_bets']}")
                    
            except Exception as e:
                print(f"    策略运行失败: {e}")
                continue
        
        # 排序
        comparison_results.sort(key=lambda x: x['optimization_score'], reverse=True)
        
        self.comparison_results = comparison_results
        
        print(f"\n=== 策略对比结果 ===")
        for i, result in enumerate(comparison_results, 1):
            print(f"{i}. {result['strategy_name']}")
            print(f"   综合评分: {result['optimization_score']:.3f}")
            print(f"   收益率: {result['return_pct']:.2f}%")
            print(f"   胜率: {result['win_rate']:.3f}")
            print(f"   夏普比率: {result['sharpe_ratio']:.3f}")
        
        return comparison_results
    
    def generate_optimization_report(self):
        """生成优化报告"""
        report = {
            'optimization_date': datetime.now().isoformat(),
            'optimization_results': self.optimization_results,
            'strategy_comparison': self.comparison_results,
            'recommendations': self._generate_recommendations()
        }
        
        return report
    
    def _generate_recommendations(self):
        """生成策略建议"""
        recommendations = []
        
        if self.comparison_results:
            best_strategy = self.comparison_results[0]
            recommendations.append(f"推荐使用: {best_strategy['strategy_name']}")
            recommendations.append(f"预期收益率: {best_strategy['return_pct']:.2f}%")
            recommendations.append(f"预期胜率: {best_strategy['win_rate']:.3f}")
            
            # 风险提示
            if best_strategy['total_bets'] < 10:
                recommendations.append("注意: 投注次数较少，结果可能不够稳定")
            
            if best_strategy['sharpe_ratio'] < 1:
                recommendations.append("注意: 夏普比率较低，风险调整后收益有限")
        
        # 优化建议
        if 'confidence' in self.optimization_results:
            conf_results = self.optimization_results['confidence']['all_results']
            if conf_results:
                best_conf = max(conf_results, key=lambda x: x['score'])
                recommendations.append(f"置信度策略最佳参数: min_confidence={best_conf['min_confidence']}, max_stake_ratio={best_conf['max_stake_ratio']}")
        
        return recommendations
    
    def save_optimization_results(self, filename_prefix="strategy_optimization"):
        """保存优化结果"""
        report = self.generate_optimization_report()
        
        output_file = f"{filename_prefix}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        
        # 转换为可序列化格式
        serializable_report = self._make_serializable(report)
        
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(serializable_report, f, ensure_ascii=False, indent=2)
        
        print(f"\n优化结果已保存到: {output_file}")
        
        # 输出建议
        print(f"\n=== 策略建议 ===")
        for rec in report['recommendations']:
            print(f"  - {rec}")
        
        return output_file
    
    def _make_serializable(self, obj):
        """将对象转换为可序列化的格式"""
        if isinstance(obj, dict):
            return {k: self._make_serializable(v) for k, v in obj.items()}
        elif isinstance(obj, (list, tuple)):
            return [self._make_serializable(item) for item in obj]
        elif isinstance(obj, (np.integer, np.int64)):
            return int(obj)
        elif isinstance(obj, (np.floating, np.float64)):
            return float(obj)
        elif isinstance(obj, (np.bool_, bool)):
            return bool(obj)
        elif isinstance(obj, np.ndarray):
            return obj.tolist()
        elif pd.isna(obj) or obj is None:
            return None
        else:
            return str(obj)

def main():
    optimizer = StrategyOptimizer()
    
    # 加载数据
    if not optimizer.load_data():
        return
    
    # 优化置信度策略
    optimizer.optimize_confidence_strategy()
    
    # 优化价值投注策略
    optimizer.optimize_value_betting_strategy()
    
    # 对比策略
    optimizer.compare_strategies()
    
    # 保存结果
    optimizer.save_optimization_results()

if __name__ == "__main__":
    main()

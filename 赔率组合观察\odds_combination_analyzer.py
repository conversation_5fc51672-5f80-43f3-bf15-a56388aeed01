#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
赔率组合观察 - 分析核心模块
实现赔率组合匹配和分析功能
"""

import logging
from typing import List, Dict, Tuple, Optional
from odds_combination_database import OddsCombinationDatabase

logger = logging.getLogger(__name__)

class OddsCombinationAnalyzer:
    def __init__(self, db_path: str = None):
        """初始化分析器"""
        self.database = OddsCombinationDatabase(db_path)
    
    def generate_odds_combinations(self, odds_data: List[Dict], combination_size: int) -> List[List[Dict]]:
        """
        生成赔率组合
        
        Args:
            odds_data: 按时间排序的赔率数据
            combination_size: 组合大小
            
        Returns:
            赔率组合列表
        """
        combinations = []
        
        if len(odds_data) < combination_size:
            return combinations
        
        # 按时间从早到晚，每连续的combination_size组为1个观察组合
        for i in range(len(odds_data) - combination_size + 1):
            combination = odds_data[i:i + combination_size]
            combinations.append(combination)
        
        return combinations
    
    def odds_match(self, odds1: Dict, odds2: Dict, tolerance: float = 0.001) -> bool:
        """
        判断两个赔率是否匹配（考虑浮点数精度）
        
        Args:
            odds1: 第一个赔率
            odds2: 第二个赔率
            tolerance: 容差值
            
        Returns:
            是否匹配
        """
        try:
            home_match = abs(float(odds1['home_odds']) - float(odds2['home_odds'])) <= tolerance
            draw_match = abs(float(odds1['draw_odds']) - float(odds2['draw_odds'])) <= tolerance
            away_match = abs(float(odds1['away_odds']) - float(odds2['away_odds'])) <= tolerance
            
            return home_match and draw_match and away_match
        except (ValueError, TypeError, KeyError):
            return False
    
    def combination_match(self, target_combination: List[Dict], candidate_combination: List[Dict]) -> bool:
        """
        判断两个赔率组合是否完全匹配
        
        Args:
            target_combination: 目标组合
            candidate_combination: 候选组合
            
        Returns:
            是否完全匹配
        """
        if len(target_combination) != len(candidate_combination):
            return False
        
        for i in range(len(target_combination)):
            if not self.odds_match(target_combination[i], candidate_combination[i]):
                return False
        
        return True
    
    def find_matching_combinations_in_match(self, target_combination: List[Dict], 
                                          match_id: str, company_name: str) -> List[int]:
        """
        在指定比赛中查找匹配的赔率组合
        
        Args:
            target_combination: 目标组合
            match_id: 比赛ID
            company_name: 博彩公司名称
            
        Returns:
            匹配位置列表（从0开始的索引）
        """
        match_positions = []
        
        # 获取该比赛的赔率数据
        match_odds = self.database.get_match_odds_by_company(match_id, company_name)
        
        if len(match_odds) < len(target_combination):
            return match_positions
        
        # 生成该比赛的所有可能组合
        candidate_combinations = self.generate_odds_combinations(match_odds, len(target_combination))
        
        # 检查每个候选组合是否匹配
        for i, candidate in enumerate(candidate_combinations):
            if self.combination_match(target_combination, candidate):
                match_positions.append(i)
        
        return match_positions
    
    def calculate_match_result_type(self, match_result: Dict) -> str:
        """
        计算比赛结果类型（胜/平/负）
        
        Args:
            match_result: 比赛结果
            
        Returns:
            结果类型：'win', 'draw', 'loss', 'unknown'
        """
        try:
            home_score = int(match_result.get('home_score', 0))
            away_score = int(match_result.get('away_score', 0))
            
            if home_score > away_score:
                return 'win'
            elif home_score == away_score:
                return 'draw'
            else:
                return 'loss'
        except (ValueError, TypeError):
            return 'unknown'
    
    def analyze_odds_combinations(self, match_id: str, company_name: str,
                                combination_size: int, only_historical: bool = True) -> Dict:
        """
        分析赔率组合
        
        Args:
            match_id: 观察的比赛ID
            company_name: 博彩公司名称
            combination_size: 组合大小
            
        Returns:
            分析结果
        """
        logger.info(f"开始分析比赛 {match_id} 的赔率组合")
        
        # 检查比赛是否存在
        if not self.database.check_match_exists(match_id):
            return {
                'success': False,
                'error': f'比赛 {match_id} 不存在'
            }
        
        # 获取观察比赛的赔率数据
        target_odds = self.database.get_match_odds_by_company(match_id, company_name)
        
        if not target_odds:
            return {
                'success': False,
                'error': f'比赛 {match_id} 没有 {company_name} 的赔率数据'
            }
        
        if len(target_odds) < combination_size:
            return {
                'success': False,
                'error': f'赔率数据不足，需要至少 {combination_size} 条记录，实际只有 {len(target_odds)} 条'
            }
        
        # 生成目标组合
        target_combinations = self.generate_odds_combinations(target_odds, combination_size)
        
        logger.info(f"生成了 {len(target_combinations)} 个目标组合")
        
        # 获取所有其他比赛
        other_matches = self.database.get_all_other_matches(match_id, only_historical)

        if only_historical:
            logger.info(f"将在 {len(other_matches)} 场历史比赛中查找匹配")
        else:
            logger.info(f"将在 {len(other_matches)} 场其他比赛中查找匹配")
        
        # 分析结果
        combination_results = []
        
        for i, target_combination in enumerate(target_combinations):
            logger.info(f"分析第 {i+1}/{len(target_combinations)} 个组合")
            
            match_count = 0  # 匹配的比赛数量
            matched_matches = []  # 匹配的比赛详情
            result_stats = {'win': 0, 'draw': 0, 'loss': 0, 'unknown': 0}
            
            for other_match_id in other_matches:
                # 在该比赛中查找匹配
                match_positions = self.find_matching_combinations_in_match(
                    target_combination, other_match_id, company_name
                )
                
                if match_positions:
                    # 该比赛有匹配，计数+1（每场比赛只计数一次）
                    match_count += 1
                    
                    # 获取比赛结果
                    match_result = self.database.get_match_result(other_match_id)
                    result_type = self.calculate_match_result_type(match_result) if match_result else 'unknown'
                    result_stats[result_type] += 1
                    
                    # 记录匹配详情（每个匹配位置都记录）
                    for position in match_positions:
                        matched_matches.append({
                            'match_id': other_match_id,
                            'result_type': result_type,
                            'match_position': position,
                            'match_result': match_result
                        })
            
            combination_results.append({
                'combination_index': i,
                'combination': target_combination,
                'match_count': match_count,
                'result_stats': result_stats,
                'matched_matches': matched_matches
            })
        
        return {
            'success': True,
            'target_match_id': match_id,
            'company_name': company_name,
            'combination_size': combination_size,
            'total_combinations': len(target_combinations),
            'total_other_matches': len(other_matches),
            'results': combination_results
        }

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
时间线图表模块
用于显示博彩公司开盘时间线
"""

try:
    import matplotlib.pyplot as plt
    import matplotlib.dates as mdates
    from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
    import pandas as pd
    import numpy as np

    # 设置中文字体
    plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'Arial Unicode MS']
    plt.rcParams['axes.unicode_minus'] = False
    MATPLOTLIB_AVAILABLE = True
except ImportError:
    # 如果matplotlib不可用，创建占位符
    MATPLOTLIB_AVAILABLE = False
    print("警告: matplotlib不可用，时间线图表功能将被禁用")

from datetime import datetime, timedelta
from typing import List, Dict, Optional
import logging

logger = logging.getLogger(__name__)

class TimelineChart:
    def __init__(self, parent_frame):
        """初始化时间线图表"""
        self.parent_frame = parent_frame
        self.figure = None
        self.canvas = None
        self.ax = None
        self.matplotlib_available = MATPLOTLIB_AVAILABLE
        
    def create_timeline_chart(self, match_info: Dict, odds_data: List[Dict]) -> bool:
        """创建时间线图表"""
        if not self.matplotlib_available:
            logger.warning("matplotlib不可用，无法创建时间线图表")
            return False

        try:
            # 清除之前的图表
            if self.canvas:
                self.canvas.get_tk_widget().destroy()

            # 处理数据
            timeline_data = self._prepare_timeline_data(match_info, odds_data)

            if not timeline_data:
                logger.warning("没有有效的时间线数据")
                return False

            # 创建图表
            self.figure, self.ax = plt.subplots(figsize=(12, 8))

            # 绘制时间线
            self._plot_timeline(timeline_data, match_info)

            # 创建Canvas并嵌入到Tkinter
            self.canvas = FigureCanvasTkAgg(self.figure, self.parent_frame)
            self.canvas.draw()
            self.canvas.get_tk_widget().pack(fill='both', expand=True)

            logger.info("时间线图表创建成功")
            return True

        except Exception as e:
            logger.error(f"创建时间线图表失败: {e}")
            return False
    
    def _prepare_timeline_data(self, match_info: Dict, odds_data: List[Dict]) -> List[Dict]:
        """准备时间线数据"""
        try:
            # 获取比赛开始时间
            match_time_str = match_info.get('match_time')
            if not match_time_str:
                logger.error("比赛时间信息缺失")
                return []
            
            # 解析比赛时间
            match_time = datetime.strptime(match_time_str, "%Y-%m-%d %H:%M:%S")
            
            # 按公司分组并找到每个公司的最早开盘时间
            company_first_odds = {}
            
            for odds in odds_data:
                company_name = odds.get('company_name')
                date_str = odds.get('date')  # MM-DD格式
                time_str = odds.get('time')  # HH:MM格式
                
                if not all([company_name, date_str, time_str]):
                    continue
                
                try:
                    # 构造完整的日期时间字符串
                    year = match_time.year
                    full_datetime_str = f"{year}-{date_str} {time_str}:00"
                    odds_time = datetime.strptime(full_datetime_str, "%Y-%m-%d %H:%M:%S")
                    
                    # 记录每个公司的最早开盘时间
                    if company_name not in company_first_odds:
                        company_first_odds[company_name] = {
                            'first_time': odds_time,
                            'company_id': odds.get('company_id', ''),
                            'first_odds': {
                                'home': odds.get('home_odds'),
                                'draw': odds.get('draw_odds'),
                                'away': odds.get('away_odds'),
                                'return_rate': odds.get('return_rate')
                            }
                        }
                    else:
                        if odds_time < company_first_odds[company_name]['first_time']:
                            company_first_odds[company_name]['first_time'] = odds_time
                            company_first_odds[company_name]['first_odds'] = {
                                'home': odds.get('home_odds'),
                                'draw': odds.get('draw_odds'),
                                'away': odds.get('away_odds'),
                                'return_rate': odds.get('return_rate')
                            }
                
                except ValueError as e:
                    logger.warning(f"时间解析失败: {full_datetime_str}, 错误: {e}")
                    continue
            
            # 计算距离开赛的时间间隔
            timeline_data = []
            for company_name, data in company_first_odds.items():
                first_time = data['first_time']
                time_diff = match_time - first_time
                hours_before_match = time_diff.total_seconds() / 3600
                
                timeline_data.append({
                    'company_name': company_name,
                    'company_id': data['company_id'],
                    'first_time': first_time,
                    'hours_before_match': hours_before_match,
                    'first_odds': data['first_odds']
                })
            
            # 按开盘时间排序（最早的在前）
            timeline_data.sort(key=lambda x: x['hours_before_match'], reverse=True)
            
            logger.info(f"准备了 {len(timeline_data)} 家公司的时间线数据")
            return timeline_data
            
        except Exception as e:
            logger.error(f"准备时间线数据失败: {e}")
            return []
    
    def _plot_timeline(self, timeline_data: List[Dict], match_info: Dict):
        """绘制时间线图表"""
        if not self.matplotlib_available:
            return

        try:
            # 准备数据
            companies = [item['company_name'] for item in timeline_data]
            hours_before = [item['hours_before_match'] for item in timeline_data]
            
            # 创建颜色映射
            colors = plt.cm.Set3(np.linspace(0, 1, len(companies)))
            
            # 绘制水平条形图
            y_positions = range(len(companies))
            bars = self.ax.barh(y_positions, hours_before, color=colors, alpha=0.7, height=0.6)
            
            # 设置Y轴标签
            self.ax.set_yticks(y_positions)
            self.ax.set_yticklabels(companies)
            
            # 设置X轴
            self.ax.set_xlabel('距离开赛时间 (小时)', fontsize=12)
            self.ax.set_ylabel('博彩公司', fontsize=12)
            
            # 设置标题
            match_title = f"{match_info.get('home_team', '')} vs {match_info.get('away_team', '')}"
            match_time = match_info.get('match_time', '')
            self.ax.set_title(f'博彩公司开盘时间线\n{match_title} ({match_time})', fontsize=14, fontweight='bold')
            
            # 添加网格
            self.ax.grid(True, axis='x', alpha=0.3)
            
            # 在每个条形图上添加具体时间和赔率信息
            for i, (bar, data) in enumerate(zip(bars, timeline_data)):
                # 显示开盘时间
                first_time_str = data['first_time'].strftime('%m-%d %H:%M')
                
                # 显示首次赔率
                odds = data['first_odds']
                odds_text = f"{odds['home']:.2f} {odds['draw']:.2f} {odds['away']:.2f}"
                
                # 在条形图右侧显示信息
                text_x = bar.get_width() + max(hours_before) * 0.01
                self.ax.text(text_x, bar.get_y() + bar.get_height()/2, 
                           f'{first_time_str}\n{odds_text}', 
                           va='center', ha='left', fontsize=9)
                
                # 在条形图内显示小时数
                if bar.get_width() > max(hours_before) * 0.1:
                    self.ax.text(bar.get_width()/2, bar.get_y() + bar.get_height()/2, 
                               f'{data["hours_before_match"]:.1f}h', 
                               va='center', ha='center', fontweight='bold', color='white')
            
            # 添加开赛时间线
            self.ax.axvline(x=0, color='red', linestyle='--', linewidth=2, alpha=0.8, label='开赛时间')
            
            # 设置X轴范围
            max_hours = max(hours_before) if hours_before else 24
            self.ax.set_xlim(-max_hours * 0.05, max_hours * 1.3)
            
            # 添加图例
            self.ax.legend(loc='upper right')
            
            # 调整布局
            self.figure.tight_layout()
            
            # 添加统计信息文本
            stats_text = f"""统计信息:
• 参与公司: {len(timeline_data)} 家
• 最早开盘: {max(hours_before):.1f} 小时前
• 最晚开盘: {min(hours_before):.1f} 小时前
• 平均提前: {np.mean(hours_before):.1f} 小时"""
            
            self.ax.text(0.02, 0.98, stats_text, transform=self.ax.transAxes, 
                        verticalalignment='top', bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.8),
                        fontsize=10)
            
        except Exception as e:
            logger.error(f"绘制时间线图表失败: {e}")
            raise
    
    def clear_chart(self):
        """清除图表"""
        if self.canvas:
            self.canvas.get_tk_widget().destroy()
            self.canvas = None
        if self.figure and self.matplotlib_available:
            plt.close(self.figure)
            self.figure = None
        self.ax = None
    
    def save_chart(self, filename: str = None):
        """保存图表"""
        if not self.matplotlib_available or not self.figure:
            return False

        try:
            if filename is None:
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                filename = f"timeline_chart_{timestamp}.png"
            
            self.figure.savefig(filename, dpi=300, bbox_inches='tight')
            logger.info(f"时间线图表已保存到: {filename}")
            return True
            
        except Exception as e:
            logger.error(f"保存图表失败: {e}")
            return False

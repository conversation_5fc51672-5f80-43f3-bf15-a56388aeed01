#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
GUI功能测试脚本
测试数据库分析功能是否正常工作
"""

import sys
import os
import json
from datetime import datetime

def test_database_analyzer():
    """测试数据库分析器功能"""
    print("=== 测试数据库分析器功能 ===")
    
    try:
        from database_analyzer import DatabaseAnalyzer
        
        # 创建分析器实例
        analyzer = DatabaseAnalyzer()
        print("✓ 数据库分析器创建成功")
        
        # 测试主数据库分析
        print("正在测试主数据库分析...")
        main_result = analyzer.analyze_main_database()
        if main_result:
            print(f"✓ 主数据库分析成功，发现 {main_result.get('table_count', 0)} 个表")
        else:
            print("✗ 主数据库分析失败")
            return False
        
        # 测试联赛数据库分析
        print("正在测试联赛数据库分析...")
        league_results = analyzer.analyze_league_databases()
        if league_results:
            print(f"✓ 联赛数据库分析成功，发现 {len(league_results)} 个数据库")
        else:
            print("✗ 联赛数据库分析失败")
            return False
        
        # 测试报告生成
        print("正在测试报告生成...")
        report = analyzer.generate_comprehensive_report()
        if report:
            print("✓ 综合报告生成成功")
        else:
            print("✗ 综合报告生成失败")
            return False
        
        # 测试结果保存
        print("正在测试结果保存...")
        output_file = analyzer.save_analysis_results()
        if output_file and os.path.exists(output_file):
            print(f"✓ 结果保存成功: {output_file}")
        else:
            print("✗ 结果保存失败")
            return False
        
        print("✓ 所有数据库分析功能测试通过")
        return True
        
    except ImportError as e:
        print(f"✗ 导入错误: {e}")
        return False
    except Exception as e:
        print(f"✗ 测试过程中出错: {e}")
        return False

def test_gui_dependencies():
    """测试GUI依赖项"""
    print("\n=== 测试GUI依赖项 ===")
    
    dependencies = [
        ('PyQt5.QtWidgets', 'PyQt5图形界面库'),
        ('PyQt5.QtCore', 'PyQt5核心模块'),
        ('PyQt5.QtGui', 'PyQt5GUI模块'),
        ('pandas', '数据处理库'),
        ('numpy', '数值计算库'),
        ('json', 'JSON处理'),
        ('threading', '多线程支持'),
        ('datetime', '日期时间处理')
    ]
    
    all_ok = True
    
    for module_name, description in dependencies:
        try:
            __import__(module_name)
            print(f"✓ {description} ({module_name})")
        except ImportError:
            print(f"✗ {description} ({module_name}) - 未安装")
            all_ok = False
    
    return all_ok

def test_data_files():
    """测试数据文件是否存在"""
    print("\n=== 测试数据文件 ===")
    
    # 检查主数据库
    main_db_path = "../odds_data.db"
    if os.path.exists(main_db_path):
        print(f"✓ 主数据库存在: {main_db_path}")
        main_db_ok = True
    else:
        print(f"✗ 主数据库不存在: {main_db_path}")
        main_db_ok = False
    
    # 检查联赛数据库目录
    league_db_dir = "../league_databases"
    if os.path.exists(league_db_dir):
        print(f"✓ 联赛数据库目录存在: {league_db_dir}")
        
        # 检查具体的联赛数据库文件
        league_files = [
            "bundesliga.db",
            "premier_league.db", 
            "serie_a.db",
            "la_liga.db",
            "ligue_1.db"
        ]
        
        league_count = 0
        for league_file in league_files:
            league_path = os.path.join(league_db_dir, league_file)
            if os.path.exists(league_path):
                print(f"  ✓ {league_file}")
                league_count += 1
            else:
                print(f"  ✗ {league_file}")
        
        print(f"联赛数据库文件: {league_count}/{len(league_files)}")
        league_db_ok = league_count > 0
    else:
        print(f"✗ 联赛数据库目录不存在: {league_db_dir}")
        league_db_ok = False
    
    return main_db_ok and league_db_ok

def generate_test_report():
    """生成测试报告"""
    print("\n=== 生成测试报告 ===")
    
    # 运行所有测试
    deps_ok = test_gui_dependencies()
    data_ok = test_data_files()
    analyzer_ok = test_database_analyzer()
    
    # 生成报告
    report = {
        "test_time": datetime.now().isoformat(),
        "test_results": {
            "dependencies": deps_ok,
            "data_files": data_ok,
            "database_analyzer": analyzer_ok
        },
        "overall_status": "PASS" if all([deps_ok, data_ok, analyzer_ok]) else "FAIL",
        "recommendations": []
    }
    
    # 生成建议
    if not deps_ok:
        report["recommendations"].append("请安装缺失的Python依赖包")
    if not data_ok:
        report["recommendations"].append("请确保数据库文件存在且路径正确")
    if not analyzer_ok:
        report["recommendations"].append("请检查数据库分析器模块是否正常")
    
    if report["overall_status"] == "PASS":
        report["recommendations"].append("所有测试通过，GUI应该可以正常使用")
    
    # 保存报告
    report_file = f"gui_test_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
    with open(report_file, 'w', encoding='utf-8') as f:
        json.dump(report, f, ensure_ascii=False, indent=2)
    
    print(f"\n测试报告已保存到: {report_file}")
    
    # 输出总结
    print(f"\n=== 测试总结 ===")
    print(f"整体状态: {report['overall_status']}")
    print(f"依赖项检查: {'通过' if deps_ok else '失败'}")
    print(f"数据文件检查: {'通过' if data_ok else '失败'}")
    print(f"分析器功能: {'通过' if analyzer_ok else '失败'}")
    
    if report["recommendations"]:
        print(f"\n建议:")
        for i, rec in enumerate(report["recommendations"], 1):
            print(f"  {i}. {rec}")
    
    return report

def main():
    """主函数"""
    print("🏈 赔率价值分析系统 - GUI功能测试")
    print("=" * 50)
    
    # 检查当前目录
    current_dir = os.getcwd()
    print(f"当前目录: {current_dir}")
    
    if not current_dir.endswith("赔率分析"):
        print("警告: 请在'赔率分析'目录下运行此脚本")
        print("正在尝试切换目录...")
        try:
            os.chdir("赔率分析")
            print("✓ 目录切换成功")
        except:
            print("✗ 目录切换失败，请手动切换到'赔率分析'目录")
            return
    
    # 运行测试并生成报告
    report = generate_test_report()
    
    # 根据测试结果给出启动建议
    if report["overall_status"] == "PASS":
        print(f"\n🎉 所有测试通过！")
        print(f"您可以安全地启动GUI:")
        print(f"  方法1: 双击 start_gui.bat")
        print(f"  方法2: python odds_analysis_gui.py")
    else:
        print(f"\n⚠️  测试发现问题，请先解决后再启动GUI")
        print(f"详细信息请查看测试报告")

if __name__ == "__main__":
    main()

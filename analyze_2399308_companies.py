#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分析比赛2399308的20家目标博彩公司数据
"""

import logging
from enhanced_odds_scraper import EnhancedOddsScraper

# 配置日志
logging.basicConfig(level=logging.WARNING)  # 只显示警告和错误，减少输出
logger = logging.getLogger(__name__)

def analyze_target_companies_data(match_id):
    """分析目标博彩公司的数据"""
    print(f"🎯 分析比赛 {match_id} 的目标博彩公司数据")
    print("=" * 80)
    
    # 创建抓取器
    scraper = EnhancedOddsScraper()
    
    try:
        # 使用新方案直接抓取JavaScript数据
        odds_records = scraper.parse_company_odds_from_js_direct(match_id)
        
        if not odds_records:
            print("❌ 未能获取到任何数据")
            return
        
        print(f"📊 总共解析到 {len(odds_records)} 条赔率记录")
        
        # 按公司分组
        company_data = {}
        for record in odds_records:
            company = record.get('company_name')
            if company:
                if company not in company_data:
                    company_data[company] = []
                company_data[company].append(record)
        
        print(f"📈 涉及 {len(company_data)} 家不同的博彩公司")
        
        # 定义目标公司列表（根据config.py中的已知公司）
        target_companies = [
            '竞彩官方', 'bet365', '威廉希尔', '立博', '伟德', '易胜博', 
            'betfair', 'pinnacle', 'bwin', 'Interwetten', '澳门', 
            '香港马会', '金宝博', '利记', '明升'
        ]
        
        print(f"\n🎯 目标公司分析 (共{len(target_companies)}家):")
        print("=" * 80)
        
        found_companies = []
        missing_companies = []
        
        for target in target_companies:
            if target in company_data:
                found_companies.append(target)
                records = company_data[target]
                
                print(f"\n✅ {target}:")
                print(f"   记录数: {len(records)}")
                
                # 显示所有记录的赔率
                for i, record in enumerate(records, 1):
                    home = record.get('home_odds', 'N/A')
                    draw = record.get('draw_odds', 'N/A')
                    away = record.get('away_odds', 'N/A')
                    date = record.get('date', 'unknown')
                    time = record.get('time', 'unknown')
                    print(f"   记录{i}: {home} {draw} {away} ({date} {time})")
            else:
                missing_companies.append(target)
        
        print(f"\n❌ 未找到的目标公司 ({len(missing_companies)}家):")
        for company in missing_companies:
            print(f"   - {company}")
        
        # 显示所有找到的公司（包括非目标公司）
        print(f"\n📋 所有找到的公司 ({len(company_data)}家):")
        print("=" * 80)
        
        all_companies = sorted(company_data.keys())
        for i, company in enumerate(all_companies, 1):
            records = company_data[company]
            latest_record = records[-1]  # 最后一条记录
            home = latest_record.get('home_odds', 'N/A')
            draw = latest_record.get('draw_odds', 'N/A')
            away = latest_record.get('away_odds', 'N/A')
            
            # 标记是否为目标公司
            is_target = "🎯" if company in target_companies else "  "
            
            print(f"{is_target} {i:2d}. {company:20s}: {home} {draw} {away} ({len(records)}条记录)")
        
        # 统计总结
        print(f"\n📊 统计总结:")
        print("=" * 80)
        print(f"目标公司总数: {len(target_companies)}")
        print(f"找到目标公司: {len(found_companies)}")
        print(f"缺失目标公司: {len(missing_companies)}")
        print(f"目标公司覆盖率: {len(found_companies)/len(target_companies)*100:.1f}%")
        print(f"总公司数: {len(company_data)}")
        print(f"总记录数: {len(odds_records)}")
        
        # 检查是否有重要的公司（竞猜官方、香港马会）
        important_companies = ['竞彩官方', '香港马会']
        print(f"\n🔍 重要公司检查:")
        for company in important_companies:
            if company in company_data:
                records = company_data[company]
                latest = records[-1]
                print(f"✅ {company}: {latest.get('home_odds')} {latest.get('draw_odds')} {latest.get('away_odds')} ({len(records)}条记录)")
            else:
                print(f"❌ {company}: 未找到")
        
        # 查找可能的竞猜官方和香港马会的其他名称
        print(f"\n🔍 查找可能的其他名称:")
        lottery_keywords = ['lottery', 'official', '竞彩', '竞猜']
        hk_keywords = ['hk', 'hong kong', 'jockey', '香港', '马会']
        
        for company in company_data.keys():
            company_lower = company.lower()
            
            # 检查竞猜官方相关
            for keyword in lottery_keywords:
                if keyword in company_lower:
                    records = company_data[company]
                    latest = records[-1]
                    print(f"🎲 可能的竞猜官方: {company} -> {latest.get('home_odds')} {latest.get('draw_odds')} {latest.get('away_odds')}")
                    break
            
            # 检查香港马会相关
            for keyword in hk_keywords:
                if keyword in company_lower:
                    records = company_data[company]
                    latest = records[-1]
                    print(f"🏇 可能的香港马会: {company} -> {latest.get('home_odds')} {latest.get('draw_odds')} {latest.get('away_odds')}")
                    break
        
    except Exception as e:
        print(f"❌ 分析失败: {e}")
        logger.error(f"分析比赛 {match_id} 失败: {e}")

if __name__ == "__main__":
    analyze_target_companies_data("2399308")

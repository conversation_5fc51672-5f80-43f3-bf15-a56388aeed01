#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最终测试脚本
验证更新后的抓取系统是否能正确处理新老比赛
"""

from bai_fixed import MatchAnalyzer

def test_multiple_matches():
    """测试多个不同年代的比赛"""
    analyzer = MatchAnalyzer()
    
    test_cases = [
        {
            'match_id': '2598314',
            'description': '较新比赛 - 应该能获取多家公司数据'
        },
        {
            'match_id': '2804677', 
            'description': '较老比赛 - 测试传统方法的有效性'
        },
        {
            'match_id': '2500000',
            'description': '更老比赛 - 测试极限情况'
        }
    ]
    
    results = []
    
    for test_case in test_cases:
        match_id = test_case['match_id']
        description = test_case['description']
        
        print(f"\n{'='*60}")
        print(f"🧪 测试: {description}")
        print(f"比赛ID: {match_id}")
        print(f"{'='*60}")
        
        try:
            # 测试数据抓取
            odds_data = analyzer.scrape_match_odds(match_id)
            
            if odds_data and odds_data['companies']:
                companies = odds_data['companies']
                company_count = len(companies)
                
                print(f"✅ 成功抓取 {company_count} 家公司数据")
                
                # 显示前5家公司的数据
                for i, (company, odds) in enumerate(companies.items()):
                    if i >= 5:
                        break
                    print(f"  📊 {company}: {odds['home_odds']:.2f} {odds['draw_odds']:.2f} {odds['away_odds']:.2f}")
                
                if company_count > 5:
                    print(f"  ... 还有 {company_count - 5} 家公司")
                
                # 测试筛选功能
                if '竞彩官方' in companies:
                    result1, msg1 = analyzer.analyze_betting_attitude2(match_id, 'home', 2, '竞彩官方')
                    print(f"🔍 竞彩官方筛选: {'✅通过' if result1 else '❌未通过'}")
                
                if '香港马会' in companies:
                    result2, msg2 = analyzer.analyze_betting_attitude2(match_id, 'home', 2, '香港马会')
                    print(f"🔍 香港马会筛选: {'✅通过' if result2 else '❌未通过'}")
                
                results.append({
                    'match_id': match_id,
                    'success': True,
                    'company_count': company_count,
                    'companies': list(companies.keys())
                })
                
            else:
                print("❌ 未能获取赔率数据")
                results.append({
                    'match_id': match_id,
                    'success': False,
                    'company_count': 0,
                    'companies': []
                })
                
        except Exception as e:
            print(f"❌ 测试失败: {e}")
            results.append({
                'match_id': match_id,
                'success': False,
                'error': str(e)
            })
    
    # 总结结果
    print(f"\n{'='*60}")
    print("📋 测试总结")
    print(f"{'='*60}")
    
    successful_tests = sum(1 for r in results if r['success'])
    total_tests = len(results)
    
    print(f"成功测试: {successful_tests}/{total_tests}")
    
    for result in results:
        match_id = result['match_id']
        if result['success']:
            company_count = result['company_count']
            print(f"✅ {match_id}: {company_count} 家公司")
        else:
            print(f"❌ {match_id}: 失败")
    
    return results

def test_specific_companies():
    """测试特定公司的数据获取"""
    analyzer = MatchAnalyzer()
    
    print(f"\n{'='*60}")
    print("🎯 测试特定公司数据获取")
    print(f"{'='*60}")
    
    # 使用一个已知有数据的比赛
    match_id = '2804677'
    
    target_companies = ['竞彩官方', '香港马会', 'bet365', '威廉希尔', 'pinnacle']
    
    odds_data = analyzer.scrape_match_odds(match_id)
    
    if odds_data and odds_data['companies']:
        companies = odds_data['companies']
        
        print(f"📊 比赛 {match_id} 可用公司: {list(companies.keys())}")
        
        for target_company in target_companies:
            if target_company in companies:
                odds = companies[target_company]
                print(f"✅ {target_company}: {odds['home_odds']:.2f} {odds['draw_odds']:.2f} {odds['away_odds']:.2f}")
                
                # 测试博彩态度2筛选
                result, msg = analyzer.analyze_betting_attitude2(match_id, 'home', 2, target_company)
                print(f"   筛选结果: {'✅通过' if result else '❌未通过'}")
            else:
                print(f"❌ {target_company}: 数据不可用")
    else:
        print("❌ 无法获取比赛数据")

if __name__ == "__main__":
    print("🎯 最终测试 - 验证更新后的抓取系统")
    print("=" * 60)
    
    # 测试多个比赛
    test_multiple_matches()
    
    # 测试特定公司
    test_specific_companies()
    
    print(f"\n🎉 测试完成！")
    print("如果看到成功抓取的数据，说明系统工作正常。")

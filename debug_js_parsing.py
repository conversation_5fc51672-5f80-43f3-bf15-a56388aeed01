#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试JavaScript解析
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from enhanced_odds_scraper import EnhancedOddsScraper
import re
import logging

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def debug_js_parsing():
    """调试JavaScript解析"""
    print("🔍 调试JavaScript解析 - 比赛2399071")
    print("=" * 60)
    
    scraper = EnhancedOddsScraper()
    match_id = "2399071"
    
    # JavaScript数据源URL
    js_url = f"https://1x2d.titan007.com/{match_id}.js"
    
    print(f"📋 获取JavaScript: {js_url}")
    
    content = scraper.get_page_content(js_url)
    
    if not content:
        print(f"❌ 无法获取JavaScript内容")
        return
        
    print(f"✅ JavaScript可访问，内容长度: {len(content)}")
    
    # 查找game数组的不同模式
    patterns = [
        r'var game=Array\("([^"]+)"\);',
        r'var game=Array\(([^)]+)\);',
        r'var game=\[([^\]]+)\];',
        r'game=Array\("([^"]+)"\);',
        r'game=Array\(([^)]+)\);'
    ]
    
    print(f"\n🔍 测试不同的正则表达式模式:")
    
    for i, pattern in enumerate(patterns, 1):
        print(f"  模式 {i}: {pattern}")
        match = re.search(pattern, content)
        if match:
            print(f"    ✅ 匹配成功！")
            game_data = match.group(1)
            print(f"    数据长度: {len(game_data)}")
            print(f"    前100个字符: {game_data[:100]}")
            
            # 尝试解析第一个公司数据
            if '|' in game_data:
                first_company = game_data.split('","')[0].replace('"', '')
                parts = first_company.split('|')
                if len(parts) >= 3:
                    print(f"    第一个公司: ID={parts[0]}, Record={parts[1]}, Name={parts[2]}")
            break
        else:
            print(f"    ❌ 无匹配")
    
    # 手动查找game关键词
    print(f"\n🔍 手动查找game关键词:")
    game_positions = []
    start = 0
    while True:
        pos = content.find('game', start)
        if pos == -1:
            break
        game_positions.append(pos)
        start = pos + 1
        if len(game_positions) >= 10:  # 只显示前10个
            break
    
    print(f"  找到 {len(game_positions)} 个'game'关键词位置")
    
    for i, pos in enumerate(game_positions[:5], 1):
        context = content[max(0, pos-20):pos+100]
        print(f"    {i}. 位置 {pos}: ...{context}...")
    
    # 查找Array关键词
    print(f"\n🔍 查找Array关键词:")
    if 'Array(' in content:
        array_pos = content.find('Array(')
        context = content[max(0, array_pos-50):array_pos+200]
        print(f"  找到Array(，上下文: ...{context}...")
    else:
        print(f"  ❌ 未找到Array(")

if __name__ == "__main__":
    debug_js_parsing()

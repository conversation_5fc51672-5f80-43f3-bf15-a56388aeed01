#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
赔率组合观察 - 功能测试脚本
"""

import sys
import os

# 添加父目录到Python路径
parent_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, parent_dir)

from odds_combination_database import OddsCombinationDatabase
from odds_combination_analyzer import OddsCombinationAnalyzer

def test_database_connection():
    """测试数据库连接"""
    print("=== 测试数据库连接 ===")
    try:
        db = OddsCombinationDatabase()
        print(f"✅ 数据库连接成功: {db.db_path}")
        
        # 测试获取可用数据库
        databases = db.get_available_databases()
        print(f"✅ 找到 {len(databases)} 个数据库文件:")
        for db_path in databases:
            print(f"   - {os.path.basename(db_path)}")
        
        return True
    except Exception as e:
        print(f"❌ 数据库连接失败: {e}")
        return False

def test_get_companies():
    """测试获取博彩公司列表"""
    print("\n=== 测试获取博彩公司列表 ===")
    try:
        db = OddsCombinationDatabase()
        companies = db.get_available_companies()
        print(f"✅ 找到 {len(companies)} 家博彩公司:")
        for company in companies[:10]:  # 只显示前10家
            print(f"   - {company}")
        if len(companies) > 10:
            print(f"   ... 还有 {len(companies) - 10} 家公司")
        
        return True
    except Exception as e:
        print(f"❌ 获取博彩公司列表失败: {e}")
        return False

def test_match_data():
    """测试获取比赛数据"""
    print("\n=== 测试获取比赛数据 ===")
    try:
        db = OddsCombinationDatabase()
        
        # 测试比赛ID 2701757
        match_id = "2701757"
        company = "bet365"
        
        print(f"测试比赛ID: {match_id}")
        print(f"测试博彩公司: {company}")
        
        # 检查比赛是否存在
        exists = db.check_match_exists(match_id)
        print(f"比赛存在: {exists}")
        
        if exists:
            # 获取比赛信息
            match_info = db.get_match_info(match_id)
            if match_info:
                print(f"比赛信息: {match_info['home_team']} vs {match_info['away_team']}")
            
            # 获取赔率数据
            odds_data = db.get_match_odds_by_company(match_id, company)
            print(f"✅ 获取到 {len(odds_data)} 条赔率数据")
            
            if odds_data:
                print("前3条赔率数据:")
                for i, odds in enumerate(odds_data[:3]):
                    print(f"   {i+1}. {odds['date']} {odds['time']} - "
                          f"主:{odds['home_odds']} 平:{odds['draw_odds']} 客:{odds['away_odds']}")
        
        return True
    except Exception as e:
        print(f"❌ 获取比赛数据失败: {e}")
        return False

def test_analyzer():
    """测试分析器"""
    print("\n=== 测试分析器 ===")
    try:
        analyzer = OddsCombinationAnalyzer()
        
        # 测试参数
        match_id = "2701757"
        company = "bet365"
        combination_size = 3
        
        print(f"开始分析比赛 {match_id}...")
        print(f"博彩公司: {company}")
        print(f"组合大小: {combination_size}")
        
        # 执行分析（这可能需要一些时间）
        result = analyzer.analyze_odds_combinations(match_id, company, combination_size)
        
        if result['success']:
            print(f"✅ 分析成功!")
            print(f"   - 总组合数: {result['total_combinations']}")
            print(f"   - 其他比赛数: {result['total_other_matches']}")
            
            # 显示前3个组合的结果
            print("前3个组合的匹配结果:")
            for i, combo_result in enumerate(result['results'][:3]):
                stats = combo_result['result_stats']
                print(f"   组合 {i+1}: 匹配 {combo_result['match_count']} 场 "
                      f"(胜:{stats['win']} 平:{stats['draw']} 负:{stats['loss']})")
        else:
            print(f"❌ 分析失败: {result['error']}")
            return False
        
        return True
    except Exception as e:
        print(f"❌ 分析器测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("赔率组合观察工具 - 功能测试")
    print("=" * 50)
    
    tests = [
        test_database_connection,
        test_get_companies,
        test_match_data,
        test_analyzer
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"❌ 测试异常: {e}")
    
    print("\n" + "=" * 50)
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！程序可以正常使用。")
    else:
        print("⚠️  部分测试失败，请检查相关功能。")

if __name__ == '__main__':
    main()

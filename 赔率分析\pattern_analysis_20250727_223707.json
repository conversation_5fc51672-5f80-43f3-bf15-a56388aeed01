{
  "pattern_statistics": {
    "home": {
      "explosive": {
        "sample_size": 12,
        "accuracy": 0.08333333333333333,
        "confidence_interval": [
          0.014865094404917095,
          0.35387991114111694
        ],
        "result_distribution": {
          "draw": 0.9166666666666666,
          "home_win": 0.08333333333333333
        },
        "result_counts": {
          "draw": 11,
          "home_win": 1
        }
      },
      "rebound": {
        "sample_size": 12,
        "accuracy": 0.4166666666666667,
        "confidence_interval": [
          0.19326031365875665,
          0.6804886874504503
        ],
        "result_distribution": {
          "home_win": 0.4166666666666667,
          "draw": 0.3333333333333333,
          "away_win": 0.25
        },
        "result_counts": {
          "home_win": 5,
          "draw": 4,
          "away_win": 3
        }
      },
      "other": {
        "sample_size": 19,
        "accuracy": 0.7894736842105263,
        "confidence_interval": [
          0.5666572155618583,
          0.9149232336708375
        ],
        "result_distribution": {
          "home_win": 0.7894736842105263,
          "draw": 0.15789473684210525,
          "away_win": 0.05263157894736842
        },
        "result_counts": {
          "home_win": 15,
          "draw": 3,
          "away_win": 1
        }
      },
      "down_pressure": {
        "sample_size": 4,
        "accuracy": 0.75,
        "confidence_interval": [
          0.30064184258240184,
          0.9544127391902995
        ],
        "result_distribution": {
          "home_win": 0.75,
          "away_win": 0.25
        },
        "result_counts": {
          "home_win": 3,
          "away_win": 1
        }
      }
    },
    "draw": {
      "other": {
        "sample_size": 27,
        "accuracy": 0.4074074074074074,
        "confidence_interval": [
          0.24514757074395555,
          0.592732990319353
        ],
        "result_distribution": {
          "home_win": 0.5555555555555556,
          "draw": 0.4074074074074074,
          "away_win": 0.037037037037037035
        },
        "result_counts": {
          "home_win": 15,
          "draw": 11,
          "away_win": 1
        }
      },
      "down_pressure": {
        "sample_size": 3,
        "accuracy": 0.3333333333333333,
        "confidence_interval": [
          0.06149194472039615,
          0.7923403991979523
        ],
        "result_distribution": {
          "home_win": 0.6666666666666666,
          "draw": 0.3333333333333333
        },
        "result_counts": {
          "home_win": 2,
          "draw": 1
        }
      },
      "cooling": {
        "sample_size": 8,
        "accuracy": 0.125,
        "confidence_interval": [
          0.02241749145005667,
          0.4708881822128535
        ],
        "result_distribution": {
          "home_win": 0.625,
          "away_win": 0.25,
          "draw": 0.125
        },
        "result_counts": {
          "home_win": 5,
          "away_win": 2,
          "draw": 1
        }
      },
      "stable": {
        "sample_size": 3,
        "accuracy": 0.3333333333333333,
        "confidence_interval": [
          0.06149194472039615,
          0.7923403991979523
        ],
        "result_distribution": {
          "home_win": 0.6666666666666666,
          "draw": 0.3333333333333333
        },
        "result_counts": {
          "home_win": 2,
          "draw": 1
        }
      },
      "rebound": {
        "sample_size": 5,
        "accuracy": 0.4,
        "confidence_interval": [
          0.11762077423264788,
          0.7692757187239869
        ],
        "result_distribution": {
          "draw": 0.4,
          "away_win": 0.4,
          "home_win": 0.2
        },
        "result_counts": {
          "draw": 2,
          "away_win": 2,
          "home_win": 1
        }
      }
    },
    "away": {
      "other": {
        "sample_size": 21,
        "accuracy": 0.09523809523809523,
        "confidence_interval": [
          0.026518767438074548,
          0.28914139073918255
        ],
        "result_distribution": {
          "home_win": 0.6666666666666666,
          "draw": 0.23809523809523808,
          "away_win": 0.09523809523809523
        },
        "result_counts": {
          "home_win": 14,
          "draw": 5,
          "away_win": 2
        }
      },
      "cooling": {
        "sample_size": 10,
        "accuracy": 0.2,
        "confidence_interval": [
          0.05668215145437519,
          0.5098375284633583
        ],
        "result_distribution": {
          "draw": 0.8,
          "away_win": 0.2
        },
        "result_counts": {
          "draw": 8,
          "away_win": 2
        }
      },
      "down_pressure": {
        "sample_size": 12,
        "accuracy": 0.08333333333333333,
        "confidence_interval": [
          0.014865094404917095,
          0.35387991114111694
        ],
        "result_distribution": {
          "home_win": 0.75,
          "draw": 0.16666666666666666,
          "away_win": 0.08333333333333333
        },
        "result_counts": {
          "home_win": 9,
          "draw": 2,
          "away_win": 1
        }
      },
      "explosive": {
        "sample_size": 3,
        "accuracy": 0,
        "confidence_interval": [
          5.551115123125783e-17,
          0.5614970317550454
        ],
        "result_distribution": {
          "home_win": 0.6666666666666666,
          "draw": 0.3333333333333333
        },
        "result_counts": {
          "home_win": 2,
          "draw": 1
        }
      }
    },
    "combined": {
      "other": {
        "sample_size": 22,
        "result_distribution": {
          "home_win": 0.6818181818181818,
          "draw": 0.22727272727272727,
          "away_win": 0.09090909090909091
        },
        "result_counts": {
          "home_win": 15,
          "draw": 5,
          "away_win": 2
        },
        "entropy": 1.1770225546116342
      },
      "mixed": {
        "sample_size": 18,
        "result_distribution": {
          "home_win": 0.5,
          "draw": 0.4444444444444444,
          "away_win": 0.05555555555555555
        },
        "result_counts": {
          "home_win": 9,
          "draw": 8,
          "away_win": 1
        },
        "entropy": 1.2516291673878228
      },
      "down_pressure": {
        "sample_size": 3,
        "result_distribution": {
          "home_win": 0.6666666666666666,
          "draw": 0.3333333333333333
        },
        "result_counts": {
          "home_win": 2,
          "draw": 1
        },
        "entropy": 0.9182958340544896
      },
      "rebound": {
        "sample_size": 3,
        "result_distribution": {
          "draw": 0.6666666666666666,
          "away_win": 0.3333333333333333
        },
        "result_counts": {
          "draw": 2,
          "away_win": 1
        },
        "entropy": 0.9182958340544896
      }
    }
  },
  "significance_tests": {
    "home": {
      "chi2_statistic": 28.09472934472935,
      "p_value": 0.001743261421039656,
      "degrees_of_freedom": 10,
      "is_significant": 
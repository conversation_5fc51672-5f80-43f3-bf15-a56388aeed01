#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试赔率数据保存修复
"""

import sys
import os

def test_save_odds_method():
    """测试赔率数据保存方法"""
    
    print("🔍 测试赔率数据保存方法")
    print("=" * 50)
    
    try:
        from database import OddsDatabase
        
        # 查找数据库文件
        db_files = [f for f in os.listdir('.') if f.endswith('.db')]
        if not db_files:
            print("❌ 未找到数据库文件")
            return
        
        db_path = db_files[0]
        print(f"📂 使用数据库: {db_path}")
        
        # 创建数据库实例
        db = OddsDatabase(db_path)
        print("✅ 数据库实例创建成功")
        
        # 检查方法签名
        import inspect
        sig = inspect.signature(db.save_odds_data)
        print(f"📋 save_odds_data 方法签名: {sig}")
        
        params = list(sig.parameters.keys())
        print(f"📊 参数列表: {params}")
        
        # 验证参数
        if 'match_id' in params:
            print("✅ match_id 参数存在")
        else:
            print("❌ match_id 参数不存在")
            
        if 'odds_data' in params:
            print("✅ odds_data 参数存在")
        else:
            print("❌ odds_data 参数不存在")
        
        # 模拟数据
        test_match_id = "test_match_123"
        test_odds_data = [
            {
                'match_id': test_match_id,
                'company_name': 'bet365',
                'company_id': '281',
                'date': '2025-01-01',
                'time': '19:00:00',
                'home_odds': 2.10,
                'draw_odds': 3.20,
                'away_odds': 3.50,
                'return_rate': 95.2,
                'kelly_home': 0.693,
                'kelly_draw': 1.056,
                'kelly_away': 1.155
            },
            {
                'match_id': test_match_id,
                'company_name': 'pinnacle',
                'company_id': '474',
                'date': '2025-01-01',
                'time': '19:30:00',
                'home_odds': 2.05,
                'draw_odds': 3.15,
                'away_odds': 3.60,
                'return_rate': 95.8,
                'kelly_home': 0.677,
                'kelly_draw': 1.040,
                'kelly_away': 1.188
            }
        ]
        
        print(f"\n🧪 测试数据保存:")
        print(f"   比赛ID: {test_match_id}")
        print(f"   赔率记录数: {len(test_odds_data)}")
        
        # 测试方法调用（不实际保存）
        print(f"\n📝 正确的调用方式:")
        print(f"   db.save_odds_data(match_id, odds_data)")
        print(f"   db.save_odds_data('{test_match_id}', odds_data_list)")
        
        print(f"\n❌ 错误的调用方式:")
        print(f"   for record in odds_data:")
        print(f"       db.save_odds_data(record)  # 缺少 match_id 参数")
        
        print(f"\n✅ 修复后的调用方式:")
        print(f"   db.save_odds_data(match_id, result['odds_data'])")
        
    except ImportError as e:
        print(f"❌ 导入数据库模块失败: {e}")
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")

def test_ui_fix():
    """测试UI修复"""
    
    print(f"\n🔧 测试UI修复:")
    print("-" * 30)
    
    try:
        # 检查UI文件
        with open('odds_scraper_ui.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查修复后的代码
        if 'current_db.save_odds_data(match_id, result[\'odds_data\'])' in content:
            print("✅ 赔率数据保存调用已修复")
        else:
            print("❌ 赔率数据保存调用未修复")
        
        # 检查是否还有错误的调用
        if 'for odds_record in result[\'odds_data\']:' in content and 'save_odds_data(odds_record)' in content:
            print("❌ 仍存在错误的循环调用")
        else:
            print("✅ 错误的循环调用已移除")
        
        print("✅ UI修复检查完成")
        
    except Exception as e:
        print(f"❌ UI修复检查失败: {e}")

def test_complete_flow():
    """测试完整流程"""
    
    print(f"\n🎯 完整流程测试:")
    print("-" * 30)
    
    print("📋 修复的问题:")
    print("1. save_odds_data 方法需要两个参数: match_id, odds_data")
    print("2. 不应该循环调用 save_odds_data")
    print("3. 应该一次性传递整个 odds_data 列表")
    
    print(f"\n📊 修复前后对比:")
    print("修复前（错误）:")
    print("   for odds_record in result['odds_data']:")
    print("       current_db.save_odds_data(odds_record)  # 缺少 match_id")
    
    print("\n修复后（正确）:")
    print("   current_db.save_odds_data(match_id, result['odds_data'])")
    
    print(f"\n🎉 预期效果:")
    print("- 不再出现 'missing 1 required positional argument' 错误")
    print("- 赔率数据能够正确保存到数据库")
    print("- 更新过程能够顺利完成")

if __name__ == "__main__":
    test_save_odds_method()
    test_ui_fix()
    test_complete_flow()
    
    print(f"\n🚀 修复完成！")
    print("现在可以重新测试完赛后更新功能：")
    print("python odds_scraper_ui.py")

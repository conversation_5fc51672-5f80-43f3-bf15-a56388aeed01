#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
查找测试用的比赛ID
"""

import sys
import os

# 添加父目录到Python路径
parent_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, parent_dir)

from odds_combination_database import OddsCombinationDatabase

def find_test_match():
    """查找适合测试的比赛"""
    print("查找测试用比赛...")
    
    try:
        db = OddsCombinationDatabase()
        
        # 查找有bet365数据且赔率数据较多的比赛
        import sqlite3
        with sqlite3.connect(db.db_path) as conn:
            cursor = conn.cursor()
            
            # 查找bet365的比赛，按赔率数据数量排序
            cursor.execute('''
                SELECT match_id, COUNT(*) as odds_count
                FROM odds 
                WHERE company_name = 'bet365'
                  AND home_odds IS NOT NULL 
                  AND draw_odds IS NOT NULL 
                  AND away_odds IS NOT NULL
                GROUP BY match_id
                HAVING COUNT(*) >= 3
                ORDER BY COUNT(*) DESC
                LIMIT 10
            ''')
            
            matches = cursor.fetchall()
            
            print(f"找到 {len(matches)} 个有bet365数据的比赛:")
            print("比赛ID     赔率数据条数")
            print("-" * 25)
            
            for match_id, count in matches:
                print(f"{match_id:10s} {count:6d}")
            
            if matches:
                # 选择第一个比赛进行详细检查
                test_match_id = matches[0][0]
                print(f"\n选择比赛 {test_match_id} 进行详细检查:")
                
                # 获取比赛信息
                match_info = db.get_match_info(test_match_id)
                if match_info:
                    print(f"  比赛: {match_info['home_team']} vs {match_info['away_team']}")
                    print(f"  联赛: {match_info.get('league', '未知')}")
                    print(f"  时间: {match_info.get('match_time', '未知')}")
                
                # 获取赔率数据
                odds_data = db.get_match_odds_by_company(test_match_id, 'bet365')
                print(f"  bet365赔率数据: {len(odds_data)} 条")
                
                if len(odds_data) >= 3:
                    print(f"  ✅ 适合测试（可生成 {len(odds_data)-3+1} 个3组组合）")
                    
                    # 显示前几条赔率数据
                    print(f"  前3条赔率数据:")
                    for i, odds in enumerate(odds_data[:3]):
                        print(f"    {i+1}: {odds['date']} {odds['time']} - {odds['home_odds']} {odds['draw_odds']} {odds['away_odds']}")
                    
                    return test_match_id
                else:
                    print(f"  ❌ 数据不足")
            
            return None
            
    except Exception as e:
        print(f"❌ 查找失败: {e}")
        return None

if __name__ == '__main__':
    test_match_id = find_test_match()
    if test_match_id:
        print(f"\n推荐测试比赛ID: {test_match_id}")
    else:
        print("\n未找到合适的测试比赛")

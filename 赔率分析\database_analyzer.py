#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据库结构深度分析器
用于分析赔率数据的结构特征，为后续建模做准备
"""

import sqlite3
import pandas as pd
import numpy as np
import os
from datetime import datetime, timedelta
import json
from collections import defaultdict, Counter
import re

class DatabaseAnalyzer:
    def __init__(self, main_db_path="../odds_data.db", league_db_dir="../league_databases"):
        self.main_db_path = main_db_path
        self.league_db_dir = league_db_dir
        self.analysis_results = {}
        
    def connect_readonly(self, db_path):
        """只读方式连接数据库"""
        return sqlite3.connect(f"file:{db_path}?mode=ro", uri=True)
    
    def analyze_odds_table_structure(self, db_path, db_name="main"):
        """分析odds表的结构和数据特征"""
        print(f"\n=== 分析 {db_name} 数据库的odds表 ===")
        
        try:
            conn = self.connect_readonly(db_path)
            cursor = conn.cursor()
            
            # 检查是否存在odds表
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='odds'")
            if not cursor.fetchone():
                print(f"  {db_name} 数据库中没有odds表")
                conn.close()
                return None
            
            # 获取表结构
            cursor.execute("PRAGMA table_info(odds)")
            columns = cursor.fetchall()
            
            print("  字段结构:")
            for col in columns:
                print(f"    {col[1]} ({col[2]})")
            
            # 获取数据量
            cursor.execute("SELECT COUNT(*) FROM odds")
            total_count = cursor.fetchone()[0]
            print(f"  总记录数: {total_count}")
            
            if total_count == 0:
                conn.close()
                return None
            
            # 分析公司数据
            cursor.execute("SELECT DISTINCT company_name FROM odds WHERE company_name IS NOT NULL")
            companies = [row[0] for row in cursor.fetchall()]
            print(f"  博彩公司数量: {len(companies)}")
            print(f"  公司列表: {companies[:10]}{'...' if len(companies) > 10 else ''}")
            
            # 分析比赛数据
            cursor.execute("SELECT DISTINCT match_id FROM odds WHERE match_id IS NOT NULL")
            matches = [row[0] for row in cursor.fetchall()]
            print(f"  比赛数量: {len(matches)}")
            
            # 分析时间数据格式
            cursor.execute("SELECT DISTINCT date FROM odds WHERE date IS NOT NULL LIMIT 20")
            date_samples = [row[0] for row in cursor.fetchall()]
            print(f"  日期格式样本: {date_samples[:5]}")
            
            cursor.execute("SELECT DISTINCT time FROM odds WHERE time IS NOT NULL LIMIT 20")
            time_samples = [row[0] for row in cursor.fetchall()]
            print(f"  时间格式样本: {time_samples[:5]}")
            
            # 分析赔率数据范围
            cursor.execute("""
                SELECT 
                    MIN(home_odds) as min_home, MAX(home_odds) as max_home,
                    MIN(draw_odds) as min_draw, MAX(draw_odds) as max_draw,
                    MIN(away_odds) as min_away, MAX(away_odds) as max_away,
                    AVG(return_rate) as avg_return_rate
                FROM odds 
                WHERE home_odds IS NOT NULL AND draw_odds IS NOT NULL AND away_odds IS NOT NULL
            """)
            odds_stats = cursor.fetchone()
            if odds_stats:
                print(f"  赔率范围:")
                print(f"    主胜: {odds_stats[0]:.2f} - {odds_stats[1]:.2f}")
                print(f"    平局: {odds_stats[2]:.2f} - {odds_stats[3]:.2f}")
                print(f"    客胜: {odds_stats[4]:.2f} - {odds_stats[5]:.2f}")
                print(f"    平均返还率: {odds_stats[6]:.2f}%")
            
            # 分析每场比赛的赔率变化次数
            cursor.execute("""
                SELECT match_id, COUNT(*) as change_count 
                FROM odds 
                GROUP BY match_id 
                ORDER BY change_count DESC 
                LIMIT 10
            """)
            match_changes = cursor.fetchall()
            print(f"  赔率变化最多的比赛:")
            for match_id, count in match_changes[:5]:
                print(f"    比赛{match_id}: {count}次变化")
            
            # 分析公司活跃度
            cursor.execute("""
                SELECT company_name, COUNT(*) as record_count 
                FROM odds 
                WHERE company_name IS NOT NULL
                GROUP BY company_name 
                ORDER BY record_count DESC 
                LIMIT 10
            """)
            company_activity = cursor.fetchall()
            print(f"  最活跃的博彩公司:")
            for company, count in company_activity[:5]:
                print(f"    {company}: {count}条记录")
            
            conn.close()
            
            # 保存分析结果
            analysis_result = {
                'db_name': db_name,
                'total_records': total_count,
                'companies': companies,
                'matches_count': len(matches),
                'date_samples': date_samples,
                'time_samples': time_samples,
                'odds_stats': odds_stats,
                'top_matches_by_changes': match_changes,
                'top_companies_by_activity': company_activity
            }
            
            return analysis_result
            
        except Exception as e:
            print(f"  分析 {db_name} 数据库时出错: {e}")
            return None
    
    def analyze_time_patterns(self, db_path, db_name="main"):
        """分析时间模式和赔率变化时间分布"""
        print(f"\n=== 分析 {db_name} 数据库的时间模式 ===")
        
        try:
            conn = self.connect_readonly(db_path)
            
            # 使用pandas读取数据进行分析
            query = """
                SELECT match_id, company_name, date, time, 
                       home_odds, draw_odds, away_odds, extraction_time
                FROM odds 
                WHERE date IS NOT NULL AND time IS NOT NULL
                ORDER BY match_id, company_name, date, time
            """
            
            df = pd.read_sql_query(query, conn)
            conn.close()
            
            if df.empty:
                print("  没有有效的时间数据")
                return None
            
            print(f"  有效记录数: {len(df)}")
            
            # 分析日期格式
            date_patterns = df['date'].value_counts().head(10)
            print(f"  常见日期格式:")
            for date_val, count in date_patterns.items():
                print(f"    {date_val}: {count}次")
            
            # 分析时间格式
            time_patterns = df['time'].value_counts().head(10)
            print(f"  常见时间格式:")
            for time_val, count in time_patterns.items():
                print(f"    {time_val}: {count}次")
            
            # 分析每场比赛的时间跨度
            match_time_spans = []
            for match_id in df['match_id'].unique()[:10]:  # 分析前10场比赛
                match_data = df[df['match_id'] == match_id]
                unique_times = match_data[['date', 'time']].drop_duplicates()
                print(f"  比赛 {match_id} 的赔率变化时间点: {len(unique_times)}个")
                if len(unique_times) <= 5:
                    for _, row in unique_times.iterrows():
                        print(f"    {row['date']} {row['time']}")
            
            return {
                'db_name': db_name,
                'valid_records': len(df),
                'date_patterns': dict(date_patterns),
                'time_patterns': dict(time_patterns)
            }
            
        except Exception as e:
            print(f"  分析 {db_name} 时间模式时出错: {e}")
            return None
    
    def run_full_analysis(self):
        """运行完整的数据库分析"""
        print("开始数据库结构深度分析...")
        
        # 分析主数据库
        if os.path.exists(self.main_db_path):
            main_result = self.analyze_odds_table_structure(self.main_db_path, "主数据库")
            if main_result:
                self.analysis_results['main'] = main_result
                time_result = self.analyze_time_patterns(self.main_db_path, "主数据库")
                if time_result:
                    self.analysis_results['main']['time_analysis'] = time_result
        
        # 分析联赛分库
        if os.path.exists(self.league_db_dir):
            for file in os.listdir(self.league_db_dir):
                if file.endswith('.db'):
                    league_name = file[:-3]  # 去掉.db后缀
                    db_path = os.path.join(self.league_db_dir, file)
                    
                    league_result = self.analyze_odds_table_structure(db_path, f"联赛-{league_name}")
                    if league_result:
                        self.analysis_results[f'league_{league_name}'] = league_result
                        time_result = self.analyze_time_patterns(db_path, f"联赛-{league_name}")
                        if time_result:
                            self.analysis_results[f'league_{league_name}']['time_analysis'] = time_result
        
        # 保存分析结果
        self.save_analysis_results()
        
        return self.analysis_results
    
    def save_analysis_results(self):
        """保存分析结果到JSON文件"""
        output_file = "database_analysis_results.json"
        
        # 处理不能序列化的数据类型
        serializable_results = {}
        for key, value in self.analysis_results.items():
            serializable_results[key] = self._make_serializable(value)
        
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(serializable_results, f, ensure_ascii=False, indent=2)
        
        print(f"\n分析结果已保存到: {output_file}")
    
    def _make_serializable(self, obj):
        """将对象转换为可序列化的格式"""
        if isinstance(obj, dict):
            return {k: self._make_serializable(v) for k, v in obj.items()}
        elif isinstance(obj, (list, tuple)):
            return [self._make_serializable(item) for item in obj]
        elif obj is None:
            return None
        else:
            return str(obj)

def main():
    analyzer = DatabaseAnalyzer()
    results = analyzer.run_full_analysis()
    
    print("\n=== 分析总结 ===")
    for db_name, result in results.items():
        if 'total_records' in result:
            print(f"{result['db_name']}: {result['total_records']}条记录, {result['matches_count']}场比赛, {len(result['companies'])}家公司")

if __name__ == "__main__":
    main()

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
天启代理深度分析
基于测试结果进行详细分析
"""

import requests
from bs4 import BeautifulSoup
import re

def analyze_tianqi_website():
    """分析天启代理网站内容"""
    print("🔍 深度分析天启代理网站")
    print("=" * 60)
    
    # 读取API密钥
    try:
        with open('ip_keys.txt', 'r') as f:
            api_key = f.read().strip()
        print(f"📋 API密钥: {api_key}")
    except Exception as e:
        print(f"❌ 读取密钥失败: {e}")
        return
    
    # 分析主要页面
    pages_to_analyze = [
        ("主页", "https://www.tianqiip.com"),
        ("API页面", "https://www.tianqiip.com/api"),
        ("帮助页面", "https://www.tianqiip.com/help"),
        ("用户页面", "https://www.tianqiip.com/user"),
    ]
    
    for page_name, url in pages_to_analyze:
        print(f"\n🧪 分析 {page_name}: {url}")
        try:
            response = requests.get(url, timeout=10)
            if response.status_code == 200:
                soup = BeautifulSoup(response.text, 'html.parser')
                
                # 查找API相关信息
                api_info = find_api_info(soup, api_key)
                if api_info:
                    print(f"✅ 找到API信息:")
                    for info in api_info:
                        print(f"  - {info}")
                else:
                    print("❌ 未找到明确的API信息")
                    
                # 查找代理服务器信息
                proxy_info = find_proxy_info(soup)
                if proxy_info:
                    print(f"✅ 找到代理信息:")
                    for info in proxy_info:
                        print(f"  - {info}")
                        
            else:
                print(f"❌ 访问失败: {response.status_code}")
                
        except Exception as e:
            print(f"❌ 分析失败: {e}")

def find_api_info(soup, api_key):
    """查找API相关信息"""
    api_info = []
    
    # 查找包含API关键词的文本
    text = soup.get_text().lower()
    
    # 查找API端点
    api_patterns = [
        r'https?://[^/\s]+/api/[^\s<>"\']+',
        r'/api/[^\s<>"\']+',
        r'getip[^\s<>"\']*',
        r'proxy[^\s<>"\']*'
    ]
    
    for pattern in api_patterns:
        matches = re.findall(pattern, text, re.IGNORECASE)
        for match in matches:
            if match not in api_info:
                api_info.append(f"API端点: {match}")
    
    # 查找参数信息
    param_keywords = ['key', 'token', 'apikey', 'secret', 'num', 'count', 'format', 'type']
    for keyword in param_keywords:
        if keyword in text:
            api_info.append(f"参数关键词: {keyword}")
    
    # 查找具体的API调用示例
    if api_key.lower() in text:
        api_info.append("✅ 页面中包含您的API密钥")
    
    return api_info

def find_proxy_info(soup):
    """查找代理服务器信息"""
    proxy_info = []
    
    text = soup.get_text()
    
    # 查找代理服务器地址
    proxy_patterns = [
        r'[a-zA-Z0-9.-]+\.tianqiip\.com',
        r'\d+\.\d+\.\d+\.\d+:\d+',
        r'proxy[^:\s]*:\d+',
        r'tunnel[^:\s]*:\d+',
        r'api[^:\s]*:\d+'
    ]
    
    for pattern in proxy_patterns:
        matches = re.findall(pattern, text, re.IGNORECASE)
        for match in matches:
            if match not in proxy_info:
                proxy_info.append(f"代理地址: {match}")
    
    # 查找端口信息
    port_pattern = r':(\d{4,5})'
    ports = re.findall(port_pattern, text)
    unique_ports = list(set(ports))
    if unique_ports:
        proxy_info.append(f"发现端口: {', '.join(unique_ports)}")
    
    return proxy_info

def test_specific_endpoints():
    """测试特定的API端点"""
    print("\n🧪 测试特定API端点")
    print("=" * 40)
    
    # 读取API密钥
    try:
        with open('ip_keys.txt', 'r') as f:
            api_key = f.read().strip()
    except Exception as e:
        print(f"❌ 读取密钥失败: {e}")
        return
    
    # 基于常见代理服务的API格式进行测试
    test_cases = [
        {
            "name": "标准格式1",
            "url": "https://api.tianqiip.com/getip",
            "params": {"key": api_key, "num": 1, "format": "json"}
        },
        {
            "name": "标准格式2", 
            "url": "https://www.tianqiip.com/api/getip",
            "params": {"token": api_key, "num": 1, "format": "txt"}
        },
        {
            "name": "简化格式",
            "url": f"https://www.tianqiip.com/api/getip?key={api_key}&num=1"
        },
        {
            "name": "直接访问",
            "url": f"https://www.tianqiip.com/getip/{api_key}"
        }
    ]
    
    for case in test_cases:
        print(f"\n🧪 测试: {case['name']}")
        print(f"URL: {case['url']}")
        
        try:
            if 'params' in case:
                response = requests.get(case['url'], params=case['params'], timeout=10)
                print(f"完整URL: {response.url}")
            else:
                response = requests.get(case['url'], timeout=10)
            
            print(f"状态码: {response.status_code}")
            print(f"Content-Type: {response.headers.get('Content-Type', 'N/A')}")
            
            # 检查响应内容
            content = response.text.strip()
            if response.status_code == 200:
                if content.startswith('{') or content.startswith('['):
                    print("✅ 可能是JSON响应")
                    print(f"响应: {content[:200]}...")
                elif re.match(r'\d+\.\d+\.\d+\.\d+', content):
                    print("✅ 可能是IP地址响应")
                    print(f"响应: {content}")
                elif 'html' in content.lower():
                    print("❌ 返回HTML页面，不是API响应")
                else:
                    print(f"响应内容: {content[:100]}...")
            else:
                print(f"❌ 请求失败")
                
        except Exception as e:
            print(f"❌ 请求异常: {e}")

def generate_usage_recommendations():
    """生成使用建议"""
    print("\n💡 天启代理使用建议")
    print("=" * 40)
    
    print("基于测试结果，以下是可能的使用方法：")
    print()
    
    print("1. 🌐 访问官网获取详细文档:")
    print("   - 主页: https://www.tianqiip.com")
    print("   - 帮助: https://www.tianqiip.com/help")
    print("   - API: https://www.tianqiip.com/api")
    print()
    
    print("2. 📞 联系客服获取正确的使用方法:")
    print("   - 天启代理可能需要特定的API格式")
    print("   - 您的密钥可能需要激活或配置")
    print()
    
    print("3. 🔍 检查账户状态:")
    print("   - 登录官网查看账户余额")
    print("   - 确认API密钥是否有效")
    print("   - 检查是否有使用限制")
    print()
    
    print("4. 📖 常见的代理API格式:")
    print("   - GET https://api.tianqiip.com/getip?key=YOUR_KEY&num=1")
    print("   - GET https://www.tianqiip.com/api/getip?token=YOUR_KEY&format=json")
    print("   - 代理认证: http://username:<EMAIL>:port")

def main():
    """主函数"""
    print("🎯 天启代理深度分析工具")
    print("=" * 60)
    
    try:
        analyze_tianqi_website()
        test_specific_endpoints()
        generate_usage_recommendations()
        
        print("\n🎯 分析完成！")
        print("建议：")
        print("1. 访问官网查看详细文档")
        print("2. 联系客服获取正确的API使用方法")
        print("3. 确认账户状态和密钥有效性")
        
    except KeyboardInterrupt:
        print("\n👋 用户取消分析")
    except Exception as e:
        print(f"❌ 分析失败: {e}")

if __name__ == "__main__":
    main()

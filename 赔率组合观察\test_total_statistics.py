#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试总统计功能
验证在"开始分析"后首行是否正确显示总统计信息
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from odds_combination_database import OddsCombinationDatabase
from odds_combination_analyzer import OddsCombinationAnalyzer

def test_total_statistics():
    """测试总统计功能"""
    print("🔧 测试总统计功能")
    print("=" * 60)
    
    # 初始化数据库和分析器
    try:
        # 获取可用的数据库
        temp_db = OddsCombinationDatabase()
        databases = temp_db.get_available_databases()
        if not databases:
            print("❌ 没有找到可用的数据库")
            return

        print(f"📊 找到 {len(databases)} 个数据库")

        # 使用第一个数据库
        db_path = databases[0]
        print(f"📂 使用数据库: {os.path.basename(db_path)}")

        # 创建数据库和分析器实例
        db = OddsCombinationDatabase(db_path)
        analyzer = OddsCombinationAnalyzer()
        
        # 获取博彩公司列表
        companies = db.get_available_companies()
        if not companies:
            print("❌ 没有找到博彩公司")
            return

        print(f"🏢 找到 {len(companies)} 家博彩公司")

        # 使用第一家公司
        company = companies[0]
        print(f"🎯 使用博彩公司: {company}")

        # 查找一个有数据的比赛
        test_match_id = None
        for i in range(2598000, 2598100):  # 搜索范围
            match_id = str(i)
            odds_data = db.get_match_odds_by_company(match_id, company)
            if odds_data and len(odds_data) >= 3:  # 至少需要3条数据
                test_match_id = match_id
                break
        
        if not test_match_id:
            print("❌ 没有找到合适的测试比赛")
            return
        
        print(f"🎮 使用测试比赛: {test_match_id}")
        
        # 执行分析
        print("\n🔍 开始分析...")
        results = analyzer.analyze_odds_combinations(
            match_id=test_match_id,
            company_name=company,
            combination_size=3,
            only_historical=True
        )
        
        if not results or not results.get('results'):
            print("❌ 分析失败或无结果")
            return
        
        print(f"✅ 分析完成，找到 {len(results['results'])} 个组合")
        
        # 手动计算总统计（模拟UI中的逻辑）
        combination_results = results['results']
        total_matches = 0
        total_wins = 0
        total_draws = 0
        total_losses = 0
        non_zero_count = 0
        
        print("\n📊 组合详情:")
        for i, result in enumerate(combination_results):
            match_count = result['match_count']
            stats = result['result_stats']
            
            print(f"  组合 {i+1}: 匹配{match_count}次, 主胜{stats['win']}, 平局{stats['draw']}, 客胜{stats['loss']}")
            
            if match_count > 0:  # 只统计匹配次数不为零的行
                non_zero_count += 1
                total_matches += match_count
                total_wins += stats['win']
                total_draws += stats['draw']
                total_losses += stats['loss']
        
        print(f"\n📈 总统计计算结果:")
        print(f"  有效组合数: {non_zero_count}")
        print(f"  总匹配次数: {total_matches}")
        print(f"  总主胜: {total_wins}")
        print(f"  总平局: {total_draws}")
        print(f"  总客胜: {total_losses}")
        
        # 验证数据一致性
        if total_wins + total_draws + total_losses == total_matches:
            print("✅ 数据一致性检查通过")
        else:
            print("❌ 数据一致性检查失败")
            print(f"   胜平负总和: {total_wins + total_draws + total_losses}")
            print(f"   总匹配次数: {total_matches}")
        
        print(f"\n💡 UI中首行应该显示:")
        print(f"   组合序号: '总计'")
        print(f"   组合内容: '所有匹配组合的统计汇总'")
        print(f"   匹配次数: {total_matches}")
        print(f"   主胜: {total_wins}")
        print(f"   平局: {total_draws}")
        print(f"   客胜: {total_losses}")
        print(f"   概率: '-'")
        print(f"   尾部概率: '-'")
        
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()

def main():
    """主函数"""
    print("🎯 总统计功能测试")
    print("=" * 80)
    
    test_total_statistics()
    
    print("\n" + "=" * 80)
    print("🎉 测试完成！")
    print("\n📝 说明:")
    print("- 修改后的UI会在首行显示总统计信息")
    print("- 只统计匹配次数不为零的组合")
    print("- 首行有特殊样式（加粗、浅蓝色背景）")
    print("- 点击首行不会显示详细匹配信息")

if __name__ == "__main__":
    main()

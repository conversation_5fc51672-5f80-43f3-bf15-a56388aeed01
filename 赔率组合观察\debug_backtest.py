#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试回测功能
分析比赛ID 2596914的具体情况
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_odds_extraction():
    """测试赔率提取功能"""
    
    # 模拟比赛2596914的组合内容
    # 从图片看到的组合内容：1:(2.6,3.5,2.6)|2:(2.6,3.3,2.6)
    combination_content = "1:(2.6,3.5,2.6)|2:(2.6,3.3,2.6)"
    
    print("🔍 调试回测功能")
    print("=" * 50)
    print(f"📊 比赛ID: 2596914")
    print(f"📋 组合内容: {combination_content}")
    print(f"🎯 实际结果: 主胜")
    print(f"📈 总主胜: 11, 总匹配次数: 12")
    print(f"📊 胜率: {11/12:.3f}")
    print("")
    
    # 测试赔率提取
    def extract_home_odds_from_combination(combination_content: str) -> float:
        """从组合内容中提取第二组赔率的主胜赔率"""
        try:
            print(f"🔧 开始解析组合内容: {combination_content}")
            
            parts = combination_content.split('|')
            print(f"📝 分割后的部分: {parts}")
            
            if len(parts) >= 2:
                second_part = parts[1]  # 第二组
                print(f"🎯 第二组内容: {second_part}")
                
                # 提取括号内的内容
                if ':(' in second_part and ')' in second_part:
                    odds_str = second_part.split(':(')[1].split(')')[0]
                    print(f"📊 赔率字符串: {odds_str}")
                    
                    odds_values = odds_str.split(',')
                    print(f"📈 赔率数组: {odds_values}")
                    
                    if len(odds_values) >= 1:
                        home_odds = float(odds_values[0])  # 主胜赔率
                        print(f"✅ 提取的主胜赔率: {home_odds}")
                        return home_odds
            
            print("❌ 无法提取赔率")
            return 0.0
            
        except Exception as e:
            print(f"❌ 解析组合内容时发生错误: {e}")
            return 0.0
    
    # 执行测试
    home_odds = extract_home_odds_from_combination(combination_content)
    
    print("")
    print("📊 回测计算:")
    print("-" * 30)
    
    # 模拟回测计算
    total_wins_all = 11
    total_matches_all = 12
    investment_threshold = 1.05
    match_threshold = 5
    
    win_rate = total_wins_all / total_matches_all
    investment_value = win_rate * home_odds
    
    print(f"胜率: {win_rate:.3f}")
    print(f"主胜赔率: {home_odds}")
    print(f"投资价值: {investment_value:.3f}")
    print(f"投资阈值: {investment_threshold}")
    print(f"场次阈值: {match_threshold}")
    print("")
    
    # 检查投资条件
    meets_match_threshold = total_wins_all >= match_threshold
    meets_investment_threshold = investment_value >= investment_threshold
    
    print("🔍 投资条件检查:")
    print(f"满足场次阈值 ({total_wins_all} >= {match_threshold}): {meets_match_threshold}")
    print(f"满足投资阈值 ({investment_value:.3f} >= {investment_threshold}): {meets_investment_threshold}")
    
    if meets_match_threshold and meets_investment_threshold:
        print("✅ 满足投资条件")
        
        # 计算收益
        actual_result = "主胜"
        if actual_result == "主胜":
            return_value = home_odds
            profit = return_value - 1
            print(f"💰 投资1单位，回报: {return_value}")
            print(f"💵 净利润: {profit}")
        else:
            return_value = 0
            profit = -1
            print(f"💸 投资1单位，回报: {return_value}")
            print(f"💸 净利润: {profit}")
            
    else:
        print("❌ 不满足投资条件")
    
    print("")
    print("🎯 问题分析:")
    if home_odds == 0:
        print("❌ 主要问题：无法正确提取主胜赔率")
        print("💡 建议：检查组合内容格式是否正确")
    elif not meets_investment_threshold:
        print("❌ 主要问题：不满足投资阈值")
        print(f"💡 当前投资价值 {investment_value:.3f} < 阈值 {investment_threshold}")
    else:
        print("✅ 计算逻辑正确")

if __name__ == "__main__":
    test_odds_extraction()

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试UI兼容性 - 验证两种分析器的结果都能正确显示
"""

import sys
import os

# 添加父目录到Python路径
parent_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, parent_dir)

from odds_combination_analyzer import OddsCombinationAnalyzer
from optimized_analyzer import OptimizedOddsCombinationAnalyzer

def test_result_compatibility():
    """测试结果兼容性"""
    print("测试UI结果兼容性")
    print("=" * 40)
    
    test_match_id = "2617497"
    test_company = "bet365"
    test_combination_size = 3
    
    print(f"测试参数:")
    print(f"  比赛ID: {test_match_id}")
    print(f"  博彩公司: {test_company}")
    print(f"  组合大小: {test_combination_size}")
    print()
    
    try:
        # 测试优化分析器结果结构
        print("🚀 测试优化分析器结果结构...")
        
        optimized_analyzer = OptimizedOddsCombinationAnalyzer(
            max_workers=2,  # 减少线程数以加快测试
            batch_size=50   # 减少批量大小以加快测试
        )
        
        optimized_result = optimized_analyzer.analyze_odds_combinations_optimized(
            test_match_id, test_company, test_combination_size
        )
        
        if optimized_result['success']:
            print("✅ 优化分析器执行成功")
            
            # 检查结果结构
            first_result = optimized_result['results'][0]
            print(f"   第一个组合结果包含的键: {list(first_result.keys())}")
            
            # 测试UI兼容性逻辑
            combination = first_result.get('combination') or first_result.get('target_combination')
            if combination:
                print("✅ UI兼容性检查通过 - 能正确获取组合数据")
                print(f"   组合数据类型: {type(combination)}")
                print(f"   组合长度: {len(combination)}")
                
                # 测试格式化函数
                if len(combination) > 0:
                    first_odds = combination[0]
                    print(f"   第一个赔率数据: 主{first_odds['home_odds']} 平{first_odds['draw_odds']} 客{first_odds['away_odds']}")
                    
                    # 模拟UI格式化
                    parts = []
                    for i, odds in enumerate(combination):
                        part = f"{i+1}:({odds['home_odds']},{odds['draw_odds']},{odds['away_odds']})"
                        parts.append(part)
                    formatted_text = " ".join(parts)
                    print(f"   格式化文本: {formatted_text}")
                    
            else:
                print("❌ UI兼容性检查失败 - 无法获取组合数据")
                
            # 检查其他必需字段
            required_fields = ['combination_index', 'match_count', 'result_stats', 'matched_matches']
            missing_fields = []
            
            for field in required_fields:
                if field not in first_result:
                    missing_fields.append(field)
            
            if missing_fields:
                print(f"❌ 缺少必需字段: {missing_fields}")
            else:
                print("✅ 所有必需字段都存在")
                
            # 检查result_stats结构
            stats = first_result['result_stats']
            expected_stats = ['win', 'draw', 'loss', 'unknown']
            missing_stats = [s for s in expected_stats if s not in stats]
            
            if missing_stats:
                print(f"❌ result_stats缺少字段: {missing_stats}")
            else:
                print("✅ result_stats结构正确")
                print(f"   统计数据: 胜{stats['win']} 平{stats['draw']} 负{stats['loss']} 未知{stats['unknown']}")
                
        else:
            print(f"❌ 优化分析器执行失败: {optimized_result['error']}")
            return False
            
        print("\n" + "=" * 40)
        print("✅ UI兼容性测试通过！")
        print("💡 优化分析器的结果可以正确在UI中显示")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试过程中发生异常: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_format_function():
    """测试格式化函数"""
    print("\n测试格式化函数")
    print("=" * 30)
    
    # 模拟组合数据
    test_combination = [
        {'home_odds': 1.67, 'draw_odds': 3.5, 'away_odds': 5.5, 'date': '04-28', 'time': '10:23'},
        {'home_odds': 1.62, 'draw_odds': 3.6, 'away_odds': 5.75, 'date': '04-28', 'time': '17:39'},
        {'home_odds': 1.73, 'draw_odds': 3.4, 'away_odds': 5.0, 'date': '04-28', 'time': '18:45'}
    ]
    
    try:
        # 模拟UI的格式化逻辑
        parts = []
        for i, odds in enumerate(test_combination):
            part = f"{i+1}:({odds['home_odds']},{odds['draw_odds']},{odds['away_odds']})"
            parts.append(part)
        formatted_text = " ".join(parts)
        
        print(f"✅ 格式化成功")
        print(f"   输入: {len(test_combination)} 个赔率数据")
        print(f"   输出: {formatted_text}")
        
        return True
        
    except Exception as e:
        print(f"❌ 格式化失败: {e}")
        return False

def main():
    """主函数"""
    print("赔率组合观察工具 - UI兼容性测试")
    print("=" * 50)
    
    # 测试结果兼容性
    success1 = test_result_compatibility()
    
    # 测试格式化函数
    success2 = test_format_function()
    
    print(f"\n" + "=" * 50)
    if success1 and success2:
        print("🎉 所有兼容性测试通过！")
        print("💡 现在可以安全地使用优化分析器了")
        print("💡 UI界面应该能正确显示分析结果")
    else:
        print("❌ 部分测试失败")
        print("💡 请检查代码修复")

if __name__ == '__main__':
    main()

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试比赛结果获取修复
验证比赛结果判断是否正确
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_match_result_extraction():
    """测试比赛结果提取"""
    
    print("🔍 测试比赛结果获取修复")
    print("=" * 50)
    
    try:
        from odds_combination_database import OddsCombinationDatabase
        
        # 查找数据库文件
        db_files = []
        for file in os.listdir('.'):
            if file.endswith('.db'):
                db_files.append(file)
        
        if not db_files:
            print("❌ 未找到数据库文件")
            return
        
        db_path = db_files[0]
        print(f"📂 使用数据库: {db_path}")
        
        db = OddsCombinationDatabase(db_path)
        
        # 测试比赛2596914
        test_match_id = "2596914"
        
        print(f"\n🎯 测试比赛: {test_match_id}")
        
        # 使用数据库方法获取比赛信息
        match_info = db.get_match_info(test_match_id)
        if match_info:
            print(f"📊 比赛信息:")
            print(f"  主队: {match_info.get('home_team')}")
            print(f"  客队: {match_info.get('away_team')}")
            print(f"  主队得分: {match_info.get('home_score')} (类型: {type(match_info.get('home_score'))})")
            print(f"  客队得分: {match_info.get('away_score')} (类型: {type(match_info.get('away_score'))})")
            print(f"  比赛状态: {match_info.get('match_state')}")
            
            # 手动判断结果
            home_score = match_info.get('home_score')
            away_score = match_info.get('away_score')
            
            if home_score is not None and away_score is not None:
                try:
                    home_score_int = int(home_score)
                    away_score_int = int(away_score)
                    
                    if home_score_int > away_score_int:
                        result = "主胜"
                    elif home_score_int < away_score_int:
                        result = "客胜"
                    else:
                        result = "平局"
                    
                    print(f"✅ 判断结果: {result}")
                    
                except (ValueError, TypeError) as e:
                    print(f"❌ 得分转换失败: {e}")
                    result = "未知"
            else:
                print(f"❌ 得分数据缺失")
                result = "未知"
        else:
            print(f"❌ 未找到比赛 {test_match_id} 的信息")
            
        # 测试其他几个比赛
        test_matches = ["2596915", "2596916", "2596917"]
        
        print(f"\n🔍 测试其他比赛:")
        for match_id in test_matches:
            match_info = db.get_match_info(match_id)
            if match_info:
                home_score = match_info.get('home_score')
                away_score = match_info.get('away_score')
                
                if home_score is not None and away_score is not None:
                    try:
                        home_score_int = int(home_score)
                        away_score_int = int(away_score)
                        
                        if home_score_int > away_score_int:
                            result = "主胜"
                        elif home_score_int < away_score_int:
                            result = "客胜"
                        else:
                            result = "平局"
                        
                        print(f"  {match_id}: {home_score}:{away_score} -> {result}")
                        
                    except (ValueError, TypeError):
                        print(f"  {match_id}: 得分转换失败")
                else:
                    print(f"  {match_id}: 得分数据缺失")
            else:
                print(f"  {match_id}: 未找到比赛信息")
        
        print(f"\n✅ 测试完成")
        print(f"💡 现在可以运行回测功能，应该能正确判断比赛结果了")
        
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")

if __name__ == "__main__":
    test_match_result_extraction()

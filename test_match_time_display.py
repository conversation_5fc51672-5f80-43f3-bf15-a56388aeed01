#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试比赛时间显示功能
验证准确时间信息的抓取和显示
"""

import sqlite3
import logging
from database import OddsDatabase
from match_time_scraper import MatchTimeScraper

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_database_time_fields():
    """测试数据库中的时间字段"""
    try:
        print("🔍 测试数据库中的时间字段...")
        
        with sqlite3.connect("odds_data.db") as conn:
            cursor = conn.cursor()
            
            # 检查时间字段是否存在
            cursor.execute("PRAGMA table_info(matches)")
            columns = [column[1] for column in cursor.fetchall()]
            
            time_fields = [
                'accurate_datetime', 'accurate_date', 'accurate_time', 'weekday',
                'match_year', 'match_month', 'match_day', 'match_hour', 'match_minute', 'time_source'
            ]
            
            print("📋 时间相关字段检查:")
            print("-" * 50)
            for field in time_fields:
                exists = field in columns
                status = "✅" if exists else "❌"
                print(f"{status} {field}")
            
            # 统计时间数据
            cursor.execute("SELECT COUNT(*) FROM matches")
            total_matches = cursor.fetchone()[0]
            
            cursor.execute("SELECT COUNT(*) FROM matches WHERE accurate_datetime IS NOT NULL AND accurate_datetime != ''")
            accurate_time_count = cursor.fetchone()[0]
            
            cursor.execute("SELECT time_source, COUNT(*) FROM matches GROUP BY time_source")
            time_source_distribution = dict(cursor.fetchall())
            
            print(f"\n📊 时间数据统计:")
            print(f"   总比赛数: {total_matches}")
            print(f"   有准确时间: {accurate_time_count}")
            print(f"   时间来源分布: {time_source_distribution}")
            
            return True
            
    except Exception as e:
        logger.error(f"测试失败: {e}")
        return False

def test_time_display_logic():
    """测试时间显示逻辑"""
    try:
        print("\n🧪 测试时间显示逻辑...")
        
        # 连接数据库
        db = OddsDatabase()
        matches = db.get_all_matches()
        
        print(f"📋 前10场比赛的时间信息:")
        print("-" * 120)
        print(f"{'比赛ID':<10} {'联赛':<15} {'原始时间':<20} {'准确时间':<20} {'星期':<8} {'来源':<10}")
        print("-" * 120)
        
        for i, match in enumerate(matches[:10]):
            match_id = match.get('match_id', '') or ''
            league = (match.get('league', '') or '')[:14]
            original_time = (match.get('match_time', '') or '')[:19]
            accurate_datetime = match.get('accurate_datetime', '') or ''
            weekday = match.get('weekday', '') or ''
            time_source = match.get('time_source', '') or ''

            # 模拟UI显示逻辑
            display_time = original_time
            if accurate_datetime:
                accurate_date = match.get('accurate_date', '') or ''
                accurate_time = match.get('accurate_time', '') or ''
                if weekday:
                    display_time = f"{accurate_date} {accurate_time} {weekday}"
                else:
                    display_time = f"{accurate_date} {accurate_time}"
                if time_source == 'analysis':
                    display_time += " ✓"

            print(f"{match_id:<10} {league:<15} {original_time:<20} {display_time:<20} {weekday:<8} {time_source:<10}")
        
        return True
        
    except Exception as e:
        logger.error(f"测试失败: {e}")
        return False

def test_time_scraper_functionality():
    """测试时间抓取器功能"""
    try:
        print("\n🕐 测试时间抓取器功能...")
        
        scraper = MatchTimeScraper()
        
        # 测试几个比赛ID
        test_match_ids = ["2741804", "2741803", "2512125"]
        
        print("📋 时间抓取测试结果:")
        print("-" * 100)
        print(f"{'比赛ID':<10} {'状态':<8} {'完整时间':<20} {'日期':<12} {'时间':<8} {'星期':<8}")
        print("-" * 100)
        
        for match_id in test_match_ids:
            try:
                time_info = scraper.scrape_match_time(match_id)
                
                if time_info.get('error'):
                    status = "❌失败"
                    full_datetime = time_info.get('error', '')[:19]
                    match_date = ""
                    match_time = ""
                    weekday = ""
                else:
                    status = "✅成功"
                    full_datetime = time_info.get('full_datetime', '')
                    match_date = time_info.get('match_date', '')
                    match_time = time_info.get('match_time', '')
                    weekday = time_info.get('weekday', '')
                
                print(f"{match_id:<10} {status:<8} {full_datetime:<20} {match_date:<12} {match_time:<8} {weekday:<8}")
                
            except Exception as e:
                print(f"{match_id:<10} {'❌异常':<8} {str(e)[:19]:<20} {'':12} {'':8} {'':8}")
        
        return True
        
    except Exception as e:
        logger.error(f"测试失败: {e}")
        return False

def test_time_comparison():
    """测试时间对比"""
    try:
        print("\n⚖️  测试时间对比...")
        
        with sqlite3.connect("odds_data.db") as conn:
            cursor = conn.cursor()
            
            # 查找有准确时间的比赛
            cursor.execute("""
                SELECT match_id, match_time, accurate_datetime, accurate_date, accurate_time, weekday
                FROM matches 
                WHERE accurate_datetime IS NOT NULL AND accurate_datetime != ''
                LIMIT 5
            """)
            
            matches = cursor.fetchall()
            
            if not matches:
                print("❌ 没有找到有准确时间的比赛")
                return False
            
            print("📋 原始时间 vs 准确时间对比:")
            print("-" * 80)
            print(f"{'比赛ID':<10} {'原始时间':<20} {'准确时间':<25} {'改进':<10}")
            print("-" * 80)
            
            for match in matches:
                match_id, original_time, accurate_datetime, accurate_date, accurate_time, weekday = match
                
                # 格式化显示
                original_display = original_time or "无"
                accurate_display = f"{accurate_date} {accurate_time}"
                if weekday:
                    accurate_display += f" {weekday}"
                
                # 判断是否有改进
                improvement = "✅有改进" if accurate_datetime else "⚪无变化"
                
                print(f"{match_id:<10} {original_display:<20} {accurate_display:<25} {improvement:<10}")
            
            return True
            
    except Exception as e:
        logger.error(f"测试失败: {e}")
        return False

def main():
    """主函数"""
    print("🚀 开始测试比赛时间显示功能")
    print("=" * 80)
    
    # 测试数据库时间字段
    if not test_database_time_fields():
        print("❌ 数据库时间字段测试失败")
        return
    
    # 测试时间显示逻辑
    if not test_time_display_logic():
        print("❌ 时间显示逻辑测试失败")
        return
    
    # 测试时间抓取器功能
    if not test_time_scraper_functionality():
        print("❌ 时间抓取器功能测试失败")
        return
    
    # 测试时间对比
    if not test_time_comparison():
        print("❌ 时间对比测试失败")
        return
    
    print("\n" + "=" * 80)
    print("✅ 所有测试通过！比赛时间功能正常工作")
    
    print("\n💡 功能说明:")
    print("1. 📅 准确时间抓取: 从 https://zq.titan007.com/analysis/{match_id}.htm 抓取")
    print("2. 🕐 时间信息分离: 年月日、时间、星期分别存储")
    print("3. ✅ 时间来源标记: analysis(分析页面) / estimated(估算)")
    print("4. 🔄 自动时间更新: 新抓取的比赛自动获取准确时间")
    print("5. 📊 UI显示优化: 优先显示准确时间，带✓标记")
    
    print("\n🔧 使用方法:")
    print("1. 启动程序: python odds_scraper_ui.py")
    print("2. 抓取新比赛时会自动获取准确时间")
    print("3. 在比赛列表中查看时间信息（带✓表示准确时间）")
    print("4. 运行迁移脚本更新已有数据: python match_time_migration.py")

if __name__ == "__main__":
    main()

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试博彩态度筛选修复效果
"""

import sqlite3
import sys
import os

def test_betting_attitude_logic():
    """测试博彩态度筛选逻辑"""
    print("🔍 测试博彩态度筛选修复效果")
    print("=" * 50)
    
    if not os.path.exists("odds_data.db"):
        print("❌ 数据库文件不存在")
        return False
    
    # 测试参数（根据您的截图）
    selected_companies = ["澳门"]
    odds_type = "home_odds"
    threshold = 1.3
    
    # 从截图中的4场比赛ID
    test_match_ids = ['2538864', '2511628', '2511603', '2511668']
    
    print(f"测试条件:")
    print(f"  选择公司: {selected_companies}")
    print(f"  赔率类型: 主胜")
    print(f"  阈值: {threshold}")
    print(f"  测试比赛: {test_match_ids}")
    print()
    
    try:
        with sqlite3.connect("odds_data.db") as conn:
            conn.row_factory = sqlite3.Row
            cursor = conn.cursor()
            
            for match_id in test_match_ids:
                print(f"--- 测试比赛 {match_id} ---")
                result = analyze_match_betting_attitude_fixed(cursor, match_id, odds_type, threshold, selected_companies)
                print(f"结果: {'✅ 通过筛选' if result else '❌ 不通过筛选'}")
                print()
                
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False
    
    return True

def analyze_match_betting_attitude_fixed(cursor, match_id, odds_type, threshold, selected_companies):
    """使用修复后的逻辑分析单场比赛的博彩态度"""
    try:
        # 获取比赛信息
        cursor.execute('SELECT home_team, away_team, match_time FROM matches WHERE match_id = ?', (match_id,))
        match_info = cursor.fetchone()
        if match_info:
            print(f"  比赛: {match_info['home_team']} vs {match_info['away_team']}")
        
        # 对每个选中的博彩公司进行分析
        companies_analyzed = 0  # 记录实际分析的公司数量
        
        for target_company in selected_companies:
            print(f"  分析公司: {target_company}")
            
            # 获取该公司的开盘时间和赔率
            cursor.execute(f'''
                SELECT date, time, {odds_type}
                FROM odds
                WHERE match_id = ? AND company_name = ? AND {odds_type} IS NOT NULL
                ORDER BY date ASC, time ASC
                LIMIT 1
            ''', (match_id, target_company))

            target_odds_record = cursor.fetchone()
            if not target_odds_record:
                # 如果该公司没有数据，跳过这个公司
                print(f"    ❌ {target_company} 没有数据")
                continue

            target_date = target_odds_record['date']
            target_time = target_odds_record['time']
            target_odds = float(target_odds_record[odds_type])
            
            print(f"    {target_company} 开盘: {target_date} {target_time}, 赔率: {target_odds}")

            # 获取该时间点所有其他公司的赔率（严格要求同一时间点）
            cursor.execute(f'''
                SELECT company_name, {odds_type}
                FROM odds
                WHERE match_id = ? AND company_name != ? AND {odds_type} IS NOT NULL
                AND date = ? AND time = ?
            ''', (match_id, target_company, target_date, target_time))

            other_companies_odds = cursor.fetchall()

            if len(other_companies_odds) == 0:
                # 如果没有其他公司在同一时间点的数据，跳过这个公司
                # 不使用最接近时间的数据，严格要求同一时间点
                print(f"    ❌ 在 {target_company} 开盘时间点 {target_date} {target_time} 没有其他公司数据")
                continue

            # 计算其他公司赔率的平均值
            other_odds_values = [float(record[odds_type]) for record in other_companies_odds]
            avg_other_odds = sum(other_odds_values) / len(other_odds_values)

            # 计算比率
            if avg_other_odds > 0:
                ratio = target_odds / avg_other_odds
            else:
                print(f"    ❌ 其他公司平均赔率为0")
                continue

            companies_analyzed += 1  # 成功分析了一个公司
            
            print(f"    其他公司数量: {len(other_companies_odds)}")
            print(f"    其他公司平均赔率: {avg_other_odds:.3f}")
            print(f"    比率: {target_odds:.3f} ÷ {avg_other_odds:.3f} = {ratio:.3f}")
            print(f"    阈值条件: {ratio:.3f} >= {threshold} = {ratio >= threshold}")

            # 检查是否满足阈值条件
            if ratio < threshold:
                # 如果任何一个公司不满足条件，整场比赛就不符合
                print(f"    ❌ {target_company} 不满足条件")
                return False

            print(f"    ✅ {target_company} 满足条件")

        # 只有当至少分析了一个公司且所有分析的公司都满足条件时，才返回True
        if companies_analyzed > 0:
            print(f"  ✅ 分析了 {companies_analyzed} 个公司，全部满足条件")
            return True
        else:
            print(f"  ❌ 没有可分析的公司数据")
            return False
            
    except Exception as e:
        print(f"  ❌ 分析失败: {e}")
        return False

def main():
    """主函数"""
    success = test_betting_attitude_logic()
    print("=" * 50)
    print("🎯 测试完成")
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)

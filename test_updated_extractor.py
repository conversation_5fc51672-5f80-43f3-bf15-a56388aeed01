#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试更新后的日期比赛提取器
"""

import sys
import os

def test_updated_extractor():
    """测试更新后的提取器"""
    
    print("🔍 测试更新后的日期比赛提取器")
    print("=" * 50)
    
    try:
        from date_match_extractor import DateMatchExtractor
        
        # 创建提取器
        extractor = DateMatchExtractor()
        print("✅ 日期比赛提取器创建成功")
        
        # 测试URL
        test_url = "https://m.titan007.com/Schedule.htm?date=2025-07-22"
        print(f"📋 测试URL: {test_url}")
        
        # 提取比赛信息
        print("🚀 开始提取比赛信息...")
        matches_by_league = extractor.extract_matches_from_date_page(test_url)
        
        # 显示结果
        if matches_by_league:
            total_matches = sum(len(matches) for matches in matches_by_league.values())
            print(f"✅ 提取成功！")
            print(f"📊 找到 {len(matches_by_league)} 个联赛，{total_matches} 场比赛")
            
            print(f"\n🏆 联赛详情:")
            for league, matches in matches_by_league.items():
                print(f"  {league}: {len(matches)} 场比赛")
                
                # 显示前3场比赛
                for i, match in enumerate(matches[:3], 1):
                    print(f"    {i}. {match['match_id']}: {match['home_team']} vs {match['away_team']} ({match['match_time']})")
                
                if len(matches) > 3:
                    print(f"    ... 还有 {len(matches) - 3} 场比赛")
                print()
            
            # 获取摘要
            summary = extractor.get_leagues_summary(matches_by_league)
            print(f"📋 摘要:")
            print(summary)
            
        else:
            print("❌ 未找到任何比赛信息")
            print("可能的原因:")
            print("1. 网络连接问题")
            print("2. 页面结构发生变化")
            print("3. 日期没有比赛")
            print("4. 解析逻辑需要调整")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

def test_different_dates():
    """测试不同日期"""
    
    print(f"\n🗓️ 测试不同日期")
    print("-" * 30)
    
    try:
        from date_match_extractor import DateMatchExtractor
        extractor = DateMatchExtractor()
        
        # 测试多个日期
        test_dates = [
            "2025-07-22",
            "2025-07-23", 
            "2025-07-24"
        ]
        
        for date in test_dates:
            test_url = f"https://m.titan007.com/Schedule.htm?date={date}"
            print(f"\n📅 测试日期: {date}")
            
            try:
                matches_by_league = extractor.extract_matches_from_date_page(test_url)
                
                if matches_by_league:
                    total_matches = sum(len(matches) for matches in matches_by_league.values())
                    print(f"  ✅ {len(matches_by_league)} 个联赛，{total_matches} 场比赛")
                else:
                    print(f"  ❌ 未找到比赛")
                    
            except Exception as e:
                print(f"  ❌ 提取失败: {e}")
        
    except Exception as e:
        print(f"❌ 测试不同日期失败: {e}")

def test_ui_integration():
    """测试UI集成"""
    
    print(f"\n🔧 测试UI集成")
    print("-" * 30)
    
    try:
        # 模拟UI中的使用方式
        from date_match_extractor import DateMatchExtractor
        
        extractor = DateMatchExtractor()
        test_url = "https://m.titan007.com/Schedule.htm?date=2025-07-22"
        
        # 模拟UI调用流程
        print("1. 用户输入URL并点击按钮")
        print("2. 系统开始提取比赛信息...")
        
        matches_by_league = extractor.extract_matches_from_date_page(test_url)
        
        if matches_by_league:
            print("3. 提取成功，准备显示联赛选择对话框")
            
            # 模拟联赛选择对话框的数据
            dialog_data = {
                'matches_by_league': matches_by_league,
                'date_url': test_url
            }
            
            print("4. 联赛选择对话框数据准备完成")
            print(f"   - 联赛数: {len(matches_by_league)}")
            print(f"   - 总比赛数: {sum(len(matches) for matches in matches_by_league.values())}")
            
            # 模拟用户选择
            print("5. 用户可以选择要抓取的联赛")
            print("6. 确定后开始批量抓取")
            
            print("✅ UI集成流程测试完成")
            
        else:
            print("3. 提取失败，显示错误信息")
            print("❌ UI集成测试失败")
        
    except Exception as e:
        print(f"❌ UI集成测试失败: {e}")

if __name__ == "__main__":
    test_updated_extractor()
    test_different_dates()
    test_ui_integration()
    
    print(f"\n🎉 测试完成！")
    print("如果提取成功，现在可以在主程序中使用按日期抓取功能了。")
    print("如果仍有问题，请检查网络连接或页面结构是否发生变化。")

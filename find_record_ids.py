#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
寻找获取record ID的方法
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from enhanced_odds_scraper import EnhancedOddsScraper
from bs4 import BeautifulSoup
import re
import requests

def find_record_ids():
    """寻找获取record ID的方法"""
    print("🔍 寻找获取record ID的方法 - 比赛ID 2213559")
    print("=" * 60)
    
    scraper = EnhancedOddsScraper()
    match_id = "2213559"
    
    # 已知的正确record ID
    known_records = {
        '281': '114278325',  # bet365
        '115': '112833952',  # 威廉希尔
    }
    
    print("🎯 已知的正确record ID:")
    for cid, record_id in known_records.items():
        print(f"  公司ID {cid}: record ID {record_id}")
    
    # 方法1：尝试通过AJAX请求获取
    print(f"\n📡 方法1：尝试AJAX请求")
    print("-" * 40)
    
    ajax_urls = [
        f"https://1x2.titan007.com/ajax/OddsList.aspx?id={match_id}",
        f"https://op1.titan007.com/ajax/OddsList.aspx?id={match_id}",
        f"https://1x2.titan007.com/ajax/odds.aspx?id={match_id}",
        f"https://op1.titan007.com/ajax/odds.aspx?id={match_id}",
    ]
    
    for url in ajax_urls:
        print(f"🌐 测试AJAX URL: {url}")
        try:
            response = requests.get(url, timeout=10)
            if response.status_code == 200:
                content = response.text
                print(f"✅ 成功，内容长度: {len(content)}")
                
                # 查找已知的record ID
                for cid, record_id in known_records.items():
                    if record_id in content:
                        print(f"  📋 找到公司{cid}的record ID: {record_id}")
                        
                        # 查找周围的上下文
                        index = content.find(record_id)
                        context = content[max(0, index-50):index+100]
                        print(f"    上下文: ...{context}...")
            else:
                print(f"❌ 失败，状态码: {response.status_code}")
        except Exception as e:
            print(f"❌ 请求失败: {e}")
    
    # 方法2：尝试分析页面中的JavaScript数据
    print(f"\n📜 方法2：分析JavaScript数据")
    print("-" * 40)
    
    url = f"https://1x2.titan007.com/oddslist/{match_id}.htm"
    content = scraper.get_page_content(url)
    
    if content:
        # 查找所有可能包含数据的JavaScript变量
        js_patterns = [
            r'var\s+\w+\s*=\s*\[.*?\]',  # 数组变量
            r'var\s+\w+\s*=\s*\{.*?\}',  # 对象变量
            r'\w+\s*=\s*"[^"]*\d{8,}[^"]*"',  # 包含长数字的字符串
        ]
        
        for pattern in js_patterns:
            matches = re.findall(pattern, content, re.DOTALL)
            for match in matches:
                # 检查是否包含已知的record ID
                for cid, record_id in known_records.items():
                    if record_id in match:
                        print(f"📋 找到包含record ID {record_id}的JavaScript:")
                        print(f"    {match[:200]}...")
    
    # 方法3：尝试通过点击事件或其他交互方式
    print(f"\n🖱️ 方法3：分析交互方式")
    print("-" * 40)
    
    if content:
        soup = BeautifulSoup(content, 'html.parser')
        
        # 查找所有可点击的元素
        clickable_elements = soup.find_all(['a', 'span', 'div'], attrs={'onclick': True})
        
        for element in clickable_elements:
            onclick = element.get('onclick', '')
            text = element.get_text(strip=True)
            
            # 查找可能触发赔率历史的点击事件
            if any(keyword in onclick.lower() for keyword in ['odds', 'history', 'detail']):
                print(f"🔗 可能相关的点击事件:")
                print(f"    文本: {text[:30]}")
                print(f"    事件: {onclick[:100]}...")
    
    # 方法4：尝试构造规律
    print(f"\n🧮 方法4：分析record ID规律")
    print("-" * 40)
    
    print("已知数据:")
    print(f"  bet365 (cid=281): record_id=114278325")
    print(f"  威廉希尔 (cid=115): record_id=112833952")
    
    # 计算差值
    bet365_record = int(known_records['281'])
    william_record = int(known_records['115'])
    diff = bet365_record - william_record
    
    print(f"\n差值分析:")
    print(f"  bet365 - 威廉希尔 = {diff}")
    print(f"  公司ID差值: 281 - 115 = {281 - 115}")
    
    # 尝试为其他公司推测record ID
    print(f"\n🔮 为其他公司推测record ID:")
    other_companies = {
        '82': '立博',
        '81': '伟德', 
        '90': '易胜博',
    }
    
    for cid, name in other_companies.items():
        cid_int = int(cid)
        # 基于威廉希尔的record ID进行推测
        predicted_record = william_record + (cid_int - 115) * 100  # 简单的线性推测
        print(f"  {name} (cid={cid}): 推测record_id={predicted_record}")
        
        # 测试这个推测的链接是否有效
        test_url = f"https://op1.titan007.com/OddsHistory.aspx?id={predicted_record}&sid={match_id}&cid={cid}&l=0"
        try:
            response = requests.head(test_url, timeout=5)
            if response.status_code == 200:
                print(f"    ✅ 推测链接有效")
            else:
                print(f"    ❌ 推测链接无效 (状态码: {response.status_code})")
        except:
            print(f"    ❌ 推测链接无法访问")

if __name__ == "__main__":
    find_record_ids()

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
策略性能评估
计算夏普比率、最大回撤、胜率等关键指标，评估策略表现
"""

import pandas as pd
import numpy as np
import json
import os
from datetime import datetime
import matplotlib.pyplot as plt
import seaborn as sns

import warnings
warnings.filterwarnings('ignore')

class StrategyPerformanceEvaluator:
    def __init__(self):
        self.backtest_results = None
        self.performance_metrics = {}
        
    def load_backtest_results(self, results_file=None):
        """加载回测结果"""
        if results_file is None:
            # 查找最新的回测结果文件
            files = [f for f in os.listdir('.') if f.startswith('backtest_results_') and f.endswith('.json')]
            if files:
                results_file = sorted(files)[-1]
                print(f"自动选择回测结果文件: {results_file}")
            else:
                print("未找到回测结果文件")
                return False
        
        with open(results_file, 'r', encoding='utf-8') as f:
            self.backtest_results = json.load(f)
        
        print(f"回测结果加载成功")
        return True
    
    def calculate_basic_metrics(self, strategy_result):
        """计算基础性能指标"""
        metrics = {}

        # 基础指标（确保数值类型）
        metrics['total_return'] = float(strategy_result['total_return'])
        metrics['total_return_pct'] = float(strategy_result['total_return_pct'])
        metrics['win_rate'] = float(strategy_result['win_rate'])
        metrics['total_bets'] = int(strategy_result['total_bets'])
        metrics['winning_bets'] = int(strategy_result['winning_bets'])
        metrics['losing_bets'] = metrics['total_bets'] - metrics['winning_bets']
        
        # 平均收益指标
        if metrics['total_bets'] > 0:
            metrics['avg_return_per_bet'] = metrics['total_return'] / metrics['total_bets']
            metrics['avg_stake_per_bet'] = float(strategy_result['total_stake']) / metrics['total_bets']
        else:
            metrics['avg_return_per_bet'] = 0
            metrics['avg_stake_per_bet'] = 0
        
        # 盈亏比
        trade_history = strategy_result['trade_history']
        winning_trades = [trade for trade in trade_history if float(trade['profit']) > 0]
        losing_trades = [trade for trade in trade_history if float(trade['profit']) < 0]
        
        if winning_trades and losing_trades:
            avg_win = np.mean([float(trade['profit']) for trade in winning_trades])
            avg_loss = abs(np.mean([float(trade['profit']) for trade in losing_trades]))
            metrics['profit_loss_ratio'] = avg_win / avg_loss if avg_loss > 0 else 0
            metrics['avg_win'] = avg_win
            metrics['avg_loss'] = -avg_loss
        else:
            metrics['profit_loss_ratio'] = 0
            metrics['avg_win'] = 0
            metrics['avg_loss'] = 0
        
        return metrics
    
    def calculate_risk_metrics(self, strategy_result):
        """计算风险指标"""
        bankroll_history = strategy_result['bankroll_history']

        if len(bankroll_history) < 2:
            return {}

        # 转换为numpy数组，确保数值类型
        bankroll_series = np.array([float(x) for x in bankroll_history])
        
        # 最大回撤
        peak = bankroll_series[0]
        max_drawdown = 0
        max_drawdown_duration = 0
        current_drawdown_duration = 0
        
        for i, value in enumerate(bankroll_series):
            if value > peak:
                peak = value
                current_drawdown_duration = 0
            else:
                current_drawdown_duration += 1
                drawdown = (peak - value) / peak
                if drawdown > max_drawdown:
                    max_drawdown = drawdown
                    max_drawdown_duration = current_drawdown_duration
        
        # 计算收益序列
        returns = []
        for i in range(1, len(bankroll_series)):
            if bankroll_series[i-1] > 0:
                ret = (bankroll_series[i] - bankroll_series[i-1]) / bankroll_series[i-1]
                returns.append(ret)
        
        returns = np.array(returns)
        
        # 夏普比率（假设无风险利率为0）
        if len(returns) > 1 and np.std(returns) > 0:
            sharpe_ratio = np.mean(returns) / np.std(returns) * np.sqrt(252)  # 年化
        else:
            sharpe_ratio = 0
        
        # 索提诺比率（只考虑下行风险）
        negative_returns = returns[returns < 0]
        if len(negative_returns) > 1:
            downside_deviation = np.std(negative_returns) * np.sqrt(252)
            sortino_ratio = (np.mean(returns) * 252) / downside_deviation if downside_deviation > 0 else 0
        else:
            sortino_ratio = 0
        
        # 卡尔马比率（年化收益/最大回撤）
        annual_return = (bankroll_series[-1] / bankroll_series[0]) ** (252 / len(bankroll_series)) - 1
        calmar_ratio = annual_return / max_drawdown if max_drawdown > 0 else 0
        
        # 波动率
        volatility = np.std(returns) * np.sqrt(252) if len(returns) > 1 else 0
        
        # VaR (Value at Risk) - 95%置信度
        var_95 = np.percentile(returns, 5) if len(returns) > 0 else 0
        
        # 最大连续亏损
        max_consecutive_losses = self._calculate_max_consecutive_losses(strategy_result['trade_history'])
        
        risk_metrics = {
            'max_drawdown': max_drawdown,
            'max_drawdown_duration': max_drawdown_duration,
            'sharpe_ratio': sharpe_ratio,
            'sortino_ratio': sortino_ratio,
            'calmar_ratio': calmar_ratio,
            'volatility': volatility,
            'var_95': var_95,
            'max_consecutive_losses': max_consecutive_losses,
            'return_std': np.std(returns) if len(returns) > 1 else 0
        }
        
        return risk_metrics
    
    def _calculate_max_consecutive_losses(self, trade_history):
        """计算最大连续亏损次数"""
        max_losses = 0
        current_losses = 0

        for trade in trade_history:
            if float(trade['profit']) < 0:
                current_losses += 1
                max_losses = max(max_losses, current_losses)
            else:
                current_losses = 0

        return max_losses
    
    def calculate_advanced_metrics(self, strategy_result):
        """计算高级性能指标"""
        trade_history = strategy_result['trade_history']
        
        if not trade_history:
            return {}
        
        # 月度收益分析
        monthly_returns = self._calculate_monthly_returns(trade_history)
        
        # 胜率稳定性
        win_rate_stability = self._calculate_win_rate_stability(trade_history)
        
        # 收益分布分析
        profit_distribution = self._analyze_profit_distribution(trade_history)
        
        # 置信度分析
        confidence_analysis = self._analyze_confidence_performance(trade_history)
        
        advanced_metrics = {
            'monthly_returns': monthly_returns,
            'win_rate_stability': win_rate_stability,
            'profit_distribution': profit_distribution,
            'confidence_analysis': confidence_analysis
        }
        
        return advanced_metrics
    
    def _calculate_monthly_returns(self, trade_history):
        """计算月度收益（简化实现）"""
        # 这里简化处理，实际应该按真实日期分组
        chunk_size = max(1, len(trade_history) // 12)  # 假设分12个月
        monthly_returns = []
        
        for i in range(0, len(trade_history), chunk_size):
            chunk = trade_history[i:i+chunk_size]
            monthly_profit = sum(float(trade['profit']) for trade in chunk)
            monthly_returns.append(monthly_profit)
        
        return {
            'returns': monthly_returns,
            'avg_monthly_return': np.mean(monthly_returns) if monthly_returns else 0,
            'monthly_volatility': np.std(monthly_returns) if len(monthly_returns) > 1 else 0,
            'positive_months': sum(1 for r in monthly_returns if r > 0),
            'total_months': len(monthly_returns)
        }
    
    def _calculate_win_rate_stability(self, trade_history):
        """计算胜率稳定性"""
        if len(trade_history) < 10:
            return {'stability_score': 0, 'rolling_win_rates': []}
        
        window_size = max(5, len(trade_history) // 10)
        rolling_win_rates = []
        
        for i in range(window_size, len(trade_history) + 1):
            window_trades = trade_history[i-window_size:i]
            wins = sum(1 for trade in window_trades if float(trade['profit']) > 0)
            win_rate = wins / len(window_trades)
            rolling_win_rates.append(win_rate)
        
        stability_score = 1 - np.std(rolling_win_rates) if len(rolling_win_rates) > 1 else 0
        
        return {
            'stability_score': stability_score,
            'rolling_win_rates': rolling_win_rates,
            'win_rate_range': max(rolling_win_rates) - min(rolling_win_rates) if rolling_win_rates else 0
        }
    
    def _analyze_profit_distribution(self, trade_history):
        """分析收益分布"""
        profits = [float(trade['profit']) for trade in trade_history]
        
        if not profits:
            return {}
        
        return {
            'mean_profit': np.mean(profits),
            'median_profit': np.median(profits),
            'profit_skewness': self._calculate_skewness(profits),
            'profit_kurtosis': self._calculate_kurtosis(profits),
            'percentiles': {
                '10th': np.percentile(profits, 10),
                '25th': np.percentile(profits, 25),
                '75th': np.percentile(profits, 75),
                '90th': np.percentile(profits, 90)
            }
        }
    
    def _analyze_confidence_performance(self, trade_history):
        """分析置信度与表现的关系"""
        if not trade_history:
            return {}
        
        # 按置信度分组
        high_conf_trades = [t for t in trade_history if t.get('confidence', 0) >= 0.8]
        med_conf_trades = [t for t in trade_history if 0.6 <= t.get('confidence', 0) < 0.8]
        low_conf_trades = [t for t in trade_history if t.get('confidence', 0) < 0.6]
        
        def analyze_group(trades, name):
            if not trades:
                return {'count': 0, 'win_rate': 0, 'avg_profit': 0}

            wins = sum(1 for t in trades if float(t['profit']) > 0)
            return {
                'count': len(trades),
                'win_rate': wins / len(trades),
                'avg_profit': np.mean([float(t['profit']) for t in trades])
            }
        
        return {
            'high_confidence': analyze_group(high_conf_trades, 'high'),
            'medium_confidence': analyze_group(med_conf_trades, 'medium'),
            'low_confidence': analyze_group(low_conf_trades, 'low')
        }
    
    def _calculate_skewness(self, data):
        """计算偏度"""
        if len(data) < 3:
            return 0
        
        mean = np.mean(data)
        std = np.std(data)
        if std == 0:
            return 0
        
        skewness = np.mean([(x - mean) ** 3 for x in data]) / (std ** 3)
        return skewness
    
    def _calculate_kurtosis(self, data):
        """计算峰度"""
        if len(data) < 4:
            return 0
        
        mean = np.mean(data)
        std = np.std(data)
        if std == 0:
            return 0
        
        kurtosis = np.mean([(x - mean) ** 4 for x in data]) / (std ** 4) - 3
        return kurtosis
    
    def evaluate_all_strategies(self):
        """评估所有策略"""
        print("\n=== 策略性能评估 ===")
        
        if not self.backtest_results:
            print("请先加载回测结果")
            return None
        
        detailed_results = self.backtest_results.get('detailed_results', {})
        
        for strategy_name, strategy_result in detailed_results.items():
            print(f"\n评估策略: {strategy_name}")
            
            # 计算各类指标
            basic_metrics = self.calculate_basic_metrics(strategy_result)
            risk_metrics = self.calculate_risk_metrics(strategy_result)
            advanced_metrics = self.calculate_advanced_metrics(strategy_result)
            
            # 综合评估
            performance_score = self._calculate_performance_score(basic_metrics, risk_metrics)
            
            strategy_performance = {
                'strategy_name': strategy_name,
                'basic_metrics': basic_metrics,
                'risk_metrics': risk_metrics,
                'advanced_metrics': advanced_metrics,
                'performance_score': performance_score
            }
            
            self.performance_metrics[strategy_name] = strategy_performance
            
            # 输出关键指标
            print(f"  总收益率: {basic_metrics['total_return_pct']:.2f}%")
            print(f"  胜率: {basic_metrics['win_rate']:.3f}")
            print(f"  夏普比率: {risk_metrics.get('sharpe_ratio', 0):.3f}")
            print(f"  最大回撤: {risk_metrics.get('max_drawdown', 0)*100:.2f}%")
            print(f"  综合评分: {performance_score:.3f}")
        
        return self.performance_metrics
    
    def _calculate_performance_score(self, basic_metrics, risk_metrics):
        """计算综合性能评分"""
        # 权重设置
        weights = {
            'return': 0.3,
            'win_rate': 0.2,
            'sharpe': 0.25,
            'drawdown': 0.25
        }
        
        # 标准化指标（0-1范围）
        return_score = min(1.0, max(0, basic_metrics['total_return_pct'] / 100))
        win_rate_score = basic_metrics['win_rate']
        sharpe_score = min(1.0, max(0, risk_metrics.get('sharpe_ratio', 0) / 3))
        drawdown_score = max(0, 1 - risk_metrics.get('max_drawdown', 1))
        
        # 加权综合评分
        performance_score = (
            weights['return'] * return_score +
            weights['win_rate'] * win_rate_score +
            weights['sharpe'] * sharpe_score +
            weights['drawdown'] * drawdown_score
        )
        
        return performance_score
    
    def generate_performance_report(self):
        """生成性能报告"""
        if not self.performance_metrics:
            print("请先进行策略评估")
            return None
        
        # 策略排名
        strategy_ranking = sorted(
            self.performance_metrics.items(),
            key=lambda x: x[1]['performance_score'],
            reverse=True
        )
        
        # 生成报告
        report = {
            'evaluation_date': datetime.now().isoformat(),
            'strategies_evaluated': len(self.performance_metrics),
            'strategy_ranking': [
                {
                    'rank': i + 1,
                    'strategy': strategy_name,
                    'performance_score': metrics['performance_score'],
                    'return_pct': metrics['basic_metrics']['total_return_pct'],
                    'win_rate': metrics['basic_metrics']['win_rate'],
                    'sharpe_ratio': metrics['risk_metrics'].get('sharpe_ratio', 0),
                    'max_drawdown': metrics['risk_metrics'].get('max_drawdown', 0)
                }
                for i, (strategy_name, metrics) in enumerate(strategy_ranking)
            ],
            'detailed_metrics': self.performance_metrics,
            'summary_statistics': self._generate_summary_statistics()
        }
        
        return report
    
    def _generate_summary_statistics(self):
        """生成汇总统计"""
        if not self.performance_metrics:
            return {}
        
        all_returns = [m['basic_metrics']['total_return_pct'] for m in self.performance_metrics.values()]
        all_win_rates = [m['basic_metrics']['win_rate'] for m in self.performance_metrics.values()]
        all_sharpe = [m['risk_metrics'].get('sharpe_ratio', 0) for m in self.performance_metrics.values()]
        
        return {
            'avg_return': np.mean(all_returns),
            'avg_win_rate': np.mean(all_win_rates),
            'avg_sharpe': np.mean(all_sharpe),
            'best_return': max(all_returns),
            'worst_return': min(all_returns),
            'return_volatility': np.std(all_returns)
        }
    
    def save_performance_report(self, filename_prefix="performance_report"):
        """保存性能报告"""
        report = self.generate_performance_report()
        
        if not report:
            return None
        
        output_file = f"{filename_prefix}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        
        # 转换为可序列化格式
        serializable_report = self._make_serializable(report)
        
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(serializable_report, f, ensure_ascii=False, indent=2)
        
        print(f"\n性能报告已保存到: {output_file}")
        
        # 输出排名
        print(f"\n=== 策略性能排名 ===")
        for item in report['strategy_ranking']:
            print(f"{item['rank']}. {item['strategy']}: "
                  f"评分{item['performance_score']:.3f}, "
                  f"收益{item['return_pct']:.2f}%, "
                  f"胜率{item['win_rate']:.3f}")
        
        return output_file
    
    def _make_serializable(self, obj):
        """将对象转换为可序列化的格式"""
        if isinstance(obj, dict):
            return {k: self._make_serializable(v) for k, v in obj.items()}
        elif isinstance(obj, (list, tuple)):
            return [self._make_serializable(item) for item in obj]
        elif isinstance(obj, (np.integer, np.int64)):
            return int(obj)
        elif isinstance(obj, (np.floating, np.float64)):
            return float(obj)
        elif isinstance(obj, (np.bool_, bool)):
            return bool(obj)
        elif isinstance(obj, np.ndarray):
            return obj.tolist()
        elif pd.isna(obj) or obj is None:
            return None
        else:
            return str(obj)

def main():
    evaluator = StrategyPerformanceEvaluator()
    
    # 加载回测结果
    if not evaluator.load_backtest_results():
        return
    
    # 评估所有策略
    evaluator.evaluate_all_strategies()
    
    # 保存性能报告
    evaluator.save_performance_report()

if __name__ == "__main__":
    main()

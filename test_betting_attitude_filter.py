#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试博彩态度筛选功能
验证新增的博彩态度筛选是否正确实现
"""

import sqlite3
import sys
import os
from datetime import datetime

def test_betting_attitude_data_availability():
    """测试博彩态度筛选所需的数据可用性"""
    print("=== 测试博彩态度筛选数据可用性 ===")
    
    try:
        with sqlite3.connect("odds_data.db") as conn:
            conn.row_factory = sqlite3.Row
            cursor = conn.cursor()
            
            # 检查赔率数据的完整性
            cursor.execute('''
                SELECT COUNT(DISTINCT match_id) as match_count,
                       COUNT(DISTINCT company_name) as company_count,
                       COUNT(*) as total_records
                FROM odds 
                WHERE home_odds IS NOT NULL 
                  AND draw_odds IS NOT NULL 
                  AND away_odds IS NOT NULL
                  AND date IS NOT NULL 
                  AND time IS NOT NULL
            ''')
            
            stats = cursor.fetchone()
            print(f"赔率数据统计:")
            print(f"  有完整赔率的比赛数: {stats['match_count']}")
            print(f"  博彩公司数: {stats['company_count']}")
            print(f"  总赔率记录数: {stats['total_records']}")
            
            if stats['match_count'] == 0:
                print("❌ 没有找到完整的赔率数据")
                return False
            
            # 检查时间数据的分布
            cursor.execute('''
                SELECT date, time, COUNT(*) as count
                FROM odds 
                WHERE home_odds IS NOT NULL AND date IS NOT NULL AND time IS NOT NULL
                GROUP BY date, time
                ORDER BY count DESC
                LIMIT 5
            ''')
            
            time_stats = cursor.fetchall()
            print(f"\n前5个时间点的记录数:")
            for stat in time_stats:
                print(f"  {stat['date']} {stat['time']}: {stat['count']} 条记录")
            
            return True
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def test_company_opening_times():
    """测试博彩公司开盘时间数据"""
    print("\n=== 测试博彩公司开盘时间数据 ===")
    
    try:
        with sqlite3.connect("odds_data.db") as conn:
            conn.row_factory = sqlite3.Row
            cursor = conn.cursor()
            
            # 找到一场有多家公司数据的比赛
            cursor.execute('''
                SELECT match_id, COUNT(DISTINCT company_name) as company_count
                FROM odds 
                WHERE home_odds IS NOT NULL AND date IS NOT NULL AND time IS NOT NULL
                GROUP BY match_id
                HAVING company_count >= 3
                ORDER BY company_count DESC
                LIMIT 1
            ''')
            
            test_match = cursor.fetchone()
            if not test_match:
                print("❌ 没有找到有多家公司数据的比赛")
                return False
            
            match_id = test_match['match_id']
            company_count = test_match['company_count']
            print(f"测试比赛 {match_id} (有 {company_count} 家公司数据)")
            
            # 获取各公司的开盘时间
            cursor.execute('''
                SELECT company_name, date, time, home_odds, draw_odds, away_odds
                FROM odds
                WHERE match_id = ? AND home_odds IS NOT NULL
                ORDER BY company_name, date ASC, time ASC
            ''', (match_id,))
            
            all_odds = cursor.fetchall()
            
            # 按公司分组，找到每家公司的开盘时间
            company_opening = {}
            for odds in all_odds:
                company = odds['company_name']
                if company not in company_opening:
                    company_opening[company] = odds
            
            print(f"\n各公司开盘时间和赔率:")
            for company, opening_odds in company_opening.items():
                print(f"  {company:15s}: {opening_odds['date']} {opening_odds['time']} - "
                      f"主胜:{opening_odds['home_odds']} 平局:{opening_odds['draw_odds']} 客胜:{opening_odds['away_odds']}")
            
            return True
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def test_betting_attitude_logic():
    """测试博彩态度筛选逻辑"""
    print("\n=== 测试博彩态度筛选逻辑 ===")
    
    try:
        with sqlite3.connect("odds_data.db") as conn:
            conn.row_factory = sqlite3.Row
            cursor = conn.cursor()
            
            # 找到一场有多家公司同时开盘的比赛
            cursor.execute('''
                SELECT match_id, date, time, COUNT(DISTINCT company_name) as company_count
                FROM odds 
                WHERE home_odds IS NOT NULL AND date IS NOT NULL AND time IS NOT NULL
                GROUP BY match_id, date, time
                HAVING company_count >= 3
                ORDER BY company_count DESC
                LIMIT 1
            ''')
            
            test_data = cursor.fetchone()
            if not test_data:
                print("❌ 没有找到有多家公司同时开盘的比赛")
                return False
            
            match_id = test_data['match_id']
            test_date = test_data['date']
            test_time = test_data['time']
            company_count = test_data['company_count']
            
            print(f"测试比赛 {match_id} 在 {test_date} {test_time} (有 {company_count} 家公司)")
            
            # 获取该时间点所有公司的赔率
            cursor.execute('''
                SELECT company_name, home_odds, draw_odds, away_odds
                FROM odds
                WHERE match_id = ? AND date = ? AND time = ? AND home_odds IS NOT NULL
                ORDER BY company_name
            ''', (match_id, test_date, test_time))
            
            companies_odds = cursor.fetchall()
            
            print(f"\n该时间点各公司赔率:")
            home_odds_list = []
            for odds in companies_odds:
                home_odds = float(odds['home_odds'])
                home_odds_list.append(home_odds)
                print(f"  {odds['company_name']:15s}: 主胜:{odds['home_odds']} 平局:{odds['draw_odds']} 客胜:{odds['away_odds']}")
            
            # 模拟博彩态度分析
            if len(home_odds_list) >= 2:
                # 选择第一家公司作为目标
                target_company = companies_odds[0]['company_name']
                target_odds = home_odds_list[0]
                other_odds = home_odds_list[1:]
                
                avg_other_odds = sum(other_odds) / len(other_odds)
                ratio = target_odds / avg_other_odds
                
                print(f"\n博彩态度分析示例:")
                print(f"  目标公司: {target_company}")
                print(f"  目标赔率: {target_odds:.3f}")
                print(f"  其他公司平均赔率: {avg_other_odds:.3f}")
                print(f"  比率: {ratio:.3f}")
                
                threshold = 1.05
                result = ratio >= threshold
                print(f"  阈值: {threshold}")
                print(f"  结果: {result} ({'符合条件' if result else '不符合条件'})")
                
                return True
            else:
                print("❌ 数据不足以进行博彩态度分析")
                return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def test_multiple_companies_scenario():
    """测试多公司选择场景"""
    print("\n=== 测试多公司选择场景 ===")
    
    try:
        with sqlite3.connect("odds_data.db") as conn:
            conn.row_factory = sqlite3.Row
            cursor = conn.cursor()
            
            # 获取出现频率最高的几家公司
            cursor.execute('''
                SELECT company_name, COUNT(*) as count
                FROM odds 
                WHERE home_odds IS NOT NULL
                GROUP BY company_name
                ORDER BY count DESC
                LIMIT 5
            ''')
            
            top_companies = cursor.fetchall()
            print(f"出现频率最高的5家公司:")
            for company in top_companies:
                print(f"  {company['company_name']:15s}: {company['count']:6d} 条记录")
            
            if len(top_companies) >= 2:
                # 选择前两家公司进行测试
                selected_companies = [top_companies[0]['company_name'], top_companies[1]['company_name']]
                print(f"\n选择测试公司: {selected_companies}")
                
                # 找到同时有这两家公司数据的比赛
                placeholders = ','.join(['?' for _ in selected_companies])
                cursor.execute(f'''
                    SELECT match_id, COUNT(DISTINCT company_name) as company_count
                    FROM odds 
                    WHERE company_name IN ({placeholders}) AND home_odds IS NOT NULL
                    GROUP BY match_id
                    HAVING company_count = ?
                    LIMIT 1
                ''', selected_companies + [len(selected_companies)])
                
                test_match = cursor.fetchone()
                if test_match:
                    match_id = test_match['match_id']
                    print(f"找到测试比赛 {match_id}，包含所选的 {len(selected_companies)} 家公司")
                    
                    # 模拟多公司博彩态度分析
                    print(f"✅ 多公司场景测试数据准备完成")
                    return True
                else:
                    print("❌ 没有找到包含所选公司的比赛")
                    return False
            else:
                print("❌ 公司数据不足")
                return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🔍 博彩态度筛选功能测试")
    print("=" * 60)
    
    # 检查数据库文件是否存在
    if not os.path.exists("odds_data.db"):
        print("❌ 数据库文件 odds_data.db 不存在")
        return False
    
    tests = [
        test_betting_attitude_data_availability,
        test_company_opening_times,
        test_betting_attitude_logic,
        test_multiple_companies_scenario
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"❌ 测试异常: {e}")
    
    print("\n" + "=" * 60)
    print(f"📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！")
        print("\n💡 博彩态度筛选功能说明:")
        print("- 可以选择单个或多个博彩公司")
        print("- 可以选择主胜、平局、客胜赔率类型")
        print("- 可以设置阈值（默认1.05）")
        print("- 单个公司：该公司开盘赔率/其他公司同时点平均赔率 >= 阈值")
        print("- 多个公司：所有选中公司都必须满足条件")
        print("- 基于动态时间线原理，确保时间点的准确性")
        return True
    else:
        print("⚠️  部分测试失败，请检查相关功能。")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)

# 升降统计功能说明

## 🎯 功能概述

新增的"升降统计"功能可以对指定比赛和博彩公司的赔率变化进行详细统计，包括：
- **赔率下降次数统计**
- **赔率上升次数统计**  
- **初始赔率与最终赔率对比**
- **赔率差值和变化率计算**

## 🔧 功能特点

### 1. 智能分析
- **自动识别赔率变化方向**：准确统计每次赔率变化是上升还是下降
- **分类统计**：分别统计主胜、平局、客胜三种结果的赔率变化
- **完整数据**：提供初始赔率、最终赔率、差值、变化率等完整信息

### 2. 可视化展示
- **颜色分区**：主胜(浅红)、平局(浅绿)、客胜(浅蓝)用不同颜色区分
- **升降标识**：上升用绿色背景，下降用红色背景
- **直观对比**：变化率用正负号和颜色清晰显示

### 3. 详细统计
- **下降次数**：统计赔率下降的总次数
- **上升次数**：统计赔率上升的总次数
- **数据点数**：显示参与统计的赔率数据条数
- **变化幅度**：计算总体变化率百分比

## 📊 表格结构

升降统计表格包含以下10列：

| 列名 | 说明 | 颜色标识 |
|------|------|----------|
| 比赛ID | 分析的比赛标识 | - |
| 博彩公司 | 选择的博彩公司名称 | - |
| 结果类型 | 主胜/平局/客胜 | 浅红/浅绿/浅蓝 |
| 下降次数 | 赔率下降的次数 | 红色背景(有下降时) |
| 上升次数 | 赔率上升的次数 | 绿色背景(有上升时) |
| 初始赔率 | 第一条赔率数据 | - |
| 最终赔率 | 最后一条赔率数据 | - |
| 赔率差值 | 最终-初始赔率 | 绿色(正)/红色(负) |
| 变化率(%) | 相对变化百分比 | 绿色(正)/红色(负) |
| 数据点数 | 参与统计的数据条数 | - |

## 🎮 使用方法

### 步骤1：输入基本信息
1. **输入比赛ID** - 在"比赛ID"输入框中输入要分析的比赛ID
2. **选择博彩公司** - 在"博彩公司"下拉框中选择要分析的博彩公司
3. **确保数据库连接** - 确认已选择正确的数据库

### 步骤2：执行升降统计
1. **点击"升降统计"按钮** - 橙红色的按钮，位于分析按钮组中
2. **等待分析完成** - 系统会自动获取赔率数据并进行统计
3. **查看结果** - 结果会显示在新的升降统计表格中

### 步骤3：解读结果
1. **查看整体趋势** - 通过变化率了解赔率总体走向
2. **分析变化频率** - 通过升降次数了解赔率波动情况
3. **对比不同结果** - 比较主胜、平局、客胜的变化模式

## 📈 统计算法

### 升降判断逻辑
```python
for i in range(1, len(odds_sequence)):
    if odds_sequence[i] < odds_sequence[i-1]:
        down_count += 1  # 赔率下降
    elif odds_sequence[i] > odds_sequence[i-1]:
        up_count += 1    # 赔率上升
    # 相等时不计入升降次数
```

### 变化率计算
```python
change_rate = (final_odds - initial_odds) / initial_odds * 100
```

## 🎨 颜色说明

### 结果类型颜色
- **主胜**：浅红色背景 (RGB: 255, 235, 235)
- **平局**：浅绿色背景 (RGB: 235, 255, 235)  
- **客胜**：浅蓝色背景 (RGB: 235, 235, 255)

### 升降颜色
- **上升/正值**：绿色背景 (RGB: 200, 255, 200)
- **下降/负值**：红色背景 (RGB: 255, 200, 200)

## 💡 使用场景

### 1. 赔率趋势分析
- **观察市场情绪**：通过升降次数了解市场对比赛结果的看法变化
- **识别关键时点**：找出赔率变化的关键时间节点
- **预测走势**：基于历史变化模式预测未来趋势

### 2. 投注策略制定
- **时机选择**：根据赔率变化选择最佳投注时机
- **风险评估**：通过变化频率评估投注风险
- **价值发现**：识别被市场低估或高估的投注选项

### 3. 数据质量检查
- **异常检测**：发现异常的赔率变化模式
- **数据完整性**：通过数据点数确认数据完整性
- **趋势验证**：验证赔率变化是否符合预期

## ⚠️ 注意事项

### 数据要求
- **最少数据量**：需要至少2条赔率数据才能进行升降统计
- **数据连续性**：统计基于时间顺序的连续赔率数据
- **数据有效性**：只统计有效的非空赔率数据

### 统计限制
- **时间顺序**：统计严格按照数据库中的时间顺序进行
- **相等处理**：相等的赔率不计入升降次数
- **精度限制**：赔率显示精度为3位小数，变化率为2位小数

### 解读建议
- **综合分析**：结合升降次数、变化率、数据点数综合分析
- **对比分析**：比较不同结果类型的变化模式
- **历史对比**：与其他类似比赛的统计结果进行对比

## 🔧 技术实现

### 核心方法
- `start_odds_trend_analysis()` - 启动升降统计分析
- `analyze_odds_trends()` - 执行赔率趋势分析算法
- `populate_odds_trend_table()` - 填充统计结果到表格

### 数据流程
1. **数据获取** - 从数据库获取指定比赛和公司的赔率数据
2. **数据处理** - 按结果类型分组并提取赔率序列
3. **统计计算** - 计算升降次数、差值、变化率等指标
4. **结果展示** - 将统计结果填充到表格并应用颜色标识

## ✅ 功能验证

### 测试建议
1. **选择数据丰富的比赛** - 选择有较多赔率变化记录的比赛进行测试
2. **验证计算准确性** - 手动验证几个关键统计数据的准确性
3. **检查颜色显示** - 确认颜色标识正确显示升降和结果类型
4. **测试边界情况** - 测试数据不足、无变化等边界情况

### 预期效果
- **清晰的统计结果** - 每种结果类型都有完整的升降统计
- **直观的可视化** - 通过颜色快速识别变化趋势
- **准确的数值计算** - 所有统计数值准确无误
- **良好的用户体验** - 操作简单，结果易懂

## 🎉 总结

升降统计功能为赔率分析提供了新的维度，通过详细的升降次数统计和变化率分析，帮助用户：

1. **深入理解市场动态** - 通过升降模式了解市场情绪变化
2. **优化投注决策** - 基于赔率变化趋势制定更好的投注策略  
3. **提高分析效率** - 通过可视化表格快速获取关键信息
4. **增强风险控制** - 通过变化频率和幅度评估投注风险

这个功能完全独立于现有功能，不会影响任何现有的分析流程，为用户提供了更丰富的分析工具。

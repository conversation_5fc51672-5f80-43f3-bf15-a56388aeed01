#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试完赛后更新功能
"""

import sys
import os
import sqlite3
from datetime import datetime

def test_find_matches_without_results():
    """测试查找无比赛结果的比赛功能"""
    
    print("🔍 测试完赛后更新功能")
    print("=" * 50)
    
    # 查找数据库文件
    db_files = []
    for file in os.listdir('.'):
        if file.endswith('.db'):
            db_files.append(file)
    
    if not db_files:
        print("❌ 未找到数据库文件")
        return
    
    db_path = db_files[0]
    print(f"📂 使用数据库: {db_path}")
    
    try:
        with sqlite3.connect(db_path) as conn:
            cursor = conn.cursor()
            
            # 查看数据库表结构
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
            tables = cursor.fetchall()
            print(f"📋 数据库表: {[table[0] for table in tables]}")
            
            # 检查matches表的字段
            if any('matches' in str(table).lower() for table in tables):
                cursor.execute("PRAGMA table_info(matches);")
                columns = cursor.fetchall()
                print(f"📊 matches表字段: {[col[1] for col in columns]}")
                
                # 查找无比赛结果的比赛
                cursor.execute("""
                    SELECT match_id, league, home_team, away_team, match_time, home_score, away_score
                    FROM matches 
                    WHERE (home_score IS NULL OR home_score = '' OR 
                           away_score IS NULL OR away_score = '')
                    ORDER BY match_time DESC
                    LIMIT 10
                """)
                
                matches_without_results = cursor.fetchall()
                
                print(f"\n🎯 找到 {len(matches_without_results)} 场无比赛结果的比赛（显示前10场）:")
                print("-" * 80)
                
                for match in matches_without_results:
                    match_id, league, home_team, away_team, match_time, home_score, away_score = match
                    print(f"ID: {match_id}")
                    print(f"  联赛: {league}")
                    print(f"  对阵: {home_team} vs {away_team}")
                    print(f"  时间: {match_time}")
                    print(f"  比分: {home_score}:{away_score}")
                    print("-" * 40)
                
                # 统计总数
                cursor.execute("""
                    SELECT COUNT(*) 
                    FROM matches 
                    WHERE (home_score IS NULL OR home_score = '' OR 
                           away_score IS NULL OR away_score = '')
                """)
                
                total_without_results = cursor.fetchone()[0]
                
                # 统计有结果的比赛
                cursor.execute("""
                    SELECT COUNT(*) 
                    FROM matches 
                    WHERE home_score IS NOT NULL AND home_score != '' AND 
                          away_score IS NOT NULL AND away_score != ''
                """)
                
                total_with_results = cursor.fetchone()[0]
                
                # 统计总比赛数
                cursor.execute("SELECT COUNT(*) FROM matches")
                total_matches = cursor.fetchone()[0]
                
                print(f"\n📊 统计信息:")
                print(f"总比赛数: {total_matches}")
                print(f"有比赛结果: {total_with_results}")
                print(f"无比赛结果: {total_without_results}")
                print(f"完成率: {total_with_results/total_matches*100:.1f}%")
                
                if total_without_results > 0:
                    print(f"\n✅ 完赛后更新功能可以更新 {total_without_results} 场比赛")
                else:
                    print(f"\n✅ 所有比赛都已有结果，无需更新")
                
            else:
                print("❌ 未找到matches表")
                
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")

def test_ui_integration():
    """测试UI集成"""
    
    print(f"\n🔧 UI集成测试:")
    print("-" * 30)
    
    try:
        # 检查UI文件是否正确修改
        with open('odds_scraper_ui.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查按钮是否添加
        if 'update_finished_button' in content:
            print("✅ 完赛后更新按钮已添加")
        else:
            print("❌ 完赛后更新按钮未找到")
        
        # 检查方法是否添加
        methods_to_check = [
            'start_update_finished_matches',
            'find_matches_without_results',
            'update_finished_matches_worker',
            'delete_match_data'
        ]
        
        for method in methods_to_check:
            if f'def {method}' in content:
                print(f"✅ 方法 {method} 已添加")
            else:
                print(f"❌ 方法 {method} 未找到")
        
        # 检查消息处理是否添加
        if 'update_finished_complete' in content:
            print("✅ 消息处理已添加")
        else:
            print("❌ 消息处理未找到")
        
        print(f"\n🎯 功能说明:")
        print("1. 点击'完赛后更新'按钮")
        print("2. 系统会查找无比赛结果的比赛")
        print("3. 确认后重新抓取这些比赛的所有数据")
        print("4. 更新过程中会显示进度")
        print("5. 完成后显示统计结果")
        
    except Exception as e:
        print(f"❌ UI集成测试失败: {e}")

if __name__ == "__main__":
    test_find_matches_without_results()
    test_ui_integration()
    
    print(f"\n🚀 测试完成！")
    print("现在可以启动主程序测试完赛后更新功能：")
    print("python odds_scraper_ui.py")

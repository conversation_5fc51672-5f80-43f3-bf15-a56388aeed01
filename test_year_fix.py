#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试年份修复功能
验证完赛后更新功能中的年份问题是否已解决
"""

import sys
import os
from datetime import datetime

def test_year_inference():
    """测试年份推测功能"""
    
    print("🔧 测试年份推测功能修复")
    print("=" * 50)
    
    try:
        from enhanced_odds_scraper import EnhancedOddsScraper
        
        # 创建抓取器
        scraper = EnhancedOddsScraper()
        print("✅ EnhancedOddsScraper 创建成功")
        
        # 测试不同的比赛ID和日期组合
        test_cases = [
            # (日期字符串, 比赛ID, 期望年份, 描述)
            ("08-13", "2213559", 2022, "2022年的老比赛"),
            ("12-25", "2600000", 2023, "2023年的比赛"),
            ("06-20", "2900000", 2024, "2024年的比赛"),
            ("07-26", "3200000", 2025, "2025年的比赛"),
            ("01-15", "3500000", 2026, "2026年的比赛"),
        ]
        
        print(f"\n📋 测试年份推测逻辑:")
        print("-" * 40)
        
        for date_str, match_id, expected_year, description in test_cases:
            try:
                inferred_year = scraper._infer_match_year(date_str, match_id)
                status = "✅" if inferred_year == expected_year else "❌"
                print(f"{status} 日期: {date_str}, 比赛ID: {match_id}")
                print(f"    推测年份: {inferred_year}, 期望: {expected_year} ({description})")
                if inferred_year != expected_year:
                    print(f"    ⚠️ 年份推测不符合期望")
            except Exception as e:
                print(f"❌ 测试失败: {e}")
        
        print(f"\n📋 测试完整比赛信息提取:")
        print("-" * 40)
        
        # 测试一个具体的比赛ID
        test_match_id = "2213559"  # 这应该是2022年的比赛
        print(f"测试比赛ID: {test_match_id}")
        
        match_info = scraper.extract_match_info(test_match_id)
        
        if match_info:
            print("✅ 比赛信息提取成功")
            print(f"  比赛时间: {match_info.get('match_time', 'N/A')}")
            print(f"  比赛日期: {match_info.get('match_date', 'N/A')}")
            print(f"  推测年份: {match_info.get('inferred_year', 'N/A')}")
            print(f"  原始日期: {match_info.get('raw_date', 'N/A')}")
            print(f"  原始时间: {match_info.get('raw_time', 'N/A')}")
            print(f"  主队: {match_info.get('home_team', 'N/A')}")
            print(f"  客队: {match_info.get('away_team', 'N/A')}")
            
            # 检查年份是否正确
            match_time = match_info.get('match_time', '')
            if match_time.startswith('2022'):
                print("  ✅ 年份正确：2022年")
            elif match_time.startswith('2025'):
                print("  ❌ 年份错误：仍然是2025年")
            else:
                year = match_time[:4] if match_time else 'Unknown'
                print(f"  ⚠️ 年份: {year}")
        else:
            print("❌ 比赛信息提取失败")
            
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")

def test_match_info_extractor():
    """测试 MatchInfoExtractor 的年份推测功能"""
    
    print(f"\n🔧 测试 MatchInfoExtractor 年份推测功能")
    print("-" * 50)
    
    try:
        from match_info_extractor import MatchInfoExtractor
        
        # 创建提取器
        extractor = MatchInfoExtractor()
        print("✅ MatchInfoExtractor 创建成功")
        
        # 测试年份推测方法
        test_cases = [
            ("08-13", "2213559", 2022, "2022年的老比赛"),
            ("12-25", "2600000", 2023, "2023年的比赛"),
            ("06-20", "2900000", 2024, "2024年的比赛"),
        ]
        
        print(f"\n📋 测试年份推测逻辑:")
        print("-" * 40)
        
        for date_str, match_id, expected_year, description in test_cases:
            try:
                inferred_year = extractor._infer_match_year(date_str, match_id)
                status = "✅" if inferred_year == expected_year else "❌"
                print(f"{status} 日期: {date_str}, 比赛ID: {match_id}")
                print(f"    推测年份: {inferred_year}, 期望: {expected_year} ({description})")
            except Exception as e:
                print(f"❌ 测试失败: {e}")
                
    except Exception as e:
        print(f"❌ MatchInfoExtractor 测试失败: {e}")

def test_complete_update_simulation():
    """模拟完赛后更新功能的年份处理"""
    
    print(f"\n🔧 模拟完赛后更新功能")
    print("-" * 50)
    
    try:
        from enhanced_odds_scraper import EnhancedOddsScraper
        
        scraper = EnhancedOddsScraper()
        
        # 模拟一个需要更新的比赛
        test_match_id = "2213559"  # 2022年的比赛
        print(f"模拟更新比赛: {test_match_id}")
        
        # 这里只测试比赛信息提取部分，不进行完整的数据抓取
        match_info = scraper.extract_match_info(test_match_id)
        
        if match_info:
            print("✅ 比赛信息提取成功")
            
            # 检查关键字段
            required_fields = ['match_id', 'match_time', 'match_date', 'inferred_year']
            for field in required_fields:
                if field in match_info:
                    print(f"  ✅ {field}: {match_info[field]}")
                else:
                    print(f"  ❌ 缺少字段: {field}")
            
            # 验证年份是否正确
            match_time = match_info.get('match_time', '')
            inferred_year = match_info.get('inferred_year')
            
            if match_time and str(inferred_year) in match_time:
                print(f"  ✅ 年份一致性检查通过: {inferred_year}")
            else:
                print(f"  ❌ 年份一致性检查失败")
                print(f"    match_time: {match_time}")
                print(f"    inferred_year: {inferred_year}")
                
        else:
            print("❌ 比赛信息提取失败")
            
    except Exception as e:
        print(f"❌ 完赛后更新模拟失败: {e}")

def main():
    """主函数"""
    
    print("🚀 年份修复功能测试")
    print("=" * 60)
    
    # 测试1：年份推测功能
    test_year_inference()
    
    # 测试2：MatchInfoExtractor
    test_match_info_extractor()
    
    # 测试3：完赛后更新模拟
    test_complete_update_simulation()
    
    print(f"\n" + "=" * 60)
    print("📋 测试总结")
    print("=" * 60)
    
    print("✅ 年份推测逻辑已更新")
    print("✅ EnhancedOddsScraper 支持智能年份推测")
    print("✅ MatchInfoExtractor 支持智能年份推测")
    print("✅ 完赛后更新功能应该能正确处理年份")
    
    print(f"\n🎯 修复说明:")
    print("1. 更新了比赛ID范围映射，支持2025年的比赛")
    print("2. 在 enhanced_odds_scraper.py 中更新了 _infer_match_year 方法")
    print("3. 在 match_info_extractor.py 中添加了智能年份推测功能")
    print("4. 完赛后更新功能现在会使用智能年份推测")
    
    print(f"\n🚀 测试完成！现在可以测试完赛后更新功能")

if __name__ == "__main__":
    main()

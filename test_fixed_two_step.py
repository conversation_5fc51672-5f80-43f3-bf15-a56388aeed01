#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试修复后的两步方案
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from enhanced_odds_scraper import EnhancedOddsScraper
import logging

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_fixed_two_step():
    """测试修复后的两步方案"""
    print("🧪 测试修复后的两步方案 - 比赛2399071")
    print("=" * 60)
    
    scraper = EnhancedOddsScraper()
    match_id = "2399071"
    
    print(f"🎯 目标比赛: {match_id}")
    print("-" * 40)
    
    # 第一步：测试从JavaScript获取真实链接
    print("\n📋 第一步：从JavaScript获取真实链接")
    
    company_links = scraper.get_company_odds_links_from_oddslist(match_id)
    
    if company_links:
        print(f"✅ 第一步成功：找到 {len(company_links)} 家公司的链接")
        
        # 显示bet365的链接
        if '281' in company_links:
            bet365_data = company_links['281']
            print(f"  📊 bet365数据:")
            print(f"    Record ID: {bet365_data['record_id']}")
            print(f"    URL: {bet365_data['url']}")
            
            # 验证这个record ID是否正确
            correct_record_id = "*********"
            if bet365_data['record_id'] == correct_record_id:
                print(f"    ✅ Record ID正确！")
            else:
                print(f"    ❌ Record ID错误，应该是: {correct_record_id}")
        else:
            print(f"  ❌ 未找到bet365的链接")
            
        # 显示所有找到的公司
        print(f"  📋 找到的所有公司:")
        for company_id, data in company_links.items():
            print(f"    {data['company_name']} (ID: {company_id}): {data['record_id']}")
            
    else:
        print(f"❌ 第一步失败：未找到任何公司的链接")
        return
    
    # 第二步：测试完整的两步方案
    print(f"\n📋 第二步：测试完整的两步方案")
    
    all_odds_data = scraper.parse_company_odds_from_two_step_method(match_id)
    
    if all_odds_data:
        print(f"✅ 两步方案成功：获取到 {len(all_odds_data)} 条赔率记录")
        
        # 统计各公司的记录数量
        company_counts = {}
        for record in all_odds_data:
            company_name = record['company_name']
            company_counts[company_name] = company_counts.get(company_name, 0) + 1
        
        print(f"  📊 各公司记录数量:")
        for company_name, count in company_counts.items():
            print(f"    {company_name}: {count} 条记录")
        
        # 显示bet365的前几条记录
        bet365_records = [r for r in all_odds_data if r['company_name'] == 'bet365']
        if bet365_records:
            print(f"  📊 bet365前3条记录:")
            for i, record in enumerate(bet365_records[:3], 1):
                home_odds = record.get('home_odds', 'N/A')
                draw_odds = record.get('draw_odds', 'N/A')
                away_odds = record.get('away_odds', 'N/A')
                change_time = record.get('change_time', 'N/A')
                print(f"    {i}. {home_odds} {draw_odds} {away_odds} ({change_time})")
                
            # 验证数据是否正确（应该是10月份的数据）
            first_record = bet365_records[0]
            change_time = first_record.get('change_time', '')
            if '10-' in change_time:
                print(f"    ✅ 数据时间正确（10月份）")
            elif '08-' in change_time:
                print(f"    ❌ 数据时间错误（8月份，应该是10月份）")
            else:
                print(f"    ⚠️ 数据时间未知: {change_time}")
        else:
            print(f"  ❌ 未找到bet365的记录")
            
    else:
        print(f"❌ 两步方案失败：未获取到任何赔率记录")

if __name__ == "__main__":
    test_fixed_two_step()

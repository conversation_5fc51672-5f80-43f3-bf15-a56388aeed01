# 赔率组合观察分析工具

## 功能介绍

这是一个用于分析足球比赛赔率组合的工具，可以帮助用户观察特定比赛的赔率组合在其他比赛中的出现频率和结果分布。

## 主要功能

1. **数据库选择**：支持切换不同的数据库文件（主数据库和联赛分库）
2. **比赛观察**：输入要观察的比赛ID
3. **博彩公司选择**：选择要分析的博彩公司
4. **组合数设置**：设置赔率组合的大小（2-10组）
5. **智能分析**：自动生成赔率组合并在其他比赛中查找匹配
6. **结果展示**：
   - 组合统计表：显示每个组合的匹配次数和胜平负分布
   - 详细匹配表：显示具体匹配的比赛信息

## 使用方法

### 1. 启动程序

```bash
python start_odds_combination_analyzer.py
```

### 2. 操作步骤

1. **选择数据库**：在数据库下拉框中选择要使用的数据库文件
2. **输入比赛ID**：在比赛ID输入框中输入要观察的比赛ID（例如：2701757）
3. **选择博彩公司**：从下拉框中选择要分析的博彩公司（例如：bet365）
4. **设置组合数**：使用数字选择器设置赔率组合的大小（默认为3）
5. **开始分析**：点击"开始分析"按钮开始分析过程
6. **查看结果**：
   - 上方表格显示所有组合的统计信息
   - 点击某一行可在下方表格查看该组合的详细匹配信息

### 3. 结果解读

#### 组合统计表
- **组合序号**：组合的编号（从1开始）
- **组合内容**：显示组合中每个赔率的主胜、平局、客胜赔率
- **匹配次数**：该组合在其他比赛中的匹配次数
- **主胜/平局/客胜**：匹配比赛的结果分布

#### 详细匹配表
- **比赛ID**：匹配的比赛ID
- **比赛结果**：比赛的结果类型（主胜/平局/客胜）
- **匹配位置**：在该比赛赔率序列中的匹配位置
- **比分**：比赛的具体比分

## 技术特点

1. **精确匹配**：使用浮点数容差进行赔率匹配，避免精度问题
2. **时间排序**：严格按照时间顺序处理赔率数据
3. **多线程处理**：使用后台线程进行分析，避免界面卡顿
4. **实时反馈**：提供详细的进度信息和状态更新
5. **数据完整性**：自动检查数据完整性和有效性

## 文件结构

```
赔率组合观察/
├── odds_combination_database.py    # 数据库操作模块
├── odds_combination_analyzer.py    # 分析核心模块
├── odds_combination_ui.py          # PyQt5界面模块
├── start_odds_combination_analyzer.py  # 启动脚本
└── README.md                       # 说明文档
```

## 依赖要求

- Python 3.6+
- PyQt5
- sqlite3（Python标准库）

## 注意事项

1. 确保数据库文件存在且包含所需的赔率数据
2. 比赛ID必须在数据库中存在
3. 选择的博彩公司必须有该比赛的赔率数据
4. 赔率数据数量必须大于等于设置的组合数
5. 分析过程可能需要一些时间，请耐心等待

## 示例

以比赛ID "2701757"、博彩公司 "bet365"、组合数 "3" 为例：

1. 程序会获取该比赛bet365的所有赔率数据（按时间排序）
2. 生成所有可能的3组连续赔率组合
3. 在数据库中的其他比赛中查找相同的赔率组合
4. 统计匹配次数和结果分布
5. 在界面中展示分析结果

## 测试和演示

### 快速功能测试
```bash
python 赔率组合观察/fast_test.py
```

### 演示分析过程
```bash
python 赔率组合观察/demo_analysis.py
```

### 完整功能测试
```bash
python 赔率组合观察/test_functionality.py
```

## 项目文件说明

- `odds_combination_database.py` - 数据库操作核心模块
- `odds_combination_analyzer.py` - 赔率组合分析核心算法
- `odds_combination_ui.py` - PyQt5图形界面
- `start_odds_combination_analyzer.py` - UI启动脚本
- `demo_analysis.py` - 命令行演示脚本
- `fast_test.py` - 快速功能测试
- `test_functionality.py` - 完整功能测试
- `start_ui.bat` - Windows启动脚本
- `run_test.bat` - Windows测试脚本

## 故障排除

1. **数据库连接失败**：检查数据库文件路径是否正确
2. **比赛不存在**：确认比赛ID在数据库中存在
3. **无赔率数据**：确认选择的博彩公司有该比赛的数据
4. **分析失败**：查看状态信息了解具体错误原因
5. **PyQt5导入错误**：运行 `pip install PyQt5` 安装PyQt5
6. **分析时间过长**：大数据库分析可能需要较长时间，请耐心等待

## 性能说明

- 分析时间取决于数据库大小和其他比赛数量
- 示例数据库（5000+比赛）的完整分析大约需要1-5分钟
- 建议在分析大量数据时使用UI界面，可以看到实时进度
